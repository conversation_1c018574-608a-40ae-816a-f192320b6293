/* empty css                                                                    */import{_ as d,h as n,c,o as u,t as p,n as l}from"./index-BKy0rL_2.js";const o={__name:"StatusBadge",props:{status:{type:[String,Number],required:!0},label:{type:String,default:""},type:{type:String,default:"default",validator:t=>["default","order","payment","user","product"].includes(t)}},setup(t){const e=t,i=n(()=>e.status===null||e.status===void 0?"Unknown":e.label?e.label:e.type==="order"?{0:"Processing",1:"Pending",2:"Shipped",3:"Delivered",4:"Cancelled",Processing:"Processing",Pending:"Pending",Shipped:"Shipped",Delivered:"Delivered",Cancelled:"Cancelled"}[e.status]||e.status.toString():e.type==="payment"?{0:"Pending",1:"Completed",2:"Refunded",3:"Failed",Pending:"Pending",Completed:"Completed",Refunded:"Refunded",Failed:"Failed"}[e.status]||e.status.toString():e.type==="product"&&{0:"Pending",1:"Approved",2:"Rejected",pending:"Pending",approved:"Approved",rejected:"Rejected"}[e.status]||e.status.toString()),r=n(()=>{if(e.status===null||e.status===void 0)return"is-light";if(e.type==="order")return{0:"is-info",1:"is-warning",2:"is-primary",3:"is-success",4:"is-danger",Processing:"is-info",Pending:"is-warning",Shipped:"is-primary",Delivered:"is-success",Cancelled:"is-danger"}[e.status]||"is-light";if(e.type==="payment")return{0:"is-warning",1:"is-success",2:"is-info",3:"is-danger",Pending:"is-warning",Completed:"is-success",Refunded:"is-info",Failed:"is-danger"}[e.status]||"is-light";if(e.type==="product")return{0:"is-warning",1:"is-success",2:"is-danger",pending:"is-warning",approved:"is-success",rejected:"is-danger"}[e.status]||"is-light";switch(e.status.toString().toLowerCase()){case"active":case"approved":case"completed":case"1":return"is-success";case"inactive":case"pending":case"0":return"is-warning";case"rejected":case"cancelled":case"2":return"is-danger";default:return"is-light"}});return(s,a)=>(u(),c("span",{class:l(["tag",r.value])},p(i.value),3))}},m=d(o,[["__scopeId","data-v-bb5a7254"]]);export{m as S};
