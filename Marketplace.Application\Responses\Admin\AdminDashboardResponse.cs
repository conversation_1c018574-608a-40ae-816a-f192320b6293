﻿﻿namespace Marketplace.Application.Responses.Admin;

public class AdminDashboardResponse
{
    public AdminDashboardStatsResponse Stats { get; set; }
    public List<AdminSalesDataItem> SalesData { get; set; }
    public List<AdminOrdersByStatusItem> OrdersByStatus { get; set; }
    public List<AdminRecentOrderResponse> RecentOrders { get; set; }
    public List<AdminSellerRequestResponse> SellerRequests { get; set; }
}

public class AdminDashboardStatsResponse
{
    public int Products { get; set; }
    public int Users { get; set; }
    public int Orders { get; set; }
    public decimal Revenue { get; set; }
}

public class AdminSalesDataItem
{
    public string Label { get; set; }
    public decimal Value { get; set; }
    public DateTime Date { get; set; }
}

public class AdminOrdersByStatusItem
{
    public string Status { get; set; }
    public int Count { get; set; }
}

public class AdminRecentOrderResponse
{
    public Guid Id { get; set; }
    public string CustomerName { get; set; }
    public decimal Total { get; set; }
    public string Status { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class AdminSellerRequestResponse
{
    public Guid Id { get; set; }
    public string UserName { get; set; }
    public string CompanyName { get; set; }
    public string AdditionalInformation { get; set; }
    public DateTime CreatedAt { get; set; }
}
