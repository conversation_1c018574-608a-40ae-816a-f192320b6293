﻿﻿using AutoMapper;
using Marketplace.Application.Responses;
using Marketplace.Application.Services.Auth;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Configuration;

namespace Marketplace.Application.Commands.Auth;

public class PatchProfileCommandHandler : IRequestHandler<PatchProfileCommand, UserDto>
{
    private readonly IUserRepository _userRepository;
    private readonly IEmailService _emailService;
    private readonly ITokenService _tokenService;
    private readonly IMapper _mapper;
    private readonly string _baseUrl;

    public PatchProfileCommandHandler(
        IUserRepository userRepository, 
        IEmailService emailService, 
        ITokenService tokenService, 
        IMapper mapper, 
        IConfiguration configuration)
    {
        _userRepository = userRepository;
        _emailService = emailService;
        _tokenService = tokenService;
        _mapper = mapper;
        _baseUrl = configuration["Frontend:BaseUrl"] 
            ?? throw new ArgumentNullException("Frontend:BaseUrl is not configured.");
    }

    public async Task<UserDto> Handle(PatchProfileCommand request, CancellationToken cancellationToken)
    {
        var user = await _userRepository.GetByIdAsync(request.Id, cancellationToken)
        ?? throw new KeyNotFoundException("Користувача не знайдено!");

        // Оновлення імені користувача
        if (!string.IsNullOrEmpty(request.Username) && request.Username != user.Username)
        {
            user.UpdateUsername(request.Username);
        }

        // Оновлення електронної пошти
        if (!string.IsNullOrEmpty(request.Email) && request.Email != user.Email.Value)
        {
            user.Email = new Email(request.Email);
            user.EmailConfirmationToken = _tokenService.GenerateToken();
            user.EmailConfirmed = false;
            user.EmailConfirmedAt = null;

            var confirmationLink = $"{_baseUrl}/confirm-email?id={user.Id}&token={Uri.EscapeDataString(user.EmailConfirmationToken)}";
            await _emailService.SendEmailAsync(
                user.Email,
                "Підтвердження нової електронної пошти",
                $"Будь ласка, підтвердьте вашу нову пошту, перейшовши за посиланням: <a href='{confirmationLink}'>Підтвердити</a>",
                cancellationToken);
        }

        // Оновлення дати народження
        if (request.Birthday.HasValue)
        {
            user.UpdateBirthday(request.Birthday.Value);
        }

        await _userRepository.UpdateAsync(user, cancellationToken);

        return _mapper.Map<UserDto>(user);
    }
}
