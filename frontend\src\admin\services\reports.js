import api from '@/services/api';

export const reportsService = {
  async getSalesReport(params = {}) {
    try {
      const response = await api.get('/api/admin/reports/sales', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching sales report:', error);
      // Return mock data if API fails
      return {
        summary: {
          totalSales: 28456.78,
          totalOrders: 356,
          averageOrderValue: 79.93,
          comparisonPeriod: {
            totalSales: 25123.45,
            totalOrders: 320,
            averageOrderValue: 78.51
          }
        },
        byPeriod: [
          { date: '2023-01-01', sales: 956.78, orders: 12 },
          { date: '2023-01-02', sales: 1245.50, orders: 15 },
          { date: '2023-01-03', sales: 678.25, orders: 8 },
          { date: '2023-01-04', sales: 1789.99, orders: 20 },
          { date: '2023-01-05', sales: 1456.78, orders: 18 },
          { date: '2023-01-06', sales: 2100.50, orders: 25 },
          { date: '2023-01-07', sales: 1890.75, orders: 22 }
        ]
      };
    }
  },

  async getTopProducts(params = {}) {
    try {
      const response = await api.get('/api/admin/reports/top-products', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching top products report:', error);
      // Return mock data if API fails
      return [
        {
          id: '1',
          name: 'Smartphone X',
          sales: 12500.00,
          quantity: 25,
          averageRating: 4.8
        },
        {
          id: '2',
          name: 'Laptop Pro',
          sales: 9750.00,
          quantity: 15,
          averageRating: 4.9
        },
        {
          id: '3',
          name: 'Wireless Headphones',
          sales: 4500.00,
          quantity: 30,
          averageRating: 4.7
        },
        {
          id: '4',
          name: 'Smart Watch',
          sales: 3750.00,
          quantity: 25,
          averageRating: 4.6
        },
        {
          id: '5',
          name: 'Desk Chair',
          sales: 3000.00,
          quantity: 12,
          averageRating: 4.5
        }
      ];
    }
  },

  async getUserAnalytics(params = {}) {
    try {
      const response = await api.get('/api/admin/reports/users', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching user analytics:', error);
      // Return mock data if API fails
      return {
        summary: {
          totalUsers: 1289,
          newUsers: 87,
          activeUsers: 456,
          comparisonPeriod: {
            totalUsers: 1202,
            newUsers: 75,
            activeUsers: 420
          }
        },
        byPeriod: [
          { date: '2023-01-01', newUsers: 12, activeUsers: 65 },
          { date: '2023-01-02', newUsers: 15, activeUsers: 72 },
          { date: '2023-01-03', newUsers: 8, activeUsers: 68 },
          { date: '2023-01-04', newUsers: 20, activeUsers: 85 },
          { date: '2023-01-05', newUsers: 18, activeUsers: 78 },
          { date: '2023-01-06', newUsers: 25, activeUsers: 92 },
          { date: '2023-01-07', newUsers: 22, activeUsers: 88 }
        ],
        userTypes: {
          new: 87,
          returning: 369
        },
        deviceTypes: {
          desktop: 245,
          mobile: 178,
          tablet: 33
        }
      };
    }
  },

  async getConversionAnalytics(params = {}) {
    try {
      const response = await api.get('/api/admin/reports/conversion', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching conversion analytics:', error);
      // Return mock data if API fails
      return {
        summary: {
          visitToCart: 25.4,
          cartToCheckout: 68.2,
          checkoutToOrder: 85.7,
          overallConversion: 14.8,
          comparisonPeriod: {
            visitToCart: 23.8,
            cartToCheckout: 65.5,
            checkoutToOrder: 82.3,
            overallConversion: 12.9
          }
        },
        byPeriod: [
          { date: '2023-01-01', visitToCart: 24.5, cartToCheckout: 67.8, checkoutToOrder: 84.2, overallConversion: 14.0 },
          { date: '2023-01-02', visitToCart: 25.2, cartToCheckout: 68.5, checkoutToOrder: 85.1, overallConversion: 14.7 },
          { date: '2023-01-03', visitToCart: 23.8, cartToCheckout: 66.9, checkoutToOrder: 83.5, overallConversion: 13.3 },
          { date: '2023-01-04', visitToCart: 26.1, cartToCheckout: 69.2, checkoutToOrder: 86.3, overallConversion: 15.5 },
          { date: '2023-01-05', visitToCart: 25.7, cartToCheckout: 68.7, checkoutToOrder: 85.9, overallConversion: 15.1 },
          { date: '2023-01-06', visitToCart: 26.8, cartToCheckout: 70.1, checkoutToOrder: 87.2, overallConversion: 16.3 },
          { date: '2023-01-07', visitToCart: 26.3, cartToCheckout: 69.5, checkoutToOrder: 86.8, overallConversion: 15.8 }
        ],
        abandonmentReasons: [
          { reason: 'High shipping costs', percentage: 35 },
          { reason: 'Complicated checkout process', percentage: 25 },
          { reason: 'Payment issues', percentage: 20 },
          { reason: 'Just browsing', percentage: 15 },
          { reason: 'Other', percentage: 5 }
        ]
      };
    }
  },

  async getCategoryPerformance(params = {}) {
    try {
      const response = await api.get('/api/admin/reports/categories', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching category performance:', error);
      // Return mock data if API fails
      return [
        {
          id: '1',
          name: 'Electronics',
          sales: 18500.00,
          orders: 95,
          productCount: 45
        },
        {
          id: '4',
          name: 'Clothing',
          sales: 12750.00,
          orders: 85,
          productCount: 65
        },
        {
          id: '6',
          name: 'Home & Garden',
          sales: 8900.00,
          orders: 65,
          productCount: 45
        },
        {
          id: '8',
          name: 'Books',
          sales: 5600.00,
          orders: 70,
          productCount: 30
        },
        {
          id: '10',
          name: 'Sports & Outdoors',
          sales: 4800.00,
          orders: 40,
          productCount: 25
        }
      ];
    }
  },

  async exportReport(reportType, params = {}) {
    try {
      const response = await api.get(`/api/admin/reports/${reportType}/export`, {
        params,
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error(`Error exporting ${reportType} report:`, error);
      // Return mock error response
      throw new Error(`Failed to export ${reportType} report. Please try again later.`);
    }
  }
};
