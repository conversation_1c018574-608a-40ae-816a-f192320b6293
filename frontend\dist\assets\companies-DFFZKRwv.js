import{q as s}from"./index-BKy0rL_2.js";const c={async getCompanies(e={}){var n,o,r;try{console.log("Requesting companies with params:",e);const a={};e.page&&(a.page=e.page),e.pageSize&&(a.pageSize=e.pageSize),e.orderBy&&(a.orderBy=e.orderBy),e.descending!==void 0&&(a.descending=e.descending),e.search&&e.search.trim()!==""?a.filter=e.search.trim():e.filter&&e.filter.trim()!==""&&(a.filter=e.filter.trim()),e.featured&&e.featured.trim()!==""&&(a.isFeatured=e.featured==="true"),console.log("Final API params for companies:",a);const t=await s.get("/api/admin/companies",{params:a});if(console.log("Admin companies API response:",t.data),console.log("API params sent:",a),t.data&&t.data.success&&t.data.data)return console.log("Returning paginated data:",t.data.data),console.log("Total items:",t.data.data.total),console.log("Current page:",t.data.data.currentPage),console.log("Total pages:",t.data.data.lastPage),console.log("Items count:",(n=t.data.data.data)==null?void 0:n.length),t.data.data;throw console.error("Invalid response format:",t.data),new Error("Invalid response format from server")}catch(a){throw console.error("Error fetching companies:",a),new Error(((r=(o=a.response)==null?void 0:o.data)==null?void 0:r.message)||"Failed to load companies")}},async getPendingCompanies(e={}){var n,o;try{console.log("Requesting pending companies with params:",e);const r={};e.page&&(r.page=e.page),e.pageSize&&(r.pageSize=e.pageSize),e.orderBy&&(r.orderBy=e.orderBy),e.descending!==void 0&&(r.descending=e.descending),e.search&&e.search.trim()!==""?r.filter=e.search.trim():e.filter&&e.filter.trim()!==""&&(r.filter=e.filter.trim()),console.log("Final API params for pending companies:",r);const a=await s.get("/api/admin/companies/pending",{params:r});return console.log("Pending companies API response:",a.data),a.data}catch(r){throw console.error("Error fetching pending companies:",r),new Error(((o=(n=r.response)==null?void 0:n.data)==null?void 0:o.message)||"Failed to load pending companies")}},async getCompanyById(e){var n,o;try{return(await s.get(`/api/admin/companies/${e}`)).data}catch(r){throw console.error("Error fetching company:",r),new Error(((o=(n=r.response)==null?void 0:n.data)==null?void 0:o.message)||"Failed to load company details")}},async approveCompany(e){var n,o;try{return(await s.post(`/api/admin/companies/${e}/approve`)).data}catch(r){throw console.error("Error approving company:",r),new Error(((o=(n=r.response)==null?void 0:n.data)==null?void 0:o.message)||"Failed to approve company")}},async rejectCompany(e,n=""){var o,r;try{return(await s.post(`/api/admin/companies/${e}/reject`,{reason:n})).data}catch(a){throw console.error("Error rejecting company:",a),new Error(((r=(o=a.response)==null?void 0:o.data)==null?void 0:r.message)||"Failed to reject company")}},async deleteCompany(e){var n,o;try{return(await s.delete(`/api/admin/companies/${e}`)).data}catch(r){throw console.error("Error deleting company:",r),new Error(((o=(n=r.response)==null?void 0:n.data)==null?void 0:o.message)||"Failed to delete company")}},async getCompany(e){var n,o;try{return(await s.get(`/api/admin/companies/${e}`)).data}catch(r){throw console.error("Error getting company:",r),new Error(((o=(n=r.response)==null?void 0:n.data)==null?void 0:o.message)||"Failed to get company")}},async getDetailedCompany(e){var n,o;try{return(await s.get(`/api/admin/companies/${e}/detailed`)).data}catch(r){throw console.error("Error getting detailed company:",r),new Error(((o=(n=r.response)==null?void 0:n.data)==null?void 0:o.message)||"Failed to get detailed company")}},async updateCompany(e,n){var o,r;try{return(await s.put(`/api/admin/companies/${e}`,n)).data}catch(a){throw console.error("Error updating company:",a),new Error(((r=(o=a.response)==null?void 0:o.data)==null?void 0:r.message)||"Failed to update company")}},async updateDetailedCompany(e,n){var o,r;try{return(await s.put(`/api/admin/companies/${e}/detailed`,n)).data}catch(a){throw console.error("Error updating detailed company:",a),new Error(((r=(o=a.response)==null?void 0:o.data)==null?void 0:r.message)||"Failed to update detailed company")}}};export{c};
