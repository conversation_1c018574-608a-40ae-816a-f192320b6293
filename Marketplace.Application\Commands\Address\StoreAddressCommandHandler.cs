﻿using AutoMapper;
using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.Address;

public class StoreAddressCommandHandler : IRequestHandler<StoreAddressCommand, Guid>
{
    private readonly IAddressRepository _repository;
    private readonly IMapper _mapper;

    public StoreAddressCommandHandler(IAddressRepository repository, IMapper mapper)
    {
        _repository = repository;
        _mapper = mapper;
    }

    public async Task<Guid> Handle(StoreAddressCommand request, CancellationToken cancellationToken)
    {
        var item = _mapper.Map<Domain.Entities.Address>(request);

        // Встановлюємо UserId, якщо він був переданий
        if (request.UserId.HasValue)
        {
            item.UserId = request.UserId.Value;
        }

        await _repository.AddAsync(item, cancellationToken);
        return item.Id;
    }
}

