using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Marketplace.Application.Queries.Reports;
using Marketplace.Infrastructure.Persistence;

namespace Marketplace.Application.Handlers.Reports;

public class GetProductsReportQueryHandler : BaseReportResultHandler<GetProductsReportQuery>
{
    public GetProductsReportQueryHandler(MarketplaceDbContext context, ILogger<GetProductsReportQueryHandler> logger)
        : base(context, logger)
    {
    }

    protected override async Task<ReportResult> GenerateReportAsync(GetProductsReportQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Get products data (no date filter since we want all products)
            var products = await _context.Products
                .Include(p => p.Category)
                .Include(p => p.Company)
                .ToListAsync(cancellationToken);

            // Get all order items (no date filter for now to avoid PostgreSQL issues)
            var orderItems = await _context.OrderItems
                .Include(oi => oi.Product)
                .Include(oi => oi.Order)
                .ToListAsync(cancellationToken);

            // Calculate metrics
            var totalProducts = products.Count;
            var activeProducts = products.Count(p => p.Status.ToString() == "Active");
            var totalSales = orderItems.Sum(oi => oi.Quantity);
            var totalRevenue = orderItems.Sum(oi => oi.Price.Amount * oi.Quantity);

            // Create metrics
            var metrics = new ReportMetrics
            {
                Items = new List<MetricItem>
                {
                    new MetricItem
                    {
                        Key = "totalProducts",
                        Label = "Total Products",
                        Value = totalProducts,
                        Type = "number",
                        Icon = "fas fa-box",
                        PreviousValue = null,
                        ChangePercentage = 0m
                    },
                    new MetricItem
                    {
                        Key = "activeProducts",
                        Label = "Active Products",
                        Value = activeProducts,
                        Type = "number",
                        Icon = "fas fa-check-circle",
                        PreviousValue = null,
                        ChangePercentage = 0m
                    },
                    new MetricItem
                    {
                        Key = "totalSales",
                        Label = "Total Sales",
                        Value = totalSales,
                        Type = "number",
                        Icon = "fas fa-chart-line",
                        PreviousValue = null,
                        ChangePercentage = 0m
                    },
                    new MetricItem
                    {
                        Key = "totalRevenue",
                        Label = "Total Revenue",
                        Value = totalRevenue,
                        Type = "currency",
                        Icon = "fas fa-dollar-sign",
                        PreviousValue = null,
                        ChangePercentage = 0m
                    }
                }
            };

            // Generate table data
            var table = new ReportTable
            {
                Title = "Product Performance",
                Columns = new List<TableColumn>
                {
                    new TableColumn { Key = "name", Label = "Product Name", Type = "text" },
                    new TableColumn { Key = "category", Label = "Category", Type = "text" },
                    new TableColumn { Key = "sales", Label = "Sales", Type = "number" },
                    new TableColumn { Key = "revenue", Label = "Revenue", Type = "currency" },
                    new TableColumn { Key = "status", Label = "Status", Type = "status" }
                },
                Data = products.Select(p => new Dictionary<string, object>
                {
                    ["name"] = p.Name,
                    ["category"] = p.Category?.Name ?? "Unknown",
                    ["sales"] = orderItems.Where(oi => oi.ProductId == p.Id).Sum(oi => oi.Quantity),
                    ["revenue"] = orderItems.Where(oi => oi.ProductId == p.Id).Sum(oi => oi.Price.Amount * oi.Quantity),
                    ["status"] = p.Status.ToString()
                }).ToList(),
                TotalCount = products.Count,
                Page = request.Page,
                PageSize = request.PageSize
            };

            var summary = new ReportSummary
            {
                Title = "Product Report Summary",
                Description = $"Product analysis from {request.StartDate:yyyy-MM-dd} to {request.EndDate:yyyy-MM-dd}",
                GeneratedAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["totalProducts"] = totalProducts,
                    ["activeProducts"] = activeProducts,
                    ["totalSales"] = totalSales,
                    ["totalRevenue"] = totalRevenue,
                    ["period"] = new { start = request.StartDate, end = request.EndDate }
                }
            };

            return new ReportResult
            {
                Metrics = metrics,
                Table = table,
                Summary = summary
            };
        }
        catch (Exception ex)
        {
            return new ReportResult
            {
                Summary = new ReportSummary
                {
                    Title = "Error",
                    Description = $"Failed to generate product report: {ex.Message}",
                    GeneratedAt = DateTime.UtcNow
                }
            };
        }
    }
}
