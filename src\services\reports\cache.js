/**
 * Intelligent Cache System for Reports
 * Provides efficient caching with TTL, size limits, and pattern-based clearing
 */

class ReportCache {
  constructor(options = {}) {
    this.maxSize = options.maxSize || 100 // Maximum number of cached items
    this.defaultTTL = options.defaultTTL || 5 * 60 * 1000 // 5 minutes in milliseconds
    this.cache = new Map()
    this.timers = new Map()
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      clears: 0
    }
  }

  /**
   * Get cached value
   * @param {string} key - Cache key
   * @returns {any|null} Cached value or null if not found/expired
   */
  get(key) {
    const item = this.cache.get(key)
    
    if (!item) {
      this.stats.misses++
      return null
    }

    // Check if item has expired
    if (Date.now() > item.expiresAt) {
      this.delete(key)
      this.stats.misses++
      return null
    }

    // Update access time for LRU
    item.lastAccessed = Date.now()
    this.stats.hits++
    
    return item.value
  }

  /**
   * Set cached value
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {number} ttl - Time to live in milliseconds (optional)
   */
  set(key, value, ttl = this.defaultTTL) {
    // Remove existing item if it exists
    if (this.cache.has(key)) {
      this.delete(key)
    }

    // Check if we need to make room
    if (this.cache.size >= this.maxSize) {
      this.evictLRU()
    }

    const now = Date.now()
    const expiresAt = now + ttl

    const item = {
      value,
      createdAt: now,
      lastAccessed: now,
      expiresAt,
      ttl
    }

    this.cache.set(key, item)
    this.stats.sets++

    // Set expiration timer
    const timer = setTimeout(() => {
      this.delete(key)
    }, ttl)

    this.timers.set(key, timer)
  }

  /**
   * Delete cached value
   * @param {string} key - Cache key
   * @returns {boolean} True if item was deleted
   */
  delete(key) {
    const deleted = this.cache.delete(key)
    
    if (deleted) {
      this.stats.deletes++
      
      // Clear timer if it exists
      const timer = this.timers.get(key)
      if (timer) {
        clearTimeout(timer)
        this.timers.delete(key)
      }
    }

    return deleted
  }

  /**
   * Check if key exists and is not expired
   * @param {string} key - Cache key
   * @returns {boolean} True if key exists and is valid
   */
  has(key) {
    const item = this.cache.get(key)
    
    if (!item) {
      return false
    }

    // Check if expired
    if (Date.now() > item.expiresAt) {
      this.delete(key)
      return false
    }

    return true
  }

  /**
   * Clear all cached items
   */
  clear() {
    // Clear all timers
    this.timers.forEach(timer => clearTimeout(timer))
    this.timers.clear()
    
    // Clear cache
    this.cache.clear()
    this.stats.clears++
  }

  /**
   * Clear cached items matching pattern
   * @param {string} pattern - Pattern to match (supports wildcards)
   */
  clearByPattern(pattern) {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'))
    const keysToDelete = []

    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => this.delete(key))
  }

  /**
   * Evict least recently used item
   */
  evictLRU() {
    let oldestKey = null
    let oldestTime = Date.now()

    for (const [key, item] of this.cache.entries()) {
      if (item.lastAccessed < oldestTime) {
        oldestTime = item.lastAccessed
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.delete(oldestKey)
    }
  }

  /**
   * Get cache size
   * @returns {number} Number of cached items
   */
  size() {
    return this.cache.size
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0 
      ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
      : 0

    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      size: this.cache.size,
      maxSize: this.maxSize
    }
  }

  /**
   * Get all cached keys
   * @returns {Array<string>} Array of cache keys
   */
  keys() {
    return Array.from(this.cache.keys())
  }

  /**
   * Get cache info for specific key
   * @param {string} key - Cache key
   * @returns {Object|null} Cache item info or null
   */
  getInfo(key) {
    const item = this.cache.get(key)
    
    if (!item) {
      return null
    }

    const now = Date.now()
    const age = now - item.createdAt
    const timeToExpire = item.expiresAt - now

    return {
      key,
      age,
      timeToExpire,
      isExpired: timeToExpire <= 0,
      createdAt: new Date(item.createdAt).toISOString(),
      lastAccessed: new Date(item.lastAccessed).toISOString(),
      expiresAt: new Date(item.expiresAt).toISOString(),
      ttl: item.ttl
    }
  }

  /**
   * Get all cache items info
   * @returns {Array<Object>} Array of cache items info
   */
  getAllInfo() {
    return this.keys().map(key => this.getInfo(key)).filter(Boolean)
  }

  /**
   * Cleanup expired items
   * @returns {number} Number of expired items removed
   */
  cleanup() {
    const now = Date.now()
    const expiredKeys = []

    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiresAt) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => this.delete(key))
    
    return expiredKeys.length
  }

  /**
   * Set cache configuration
   * @param {Object} options - Configuration options
   */
  configure(options) {
    if (options.maxSize !== undefined) {
      this.maxSize = options.maxSize
      
      // Evict items if current size exceeds new max size
      while (this.cache.size > this.maxSize) {
        this.evictLRU()
      }
    }

    if (options.defaultTTL !== undefined) {
      this.defaultTTL = options.defaultTTL
    }
  }

  /**
   * Export cache data for debugging
   * @returns {Object} Cache data export
   */
  export() {
    const data = {}
    
    for (const [key, item] of this.cache.entries()) {
      data[key] = {
        value: item.value,
        info: this.getInfo(key)
      }
    }

    return {
      data,
      stats: this.getStats(),
      config: {
        maxSize: this.maxSize,
        defaultTTL: this.defaultTTL
      }
    }
  }

  /**
   * Import cache data (for testing/debugging)
   * @param {Object} exportData - Previously exported cache data
   */
  import(exportData) {
    this.clear()
    
    if (exportData.config) {
      this.configure(exportData.config)
    }

    if (exportData.data) {
      for (const [key, item] of Object.entries(exportData.data)) {
        if (item.info && !item.info.isExpired) {
          const ttl = Math.max(item.info.timeToExpire, 1000) // At least 1 second
          this.set(key, item.value, ttl)
        }
      }
    }
  }

  /**
   * Start automatic cleanup interval
   * @param {number} interval - Cleanup interval in milliseconds (default: 1 minute)
   */
  startAutoCleanup(interval = 60000) {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }

    this.cleanupInterval = setInterval(() => {
      const removed = this.cleanup()
      if (removed > 0) {
        console.debug(`Cache cleanup: removed ${removed} expired items`)
      }
    }, interval)
  }

  /**
   * Stop automatic cleanup
   */
  stopAutoCleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
  }

  /**
   * Destroy cache and cleanup all resources
   */
  destroy() {
    this.stopAutoCleanup()
    this.clear()
  }
}

export { ReportCache }
export default ReportCache
