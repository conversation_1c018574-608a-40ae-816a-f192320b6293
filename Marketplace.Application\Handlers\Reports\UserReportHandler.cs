using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Marketplace.Application.Queries.Reports;
using Marketplace.Infrastructure.Persistence;

namespace Marketplace.Application.Handlers.Reports;

public class GetUsersReportQueryHandler : BaseReportResultHandler<GetUsersReportQuery>
{
    public GetUsersReportQueryHandler(MarketplaceDbContext context, ILogger<GetUsersReportQueryHandler> logger)
        : base(context, logger)
    {
    }

    protected override async Task<ReportResult> GenerateReportAsync(GetUsersReportQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Get users data (no CreatedAt filter since User doesn't have CreatedAt)
            var users = await _context.Users
                .ToListAsync(cancellationToken);

            var orders = await _context.Orders
                .Include(o => o.Customer)
                .ToListAsync(cancellationToken);

            // Calculate metrics
            var totalUsers = users.Count;
            var activeUsers = users.Count(u => u.EmailConfirmed);
            var buyerUsers = users.Count(u => u.Role.ToString() == "Buyer");
            var sellerUsers = users.Count(u => u.Role.ToString() == "Seller" || u.Role.ToString() == "SellerOwner");

            // Create metrics
            var metrics = new ReportMetrics
            {
                Items = new List<MetricItem>
                {
                    new MetricItem
                    {
                        Key = "totalUsers",
                        Label = "Total Users",
                        Value = totalUsers,
                        Type = "number",
                        Icon = "fas fa-users",
                        PreviousValue = null,
                        ChangePercentage = 0m
                    },
                    new MetricItem
                    {
                        Key = "activeUsers",
                        Label = "Active Users",
                        Value = activeUsers,
                        Type = "number",
                        Icon = "fas fa-user-check",
                        PreviousValue = null,
                        ChangePercentage = 0m
                    },
                    new MetricItem
                    {
                        Key = "buyerUsers",
                        Label = "Buyers",
                        Value = buyerUsers,
                        Type = "number",
                        Icon = "fas fa-shopping-cart",
                        PreviousValue = null,
                        ChangePercentage = 0m
                    },
                    new MetricItem
                    {
                        Key = "sellerUsers",
                        Label = "Sellers",
                        Value = sellerUsers,
                        Type = "number",
                        Icon = "fas fa-store",
                        PreviousValue = null,
                        ChangePercentage = 0m
                    }
                }
            };

            // Generate table data
            var table = new ReportTable
            {
                Title = "User Activity",
                Columns = new List<TableColumn>
                {
                    new TableColumn { Key = "username", Label = "Username", Type = "text" },
                    new TableColumn { Key = "email", Label = "Email", Type = "text" },
                    new TableColumn { Key = "role", Label = "Role", Type = "text" },
                    new TableColumn { Key = "orders", Label = "Orders", Type = "number" },
                    new TableColumn { Key = "totalSpent", Label = "Total Spent", Type = "currency" },
                    new TableColumn { Key = "status", Label = "Status", Type = "status" }
                },
                Data = users.Select(u => new Dictionary<string, object>
                {
                    ["username"] = u.Username,
                    ["email"] = u.Email.Value,
                    ["role"] = u.Role.ToString(),
                    ["orders"] = orders.Count(o => o.CustomerId == u.Id),
                    ["totalSpent"] = orders.Where(o => o.CustomerId == u.Id).Sum(o => o.TotalPrice.Amount),
                    ["status"] = u.EmailConfirmed ? "Active" : "Inactive"
                }).ToList(),
                TotalCount = users.Count,
                Page = request.Page,
                PageSize = request.PageSize
            };

            var summary = new ReportSummary
            {
                Title = "User Report Summary",
                Description = $"User analysis from {request.StartDate:yyyy-MM-dd} to {request.EndDate:yyyy-MM-dd}",
                GeneratedAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["totalUsers"] = totalUsers,
                    ["activeUsers"] = activeUsers,
                    ["buyerUsers"] = buyerUsers,
                    ["sellerUsers"] = sellerUsers,
                    ["period"] = new { start = request.StartDate, end = request.EndDate }
                }
            };

            return new ReportResult
            {
                Metrics = metrics,
                Table = table,
                Summary = summary
            };
        }
        catch (Exception ex)
        {
            return new ReportResult
            {
                Summary = new ReportSummary
                {
                    Title = "Error",
                    Description = $"Failed to generate user report: {ex.Message}",
                    GeneratedAt = DateTime.UtcNow
                }
            };
        }
    }
}
