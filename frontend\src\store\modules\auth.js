import AuthService from '../../services/auth.service';

const user = JSON.parse(localStorage.getItem('user'));
const initialState = user
  ? { status: { loggedIn: true }, user }
  : { status: { loggedIn: false }, user: null };

export default {
  namespaced: true,
  state: initialState,
  getters: {
    isLoggedIn: state => state.status.loggedIn,
    user: state => state.user,
    isAdmin: state => {
      if (!state.user) return false;

      // Get the role from the user object
      const role = state.user.role;

      // Log the role for debugging
      console.log('Role in isAdmin getter:', role);
      console.log('Role type:', typeof role);

      // Handle different role formats that might come from the backend
      if (typeof role === 'string') {
        // The backend sends role as a string "Admin"
        return role === 'Admin';
      } else if (typeof role === 'number') {
        // Handle enum value (4 = Admin in the Role enum)
        // Buyer = 0, Seller = 1, SellerOwner = 2, Moderator = 3, Admin = 4
        return role === 4;
      } else if (role && typeof role === 'object') {
        // Handle case when role is an object with a value property
        if (role.hasOwnProperty('value')) {
          return role.value === 'Admin' || role.value === 4;
        }
        // Handle case when role is serialized as an object with a name property
        if (role.hasOwnProperty('name')) {
          return role.name === 'Admin';
        }
      }

      return false;
    },
    isModerator: state => {
      if (!state.user) return false;

      const role = state.user.role;

      if (typeof role === 'string') {
        return role === 'Moderator';
      } else if (typeof role === 'number') {
        // Buyer = 0, Seller = 1, SellerOwner = 2, Moderator = 3, Admin = 4
        return role === 3;
      } else if (role && typeof role === 'object') {
        if (role.hasOwnProperty('value')) {
          return role.value === 'Moderator' || role.value === 3;
        }
        if (role.hasOwnProperty('name')) {
          return role.name === 'Moderator';
        }
      }

      return false;
    },
    isAdminOrModerator: state => {
      if (!state.user) return false;

      const role = state.user.role;

      if (typeof role === 'string') {
        return role === 'Admin' || role === 'Moderator';
      } else if (typeof role === 'number') {
        // Buyer = 0, Seller = 1, SellerOwner = 2, Moderator = 3, Admin = 4
        return role === 3 || role === 4;
      } else if (role && typeof role === 'object') {
        if (role.hasOwnProperty('value')) {
          return role.value === 'Admin' || role.value === 'Moderator' || role.value === 3 || role.value === 4;
        }
        if (role.hasOwnProperty('name')) {
          return role.name === 'Admin' || role.name === 'Moderator';
        }
      }

      return false;
    }
  },
  actions: {
    async login({ commit }, { username, password }) {
      try {
        // Use username as email (keeping parameter name for backward compatibility)
        const email = username;

        // Validate input
        if (!email || !password) {
          throw new Error('Email and password are required');
        }

        // The auth service now returns { token, user } directly
        const authData = await AuthService.login(email, password);

        console.log('Auth data received from service:', authData);

        // Validate auth data
        if (!authData || !authData.token || !authData.user) {
          console.error('Invalid auth data from service:', authData);
          commit('loginFailure');
          throw new Error('Authentication failed');
        }

        // Pass the auth data to the mutation
        commit('loginSuccess', authData);
        return Promise.resolve(authData);
      } catch (error) {
        console.error('Login action error:', error);
        commit('loginFailure');
        return Promise.reject(error);
      }
    },
    async googleLogin({ commit }, idToken) {
      try {
        // Validate input
        if (!idToken) {
          throw new Error('Google ID token is required');
        }

        console.log('Attempting Google login with token in Vuex');

        // The auth service returns { token, user } directly
        const authData = await AuthService.googleLogin(idToken);

        console.log('Google auth data received from service:', authData);

        // Validate auth data
        if (!authData || !authData.token || !authData.user) {
          console.error('Invalid Google auth data from service:', authData);
          commit('loginFailure');
          throw new Error('Google authentication failed');
        }

        // Pass the auth data to the mutation (reuse the same mutation as regular login)
        commit('loginSuccess', authData);
        return Promise.resolve(authData);
      } catch (error) {
        console.error('Google login action error:', error);
        commit('loginFailure');
        return Promise.reject(error);
      }
    },
    async register({ commit }, user) {
      try {
        console.log('Register action in Vuex with user data:', user);

        // Ensure required fields are present
        if (!user.email || !user.password) {
          throw new Error('Email and password are required');
        }

        // If username is not provided, generate it from email
        if (!user.username) {
          user.username = user.email.split('@')[0];
        }

        // Role is now set on the server side, no need to set it here

        const response = await AuthService.register(user);
        console.log('Register response in Vuex:', response);

        commit('registerSuccess');
        return Promise.resolve(response.data);
      } catch (error) {
        console.error('Register error in Vuex:', error);
        if (error.response) {
          console.error('Error response in Vuex:', error.response);
          console.error('Error response data in Vuex:', error.response.data);
        }

        commit('registerFailure');
        return Promise.reject(error);
      }
    },
    logout({ commit }) {
      AuthService.logout();
      commit('logout');
    }
  },
  mutations: {
    loginSuccess(state, authData) {
      // Log the auth data for debugging
      console.log('Login success mutation with data:', authData);

      // Make sure we have user data
      if (!authData || !authData.user) {
        console.error('Missing user data in loginSuccess mutation');
        return;
      }

      state.status.loggedIn = true;
      state.user = authData.user;

      // Log the updated state
      console.log('Updated auth state:', state);
    },
    loginFailure(state) {
      state.status.loggedIn = false;
      state.user = null;
    },
    registerSuccess(state) {
      state.status.loggedIn = false;
    },
    registerFailure(state) {
      state.status.loggedIn = false;
    },
    logout(state) {
      state.status.loggedIn = false;
      state.user = null;
    }
  }
};



