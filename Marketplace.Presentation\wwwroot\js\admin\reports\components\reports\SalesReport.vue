<template>
  <div class="sales-report">
    <!-- Header -->
    <div class="report-header">
      <div class="header-content">
        <h1 class="report-title">
          <i class="fas fa-shopping-cart"></i>
          Sales Report
        </h1>
        <p class="report-description">
          Comprehensive analysis of sales performance, trends, and product insights
        </p>
      </div>
      
      <!-- Export Actions -->
      <div class="header-actions">
        <ExportButtons 
          :report-type="'sales'"
          :filters="filters"
          :disabled="isLoading"
        />
      </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
      <ReportFilters 
        :show-report-type="false"
        :report-type="'sales'"
        @filters-changed="handleFiltersChanged"
      />
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading sales data...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <div class="error-content">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Failed to Load Sales Data</h3>
        <p>{{ error }}</p>
        <button @click="refreshData" class="retry-btn">
          <i class="fas fa-redo"></i>
          Try Again
        </button>
      </div>
    </div>

    <!-- Report Content -->
    <div v-else-if="hasData" class="report-content">
      <!-- Key Metrics -->
      <div class="metrics-section">
        <h3 class="section-title">
          <i class="fas fa-chart-line"></i>
          Sales Performance Metrics
        </h3>
        <div class="metrics-grid">
          <MetricCard
            v-for="metric in salesMetrics"
            :key="metric.key"
            :metric="metric"
            :loading="isLoading"
          />
        </div>
      </div>

      <!-- Sales Trend Chart -->
      <div class="chart-section">
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">Sales Trend</h3>
            <div class="chart-controls">
              <select v-model="chartPeriod" @change="updateChart" class="chart-select">
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
              <select v-model="chartType" @change="updateChart" class="chart-select">
                <option value="line">Line Chart</option>
                <option value="bar">Bar Chart</option>
                <option value="area">Area Chart</option>
              </select>
            </div>
          </div>

          <div class="chart-content">
            <canvas ref="salesChartCanvas" id="sales-chart"></canvas>
          </div>
        </div>
      </div>

      <!-- Product Performance -->
      <div class="distribution-section">
        <div class="distribution-container">
          <div class="distribution-header">
            <h3 class="distribution-title">Top Products by Sales</h3>
          </div>

          <div class="distribution-content">
            <div class="chart-wrapper">
              <canvas ref="productsChartCanvas" id="products-chart"></canvas>
            </div>

            <div class="distribution-legend">
              <div
                v-for="(item, index) in topProductsData"
                :key="item.product"
                class="legend-item"
              >
                <div
                  class="legend-color"
                  :style="{ backgroundColor: getProductColor(index) }"
                ></div>
                <div class="legend-info">
                  <div class="legend-label">{{ item.product }}</div>
                  <div class="legend-value">
                    {{ formatCurrency(item.sales) }} ({{ item.percentage.toFixed(1) }}%)
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sales by Category -->
      <div class="category-section">
        <div class="category-container">
          <div class="category-header">
            <h3 class="category-title">
              <i class="fas fa-tags"></i>
              Sales by Category
            </h3>
          </div>

          <div class="category-content">
            <div class="category-chart">
              <canvas ref="categoryChartCanvas" id="category-chart"></canvas>
            </div>

            <div class="category-stats">
              <div
                v-for="category in categoryData"
                :key="category.name"
                class="category-stat"
              >
                <div class="stat-header">
                  <span class="category-name">{{ category.name }}</span>
                  <span class="category-growth" :class="category.growth >= 0 ? 'positive' : 'negative'">
                    <i :class="category.growth >= 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                    {{ Math.abs(category.growth) }}%
                  </span>
                </div>
                <div class="stat-values">
                  <div class="stat-value">{{ formatCurrency(category.sales) }}</div>
                  <div class="stat-label">{{ category.orders }} orders</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sales Insights -->
      <div class="insights-section">
        <h3 class="section-title">
          <i class="fas fa-lightbulb"></i>
          Sales Insights
        </h3>

        <div class="insights-grid">
          <div
            v-for="insight in salesInsights"
            :key="insight.id"
            class="insight-card"
            :class="insight.type"
          >
            <div class="insight-icon">
              <i :class="insight.icon"></i>
            </div>
            <div class="insight-content">
              <h4 class="insight-title">{{ insight.title }}</h4>
              <p class="insight-description">{{ insight.description }}</p>
              <div v-if="insight.action" class="insight-action">
                <button @click="handleInsightAction(insight)" class="action-btn">
                  {{ insight.action.label }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sales Data Table -->
      <div class="table-section">
        <ReportTable
          :data="tableData"
          :columns="tableColumns"
          :loading="isLoading"
          title="Sales Details"
          :exportable="true"
          @row-click="viewSaleDetails"
        />
      </div>

      <!-- Performance Alerts -->
      <div v-if="performanceAlerts.length" class="alert-section">
        <div class="alert-container warning">
          <div class="alert-header">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>Performance Alerts</h3>
          </div>
          <div class="alert-content">
            <p>{{ performanceAlerts.length }} items need attention:</p>
            <div class="alerts-list">
              <div
                v-for="alert in performanceAlerts.slice(0, 5)"
                :key="alert.id"
                class="alert-item"
              >
                <span class="alert-type">{{ alert.type }}</span>
                <span class="alert-message">{{ alert.message }}</span>
              </div>
              <div v-if="performanceAlerts.length > 5" class="more-items">
                +{{ performanceAlerts.length - 5 }} more alerts
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="empty-state">
      <div class="empty-content">
        <i class="fas fa-chart-line"></i>
        <h3>No Sales Data Available</h3>
        <p>Try adjusting your filters or date range to see sales data.</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useReportsStore } from '../../stores/reportsStore.js'
import { Chart } from 'chart.js'
import ReportFilters from '../shared/ReportFilters.vue'
import MetricCard from '../shared/MetricCard.vue'
import ExportButtons from '../shared/ExportButtons.vue'
import ReportTable from '../shared/ReportTable.vue'

export default {
  name: 'SalesReport',
  components: {
    ReportFilters,
    MetricCard,
    ExportButtons,
    ReportTable
  },
  setup() {
    const reportsStore = useReportsStore()

    // Reactive references
    const salesChartCanvas = ref(null)
    const productsChartCanvas = ref(null)
    const categoryChartCanvas = ref(null)
    const chartPeriod = ref('monthly')
    const chartType = ref('line')
    const salesChart = ref(null)
    const productsChart = ref(null)
    const categoryChart = ref(null)

    // Computed properties
    const isLoading = computed(() => reportsStore.isLoading)
    const error = computed(() => reportsStore.error)
    const hasData = computed(() => reportsStore.hasData && reportsStore.currentReport?.type === 'sales')
    const filters = computed(() => reportsStore.filters)

    const salesMetrics = computed(() => {
      const data = reportsStore.currentReport
      if (!data?.metrics?.items) return []

      return data.metrics.items.map(item => ({
        key: item.key,
        label: item.label,
        value: item.value,
        type: item.type || 'number',
        icon: item.icon || 'fas fa-chart-line',
        trend: item.trend || 'neutral',
        changePercentage: item.changePercentage || 0,
        previousValue: item.previousValue
      }))
    })

    const topProductsData = computed(() => {
      const data = reportsStore.currentReport
      if (!data?.charts?.topProducts?.data) return []

      return data.charts.topProducts.data.map(item => ({
        product: item.label,
        sales: item.value,
        percentage: item.percentage || 0
      }))
    })

    const categoryData = computed(() => {
      const data = reportsStore.currentReport
      if (!data?.charts?.categories?.data) return []

      return data.charts.categories.data.map(item => ({
        name: item.label,
        sales: item.value,
        orders: item.orders || 0,
        growth: item.growth || 0
      }))
    })

    const salesInsights = computed(() => {
      const data = reportsStore.currentReport
      return data?.insights || []
    })

    const tableData = computed(() => {
      const data = reportsStore.currentReport
      return data?.table?.data || []
    })

    const tableColumns = computed(() => {
      const data = reportsStore.currentReport
      return data?.table?.columns || [
        { key: 'product', label: 'Product', sortable: true },
        { key: 'category', label: 'Category', sortable: true },
        { key: 'sales', label: 'Sales', type: 'currency', sortable: true },
        { key: 'quantity', label: 'Quantity', type: 'number', sortable: true },
        { key: 'revenue', label: 'Revenue', type: 'currency', sortable: true },
        { key: 'profit', label: 'Profit', type: 'currency', sortable: true },
        { key: 'margin', label: 'Margin', type: 'percentage', sortable: true }
      ]
    })

    const performanceAlerts = computed(() => {
      const data = reportsStore.currentReport
      return data?.alerts || []
    })

    // Methods
    const handleFiltersChanged = async (newFilters) => {
      await reportsStore.fetchReport('sales', newFilters)
      await nextTick()
      updateCharts()
    }

    const refreshData = async () => {
      await reportsStore.fetchReport('sales')
      await nextTick()
      updateCharts()
    }

    const updateChart = async () => {
      await nextTick()
      updateCharts()
    }

    const updateCharts = () => {
      updateSalesChart()
      updateProductsChart()
      updateCategoryChart()
    }

    const updateSalesChart = () => {
      if (!salesChartCanvas.value) return

      const data = reportsStore.currentReport
      if (!data?.charts?.sales) return

      const ctx = salesChartCanvas.value.getContext('2d')

      if (salesChart.value) {
        salesChart.value.destroy()
      }

      const chartConfig = {
        type: chartType.value === 'area' ? 'line' : chartType.value,
        data: {
          labels: data.charts.sales.labels || [],
          datasets: [{
            label: 'Sales',
            data: data.charts.sales.data || [],
            borderColor: '#10b981',
            backgroundColor: chartType.value === 'area' ? 'rgba(16, 185, 129, 0.1)' : 'rgba(16, 185, 129, 0.8)',
            borderWidth: 2,
            fill: chartType.value === 'area',
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return formatCurrency(value)
                }
              }
            }
          }
        }
      }

      salesChart.value = new Chart(ctx, chartConfig)
    }

    const updateProductsChart = () => {
      if (!productsChartCanvas.value || !topProductsData.value.length) return

      const ctx = productsChartCanvas.value.getContext('2d')

      if (productsChart.value) {
        productsChart.value.destroy()
      }

      productsChart.value = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: topProductsData.value.map(item => item.product),
          datasets: [{
            data: topProductsData.value.map(item => item.sales),
            backgroundColor: topProductsData.value.map((_, index) => getProductColor(index))
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return formatCurrency(value)
                }
              }
            },
            x: {
              ticks: {
                maxRotation: 45
              }
            }
          }
        }
      })
    }

    const updateCategoryChart = () => {
      if (!categoryChartCanvas.value || !categoryData.value.length) return

      const ctx = categoryChartCanvas.value.getContext('2d')

      if (categoryChart.value) {
        categoryChart.value.destroy()
      }

      categoryChart.value = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: categoryData.value.map(item => item.name),
          datasets: [{
            data: categoryData.value.map(item => item.sales),
            backgroundColor: categoryData.value.map((_, index) => getCategoryColor(index))
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          }
        }
      })
    }

    const getProductColor = (index) => {
      const colors = [
        '#10b981', '#3b82f6', '#f59e0b', '#ef4444',
        '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
      ]
      return colors[index % colors.length]
    }

    const getCategoryColor = (index) => {
      const colors = [
        '#3b82f6', '#10b981', '#f59e0b', '#ef4444',
        '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
      ]
      return colors[index % colors.length]
    }

    const formatCurrency = (value) => {
      if (typeof value !== 'number') return value
      return new Intl.NumberFormat('uk-UA', {
        style: 'currency',
        currency: 'UAH'
      }).format(value)
    }

    const handleInsightAction = (insight) => {
      if (insight.action?.callback) {
        insight.action.callback()
      }
    }

    const viewSaleDetails = (sale) => {
      // Navigate to sale details or show modal
      console.log('View sale details:', sale)
    }

    // Lifecycle
    onMounted(async () => {
      await reportsStore.fetchReport('sales')
      await nextTick()
      updateCharts()
    })

    // Watch for data changes
    watch(() => reportsStore.currentReport, async () => {
      await nextTick()
      updateCharts()
    }, { deep: true })

    return {
      salesChartCanvas,
      productsChartCanvas,
      categoryChartCanvas,
      chartPeriod,
      chartType,
      isLoading,
      error,
      hasData,
      filters,
      salesMetrics,
      topProductsData,
      categoryData,
      salesInsights,
      tableData,
      tableColumns,
      performanceAlerts,
      handleFiltersChanged,
      refreshData,
      updateChart,
      getProductColor,
      getCategoryColor,
      formatCurrency,
      handleInsightAction,
      viewSaleDetails
    }
  }
}
</script>

<style scoped>
.sales-report {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 0;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.report-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.report-title i {
  color: #10b981;
}

.report-description {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  flex-shrink: 0;
}

.filters-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-container,
.error-container,
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-spinner,
.error-content,
.empty-content {
  text-align: center;
  color: #6b7280;
}

.loading-spinner i {
  font-size: 2rem;
  color: #10b981;
  margin-bottom: 1rem;
}

.error-content i,
.empty-content i {
  font-size: 3rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.empty-content i {
  color: #9ca3af;
}

.retry-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 1rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #059669;
}

.report-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.metrics-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.chart-section,
.distribution-section,
.category-section,
.insights-section,
.table-section,
.alert-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-container,
.distribution-container,
.category-container {
  width: 100%;
}

.chart-header,
.distribution-header,
.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.chart-title,
.distribution-title,
.category-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chart-controls {
  display: flex;
  gap: 1rem;
}

.chart-select {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 0.875rem;
  cursor: pointer;
}

.chart-content {
  position: relative;
  height: 400px;
}

.distribution-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  align-items: center;
}

.chart-wrapper {
  position: relative;
  height: 300px;
}

.distribution-legend {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.legend-info {
  flex: 1;
}

.legend-label {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.legend-value {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Category Styles */
.category-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  align-items: start;
}

.category-chart {
  position: relative;
  height: 300px;
}

.category-stats {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.category-stat {
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.category-name {
  font-weight: 600;
  color: #1f2937;
}

.category-growth {
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.category-growth.positive {
  color: #10b981;
}

.category-growth.negative {
  color: #ef4444;
}

.stat-values {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Insights Styles */
.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.insight-card {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #10b981;
  background: #f0fdf4;
}

.insight-card.warning {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.insight-card.success {
  border-left-color: #10b981;
  background: #f0fdf4;
}

.insight-card.danger {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.insight-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.insight-card.warning .insight-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.insight-card.success .insight-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.insight-card.danger .insight-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.insight-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.insight-action {
  margin-top: 1rem;
}

.action-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background: #059669;
}

/* Alert Styles */
.alert-container {
  border-radius: 8px;
  padding: 1.5rem;
}

.alert-container.warning {
  background: #fffbeb;
  border: 1px solid #fed7aa;
}

.alert-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.alert-header i {
  color: #f59e0b;
  font-size: 1.25rem;
}

.alert-header h3 {
  color: #d97706;
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.alert-content {
  color: #d97706;
}

.alert-content p {
  margin: 0 0 1rem 0;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.alert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 4px;
  gap: 1rem;
}

.alert-type {
  font-weight: 600;
  color: #d97706;
}

.alert-message {
  flex: 1;
  font-size: 0.875rem;
  color: #b45309;
}

.more-items {
  font-size: 0.875rem;
  color: #d97706;
  font-style: italic;
  text-align: center;
  padding: 0.5rem;
}

@media (max-width: 768px) {
  .report-header {
    flex-direction: column;
    gap: 1rem;
  }

  .distribution-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .category-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .category-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .chart-wrapper,
  .category-chart {
    height: 250px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }

  .chart-controls {
    flex-direction: column;
    gap: 0.5rem;
  }

  .alert-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
</style>
