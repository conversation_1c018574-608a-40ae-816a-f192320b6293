import{K as zt,_ as os,g as jr,i as cs,j as hs,L as us,c as kr,a as X,b as wt,k as Nn,n as St,D as Me,d as In,t as xr,F as xs,p as ds,w as S0,r as ps,z as kn,A as A0,H as vs,C as ms,o as Pr}from"./index-BKy0rL_2.js";import{o as ft,u as nt,e as gs,E as _s,g as Ts,a as Es,b as ws,c as Ss,d as As}from"./orders-HBUhG7wP.js";import{u as Fs,S as ys}from"./useAdminSearch-CW-fG_RO.js";import{S as Cs}from"./StatusBadge-DZXrI7cG.js";import{P as Os}from"./Pagination-DcbqxmDq.js";/* empty css                                                                    *//*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */var nn={};nn.version="0.18.5";var xa=1252,Ds=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],da=function(e){Ds.indexOf(e)!=-1&&(xa=e)};function Rs(){da(1252)}var It=function(e){da(e)};function Ns(){It(1200),Rs()}var Kt=function(t){return String.fromCharCode(t)},F0=function(t){return String.fromCharCode(t)},y0,Mr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function kt(e){for(var t="",r=0,n=0,a=0,i=0,s=0,f=0,o=0,l=0;l<e.length;)r=e.charCodeAt(l++),i=r>>2,n=e.charCodeAt(l++),s=(r&3)<<4|n>>4,a=e.charCodeAt(l++),f=(n&15)<<2|a>>6,o=a&63,isNaN(n)?f=o=64:isNaN(a)&&(o=64),t+=Mr.charAt(i)+Mr.charAt(s)+Mr.charAt(f)+Mr.charAt(o);return t}function Rr(e){var t="",r=0,n=0,a=0,i=0,s=0,f=0,o=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var l=0;l<e.length;)i=Mr.indexOf(e.charAt(l++)),s=Mr.indexOf(e.charAt(l++)),r=i<<2|s>>4,t+=String.fromCharCode(r),f=Mr.indexOf(e.charAt(l++)),n=(s&15)<<4|f>>2,f!==64&&(t+=String.fromCharCode(n)),o=Mr.indexOf(e.charAt(l++)),a=(f&3)<<6|o,o!==64&&(t+=String.fromCharCode(a));return t}var ve=function(){return typeof Buffer<"u"&&typeof process<"u"&&typeof process.versions<"u"&&!!process.versions.node}(),Ir=function(){if(typeof Buffer<"u"){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch{e=!0}return e?function(t,r){return r?new Buffer(t,r):new Buffer(t)}:Buffer.from.bind(Buffer)}return function(){}}();function Kr(e){return ve?Buffer.alloc?Buffer.alloc(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}function C0(e){return ve?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}var vr=function(t){return ve?Ir(t,"binary"):t.split("").map(function(r){return r.charCodeAt(0)&255})};function gn(e){if(typeof ArrayBuffer>"u")return vr(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n!=e.length;++n)r[n]=e.charCodeAt(n)&255;return t}function bt(e){if(Array.isArray(e))return e.map(function(n){return String.fromCharCode(n)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function Is(e){if(typeof Uint8Array>"u")throw new Error("Unsupported");return new Uint8Array(e)}var Ve=ve?function(e){return Buffer.concat(e.map(function(t){return Buffer.isBuffer(t)?t:Ir(t)}))}:function(e){if(typeof Uint8Array<"u"){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var n=new Uint8Array(r),a=0;for(t=0,r=0;t<e.length;r+=a,++t)if(a=e[t].length,e[t]instanceof Uint8Array)n.set(e[t],r);else{if(typeof e[t]=="string")throw"wtf";n.set(new Uint8Array(e[t]),r)}return n}return[].concat.apply([],e.map(function(i){return Array.isArray(i)?i:[].slice.call(i)}))};function ks(e){for(var t=[],r=0,n=e.length+250,a=Kr(e.length+255),i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s<128)a[r++]=s;else if(s<2048)a[r++]=192|s>>6&31,a[r++]=128|s&63;else if(s>=55296&&s<57344){s=(s&1023)+64;var f=e.charCodeAt(++i)&1023;a[r++]=240|s>>8&7,a[r++]=128|s>>2&63,a[r++]=128|f>>6&15|(s&3)<<4,a[r++]=128|f&63}else a[r++]=224|s>>12&15,a[r++]=128|s>>6&63,a[r++]=128|s&63;r>n&&(t.push(a.slice(0,r)),r=0,a=Kr(65535),n=65530)}return t.push(a.slice(0,r)),Ve(t)}var yt=/\u0000/g,Yt=/[\u0001-\u0006]/g;function ct(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function mr(e,t){var r=""+e;return r.length>=t?r:De("0",t-r.length)+r}function $n(e,t){var r=""+e;return r.length>=t?r:De(" ",t-r.length)+r}function an(e,t){var r=""+e;return r.length>=t?r:r+De(" ",t-r.length)}function Ps(e,t){var r=""+Math.round(e);return r.length>=t?r:De("0",t-r.length)+r}function Ls(e,t){var r=""+e;return r.length>=t?r:De("0",t-r.length)+r}var O0=Math.pow(2,32);function at(e,t){if(e>O0||e<-O0)return Ps(e,t);var r=Math.round(e);return Ls(r,t)}function sn(e,t){return t=t||0,e.length>=7+t&&(e.charCodeAt(t)|32)===103&&(e.charCodeAt(t+1)|32)===101&&(e.charCodeAt(t+2)|32)===110&&(e.charCodeAt(t+3)|32)===101&&(e.charCodeAt(t+4)|32)===114&&(e.charCodeAt(t+5)|32)===97&&(e.charCodeAt(t+6)|32)===108}var D0=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],Pn=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function Ms(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',e}var Re={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},R0={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},Bs={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function fn(e,t,r){for(var n=e<0?-1:1,a=e*n,i=0,s=1,f=0,o=1,l=0,c=0,d=Math.floor(a);l<t&&(d=Math.floor(a),f=d*s+i,c=d*l+o,!(a-d<5e-8));)a=1/(a-d),i=s,s=f,o=l,l=c;if(c>t&&(l>t?(c=o,f=i):(c=l,f=s)),!r)return[0,n*f,c];var h=Math.floor(n*f/c);return[h,n*f-h*c,c]}function Jt(e,t,r){if(e>2958465||e<0)return null;var n=e|0,a=Math.floor(86400*(e-n)),i=0,s=[],f={D:n,T:a,u:86400*(e-n)-a,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(f.u)<1e-6&&(f.u=0),t&&t.date1904&&(n+=1462),f.u>.9999&&(f.u=0,++a==86400&&(f.T=a=0,++n,++f.D)),n===60)s=r?[1317,10,29]:[1900,2,29],i=3;else if(n===0)s=r?[1317,8,29]:[1900,1,0],i=6;else{n>60&&--n;var o=new Date(1900,0,1);o.setDate(o.getDate()+n-1),s=[o.getFullYear(),o.getMonth()+1,o.getDate()],i=o.getDay(),n<60&&(i=(i+6)%7),r&&(i=Xs(o,s))}return f.y=s[0],f.m=s[1],f.d=s[2],f.S=a%60,a=Math.floor(a/60),f.M=a%60,a=Math.floor(a/60),f.H=a,f.q=i,f}var pa=new Date(1899,11,31,0,0,0),Us=pa.getTime(),bs=new Date(1900,2,1,0,0,0);function va(e,t){var r=e.getTime();return t?r-=1461*24*60*60*1e3:e>=bs&&(r+=24*60*60*1e3),(r-(Us+(e.getTimezoneOffset()-pa.getTimezoneOffset())*6e4))/(24*60*60*1e3)}function zn(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function Ws(e){return e.indexOf("E")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function Hs(e){var t=e<0?12:11,r=zn(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10),r.length<=t)?r:e.toExponential(5)}function Vs(e){var t=zn(e.toFixed(11));return t.length>(e<0?12:11)||t==="0"||t==="-0"?e.toPrecision(6):t}function Gs(e){var t=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),r;return t>=-4&&t<=-1?r=e.toPrecision(10+t):Math.abs(t)<=9?r=Hs(e):t===10?r=e.toFixed(10).substr(0,12):r=Vs(e),zn(Ws(r.toUpperCase()))}function Vn(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):Gs(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return Ur(14,va(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function Xs(e,t){t[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function js(e,t,r,n){var a="",i=0,s=0,f=r.y,o,l=0;switch(e){case 98:f=r.y+543;case 121:switch(t.length){case 1:case 2:o=f%100,l=2;break;default:o=f%1e4,l=4;break}break;case 109:switch(t.length){case 1:case 2:o=r.m,l=t.length;break;case 3:return Pn[r.m-1][1];case 5:return Pn[r.m-1][0];default:return Pn[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:o=r.d,l=t.length;break;case 3:return D0[r.q][0];default:return D0[r.q][1]}break;case 104:switch(t.length){case 1:case 2:o=1+(r.H+11)%12,l=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:o=r.H,l=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:o=r.M,l=t.length;break;default:throw"bad minute format: "+t}break;case 115:if(t!="s"&&t!="ss"&&t!=".0"&&t!=".00"&&t!=".000")throw"bad second format: "+t;return r.u===0&&(t=="s"||t=="ss")?mr(r.S,t.length):(n>=2?s=n===3?1e3:100:s=n===1?10:1,i=Math.round(s*(r.S+r.u)),i>=60*s&&(i=0),t==="s"?i===0?"0":""+i/s:(a=mr(i,2+n),t==="ss"?a.substr(0,2):"."+a.substr(2,t.length-1)));case 90:switch(t){case"[h]":case"[hh]":o=r.D*24+r.H;break;case"[m]":case"[mm]":o=(r.D*24+r.H)*60+r.M;break;case"[s]":case"[ss]":o=((r.D*24+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}l=t.length===3?1:2;break;case 101:o=f,l=1;break}var c=l>0?mr(o,l):"";return c}function Br(e){var t=3;if(e.length<=t)return e;for(var r=e.length%t,n=e.substr(0,r);r!=e.length;r+=t)n+=(n.length>0?",":"")+e.substr(r,t);return n}var ma=/%/g;function $s(e,t,r){var n=t.replace(ma,""),a=t.length-n.length;return Cr(e,n,r*Math.pow(10,2*a))+De("%",a)}function zs(e,t,r){for(var n=t.length-1;t.charCodeAt(n-1)===44;)--n;return Cr(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function ga(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+ga(e,-t);var a=e.indexOf(".");a===-1&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a),r.indexOf("e")===-1){var s=Math.floor(Math.log(t)*Math.LOG10E);for(r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i);r.substr(0,2)==="0.";)r=r.charAt(0)+r.substr(2,a)+"."+r.substr(2+a),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,o,l,c){return o+l+c.substr(0,(a+i)%a)+"."+c.substr(i)+"E"})}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var _a=/# (\?+)( ?)\/( ?)(\d+)/;function Ks(e,t,r){var n=parseInt(e[4],10),a=Math.round(t*n),i=Math.floor(a/n),s=a-i*n,f=n;return r+(i===0?"":""+i)+" "+(s===0?De(" ",e[1].length+1+e[4].length):$n(s,e[1].length)+e[2]+"/"+e[3]+mr(f,e[4].length))}function Ys(e,t,r){return r+(t===0?"":""+t)+De(" ",e[1].length+2+e[4].length)}var Ta=/^#*0*\.([0#]+)/,Ea=/\).*[0#]/,wa=/\(###\) ###\\?-####/;function Je(e){for(var t="",r,n=0;n!=e.length;++n)switch(r=e.charCodeAt(n)){case 35:break;case 63:t+=" ";break;case 48:t+="0";break;default:t+=String.fromCharCode(r)}return t}function N0(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function I0(e,t){var r=e-Math.floor(e),n=Math.pow(10,t);return t<(""+Math.round(r*n)).length?0:Math.round(r*n)}function Js(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}function Zs(e){return e<2147483647&&e>-2147483648?""+(e>=0?e|0:e-1|0):""+Math.floor(e)}function cr(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Ea)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?cr("n",n,r):"("+cr("n",n,-r)+")"}if(t.charCodeAt(t.length-1)===44)return zs(e,t,r);if(t.indexOf("%")!==-1)return $s(e,t,r);if(t.indexOf("E")!==-1)return ga(t,r);if(t.charCodeAt(0)===36)return"$"+cr(e,t.substr(t.charAt(1)==" "?2:1),r);var a,i,s,f,o=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+at(o,t.length);if(t.match(/^[#?]+$/))return a=at(r,0),a==="0"&&(a=""),a.length>t.length?a:Je(t.substr(0,t.length-a.length))+a;if(i=t.match(_a))return Ks(i,o,l);if(t.match(/^#+0+$/))return l+at(o,t.length-t.indexOf("0"));if(i=t.match(Ta))return a=N0(r,i[1].length).replace(/^([^\.]+)$/,"$1."+Je(i[1])).replace(/\.$/,"."+Je(i[1])).replace(/\.(\d*)$/,function(T,u){return"."+u+De("0",Je(i[1]).length-u.length)}),t.indexOf("0.")!==-1?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return l+N0(o,i[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return l+Br(at(o,0));if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+cr(e,t,-r):Br(""+(Math.floor(r)+Js(r,i[1].length)))+"."+mr(I0(r,i[1].length),i[1].length);if(i=t.match(/^#,#*,#0/))return cr(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=ct(cr(e,t.replace(/[\\-]/g,""),r)),s=0,ct(ct(t.replace(/\\/g,"")).replace(/[0#]/g,function(T){return s<a.length?a.charAt(s++):T==="0"?"0":""}));if(t.match(wa))return a=cr(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var c="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=fn(o,Math.pow(10,s)-1,!1),a=""+l,c=Cr("n",i[1],f[1]),c.charAt(c.length-1)==" "&&(c=c.substr(0,c.length-1)+"0"),a+=c+i[2]+"/"+i[3],c=an(f[2],s),c.length<i[4].length&&(c=Je(i[4].substr(i[4].length-c.length))+c),a+=c,a;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=fn(o,Math.pow(10,s)-1,!0),l+(f[0]||(f[1]?"":"0"))+" "+(f[1]?$n(f[1],s)+i[2]+"/"+i[3]+an(f[2],s):De(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=at(r,0),t.length<=a.length?a:Je(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0?]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var d=t.indexOf(".")-s,h=t.length-a.length-d;return Je(t.substr(0,d)+a+t.substr(t.length-h))}if(i=t.match(/^00,000\.([#0]*0)$/))return s=I0(r,i[1].length),r<0?"-"+cr(e,t,-r):Br(Zs(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(T){return"00,"+(T.length<3?mr(0,3-T.length):"")+T})+"."+mr(s,i[1].length);switch(t){case"###,##0.00":return cr(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var p=Br(at(o,0));return p!=="0"?l+p:"";case"###,###.00":return cr(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return cr(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function qs(e,t,r){for(var n=t.length-1;t.charCodeAt(n-1)===44;)--n;return Cr(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function Qs(e,t,r){var n=t.replace(ma,""),a=t.length-n.length;return Cr(e,n,r*Math.pow(10,2*a))+De("%",a)}function Sa(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+Sa(e,-t);var a=e.indexOf(".");a===-1&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a),!r.match(/[Ee]/)){var s=Math.floor(Math.log(t)*Math.LOG10E);r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,o,l,c){return o+l+c.substr(0,(a+i)%a)+"."+c.substr(i)+"E"})}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function Tr(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Ea)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Tr("n",n,r):"("+Tr("n",n,-r)+")"}if(t.charCodeAt(t.length-1)===44)return qs(e,t,r);if(t.indexOf("%")!==-1)return Qs(e,t,r);if(t.indexOf("E")!==-1)return Sa(t,r);if(t.charCodeAt(0)===36)return"$"+Tr(e,t.substr(t.charAt(1)==" "?2:1),r);var a,i,s,f,o=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+mr(o,t.length);if(t.match(/^[#?]+$/))return a=""+r,r===0&&(a=""),a.length>t.length?a:Je(t.substr(0,t.length-a.length))+a;if(i=t.match(_a))return Ys(i,o,l);if(t.match(/^#+0+$/))return l+mr(o,t.length-t.indexOf("0"));if(i=t.match(Ta))return a=(""+r).replace(/^([^\.]+)$/,"$1."+Je(i[1])).replace(/\.$/,"."+Je(i[1])),a=a.replace(/\.(\d*)$/,function(T,u){return"."+u+De("0",Je(i[1]).length-u.length)}),t.indexOf("0.")!==-1?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return l+(""+o).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return l+Br(""+o);if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Tr(e,t,-r):Br(""+r)+"."+De("0",i[1].length);if(i=t.match(/^#,#*,#0/))return Tr(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=ct(Tr(e,t.replace(/[\\-]/g,""),r)),s=0,ct(ct(t.replace(/\\/g,"")).replace(/[0#]/g,function(T){return s<a.length?a.charAt(s++):T==="0"?"0":""}));if(t.match(wa))return a=Tr(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var c="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=fn(o,Math.pow(10,s)-1,!1),a=""+l,c=Cr("n",i[1],f[1]),c.charAt(c.length-1)==" "&&(c=c.substr(0,c.length-1)+"0"),a+=c+i[2]+"/"+i[3],c=an(f[2],s),c.length<i[4].length&&(c=Je(i[4].substr(i[4].length-c.length))+c),a+=c,a;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=fn(o,Math.pow(10,s)-1,!0),l+(f[0]||(f[1]?"":"0"))+" "+(f[1]?$n(f[1],s)+i[2]+"/"+i[3]+an(f[2],s):De(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=""+r,t.length<=a.length?a:Je(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var d=t.indexOf(".")-s,h=t.length-a.length-d;return Je(t.substr(0,d)+a+t.substr(t.length-h))}if(i=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+Tr(e,t,-r):Br(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(T){return"00,"+(T.length<3?mr(0,3-T.length):"")+T})+"."+mr(0,i[1].length);switch(t){case"###,###":case"##,###":case"#,###":var p=Br(""+o);return p!=="0"?l+p:"";default:if(t.match(/\.[0#?]*$/))return Tr(e,t.slice(0,t.lastIndexOf(".")),r)+Je(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function Cr(e,t,r){return(r|0)===r?Tr(e,t,r):cr(e,t,r)}function ef(e){for(var t=[],r=!1,n=0,a=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:t[t.length]=e.substr(a,n-a),a=n+1}if(t[t.length]=e.substr(a),r===!0)throw new Error("Format |"+e+"| unterminated string ");return t}var Aa=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function Fa(e){for(var t=0,r="",n="";t<e.length;)switch(r=e.charAt(t)){case"G":sn(e,t)&&(t+=6),t++;break;case'"':for(;e.charCodeAt(++t)!==34&&t<e.length;);++t;break;case"\\":t+=2;break;case"_":t+=2;break;case"@":++t;break;case"B":case"b":if(e.charAt(t+1)==="1"||e.charAt(t+1)==="2")return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if(e.substr(t,3).toUpperCase()==="A/P"||e.substr(t,5).toUpperCase()==="AM/PM"||e.substr(t,5).toUpperCase()==="上午/下午")return!0;++t;break;case"[":for(n=r;e.charAt(t++)!=="]"&&t<e.length;)n+=e.charAt(t);if(n.match(Aa))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||r=="\\"&&e.charAt(t+1)=="-"&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(e.charAt(t)==" "||e.charAt(t)=="*")&&++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;case" ":++t;break;default:++t;break}return!1}function rf(e,t,r,n){for(var a=[],i="",s=0,f="",o="t",l,c,d,h="H";s<e.length;)switch(f=e.charAt(s)){case"G":if(!sn(e,s))throw new Error("unrecognized character "+f+" in "+e);a[a.length]={t:"G",v:"General"},s+=7;break;case'"':for(i="";(d=e.charCodeAt(++s))!==34&&s<e.length;)i+=String.fromCharCode(d);a[a.length]={t:"t",v:i},++s;break;case"\\":var p=e.charAt(++s),T=p==="("||p===")"?p:"t";a[a.length]={t:T,v:p},++s;break;case"_":a[a.length]={t:"t",v:" "},s+=2;break;case"@":a[a.length]={t:"T",v:t},++s;break;case"B":case"b":if(e.charAt(s+1)==="1"||e.charAt(s+1)==="2"){if(l==null&&(l=Jt(t,r,e.charAt(s+1)==="2"),l==null))return"";a[a.length]={t:"X",v:e.substr(s,2)},o=f,s+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||l==null&&(l=Jt(t,r),l==null))return"";for(i=f;++s<e.length&&e.charAt(s).toLowerCase()===f;)i+=f;f==="m"&&o.toLowerCase()==="h"&&(f="M"),f==="h"&&(f=h),a[a.length]={t:f,v:i},o=f;break;case"A":case"a":case"上":var u={t:f,v:f};if(l==null&&(l=Jt(t,r)),e.substr(s,3).toUpperCase()==="A/P"?(l!=null&&(u.v=l.H>=12?"P":"A"),u.t="T",h="h",s+=3):e.substr(s,5).toUpperCase()==="AM/PM"?(l!=null&&(u.v=l.H>=12?"PM":"AM"),u.t="T",s+=5,h="h"):e.substr(s,5).toUpperCase()==="上午/下午"?(l!=null&&(u.v=l.H>=12?"下午":"上午"),u.t="T",s+=5,h="h"):(u.t="t",++s),l==null&&u.t==="T")return"";a[a.length]=u,o=f;break;case"[":for(i=f;e.charAt(s++)!=="]"&&s<e.length;)i+=e.charAt(s);if(i.slice(-1)!=="]")throw'unterminated "[" block: |'+i+"|";if(i.match(Aa)){if(l==null&&(l=Jt(t,r),l==null))return"";a[a.length]={t:"Z",v:i.toLowerCase()},o=i.charAt(1)}else i.indexOf("$")>-1&&(i=(i.match(/\$([^-\[\]]*)/)||[])[1]||"$",Fa(e)||(a[a.length]={t:"t",v:i}));break;case".":if(l!=null){for(i=f;++s<e.length&&(f=e.charAt(s))==="0";)i+=f;a[a.length]={t:"s",v:i};break}case"0":case"#":for(i=f;++s<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(s))>-1;)i+=f;a[a.length]={t:"n",v:i};break;case"?":for(i=f;e.charAt(++s)===f;)i+=f;a[a.length]={t:f,v:i},o=f;break;case"*":++s,(e.charAt(s)==" "||e.charAt(s)=="*")&&++s;break;case"(":case")":a[a.length]={t:n===1?"t":f,v:f},++s;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(i=f;s<e.length&&"0123456789".indexOf(e.charAt(++s))>-1;)i+=e.charAt(s);a[a.length]={t:"D",v:i};break;case" ":a[a.length]={t:f,v:f},++s;break;case"$":a[a.length]={t:"t",v:"$"},++s;break;default:if(",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f)===-1)throw new Error("unrecognized character "+f+" in "+e);a[a.length]={t:"t",v:f},++s;break}var g=0,C=0,O;for(s=a.length-1,o="t";s>=0;--s)switch(a[s].t){case"h":case"H":a[s].t=h,o="h",g<1&&(g=1);break;case"s":(O=a[s].v.match(/\.0+$/))&&(C=Math.max(C,O[0].length-1)),g<3&&(g=3);case"d":case"y":case"M":case"e":o=a[s].t;break;case"m":o==="s"&&(a[s].t="M",g<2&&(g=2));break;case"X":break;case"Z":g<1&&a[s].v.match(/[Hh]/)&&(g=1),g<2&&a[s].v.match(/[Mm]/)&&(g=2),g<3&&a[s].v.match(/[Ss]/)&&(g=3)}switch(g){case 0:break;case 1:l.u>=.5&&(l.u=0,++l.S),l.S>=60&&(l.S=0,++l.M),l.M>=60&&(l.M=0,++l.H);break;case 2:l.u>=.5&&(l.u=0,++l.S),l.S>=60&&(l.S=0,++l.M);break}var F="",M;for(s=0;s<a.length;++s)switch(a[s].t){case"t":case"T":case" ":case"D":break;case"X":a[s].v="",a[s].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":a[s].v=js(a[s].t.charCodeAt(0),a[s].v,l,C),a[s].t="t";break;case"n":case"?":for(M=s+1;a[M]!=null&&((f=a[M].t)==="?"||f==="D"||(f===" "||f==="t")&&a[M+1]!=null&&(a[M+1].t==="?"||a[M+1].t==="t"&&a[M+1].v==="/")||a[s].t==="("&&(f===" "||f==="n"||f===")")||f==="t"&&(a[M].v==="/"||a[M].v===" "&&a[M+1]!=null&&a[M+1].t=="?"));)a[s].v+=a[M].v,a[M]={v:"",t:";"},++M;F+=a[s].v,s=M-1;break;case"G":a[s].t="t",a[s].v=Vn(t,r);break}var Y="",ne,D;if(F.length>0){F.charCodeAt(0)==40?(ne=t<0&&F.charCodeAt(0)===45?-t:t,D=Cr("n",F,ne)):(ne=t<0&&n>1?-t:t,D=Cr("n",F,ne),ne<0&&a[0]&&a[0].t=="t"&&(D=D.substr(1),a[0].v="-"+a[0].v)),M=D.length-1;var b=a.length;for(s=0;s<a.length;++s)if(a[s]!=null&&a[s].t!="t"&&a[s].v.indexOf(".")>-1){b=s;break}var P=a.length;if(b===a.length&&D.indexOf("E")===-1){for(s=a.length-1;s>=0;--s)a[s]==null||"n?".indexOf(a[s].t)===-1||(M>=a[s].v.length-1?(M-=a[s].v.length,a[s].v=D.substr(M+1,a[s].v.length)):M<0?a[s].v="":(a[s].v=D.substr(0,M+1),M=-1),a[s].t="t",P=s);M>=0&&P<a.length&&(a[P].v=D.substr(0,M+1)+a[P].v)}else if(b!==a.length&&D.indexOf("E")===-1){for(M=D.indexOf(".")-1,s=b;s>=0;--s)if(!(a[s]==null||"n?".indexOf(a[s].t)===-1)){for(c=a[s].v.indexOf(".")>-1&&s===b?a[s].v.indexOf(".")-1:a[s].v.length-1,Y=a[s].v.substr(c+1);c>=0;--c)M>=0&&(a[s].v.charAt(c)==="0"||a[s].v.charAt(c)==="#")&&(Y=D.charAt(M--)+Y);a[s].v=Y,a[s].t="t",P=s}for(M>=0&&P<a.length&&(a[P].v=D.substr(0,M+1)+a[P].v),M=D.indexOf(".")+1,s=b;s<a.length;++s)if(!(a[s]==null||"n?(".indexOf(a[s].t)===-1&&s!==b)){for(c=a[s].v.indexOf(".")>-1&&s===b?a[s].v.indexOf(".")+1:0,Y=a[s].v.substr(0,c);c<a[s].v.length;++c)M<D.length&&(Y+=D.charAt(M++));a[s].v=Y,a[s].t="t",P=s}}}for(s=0;s<a.length;++s)a[s]!=null&&"n?".indexOf(a[s].t)>-1&&(ne=n>1&&t<0&&s>0&&a[s-1].v==="-"?-t:t,a[s].v=Cr(a[s].t,a[s].v,ne),a[s].t="t");var V="";for(s=0;s!==a.length;++s)a[s]!=null&&(V+=a[s].v);return V}var k0=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function P0(e,t){if(t==null)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function tf(e,t){var r=ef(e),n=r.length,a=r[n-1].indexOf("@");if(n<4&&a>-1&&--n,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if(typeof t!="number")return[4,r.length===4||a>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=a>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=a>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=a>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break}var i=t>0?r[0]:t<0?r[1]:r[2];if(r[0].indexOf("[")===-1&&r[1].indexOf("[")===-1)return[n,i];if(r[0].match(/\[[=<>]/)!=null||r[1].match(/\[[=<>]/)!=null){var s=r[0].match(k0),f=r[1].match(k0);return P0(t,s)?[n,r[0]]:P0(t,f)?[n,r[1]]:[n,r[s!=null&&f!=null?2:1]]}return[n,i]}function Ur(e,t,r){r==null&&(r={});var n="";switch(typeof e){case"string":e=="m/d/yy"&&r.dateNF?n=r.dateNF:n=e;break;case"number":e==14&&r.dateNF?n=r.dateNF:n=(r.table!=null?r.table:Re)[e],n==null&&(n=r.table&&r.table[R0[e]]||Re[R0[e]]),n==null&&(n=Bs[e]||"General");break}if(sn(n,0))return Vn(t,r);t instanceof Date&&(t=va(t,r.date1904));var a=tf(n,t);if(sn(a[1]))return Vn(t,r);if(t===!0)t="TRUE";else if(t===!1)t="FALSE";else if(t===""||t==null)return"";return rf(a[1],t,r,a[0])}function ya(e,t){if(typeof t!="number"){t=+t||-1;for(var r=0;r<392;++r){if(Re[r]==null){t<0&&(t=r);continue}if(Re[r]==e){t=r;break}}t<0&&(t=391)}return Re[t]=e,t}function _n(e){for(var t=0;t!=392;++t)e[t]!==void 0&&ya(e[t],t)}function Tn(){Re=Ms()}var Ca=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function nf(e){var t=typeof e=="number"?Re[e]:e;return t=t.replace(Ca,"(\\d+)"),new RegExp("^"+t+"$")}function af(e,t,r){var n=-1,a=-1,i=-1,s=-1,f=-1,o=-1;(t.match(Ca)||[]).forEach(function(d,h){var p=parseInt(r[h+1],10);switch(d.toLowerCase().charAt(0)){case"y":n=p;break;case"d":i=p;break;case"h":s=p;break;case"s":o=p;break;case"m":s>=0?f=p:a=p;break}}),o>=0&&f==-1&&a>=0&&(f=a,a=-1);var l=(""+(n>=0?n:new Date().getFullYear())).slice(-4)+"-"+("00"+(a>=1?a:1)).slice(-2)+"-"+("00"+(i>=1?i:1)).slice(-2);l.length==7&&(l="0"+l),l.length==8&&(l="20"+l);var c=("00"+(s>=0?s:0)).slice(-2)+":"+("00"+(f>=0?f:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2);return s==-1&&f==-1&&o==-1?l:n==-1&&a==-1&&i==-1?c:l+"T"+c}var sf=function(){var e={};e.version="1.2.0";function t(){for(var D=0,b=new Array(256),P=0;P!=256;++P)D=P,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,b[P]=D;return typeof Int32Array<"u"?new Int32Array(b):b}var r=t();function n(D){var b=0,P=0,V=0,G=typeof Int32Array<"u"?new Int32Array(4096):new Array(4096);for(V=0;V!=256;++V)G[V]=D[V];for(V=0;V!=256;++V)for(P=D[V],b=256+V;b<4096;b+=256)P=G[b]=P>>>8^D[P&255];var j=[];for(V=1;V!=16;++V)j[V-1]=typeof Int32Array<"u"?G.subarray(V*256,V*256+256):G.slice(V*256,V*256+256);return j}var a=n(r),i=a[0],s=a[1],f=a[2],o=a[3],l=a[4],c=a[5],d=a[6],h=a[7],p=a[8],T=a[9],u=a[10],g=a[11],C=a[12],O=a[13],F=a[14];function M(D,b){for(var P=b^-1,V=0,G=D.length;V<G;)P=P>>>8^r[(P^D.charCodeAt(V++))&255];return~P}function Y(D,b){for(var P=b^-1,V=D.length-15,G=0;G<V;)P=F[D[G++]^P&255]^O[D[G++]^P>>8&255]^C[D[G++]^P>>16&255]^g[D[G++]^P>>>24]^u[D[G++]]^T[D[G++]]^p[D[G++]]^h[D[G++]]^d[D[G++]]^c[D[G++]]^l[D[G++]]^o[D[G++]]^f[D[G++]]^s[D[G++]]^i[D[G++]]^r[D[G++]];for(V+=15;G<V;)P=P>>>8^r[(P^D[G++])&255];return~P}function ne(D,b){for(var P=b^-1,V=0,G=D.length,j=0,re=0;V<G;)j=D.charCodeAt(V++),j<128?P=P>>>8^r[(P^j)&255]:j<2048?(P=P>>>8^r[(P^(192|j>>6&31))&255],P=P>>>8^r[(P^(128|j&63))&255]):j>=55296&&j<57344?(j=(j&1023)+64,re=D.charCodeAt(V++)&1023,P=P>>>8^r[(P^(240|j>>8&7))&255],P=P>>>8^r[(P^(128|j>>2&63))&255],P=P>>>8^r[(P^(128|re>>6&15|(j&3)<<4))&255],P=P>>>8^r[(P^(128|re&63))&255]):(P=P>>>8^r[(P^(224|j>>12&15))&255],P=P>>>8^r[(P^(128|j>>6&63))&255],P=P>>>8^r[(P^(128|j&63))&255]);return~P}return e.table=r,e.bstr=M,e.buf=Y,e.str=ne,e}(),Se=function(){var t={};t.version="1.2.1";function r(x,_){for(var v=x.split("/"),m=_.split("/"),E=0,w=0,N=Math.min(v.length,m.length);E<N;++E){if(w=v[E].length-m[E].length)return w;if(v[E]!=m[E])return v[E]<m[E]?-1:1}return v.length-m.length}function n(x){if(x.charAt(x.length-1)=="/")return x.slice(0,-1).indexOf("/")===-1?x:n(x.slice(0,-1));var _=x.lastIndexOf("/");return _===-1?x:x.slice(0,_+1)}function a(x){if(x.charAt(x.length-1)=="/")return a(x.slice(0,-1));var _=x.lastIndexOf("/");return _===-1?x:x.slice(_+1)}function i(x,_){typeof _=="string"&&(_=new Date(_));var v=_.getHours();v=v<<6|_.getMinutes(),v=v<<5|_.getSeconds()>>>1,x.write_shift(2,v);var m=_.getFullYear()-1980;m=m<<4|_.getMonth()+1,m=m<<5|_.getDate(),x.write_shift(2,m)}function s(x){var _=x.read_shift(2)&65535,v=x.read_shift(2)&65535,m=new Date,E=v&31;v>>>=5;var w=v&15;v>>>=4,m.setMilliseconds(0),m.setFullYear(v+1980),m.setMonth(w-1),m.setDate(E);var N=_&31;_>>>=5;var U=_&63;return _>>>=6,m.setHours(_),m.setMinutes(U),m.setSeconds(N<<1),m}function f(x){ar(x,0);for(var _={},v=0;x.l<=x.length-4;){var m=x.read_shift(2),E=x.read_shift(2),w=x.l+E,N={};switch(m){case 21589:v=x.read_shift(1),v&1&&(N.mtime=x.read_shift(4)),E>5&&(v&2&&(N.atime=x.read_shift(4)),v&4&&(N.ctime=x.read_shift(4))),N.mtime&&(N.mt=new Date(N.mtime*1e3));break}x.l=w,_[m]=N}return _}var o;function l(){return o||(o={})}function c(x,_){if(x[0]==80&&x[1]==75)return w0(x,_);if((x[0]|32)==109&&(x[1]|32)==105)return ns(x,_);if(x.length<512)throw new Error("CFB file size "+x.length+" < 512");var v=3,m=512,E=0,w=0,N=0,U=0,R=0,I=[],k=x.slice(0,512);ar(k,0);var z=d(k);switch(v=z[0],v){case 3:m=512;break;case 4:m=4096;break;case 0:if(z[1]==0)return w0(x,_);default:throw new Error("Major Version: Expected 3 or 4 saw "+v)}m!==512&&(k=x.slice(0,m),ar(k,28));var ee=x.slice(0,m);h(k,v);var ie=k.read_shift(4,"i");if(v===3&&ie!==0)throw new Error("# Directory Sectors: Expected 0 saw "+ie);k.l+=4,N=k.read_shift(4,"i"),k.l+=4,k.chk("00100000","Mini Stream Cutoff Size: "),U=k.read_shift(4,"i"),E=k.read_shift(4,"i"),R=k.read_shift(4,"i"),w=k.read_shift(4,"i");for(var K=-1,ae=0;ae<109&&(K=k.read_shift(4,"i"),!(K<0));++ae)I[ae]=K;var ce=p(x,m);g(R,w,ce,m,I);var ye=O(ce,N,I,m);ye[N].name="!Directory",E>0&&U!==re&&(ye[U].name="!MiniFAT"),ye[I[0]].name="!FAT",ye.fat_addrs=I,ye.ssz=m;var Ce={},$e=[],_t=[],Tt=[];F(N,ye,ce,$e,E,Ce,_t,U),T(_t,Tt,$e),$e.shift();var Et={FileIndex:_t,FullPaths:Tt};return _&&_.raw&&(Et.raw={header:ee,sectors:ce}),Et}function d(x){if(x[x.l]==80&&x[x.l+1]==75)return[0,0];x.chk(xe,"Header Signature: "),x.l+=16;var _=x.read_shift(2,"u");return[x.read_shift(2,"u"),_]}function h(x,_){var v=9;switch(x.l+=2,v=x.read_shift(2)){case 9:if(_!=3)throw new Error("Sector Shift: Expected 9 saw "+v);break;case 12:if(_!=4)throw new Error("Sector Shift: Expected 12 saw "+v);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+v)}x.chk("0600","Mini Sector Shift: "),x.chk("000000000000","Reserved: ")}function p(x,_){for(var v=Math.ceil(x.length/_)-1,m=[],E=1;E<v;++E)m[E-1]=x.slice(E*_,(E+1)*_);return m[v-1]=x.slice(v*_),m}function T(x,_,v){for(var m=0,E=0,w=0,N=0,U=0,R=v.length,I=[],k=[];m<R;++m)I[m]=k[m]=m,_[m]=v[m];for(;U<k.length;++U)m=k[U],E=x[m].L,w=x[m].R,N=x[m].C,I[m]===m&&(E!==-1&&I[E]!==E&&(I[m]=I[E]),w!==-1&&I[w]!==w&&(I[m]=I[w])),N!==-1&&(I[N]=m),E!==-1&&m!=I[m]&&(I[E]=I[m],k.lastIndexOf(E)<U&&k.push(E)),w!==-1&&m!=I[m]&&(I[w]=I[m],k.lastIndexOf(w)<U&&k.push(w));for(m=1;m<R;++m)I[m]===m&&(w!==-1&&I[w]!==w?I[m]=I[w]:E!==-1&&I[E]!==E&&(I[m]=I[E]));for(m=1;m<R;++m)if(x[m].type!==0){if(U=m,U!=I[U])do U=I[U],_[m]=_[U]+"/"+_[m];while(U!==0&&I[U]!==-1&&U!=I[U]);I[m]=-1}for(_[0]+="/",m=1;m<R;++m)x[m].type!==2&&(_[m]+="/")}function u(x,_,v){for(var m=x.start,E=x.size,w=[],N=m;v&&E>0&&N>=0;)w.push(_.slice(N*j,N*j+j)),E-=j,N=$r(v,N*4);return w.length===0?B(0):Ve(w).slice(0,x.size)}function g(x,_,v,m,E){var w=re;if(x===re){if(_!==0)throw new Error("DIFAT chain shorter than expected")}else if(x!==-1){var N=v[x],U=(m>>>2)-1;if(!N)return;for(var R=0;R<U&&(w=$r(N,R*4))!==re;++R)E.push(w);g($r(N,m-4),_-1,v,m,E)}}function C(x,_,v,m,E){var w=[],N=[];E||(E=[]);var U=m-1,R=0,I=0;for(R=_;R>=0;){E[R]=!0,w[w.length]=R,N.push(x[R]);var k=v[Math.floor(R*4/m)];if(I=R*4&U,m<4+I)throw new Error("FAT boundary crossed: "+R+" 4 "+m);if(!x[k])break;R=$r(x[k],I)}return{nodes:w,data:V0([N])}}function O(x,_,v,m){var E=x.length,w=[],N=[],U=[],R=[],I=m-1,k=0,z=0,ee=0,ie=0;for(k=0;k<E;++k)if(U=[],ee=k+_,ee>=E&&(ee-=E),!N[ee]){R=[];var K=[];for(z=ee;z>=0;){K[z]=!0,N[z]=!0,U[U.length]=z,R.push(x[z]);var ae=v[Math.floor(z*4/m)];if(ie=z*4&I,m<4+ie)throw new Error("FAT boundary crossed: "+z+" 4 "+m);if(!x[ae]||(z=$r(x[ae],ie),K[z]))break}w[ee]={nodes:U,data:V0([R])}}return w}function F(x,_,v,m,E,w,N,U){for(var R=0,I=m.length?2:0,k=_[x].data,z=0,ee=0,ie;z<k.length;z+=128){var K=k.slice(z,z+128);ar(K,64),ee=K.read_shift(2),ie=qn(K,0,ee-I),m.push(ie);var ae={name:ie,type:K.read_shift(1),color:K.read_shift(1),L:K.read_shift(4,"i"),R:K.read_shift(4,"i"),C:K.read_shift(4,"i"),clsid:K.read_shift(16),state:K.read_shift(4,"i"),start:0,size:0},ce=K.read_shift(2)+K.read_shift(2)+K.read_shift(2)+K.read_shift(2);ce!==0&&(ae.ct=M(K,K.l-8));var ye=K.read_shift(2)+K.read_shift(2)+K.read_shift(2)+K.read_shift(2);ye!==0&&(ae.mt=M(K,K.l-8)),ae.start=K.read_shift(4,"i"),ae.size=K.read_shift(4,"i"),ae.size<0&&ae.start<0&&(ae.size=ae.type=0,ae.start=re,ae.name=""),ae.type===5?(R=ae.start,E>0&&R!==re&&(_[R].name="!StreamData")):ae.size>=4096?(ae.storage="fat",_[ae.start]===void 0&&(_[ae.start]=C(v,ae.start,_.fat_addrs,_.ssz)),_[ae.start].name=ae.name,ae.content=_[ae.start].data.slice(0,ae.size)):(ae.storage="minifat",ae.size<0?ae.size=0:R!==re&&ae.start!==re&&_[R]&&(ae.content=u(ae,_[R].data,(_[U]||{}).data))),ae.content&&ar(ae.content,0),w[ie]=ae,N.push(ae)}}function M(x,_){return new Date((sr(x,_+4)/1e7*Math.pow(2,32)+sr(x,_)/1e7-11644473600)*1e3)}function Y(x,_){return l(),c(o.readFileSync(x),_)}function ne(x,_){var v=_&&_.type;switch(v||ve&&Buffer.isBuffer(x)&&(v="buffer"),v||"base64"){case"file":return Y(x,_);case"base64":return c(vr(Rr(x)),_);case"binary":return c(vr(x),_)}return c(x,_)}function D(x,_){var v=_||{},m=v.root||"Root Entry";if(x.FullPaths||(x.FullPaths=[]),x.FileIndex||(x.FileIndex=[]),x.FullPaths.length!==x.FileIndex.length)throw new Error("inconsistent CFB structure");x.FullPaths.length===0&&(x.FullPaths[0]=m+"/",x.FileIndex[0]={name:m,type:5}),v.CLSID&&(x.FileIndex[0].clsid=v.CLSID),b(x)}function b(x){var _="Sh33tJ5";if(!Se.find(x,"/"+_)){var v=B(4);v[0]=55,v[1]=v[3]=50,v[2]=54,x.FileIndex.push({name:_,type:2,content:v,size:4,L:69,R:69,C:69}),x.FullPaths.push(x.FullPaths[0]+_),P(x)}}function P(x,_){D(x);for(var v=!1,m=!1,E=x.FullPaths.length-1;E>=0;--E){var w=x.FileIndex[E];switch(w.type){case 0:m?v=!0:(x.FileIndex.pop(),x.FullPaths.pop());break;case 1:case 2:case 5:m=!0,isNaN(w.R*w.L*w.C)&&(v=!0),w.R>-1&&w.L>-1&&w.R==w.L&&(v=!0);break;default:v=!0;break}}if(!(!v&&!_)){var N=new Date(1987,1,19),U=0,R=Object.create?Object.create(null):{},I=[];for(E=0;E<x.FullPaths.length;++E)R[x.FullPaths[E]]=!0,x.FileIndex[E].type!==0&&I.push([x.FullPaths[E],x.FileIndex[E]]);for(E=0;E<I.length;++E){var k=n(I[E][0]);m=R[k],m||(I.push([k,{name:a(k).replace("/",""),type:1,clsid:Q,ct:N,mt:N,content:null}]),R[k]=!0)}for(I.sort(function(ie,K){return r(ie[0],K[0])}),x.FullPaths=[],x.FileIndex=[],E=0;E<I.length;++E)x.FullPaths[E]=I[E][0],x.FileIndex[E]=I[E][1];for(E=0;E<I.length;++E){var z=x.FileIndex[E],ee=x.FullPaths[E];if(z.name=a(ee).replace("/",""),z.L=z.R=z.C=-(z.color=1),z.size=z.content?z.content.length:0,z.start=0,z.clsid=z.clsid||Q,E===0)z.C=I.length>1?1:-1,z.size=0,z.type=5;else if(ee.slice(-1)=="/"){for(U=E+1;U<I.length&&n(x.FullPaths[U])!=ee;++U);for(z.C=U>=I.length?-1:U,U=E+1;U<I.length&&n(x.FullPaths[U])!=n(ee);++U);z.R=U>=I.length?-1:U,z.type=1}else n(x.FullPaths[E+1]||"")==n(ee)&&(z.R=E+1),z.type=2}}}function V(x,_){var v=_||{};if(v.fileType=="mad")return as(x,v);switch(P(x),v.fileType){case"zip":return Zi(x,v)}var m=function(ie){for(var K=0,ae=0,ce=0;ce<ie.FileIndex.length;++ce){var ye=ie.FileIndex[ce];if(ye.content){var Ce=ye.content.length;Ce>0&&(Ce<4096?K+=Ce+63>>6:ae+=Ce+511>>9)}}for(var $e=ie.FullPaths.length+3>>2,_t=K+7>>3,Tt=K+127>>7,Et=_t+ae+$e+Tt,Xr=Et+127>>7,Rn=Xr<=109?0:Math.ceil((Xr-109)/127);Et+Xr+Rn+127>>7>Xr;)Rn=++Xr<=109?0:Math.ceil((Xr-109)/127);var Fr=[1,Rn,Xr,Tt,$e,ae,K,0];return ie.FileIndex[0].size=K<<6,Fr[7]=(ie.FileIndex[0].start=Fr[0]+Fr[1]+Fr[2]+Fr[3]+Fr[4]+Fr[5])+(Fr[6]+7>>3),Fr}(x),E=B(m[7]<<9),w=0,N=0;{for(w=0;w<8;++w)E.write_shift(1,J[w]);for(w=0;w<8;++w)E.write_shift(2,0);for(E.write_shift(2,62),E.write_shift(2,3),E.write_shift(2,65534),E.write_shift(2,9),E.write_shift(2,6),w=0;w<3;++w)E.write_shift(2,0);for(E.write_shift(4,0),E.write_shift(4,m[2]),E.write_shift(4,m[0]+m[1]+m[2]+m[3]-1),E.write_shift(4,0),E.write_shift(4,4096),E.write_shift(4,m[3]?m[0]+m[1]+m[2]-1:re),E.write_shift(4,m[3]),E.write_shift(-4,m[1]?m[0]-1:re),E.write_shift(4,m[1]),w=0;w<109;++w)E.write_shift(-4,w<m[2]?m[1]+w:-1)}if(m[1])for(N=0;N<m[1];++N){for(;w<236+N*127;++w)E.write_shift(-4,w<m[2]?m[1]+w:-1);E.write_shift(-4,N===m[1]-1?re:N+1)}var U=function(ie){for(N+=ie;w<N-1;++w)E.write_shift(-4,w+1);ie&&(++w,E.write_shift(-4,re))};for(N=w=0,N+=m[1];w<N;++w)E.write_shift(-4,ge.DIFSECT);for(N+=m[2];w<N;++w)E.write_shift(-4,ge.FATSECT);U(m[3]),U(m[4]);for(var R=0,I=0,k=x.FileIndex[0];R<x.FileIndex.length;++R)k=x.FileIndex[R],k.content&&(I=k.content.length,!(I<4096)&&(k.start=N,U(I+511>>9)));for(U(m[6]+7>>3);E.l&511;)E.write_shift(-4,ge.ENDOFCHAIN);for(N=w=0,R=0;R<x.FileIndex.length;++R)k=x.FileIndex[R],k.content&&(I=k.content.length,!(!I||I>=4096)&&(k.start=N,U(I+63>>6)));for(;E.l&511;)E.write_shift(-4,ge.ENDOFCHAIN);for(w=0;w<m[4]<<2;++w){var z=x.FullPaths[w];if(!z||z.length===0){for(R=0;R<17;++R)E.write_shift(4,0);for(R=0;R<3;++R)E.write_shift(4,-1);for(R=0;R<12;++R)E.write_shift(4,0);continue}k=x.FileIndex[w],w===0&&(k.start=k.size?k.start-1:re);var ee=w===0&&v.root||k.name;if(I=2*(ee.length+1),E.write_shift(64,ee,"utf16le"),E.write_shift(2,I),E.write_shift(1,k.type),E.write_shift(1,k.color),E.write_shift(-4,k.L),E.write_shift(-4,k.R),E.write_shift(-4,k.C),k.clsid)E.write_shift(16,k.clsid,"hex");else for(R=0;R<4;++R)E.write_shift(4,0);E.write_shift(4,k.state||0),E.write_shift(4,0),E.write_shift(4,0),E.write_shift(4,0),E.write_shift(4,0),E.write_shift(4,k.start),E.write_shift(4,k.size),E.write_shift(4,0)}for(w=1;w<x.FileIndex.length;++w)if(k=x.FileIndex[w],k.size>=4096)if(E.l=k.start+1<<9,ve&&Buffer.isBuffer(k.content))k.content.copy(E,E.l,0,k.size),E.l+=k.size+511&-512;else{for(R=0;R<k.size;++R)E.write_shift(1,k.content[R]);for(;R&511;++R)E.write_shift(1,0)}for(w=1;w<x.FileIndex.length;++w)if(k=x.FileIndex[w],k.size>0&&k.size<4096)if(ve&&Buffer.isBuffer(k.content))k.content.copy(E,E.l,0,k.size),E.l+=k.size+63&-64;else{for(R=0;R<k.size;++R)E.write_shift(1,k.content[R]);for(;R&63;++R)E.write_shift(1,0)}if(ve)E.l=E.length;else for(;E.l<E.length;)E.write_shift(1,0);return E}function G(x,_){var v=x.FullPaths.map(function(R){return R.toUpperCase()}),m=v.map(function(R){var I=R.split("/");return I[I.length-(R.slice(-1)=="/"?2:1)]}),E=!1;_.charCodeAt(0)===47?(E=!0,_=v[0].slice(0,-1)+_):E=_.indexOf("/")!==-1;var w=_.toUpperCase(),N=E===!0?v.indexOf(w):m.indexOf(w);if(N!==-1)return x.FileIndex[N];var U=!w.match(Yt);for(w=w.replace(yt,""),U&&(w=w.replace(Yt,"!")),N=0;N<v.length;++N)if((U?v[N].replace(Yt,"!"):v[N]).replace(yt,"")==w||(U?m[N].replace(Yt,"!"):m[N]).replace(yt,"")==w)return x.FileIndex[N];return null}var j=64,re=-2,xe="d0cf11e0a1b11ae1",J=[208,207,17,224,161,177,26,225],Q="00000000000000000000000000000000",ge={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:re,FREESECT:-1,HEADER_SIGNATURE:xe,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:Q,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function ue(x,_,v){l();var m=V(x,v);o.writeFileSync(_,m)}function Pe(x){for(var _=new Array(x.length),v=0;v<x.length;++v)_[v]=String.fromCharCode(x[v]);return _.join("")}function lr(x,_){var v=V(x,_);switch(_&&_.type||"buffer"){case"file":return l(),o.writeFileSync(_.filename,v),v;case"binary":return typeof v=="string"?v:Pe(v);case"base64":return kt(typeof v=="string"?v:Pe(v));case"buffer":if(ve)return Buffer.isBuffer(v)?v:Ir(v);case"array":return typeof v=="string"?vr(v):v}return v}var tr;function S(x){try{var _=x.InflateRaw,v=new _;if(v._processChunk(new Uint8Array([3,0]),v._finishFlushFlag),v.bytesRead)tr=x;else throw new Error("zlib does not expose bytesRead")}catch(m){console.error("cannot use native zlib: "+(m.message||m))}}function L(x,_){if(!tr)return T0(x,_);var v=tr.InflateRaw,m=new v,E=m._processChunk(x.slice(x.l),m._finishFlushFlag);return x.l+=m.bytesRead,E}function y(x){return tr?tr.deflateRawSync(x):d0(x)}var A=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],H=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],le=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function oe(x){var _=(x<<1|x<<11)&139536|(x<<5|x<<15)&558144;return(_>>16|_>>8|_)&255}for(var fe=typeof Uint8Array<"u",te=fe?new Uint8Array(256):[],Ae=0;Ae<256;++Ae)te[Ae]=oe(Ae);function de(x,_){var v=te[x&255];return _<=8?v>>>8-_:(v=v<<8|te[x>>8&255],_<=16?v>>>16-_:(v=v<<8|te[x>>16&255],v>>>24-_))}function Ye(x,_){var v=_&7,m=_>>>3;return(x[m]|(v<=6?0:x[m+1]<<8))>>>v&3}function me(x,_){var v=_&7,m=_>>>3;return(x[m]|(v<=5?0:x[m+1]<<8))>>>v&7}function Sr(x,_){var v=_&7,m=_>>>3;return(x[m]|(v<=4?0:x[m+1]<<8))>>>v&15}function Oe(x,_){var v=_&7,m=_>>>3;return(x[m]|(v<=3?0:x[m+1]<<8))>>>v&31}function se(x,_){var v=_&7,m=_>>>3;return(x[m]|(v<=1?0:x[m+1]<<8))>>>v&127}function or(x,_,v){var m=_&7,E=_>>>3,w=(1<<v)-1,N=x[E]>>>m;return v<8-m||(N|=x[E+1]<<8-m,v<16-m)||(N|=x[E+2]<<16-m,v<24-m)||(N|=x[E+3]<<24-m),N&w}function Ar(x,_,v){var m=_&7,E=_>>>3;return m<=5?x[E]|=(v&7)<<m:(x[E]|=v<<m&255,x[E+1]=(v&7)>>8-m),_+3}function Vr(x,_,v){var m=_&7,E=_>>>3;return v=(v&1)<<m,x[E]|=v,_+1}function tt(x,_,v){var m=_&7,E=_>>>3;return v<<=m,x[E]|=v&255,v>>>=8,x[E+1]=v,_+8}function x0(x,_,v){var m=_&7,E=_>>>3;return v<<=m,x[E]|=v&255,v>>>=8,x[E+1]=v&255,x[E+2]=v>>>8,_+16}function yn(x,_){var v=x.length,m=2*v>_?2*v:_+5,E=0;if(v>=_)return x;if(ve){var w=C0(m);if(x.copy)x.copy(w);else for(;E<x.length;++E)w[E]=x[E];return w}else if(fe){var N=new Uint8Array(m);if(N.set)N.set(x);else for(;E<v;++E)N[E]=x[E];return N}return x.length=m,x}function _r(x){for(var _=new Array(x),v=0;v<x;++v)_[v]=0;return _}function jt(x,_,v){var m=1,E=0,w=0,N=0,U=0,R=x.length,I=fe?new Uint16Array(32):_r(32);for(w=0;w<32;++w)I[w]=0;for(w=R;w<v;++w)x[w]=0;R=x.length;var k=fe?new Uint16Array(R):_r(R);for(w=0;w<R;++w)I[E=x[w]]++,m<E&&(m=E),k[w]=0;for(I[0]=0,w=1;w<=m;++w)I[w+16]=U=U+I[w-1]<<1;for(w=0;w<R;++w)U=x[w],U!=0&&(k[w]=I[U+16]++);var z=0;for(w=0;w<R;++w)if(z=x[w],z!=0)for(U=de(k[w],m)>>m-z,N=(1<<m+4-z)-1;N>=0;--N)_[U|N<<z]=z&15|w<<4;return m}var Cn=fe?new Uint16Array(512):_r(512),On=fe?new Uint16Array(32):_r(32);if(!fe){for(var Gr=0;Gr<512;++Gr)Cn[Gr]=0;for(Gr=0;Gr<32;++Gr)On[Gr]=0}(function(){for(var x=[],_=0;_<32;_++)x.push(5);jt(x,On,32);var v=[];for(_=0;_<=143;_++)v.push(8);for(;_<=255;_++)v.push(9);for(;_<=279;_++)v.push(7);for(;_<=287;_++)v.push(8);jt(v,Cn,288)})();var zi=function(){for(var _=fe?new Uint8Array(32768):[],v=0,m=0;v<le.length-1;++v)for(;m<le[v+1];++m)_[m]=v;for(;m<32768;++m)_[m]=29;var E=fe?new Uint8Array(259):[];for(v=0,m=0;v<H.length-1;++v)for(;m<H[v+1];++m)E[m]=v;function w(U,R){for(var I=0;I<U.length;){var k=Math.min(65535,U.length-I),z=I+k==U.length;for(R.write_shift(1,+z),R.write_shift(2,k),R.write_shift(2,~k&65535);k-- >0;)R[R.l++]=U[I++]}return R.l}function N(U,R){for(var I=0,k=0,z=fe?new Uint16Array(32768):[];k<U.length;){var ee=Math.min(65535,U.length-k);if(ee<10){for(I=Ar(R,I,+(k+ee==U.length)),I&7&&(I+=8-(I&7)),R.l=I/8|0,R.write_shift(2,ee),R.write_shift(2,~ee&65535);ee-- >0;)R[R.l++]=U[k++];I=R.l*8;continue}I=Ar(R,I,+(k+ee==U.length)+2);for(var ie=0;ee-- >0;){var K=U[k];ie=(ie<<5^K)&32767;var ae=-1,ce=0;if((ae=z[ie])&&(ae|=k&-32768,ae>k&&(ae-=32768),ae<k))for(;U[ae+ce]==U[k+ce]&&ce<250;)++ce;if(ce>2){K=E[ce],K<=22?I=tt(R,I,te[K+1]>>1)-1:(tt(R,I,3),I+=5,tt(R,I,te[K-23]>>5),I+=3);var ye=K<8?0:K-4>>2;ye>0&&(x0(R,I,ce-H[K]),I+=ye),K=_[k-ae],I=tt(R,I,te[K]>>3),I-=3;var Ce=K<4?0:K-2>>1;Ce>0&&(x0(R,I,k-ae-le[K]),I+=Ce);for(var $e=0;$e<ce;++$e)z[ie]=k&32767,ie=(ie<<5^U[k])&32767,++k;ee-=ce-1}else K<=143?K=K+48:I=Vr(R,I,1),I=tt(R,I,te[K]),z[ie]=k&32767,++k}I=tt(R,I,0)-1}return R.l=(I+7)/8|0,R.l}return function(R,I){return R.length<8?w(R,I):N(R,I)}}();function d0(x){var _=B(50+Math.floor(x.length*1.1)),v=zi(x,_);return _.slice(0,v)}var p0=fe?new Uint16Array(32768):_r(32768),v0=fe?new Uint16Array(32768):_r(32768),m0=fe?new Uint16Array(128):_r(128),g0=1,_0=1;function Ki(x,_){var v=Oe(x,_)+257;_+=5;var m=Oe(x,_)+1;_+=5;var E=Sr(x,_)+4;_+=4;for(var w=0,N=fe?new Uint8Array(19):_r(19),U=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],R=1,I=fe?new Uint8Array(8):_r(8),k=fe?new Uint8Array(8):_r(8),z=N.length,ee=0;ee<E;++ee)N[A[ee]]=w=me(x,_),R<w&&(R=w),I[w]++,_+=3;var ie=0;for(I[0]=0,ee=1;ee<=R;++ee)k[ee]=ie=ie+I[ee-1]<<1;for(ee=0;ee<z;++ee)(ie=N[ee])!=0&&(U[ee]=k[ie]++);var K=0;for(ee=0;ee<z;++ee)if(K=N[ee],K!=0){ie=te[U[ee]]>>8-K;for(var ae=(1<<7-K)-1;ae>=0;--ae)m0[ie|ae<<K]=K&7|ee<<3}var ce=[];for(R=1;ce.length<v+m;)switch(ie=m0[se(x,_)],_+=ie&7,ie>>>=3){case 16:for(w=3+Ye(x,_),_+=2,ie=ce[ce.length-1];w-- >0;)ce.push(ie);break;case 17:for(w=3+me(x,_),_+=3;w-- >0;)ce.push(0);break;case 18:for(w=11+se(x,_),_+=7;w-- >0;)ce.push(0);break;default:ce.push(ie),R<ie&&(R=ie);break}var ye=ce.slice(0,v),Ce=ce.slice(v);for(ee=v;ee<286;++ee)ye[ee]=0;for(ee=m;ee<30;++ee)Ce[ee]=0;return g0=jt(ye,p0,286),_0=jt(Ce,v0,30),_}function Yi(x,_){if(x[0]==3&&!(x[1]&3))return[Kr(_),2];for(var v=0,m=0,E=C0(_||1<<18),w=0,N=E.length>>>0,U=0,R=0;(m&1)==0;){if(m=me(x,v),v+=3,m>>>1)m>>1==1?(U=9,R=5):(v=Ki(x,v),U=g0,R=_0);else{v&7&&(v+=8-(v&7));var I=x[v>>>3]|x[(v>>>3)+1]<<8;if(v+=32,I>0)for(!_&&N<w+I&&(E=yn(E,w+I),N=E.length);I-- >0;)E[w++]=x[v>>>3],v+=8;continue}for(;;){!_&&N<w+32767&&(E=yn(E,w+32767),N=E.length);var k=or(x,v,U),z=m>>>1==1?Cn[k]:p0[k];if(v+=z&15,z>>>=4,(z>>>8&255)===0)E[w++]=z;else{if(z==256)break;z-=257;var ee=z<8?0:z-4>>2;ee>5&&(ee=0);var ie=w+H[z];ee>0&&(ie+=or(x,v,ee),v+=ee),k=or(x,v,R),z=m>>>1==1?On[k]:v0[k],v+=z&15,z>>>=4;var K=z<4?0:z-2>>1,ae=le[z];for(K>0&&(ae+=or(x,v,K),v+=K),!_&&N<ie&&(E=yn(E,ie+100),N=E.length);w<ie;)E[w]=E[w-ae],++w}}}return _?[E,v+7>>>3]:[E.slice(0,w),v+7>>>3]}function T0(x,_){var v=x.slice(x.l||0),m=Yi(v,_);return x.l+=m[1],m[0]}function E0(x,_){if(x)typeof console<"u"&&console.error(_);else throw new Error(_)}function w0(x,_){var v=x;ar(v,0);var m=[],E=[],w={FileIndex:m,FullPaths:E};D(w,{root:_.root});for(var N=v.length-4;(v[N]!=80||v[N+1]!=75||v[N+2]!=5||v[N+3]!=6)&&N>=0;)--N;v.l=N+4,v.l+=4;var U=v.read_shift(2);v.l+=6;var R=v.read_shift(4);for(v.l=R,N=0;N<U;++N){v.l+=20;var I=v.read_shift(4),k=v.read_shift(4),z=v.read_shift(2),ee=v.read_shift(2),ie=v.read_shift(2);v.l+=8;var K=v.read_shift(4),ae=f(v.slice(v.l+z,v.l+z+ee));v.l+=z+ee+ie;var ce=v.l;v.l=K+4,Ji(v,I,k,w,ae),v.l=ce}return w}function Ji(x,_,v,m,E){x.l+=2;var w=x.read_shift(2),N=x.read_shift(2),U=s(x);if(w&8257)throw new Error("Unsupported ZIP encryption");for(var R=x.read_shift(4),I=x.read_shift(4),k=x.read_shift(4),z=x.read_shift(2),ee=x.read_shift(2),ie="",K=0;K<z;++K)ie+=String.fromCharCode(x[x.l++]);if(ee){var ae=f(x.slice(x.l,x.l+ee));(ae[21589]||{}).mt&&(U=ae[21589].mt),((E||{})[21589]||{}).mt&&(U=E[21589].mt)}x.l+=ee;var ce=x.slice(x.l,x.l+I);switch(N){case 8:ce=L(x,k);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+N)}var ye=!1;w&8&&(R=x.read_shift(4),R==134695760&&(R=x.read_shift(4),ye=!0),I=x.read_shift(4),k=x.read_shift(4)),I!=_&&E0(ye,"Bad compressed size: "+_+" != "+I),k!=v&&E0(ye,"Bad uncompressed size: "+v+" != "+k),Dn(m,ie,ce,{unsafe:!0,mt:U})}function Zi(x,_){var v=_||{},m=[],E=[],w=B(1),N=v.compression?8:0,U=0,R=0,I=0,k=0,z=0,ee=x.FullPaths[0],ie=ee,K=x.FileIndex[0],ae=[],ce=0;for(R=1;R<x.FullPaths.length;++R)if(ie=x.FullPaths[R].slice(ee.length),K=x.FileIndex[R],!(!K.size||!K.content||ie=="Sh33tJ5")){var ye=k,Ce=B(ie.length);for(I=0;I<ie.length;++I)Ce.write_shift(1,ie.charCodeAt(I)&127);Ce=Ce.slice(0,Ce.l),ae[z]=sf.buf(K.content,0);var $e=K.content;N==8&&($e=y($e)),w=B(30),w.write_shift(4,67324752),w.write_shift(2,20),w.write_shift(2,U),w.write_shift(2,N),K.mt?i(w,K.mt):w.write_shift(4,0),w.write_shift(-4,ae[z]),w.write_shift(4,$e.length),w.write_shift(4,K.content.length),w.write_shift(2,Ce.length),w.write_shift(2,0),k+=w.length,m.push(w),k+=Ce.length,m.push(Ce),k+=$e.length,m.push($e),w=B(46),w.write_shift(4,33639248),w.write_shift(2,0),w.write_shift(2,20),w.write_shift(2,U),w.write_shift(2,N),w.write_shift(4,0),w.write_shift(-4,ae[z]),w.write_shift(4,$e.length),w.write_shift(4,K.content.length),w.write_shift(2,Ce.length),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(4,0),w.write_shift(4,ye),ce+=w.l,E.push(w),ce+=Ce.length,E.push(Ce),++z}return w=B(22),w.write_shift(4,101010256),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,z),w.write_shift(2,z),w.write_shift(4,ce),w.write_shift(4,k),w.write_shift(2,0),Ve([Ve(m),Ve(E),w])}var $t={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function qi(x,_){if(x.ctype)return x.ctype;var v=x.name||"",m=v.match(/\.([^\.]+)$/);return m&&$t[m[1]]||_&&(m=(v=_).match(/[\.\\]([^\.\\])+$/),m&&$t[m[1]])?$t[m[1]]:"application/octet-stream"}function Qi(x){for(var _=kt(x),v=[],m=0;m<_.length;m+=76)v.push(_.slice(m,m+76));return v.join(`\r
`)+`\r
`}function es(x){var _=x.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(I){var k=I.charCodeAt(0).toString(16).toUpperCase();return"="+(k.length==1?"0"+k:k)});_=_.replace(/ $/mg,"=20").replace(/\t$/mg,"=09"),_.charAt(0)==`
`&&(_="=0D"+_.slice(1)),_=_.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,`
=0A`).replace(/([^\r\n])\n/mg,"$1=0A");for(var v=[],m=_.split(`\r
`),E=0;E<m.length;++E){var w=m[E];if(w.length==0){v.push("");continue}for(var N=0;N<w.length;){var U=76,R=w.slice(N,N+U);R.charAt(U-1)=="="?U--:R.charAt(U-2)=="="?U-=2:R.charAt(U-3)=="="&&(U-=3),R=w.slice(N,N+U),N+=U,N<w.length&&(R+="="),v.push(R)}}return v.join(`\r
`)}function rs(x){for(var _=[],v=0;v<x.length;++v){for(var m=x[v];v<=x.length&&m.charAt(m.length-1)=="=";)m=m.slice(0,m.length-1)+x[++v];_.push(m)}for(var E=0;E<_.length;++E)_[E]=_[E].replace(/[=][0-9A-Fa-f]{2}/g,function(w){return String.fromCharCode(parseInt(w.slice(1),16))});return vr(_.join(`\r
`))}function ts(x,_,v){for(var m="",E="",w="",N,U=0;U<10;++U){var R=_[U];if(!R||R.match(/^\s*$/))break;var I=R.match(/^(.*?):\s*([^\s].*)$/);if(I)switch(I[1].toLowerCase()){case"content-location":m=I[2].trim();break;case"content-type":w=I[2].trim();break;case"content-transfer-encoding":E=I[2].trim();break}}switch(++U,E.toLowerCase()){case"base64":N=vr(Rr(_.slice(U).join("")));break;case"quoted-printable":N=rs(_.slice(U));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+E)}var k=Dn(x,m.slice(v.length),N,{unsafe:!0});w&&(k.ctype=w)}function ns(x,_){if(Pe(x.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var v=_&&_.root||"",m=(ve&&Buffer.isBuffer(x)?x.toString("binary"):Pe(x)).split(`\r
`),E=0,w="";for(E=0;E<m.length;++E)if(w=m[E],!!/^Content-Location:/i.test(w)&&(w=w.slice(w.indexOf("file")),v||(v=w.slice(0,w.lastIndexOf("/")+1)),w.slice(0,v.length)!=v))for(;v.length>0&&(v=v.slice(0,v.length-1),v=v.slice(0,v.lastIndexOf("/")+1),w.slice(0,v.length)!=v););var N=(m[1]||"").match(/boundary="(.*?)"/);if(!N)throw new Error("MAD cannot find boundary");var U="--"+(N[1]||""),R=[],I=[],k={FileIndex:R,FullPaths:I};D(k);var z,ee=0;for(E=0;E<m.length;++E){var ie=m[E];ie!==U&&ie!==U+"--"||(ee++&&ts(k,m.slice(z,E),v),z=E)}return k}function as(x,_){var v=_||{},m=v.boundary||"SheetJS";m="------="+m;for(var E=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+m.slice(2)+'"',"","",""],w=x.FullPaths[0],N=w,U=x.FileIndex[0],R=1;R<x.FullPaths.length;++R)if(N=x.FullPaths[R].slice(w.length),U=x.FileIndex[R],!(!U.size||!U.content||N=="Sh33tJ5")){N=N.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(ce){return"_x"+ce.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(ce){return"_u"+ce.charCodeAt(0).toString(16)+"_"});for(var I=U.content,k=ve&&Buffer.isBuffer(I)?I.toString("binary"):Pe(I),z=0,ee=Math.min(1024,k.length),ie=0,K=0;K<=ee;++K)(ie=k.charCodeAt(K))>=32&&ie<128&&++z;var ae=z>=ee*4/5;E.push(m),E.push("Content-Location: "+(v.root||"file:///C:/SheetJS/")+N),E.push("Content-Transfer-Encoding: "+(ae?"quoted-printable":"base64")),E.push("Content-Type: "+qi(U,N)),E.push(""),E.push(ae?es(k):Qi(k))}return E.push(m+`--\r
`),E.join(`\r
`)}function is(x){var _={};return D(_,x),_}function Dn(x,_,v,m){var E=m&&m.unsafe;E||D(x);var w=!E&&Se.find(x,_);if(!w){var N=x.FullPaths[0];_.slice(0,N.length)==N?N=_:(N.slice(-1)!="/"&&(N+="/"),N=(N+_).replace("//","/")),w={name:a(_),type:2},x.FileIndex.push(w),x.FullPaths.push(N),E||Se.utils.cfb_gc(x)}return w.content=v,w.size=v?v.length:0,m&&(m.CLSID&&(w.clsid=m.CLSID),m.mt&&(w.mt=m.mt),m.ct&&(w.ct=m.ct)),w}function ss(x,_){D(x);var v=Se.find(x,_);if(v){for(var m=0;m<x.FileIndex.length;++m)if(x.FileIndex[m]==v)return x.FileIndex.splice(m,1),x.FullPaths.splice(m,1),!0}return!1}function fs(x,_,v){D(x);var m=Se.find(x,_);if(m){for(var E=0;E<x.FileIndex.length;++E)if(x.FileIndex[E]==m)return x.FileIndex[E].name=a(v),x.FullPaths[E]=v,!0}return!1}function ls(x){P(x,!0)}return t.find=G,t.read=ne,t.parse=c,t.write=lr,t.writeFile=ue,t.utils={cfb_new:is,cfb_add:Dn,cfb_del:ss,cfb_mov:fs,cfb_gc:ls,ReadShift:Ot,CheckField:ja,prep_blob:ar,bconcat:Ve,use_zlib:S,_deflateRaw:d0,_inflateRaw:T0,consts:ge},t}();function ff(e){return typeof e=="string"?gn(e):Array.isArray(e)?Is(e):e}function Wt(e,t,r){if(typeof Deno<"u"){if(r&&typeof t=="string")switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=gn(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var n=r=="utf8"?Lt(t):t;if(typeof IE_SaveFile<"u")return IE_SaveFile(n,e);if(typeof Blob<"u"){var a=new Blob([ff(n)],{type:"application/octet-stream"});if(typeof navigator<"u"&&navigator.msSaveBlob)return navigator.msSaveBlob(a,e);if(typeof saveAs<"u")return saveAs(a,e);if(typeof URL<"u"&&typeof document<"u"&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(a);if(typeof chrome=="object"&&typeof(chrome.downloads||{}).download=="function")return URL.revokeObjectURL&&typeof setTimeout<"u"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var s=document.createElement("a");if(s.download!=null)return s.download=e,s.href=i,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL&&typeof setTimeout<"u"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),i}}if(typeof $<"u"&&typeof File<"u"&&typeof Folder<"u")try{var f=File(e);return f.open("w"),f.encoding="binary",Array.isArray(t)&&(t=bt(t)),f.write(t),f.close(),t}catch(o){if(!o.message||!o.message.match(/onstruct/))throw o}throw new Error("cannot save file "+e)}function je(e){for(var t=Object.keys(e),r=[],n=0;n<t.length;++n)Object.prototype.hasOwnProperty.call(e,t[n])&&r.push(t[n]);return r}function L0(e,t){for(var r=[],n=je(e),a=0;a!==n.length;++a)r[e[n[a]][t]]==null&&(r[e[n[a]][t]]=n[a]);return r}function Kn(e){for(var t=[],r=je(e),n=0;n!==r.length;++n)t[e[r[n]]]=r[n];return t}function En(e){for(var t=[],r=je(e),n=0;n!==r.length;++n)t[e[r[n]]]=parseInt(r[n],10);return t}function lf(e){for(var t=[],r=je(e),n=0;n!==r.length;++n)t[e[r[n]]]==null&&(t[e[r[n]]]=[]),t[e[r[n]]].push(r[n]);return t}var ln=new Date(1899,11,30,0,0,0);function er(e,t){var r=e.getTime(),n=ln.getTime()+(e.getTimezoneOffset()-ln.getTimezoneOffset())*6e4;return(r-n)/(24*60*60*1e3)}var Oa=new Date,of=ln.getTime()+(Oa.getTimezoneOffset()-ln.getTimezoneOffset())*6e4,M0=Oa.getTimezoneOffset();function Da(e){var t=new Date;return t.setTime(e*24*60*60*1e3+of),t.getTimezoneOffset()!==M0&&t.setTime(t.getTime()+(t.getTimezoneOffset()-M0)*6e4),t}var B0=new Date("2017-02-19T19:06:09.000Z"),Ra=isNaN(B0.getFullYear())?new Date("2/19/17"):B0,cf=Ra.getFullYear()==2017;function qe(e,t){var r=new Date(e);if(cf)return t>0?r.setTime(r.getTime()+r.getTimezoneOffset()*60*1e3):t<0&&r.setTime(r.getTime()-r.getTimezoneOffset()*60*1e3),r;if(e instanceof Date)return e;if(Ra.getFullYear()==1917&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var a=e.match(/\d+/g)||["2017","2","19","0","0","0"],i=new Date(+a[0],+a[1]-1,+a[2],+a[3]||0,+a[4]||0,+a[5]||0);return e.indexOf("Z")>-1&&(i=new Date(i.getTime()-i.getTimezoneOffset()*60*1e3)),i}function wn(e,t){if(ve&&Buffer.isBuffer(e))return e.toString("binary");if(typeof TextDecoder<"u")try{var r={"€":"","‚":"",ƒ:"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"",Š:"","‹":"",Œ:"",Ž:"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"",š:"","›":"",œ:"",ž:"",Ÿ:""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(i){return r[i]||i})}catch{}for(var n=[],a=0;a!=e.length;++a)n.push(String.fromCharCode(e[a]));return n.join("")}function rr(e){if(typeof JSON<"u"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=rr(e[r]));return t}function De(e,t){for(var r="";r.length<t;)r+=e;return r}function Or(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return!isNaN(t=Number(n))||(n=n.replace(/[(](.*)[)]/,function(a,i){return r=-r,i}),!isNaN(t=Number(n)))?t/r:t}var hf=["january","february","march","april","may","june","july","august","september","october","november","december"];function Pt(e){var t=new Date(e),r=new Date(NaN),n=t.getYear(),a=t.getMonth(),i=t.getDate();if(isNaN(i))return r;var s=e.toLowerCase();if(s.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(s=s.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),s.length>3&&hf.indexOf(s)==-1)return r}else if(s.match(/[a-z]/))return r;return n<0||n>8099?r:(a>0||i>1)&&n!=101?t:e.match(/[^-0-9:,\/\\]/)?r:t}function he(e,t,r){if(e.FullPaths){if(typeof r=="string"){var n;return ve?n=Ir(r):n=ks(r),Se.utils.cfb_add(e,t,n)}Se.utils.cfb_add(e,t,r)}else e.file(t,r)}function Yn(){return Se.utils.cfb_new()}var ke=`<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r
`,uf={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},Jn=Kn(uf),Zn=/[&<>'"]/g,xf=/[\u0000-\u0008\u000b-\u001f]/g;function Ee(e){var t=e+"";return t.replace(Zn,function(r){return Jn[r]}).replace(xf,function(r){return"_x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+"_"})}function U0(e){return Ee(e).replace(/ /g,"_x0020_")}var Na=/[\u0000-\u001f]/g;function df(e){var t=e+"";return t.replace(Zn,function(r){return Jn[r]}).replace(/\n/g,"<br/>").replace(Na,function(r){return"&#x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+";"})}function pf(e){var t=e+"";return t.replace(Zn,function(r){return Jn[r]}).replace(Na,function(r){return"&#x"+r.charCodeAt(0).toString(16).toUpperCase()+";"})}function vf(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")}function mf(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function Ln(e){for(var t="",r=0,n=0,a=0,i=0,s=0,f=0;r<e.length;){if(n=e.charCodeAt(r++),n<128){t+=String.fromCharCode(n);continue}if(a=e.charCodeAt(r++),n>191&&n<224){s=(n&31)<<6,s|=a&63,t+=String.fromCharCode(s);continue}if(i=e.charCodeAt(r++),n<240){t+=String.fromCharCode((n&15)<<12|(a&63)<<6|i&63);continue}s=e.charCodeAt(r++),f=((n&7)<<18|(a&63)<<12|(i&63)<<6|s&63)-65536,t+=String.fromCharCode(55296+(f>>>10&1023)),t+=String.fromCharCode(56320+(f&1023))}return t}function b0(e){var t=Kr(2*e.length),r,n,a=1,i=0,s=0,f;for(n=0;n<e.length;n+=a)a=1,(f=e.charCodeAt(n))<128?r=f:f<224?(r=(f&31)*64+(e.charCodeAt(n+1)&63),a=2):f<240?(r=(f&15)*4096+(e.charCodeAt(n+1)&63)*64+(e.charCodeAt(n+2)&63),a=3):(a=4,r=(f&7)*262144+(e.charCodeAt(n+1)&63)*4096+(e.charCodeAt(n+2)&63)*64+(e.charCodeAt(n+3)&63),r-=65536,s=55296+(r>>>10&1023),r=56320+(r&1023)),s!==0&&(t[i++]=s&255,t[i++]=s>>>8,s=0),t[i++]=r%256,t[i++]=r>>>8;return t.slice(0,i).toString("ucs2")}function W0(e){return Ir(e,"binary").toString("utf8")}var Zt="foo bar bazâð£",Ct=ve&&(W0(Zt)==Ln(Zt)&&W0||b0(Zt)==Ln(Zt)&&b0)||Ln,Lt=ve?function(e){return Ir(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,n=0,a=0;r<e.length;)switch(n=e.charCodeAt(r++),!0){case n<128:t.push(String.fromCharCode(n));break;case n<2048:t.push(String.fromCharCode(192+(n>>6))),t.push(String.fromCharCode(128+(n&63)));break;case(n>=55296&&n<57344):n-=55296,a=e.charCodeAt(r++)-56320+(n<<10),t.push(String.fromCharCode(240+(a>>18&7))),t.push(String.fromCharCode(144+(a>>12&63))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(a&63)));break;default:t.push(String.fromCharCode(224+(n>>12))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(n&63)))}return t.join("")},gf=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(t){return[new RegExp("&"+t[0]+";","ig"),t[1]]});return function(r){for(var n=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,`
`).replace(/<[^>]*>/g,""),a=0;a<e.length;++a)n=n.replace(e[a][0],e[a][1]);return n}}(),Ia=/(^\s|\s$|\n)/;function Ge(e,t){return"<"+e+(t.match(Ia)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function Mt(e){return je(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function Z(e,t,r){return"<"+e+(r!=null?Mt(r):"")+(t!=null?(t.match(Ia)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function Gn(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}function _f(e,t){switch(typeof e){case"string":var r=Z("vt:lpwstr",Ee(e));return r=r.replace(/&quot;/g,"_x0022_"),r;case"number":return Z((e|0)==e?"vt:i4":"vt:r8",Ee(String(e)));case"boolean":return Z("vt:bool",e?"true":"false")}if(e instanceof Date)return Z("vt:filetime",Gn(e));throw new Error("Unable to serialize "+e)}var Be={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},pt=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],ir={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function Tf(e,t){for(var r=1-2*(e[t+7]>>>7),n=((e[t+7]&127)<<4)+(e[t+6]>>>4&15),a=e[t+6]&15,i=5;i>=0;--i)a=a*256+e[t+i];return n==2047?a==0?r*(1/0):NaN:(n==0?n=-1022:(n-=1023,a+=Math.pow(2,52)),r*Math.pow(2,n-52)*a)}function Ef(e,t,r){var n=(t<0||1/t==-1/0?1:0)<<7,a=0,i=0,s=n?-t:t;isFinite(s)?s==0?a=i=0:(a=Math.floor(Math.log(s)/Math.LN2),i=s*Math.pow(2,52-a),a<=-1023&&(!isFinite(i)||i<Math.pow(2,52))?a=-1022:(i-=Math.pow(2,52),a+=1023)):(a=2047,i=isNaN(t)?26985:0);for(var f=0;f<=5;++f,i/=256)e[r+f]=i&255;e[r+6]=(a&15)<<4|i&15,e[r+7]=a>>4|n}var H0=function(e){for(var t=[],r=10240,n=0;n<e[0].length;++n)if(e[0][n])for(var a=0,i=e[0][n].length;a<i;a+=r)t.push.apply(t,e[0][n].slice(a,a+r));return t},V0=ve?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(t){return Buffer.isBuffer(t)?t:Ir(t)})):H0(e)}:H0,G0=function(e,t,r){for(var n=[],a=t;a<r;a+=2)n.push(String.fromCharCode(Ft(e,a)));return n.join("").replace(yt,"")},qn=ve?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(yt,""):G0(e,t,r)}:G0,X0=function(e,t,r){for(var n=[],a=t;a<t+r;++a)n.push(("0"+e[a].toString(16)).slice(-2));return n.join("")},ka=ve?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):X0(e,t,r)}:X0,j0=function(e,t,r){for(var n=[],a=t;a<r;a++)n.push(String.fromCharCode(lt(e,a)));return n.join("")},Ht=ve?function(t,r,n){return Buffer.isBuffer(t)?t.toString("utf8",r,n):j0(t,r,n)}:j0,Pa=function(e,t){var r=sr(e,t);return r>0?Ht(e,t+4,t+4+r-1):""},La=Pa,Ma=function(e,t){var r=sr(e,t);return r>0?Ht(e,t+4,t+4+r-1):""},Ba=Ma,Ua=function(e,t){var r=2*sr(e,t);return r>0?Ht(e,t+4,t+4+r-1):""},ba=Ua,Wa=function(t,r){var n=sr(t,r);return n>0?qn(t,r+4,r+4+n):""},Ha=Wa,Va=function(e,t){var r=sr(e,t);return r>0?Ht(e,t+4,t+4+r):""},Ga=Va,Xa=function(e,t){return Tf(e,t)},on=Xa,Qn=function(t){return Array.isArray(t)||typeof Uint8Array<"u"&&t instanceof Uint8Array};ve&&(La=function(t,r){if(!Buffer.isBuffer(t))return Pa(t,r);var n=t.readUInt32LE(r);return n>0?t.toString("utf8",r+4,r+4+n-1):""},Ba=function(t,r){if(!Buffer.isBuffer(t))return Ma(t,r);var n=t.readUInt32LE(r);return n>0?t.toString("utf8",r+4,r+4+n-1):""},ba=function(t,r){if(!Buffer.isBuffer(t))return Ua(t,r);var n=2*t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+n-1)},Ha=function(t,r){if(!Buffer.isBuffer(t))return Wa(t,r);var n=t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+n)},Ga=function(t,r){if(!Buffer.isBuffer(t))return Va(t,r);var n=t.readUInt32LE(r);return t.toString("utf8",r+4,r+4+n)},on=function(t,r){return Buffer.isBuffer(t)?t.readDoubleLE(r):Xa(t,r)},Qn=function(t){return Buffer.isBuffer(t)||Array.isArray(t)||typeof Uint8Array<"u"&&t instanceof Uint8Array});var lt=function(e,t){return e[t]},Ft=function(e,t){return e[t+1]*256+e[t]},wf=function(e,t){var r=e[t+1]*256+e[t];return r<32768?r:(65535-r+1)*-1},sr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},$r=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},Sf=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function Ot(e,t){var r="",n,a,i=[],s,f,o,l;switch(t){case"dbcs":if(l=this.l,ve&&Buffer.isBuffer(this))r=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o)r+=String.fromCharCode(Ft(this,l)),l+=2;e*=2;break;case"utf8":r=Ht(this,this.l,this.l+e);break;case"utf16le":e*=2,r=qn(this,this.l,this.l+e);break;case"wstr":return Ot.call(this,e,"dbcs");case"lpstr-ansi":r=La(this,this.l),e=4+sr(this,this.l);break;case"lpstr-cp":r=Ba(this,this.l),e=4+sr(this,this.l);break;case"lpwstr":r=ba(this,this.l),e=4+2*sr(this,this.l);break;case"lpp4":e=4+sr(this,this.l),r=Ha(this,this.l),e&2&&(e+=2);break;case"8lpp4":e=4+sr(this,this.l),r=Ga(this,this.l),e&3&&(e+=4-(e&3));break;case"cstr":for(e=0,r="";(s=lt(this,this.l+e++))!==0;)i.push(Kt(s));r=i.join("");break;case"_wstr":for(e=0,r="";(s=Ft(this,this.l+e))!==0;)i.push(Kt(s)),e+=2;e+=2,r=i.join("");break;case"dbcs-cont":for(r="",l=this.l,o=0;o<e;++o){if(this.lens&&this.lens.indexOf(l)!==-1)return s=lt(this,l),this.l=l+1,f=Ot.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push(Kt(Ft(this,l))),l+=2}r=i.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(r="",l=this.l,o=0;o!=e;++o){if(this.lens&&this.lens.indexOf(l)!==-1)return s=lt(this,l),this.l=l+1,f=Ot.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push(Kt(lt(this,l))),l+=1}r=i.join("");break;default:switch(e){case 1:return n=lt(this,this.l),this.l++,n;case 2:return n=(t==="i"?wf:Ft)(this,this.l),this.l+=2,n;case 4:case-4:return t==="i"||(this[this.l+3]&128)===0?(n=(e>0?$r:Sf)(this,this.l),this.l+=4,n):(a=sr(this,this.l),this.l+=4,a);case 8:case-8:if(t==="f")return e==8?a=on(this,this.l):a=on([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,a;e=8;case 16:r=ka(this,this.l,e);break}}return this.l+=e,r}var Af=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},Ff=function(e,t,r){e[r]=t&255,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},yf=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255};function Cf(e,t,r){var n=0,a=0;if(r==="dbcs"){for(a=0;a!=t.length;++a)yf(this,t.charCodeAt(a),this.l+2*a);n=2*t.length}else if(r==="sbcs"){for(t=t.replace(/[^\x00-\x7F]/g,"_"),a=0;a!=t.length;++a)this[this.l+a]=t.charCodeAt(a)&255;n=t.length}else if(r==="hex"){for(;a<e;++a)this[this.l++]=parseInt(t.slice(2*a,2*a+2),16)||0;return this}else if(r==="utf16le"){var i=Math.min(this.l+e,this.length);for(a=0;a<Math.min(t.length,e);++a){var s=t.charCodeAt(a);this[this.l++]=s&255,this[this.l++]=s>>8}for(;this.l<i;)this[this.l++]=0;return this}else switch(e){case 1:n=1,this[this.l]=t&255;break;case 2:n=2,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255;break;case 3:n=3,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255,t>>>=8,this[this.l+2]=t&255;break;case 4:n=4,Af(this,t,this.l);break;case 8:if(n=8,r==="f"){Ef(this,t,this.l);break}case 16:break;case-4:n=4,Ff(this,t,this.l);break}return this.l+=n,this}function ja(e,t){var r=ka(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function ar(e,t){e.l=t,e.read_shift=Ot,e.chk=ja,e.write_shift=Cf}function wr(e,t){e.l+=t}function B(e){var t=Kr(e);return ar(t,0),t}function Qe(){var e=[],t=ve?256:2048,r=function(l){var c=B(l);return ar(c,0),c},n=r(t),a=function(){n&&(n.length>n.l&&(n=n.slice(0,n.l),n.l=n.length),n.length>0&&e.push(n),n=null)},i=function(l){return n&&l<n.length-n.l?n:(a(),n=r(Math.max(l+1,t)))},s=function(){return a(),Ve(e)},f=function(l){a(),n=l,n.l==null&&(n.l=n.length),i(t)};return{next:i,push:f,end:s,_bufs:e}}function W(e,t,r,n){var a=+t,i;if(!isNaN(a)){n||(n=Eu[a].p||(r||[]).length||0),i=1+(a>=128?1:0)+1,n>=128&&++i,n>=16384&&++i,n>=2097152&&++i;var s=e.next(i);a<=127?s.write_shift(1,a):(s.write_shift(1,(a&127)+128),s.write_shift(1,a>>7));for(var f=0;f!=4;++f)if(n>=128)s.write_shift(1,(n&127)+128),n>>=7;else{s.write_shift(1,n);break}n>0&&Qn(r)&&e.push(r)}}function Dt(e,t,r){var n=rr(e);if(t.s?(n.cRel&&(n.c+=t.s.c),n.rRel&&(n.r+=t.s.r)):(n.cRel&&(n.c+=t.c),n.rRel&&(n.r+=t.r)),!r||r.biff<12){for(;n.c>=256;)n.c-=256;for(;n.r>=65536;)n.r-=65536}return n}function $0(e,t,r){var n=rr(e);return n.s=Dt(n.s,t.s,r),n.e=Dt(n.e,t.s,r),n}function Rt(e,t){if(e.cRel&&e.c<0)for(e=rr(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=rr(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=we(e);return!e.cRel&&e.cRel!=null&&(r=Rf(r)),!e.rRel&&e.rRel!=null&&(r=Of(r)),r}function Mn(e,t){return e.s.r==0&&!e.s.rRel&&e.e.r==(t.biff>=12?1048575:t.biff>=8?65536:16384)&&!e.e.rRel?(e.s.cRel?"":"$")+ze(e.s.c)+":"+(e.e.cRel?"":"$")+ze(e.e.c):e.s.c==0&&!e.s.cRel&&e.e.c==(t.biff>=12?16383:255)&&!e.e.cRel?(e.s.rRel?"":"$")+Xe(e.s.r)+":"+(e.e.rRel?"":"$")+Xe(e.e.r):Rt(e.s,t.biff)+":"+Rt(e.e,t.biff)}function e0(e){return parseInt(Df(e),10)-1}function Xe(e){return""+(e+1)}function Of(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function Df(e){return e.replace(/\$(\d+)$/,"$1")}function r0(e){for(var t=Nf(e),r=0,n=0;n!==t.length;++n)r=26*r+t.charCodeAt(n)-64;return r-1}function ze(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function Rf(e){return e.replace(/^([A-Z])/,"$$$1")}function Nf(e){return e.replace(/^\$([A-Z])/,"$1")}function If(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function Ue(e){for(var t=0,r=0,n=0;n<e.length;++n){var a=e.charCodeAt(n);a>=48&&a<=57?t=10*t+(a-48):a>=65&&a<=90&&(r=26*r+(a-64))}return{c:r-1,r:t-1}}function we(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function fr(e){var t=e.indexOf(":");return t==-1?{s:Ue(e),e:Ue(e)}:{s:Ue(e.slice(0,t)),e:Ue(e.slice(t+1))}}function Ie(e,t){return typeof t>"u"||typeof t=="number"?Ie(e.s,e.e):(typeof e!="string"&&(e=we(e)),typeof t!="string"&&(t=we(t)),e==t?e:e+":"+t)}function Fe(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,a=0,i=e.length;for(r=0;n<i&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.s.c=--r,r=0;n<i&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;if(t.s.r=--r,n===i||a!=10)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++n,r=0;n!=i&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.e.c=--r,r=0;n!=i&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;return t.e.r=--r,t}function z0(e,t){var r=e.t=="d"&&t instanceof Date;if(e.z!=null)try{return e.w=Ur(e.z,r?er(t):t)}catch{}try{return e.w=Ur((e.XF||{}).numFmtId||(r?14:0),r?er(t):t)}catch{return""+t}}function Nr(e,t,r){return e==null||e.t==null||e.t=="z"?"":e.w!==void 0?e.w:(e.t=="d"&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),e.t=="e"?Vt[e.v]||e.v:t==null?z0(e,e.v):z0(e,t))}function Zr(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function $a(e,t,r){var n=r||{},a=e?Array.isArray(e):n.dense,i=e||(a?[]:{}),s=0,f=0;if(i&&n.origin!=null){if(typeof n.origin=="number")s=n.origin;else{var o=typeof n.origin=="string"?Ue(n.origin):n.origin;s=o.r,f=o.c}i["!ref"]||(i["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var c=Fe(i["!ref"]);l.s.c=c.s.c,l.s.r=c.s.r,l.e.c=Math.max(l.e.c,c.e.c),l.e.r=Math.max(l.e.r,c.e.r),s==-1&&(l.e.r=s=c.e.r+1)}for(var d=0;d!=t.length;++d)if(t[d]){if(!Array.isArray(t[d]))throw new Error("aoa_to_sheet expects an array of arrays");for(var h=0;h!=t[d].length;++h)if(!(typeof t[d][h]>"u")){var p={v:t[d][h]},T=s+d,u=f+h;if(l.s.r>T&&(l.s.r=T),l.s.c>u&&(l.s.c=u),l.e.r<T&&(l.e.r=T),l.e.c<u&&(l.e.c=u),t[d][h]&&typeof t[d][h]=="object"&&!Array.isArray(t[d][h])&&!(t[d][h]instanceof Date))p=t[d][h];else if(Array.isArray(p.v)&&(p.f=t[d][h][1],p.v=p.v[0]),p.v===null)if(p.f)p.t="n";else if(n.nullError)p.t="e",p.v=0;else if(n.sheetStubs)p.t="z";else continue;else typeof p.v=="number"?p.t="n":typeof p.v=="boolean"?p.t="b":p.v instanceof Date?(p.z=n.dateNF||Re[14],n.cellDates?(p.t="d",p.w=Ur(p.z,er(p.v))):(p.t="n",p.v=er(p.v),p.w=Ur(p.z,p.v))):p.t="s";if(a)i[T]||(i[T]=[]),i[T][u]&&i[T][u].z&&(p.z=i[T][u].z),i[T][u]=p;else{var g=we({c:u,r:T});i[g]&&i[g].z&&(p.z=i[g].z),i[g]=p}}}return l.s.c<1e7&&(i["!ref"]=Ie(l)),i}function vt(e,t){return $a(null,e,t)}function kf(e){return e.read_shift(4,"i")}function gr(e,t){return t||(t=B(4)),t.write_shift(4,e),t}function Ke(e){var t=e.read_shift(4);return t===0?"":e.read_shift(t,"dbcs")}function be(e,t){var r=!1;return t==null&&(r=!0,t=B(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function Pf(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function Lf(e,t){return t||(t=B(4)),t.write_shift(2,0),t.write_shift(2,0),t}function t0(e,t){var r=e.l,n=e.read_shift(1),a=Ke(e),i=[],s={t:a,h:a};if((n&1)!==0){for(var f=e.read_shift(4),o=0;o!=f;++o)i.push(Pf(e));s.r=i}else s.r=[{ich:0,ifnt:0}];return e.l=r+t,s}function Mf(e,t){var r=!1;return t==null&&(r=!0,t=B(15+4*e.t.length)),t.write_shift(1,0),be(e.t,t),r?t.slice(0,t.l):t}var Bf=t0;function Uf(e,t){var r=!1;return t==null&&(r=!0,t=B(23+4*e.t.length)),t.write_shift(1,1),be(e.t,t),t.write_shift(4,1),Lf({},t),r?t.slice(0,t.l):t}function ur(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function qr(e,t){return t==null&&(t=B(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function Qr(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function et(e,t){return t==null&&(t=B(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var bf=Ke,za=be;function n0(e){var t=e.read_shift(4);return t===0||t===4294967295?"":e.read_shift(t,"dbcs")}function cn(e,t){var r=!1;return t==null&&(r=!0,t=B(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var Wf=Ke,Xn=n0,a0=cn;function Ka(e){var t=e.slice(e.l,e.l+4),r=t[0]&1,n=t[0]&2;e.l+=4;var a=n===0?on([0,0,0,0,t[0]&252,t[1],t[2],t[3]],0):$r(t,0)>>2;return r?a/100:a}function Ya(e,t){t==null&&(t=B(4));var r=0,n=0,a=e*100;if(e==(e|0)&&e>=-536870912&&e<1<<29?n=1:a==(a|0)&&a>=-536870912&&a<1<<29&&(n=1,r=1),n)t.write_shift(-4,((r?a:e)<<2)+(r+2));else throw new Error("unsupported RkNumber "+e)}function Ja(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}function Hf(e,t){return t||(t=B(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t}var rt=Ja,mt=Hf;function gt(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function Yr(e,t){return(t||B(8)).write_shift(8,e,"f")}function Vf(e){var t={},r=e.read_shift(1),n=r>>>1,a=e.read_shift(1),i=e.read_shift(2,"i"),s=e.read_shift(1),f=e.read_shift(1),o=e.read_shift(1);switch(e.l++,n){case 0:t.auto=1;break;case 1:t.index=a;var l=Zf[a];l&&(t.rgb=aa(l));break;case 2:t.rgb=aa([s,f,o]);break;case 3:t.theme=a;break}return i!=0&&(t.tint=i>0?i/32767:i/32768),t}function hn(e,t){if(t||(t=B(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;e.index!=null?(t.write_shift(1,2),t.write_shift(1,e.index)):e.theme!=null?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),!e.rgb||e.theme!=null)t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);else{var n=e.rgb||"FFFFFF";typeof n=="number"&&(n=("000000"+n.toString(16)).slice(-6)),t.write_shift(1,parseInt(n.slice(0,2),16)),t.write_shift(1,parseInt(n.slice(2,4),16)),t.write_shift(1,parseInt(n.slice(4,6),16)),t.write_shift(1,255)}return t}function Gf(e){var t=e.read_shift(1);e.l++;var r={fBold:t&1,fItalic:t&2,fUnderline:t&4,fStrikeout:t&8,fOutline:t&16,fShadow:t&32,fCondense:t&64,fExtend:t&128};return r}function Xf(e,t){t||(t=B(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);return t.write_shift(1,r),t.write_shift(1,0),t}var Za=2,nr=3,qt=11,un=19,Qt=64,jf=65,$f=71,zf=4108,Kf=4126,He=80,K0={1:{n:"CodePage",t:Za},2:{n:"Category",t:He},3:{n:"PresentationFormat",t:He},4:{n:"ByteCount",t:nr},5:{n:"LineCount",t:nr},6:{n:"ParagraphCount",t:nr},7:{n:"SlideCount",t:nr},8:{n:"NoteCount",t:nr},9:{n:"HiddenCount",t:nr},10:{n:"MultimediaClipCount",t:nr},11:{n:"ScaleCrop",t:qt},12:{n:"HeadingPairs",t:zf},13:{n:"TitlesOfParts",t:Kf},14:{n:"Manager",t:He},15:{n:"Company",t:He},16:{n:"LinksUpToDate",t:qt},17:{n:"CharacterCount",t:nr},19:{n:"SharedDoc",t:qt},22:{n:"HyperlinksChanged",t:qt},23:{n:"AppVersion",t:nr,p:"version"},24:{n:"DigSig",t:jf},26:{n:"ContentType",t:He},27:{n:"ContentStatus",t:He},28:{n:"Language",t:He},29:{n:"Version",t:He},255:{},2147483648:{n:"Locale",t:un},2147483651:{n:"Behavior",t:un},1919054434:{}},Y0={1:{n:"CodePage",t:Za},2:{n:"Title",t:He},3:{n:"Subject",t:He},4:{n:"Author",t:He},5:{n:"Keywords",t:He},6:{n:"Comments",t:He},7:{n:"Template",t:He},8:{n:"LastAuthor",t:He},9:{n:"RevNumber",t:He},10:{n:"EditTime",t:Qt},11:{n:"LastPrinted",t:Qt},12:{n:"CreatedDate",t:Qt},13:{n:"ModifiedDate",t:Qt},14:{n:"PageCount",t:nr},15:{n:"WordCount",t:nr},16:{n:"CharCount",t:nr},17:{n:"Thumbnail",t:$f},18:{n:"Application",t:He},19:{n:"DocSecurity",t:nr},255:{},2147483648:{n:"Locale",t:un},2147483651:{n:"Behavior",t:un},1919054434:{}};function Yf(e){return e.map(function(t){return[t>>16&255,t>>8&255,t&255]})}var Jf=Yf([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),Zf=rr(Jf),Vt={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},qf={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},en={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function qa(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function Qa(e,t){var r=lf(qf),n=[],a;n[n.length]=ke,n[n.length]=Z("Types",null,{xmlns:Be.CT,"xmlns:xsd":Be.xsd,"xmlns:xsi":Be.xsi}),n=n.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(o){return Z("Default",null,{Extension:o[0],ContentType:o[1]})}));var i=function(o){e[o]&&e[o].length>0&&(a=e[o][0],n[n.length]=Z("Override",null,{PartName:(a[0]=="/"?"":"/")+a,ContentType:en[o][t.bookType]||en[o].xlsx}))},s=function(o){(e[o]||[]).forEach(function(l){n[n.length]=Z("Override",null,{PartName:(l[0]=="/"?"":"/")+l,ContentType:en[o][t.bookType]||en[o].xlsx})})},f=function(o){(e[o]||[]).forEach(function(l){n[n.length]=Z("Override",null,{PartName:(l[0]=="/"?"":"/")+l,ContentType:r[o][0]})})};return i("workbooks"),s("sheets"),s("charts"),f("themes"),["strs","styles"].forEach(i),["coreprops","extprops","custprops"].forEach(f),f("vba"),f("comments"),f("threadedcomments"),f("drawings"),s("metadata"),f("people"),n.length>2&&(n[n.length]="</Types>",n[1]=n[1].replace("/>",">")),n.join("")}var pe={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function ei(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function ht(e){var t=[ke,Z("Relationships",null,{xmlns:Be.RELS})];return je(e["!id"]).forEach(function(r){t[t.length]=Z("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function Te(e,t,r,n,a,i){if(a||(a={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,a.Id="rId"+t,a.Type=n,a.Target=r,[pe.HLINK,pe.XPATH,pe.XMISS].indexOf(a.Type)>-1&&(a.TargetMode="External"),e["!id"][a.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][a.Id]=a,e[("/"+a.Target).replace("//","/")]=a,t}function Qf(e){var t=[ke];t.push(`<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">
`),t.push(`  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>
`);for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+`"/>
`);return t.push("</manifest:manifest>"),t.join("")}function J0(e,t,r){return['  <rdf:Description rdf:about="'+e+`">
`,'    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+`"/>
`,`  </rdf:Description>
`].join("")}function el(e,t){return['  <rdf:Description rdf:about="'+e+`">
`,'    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+t+`"/>
`,`  </rdf:Description>
`].join("")}function rl(e){var t=[ke];t.push(`<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
`);for(var r=0;r!=e.length;++r)t.push(J0(e[r][0],e[r][1])),t.push(el("",e[r][0]));return t.push(J0("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}function ri(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+nn.version+"</meta:generator></office:meta></office:document-meta>"}var zr=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function Bn(e,t,r,n,a){a[e]!=null||t==null||t===""||(a[e]=t,t=Ee(t),n[n.length]=r?Z(e,t,r):Ge(e,t))}function ti(e,t){var r=t||{},n=[ke,Z("cp:coreProperties",null,{"xmlns:cp":Be.CORE_PROPS,"xmlns:dc":Be.dc,"xmlns:dcterms":Be.dcterms,"xmlns:dcmitype":Be.dcmitype,"xmlns:xsi":Be.xsi})],a={};if(!e&&!r.Props)return n.join("");e&&(e.CreatedDate!=null&&Bn("dcterms:created",typeof e.CreatedDate=="string"?e.CreatedDate:Gn(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a),e.ModifiedDate!=null&&Bn("dcterms:modified",typeof e.ModifiedDate=="string"?e.ModifiedDate:Gn(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a));for(var i=0;i!=zr.length;++i){var s=zr[i],f=r.Props&&r.Props[s[1]]!=null?r.Props[s[1]]:e?e[s[1]]:null;f===!0?f="1":f===!1?f="0":typeof f=="number"&&(f=String(f)),f!=null&&Bn(s[0],f,null,n,a)}return n.length>2&&(n[n.length]="</cp:coreProperties>",n[1]=n[1].replace("/>",">")),n.join("")}var ut=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],ni=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function ai(e){var t=[],r=Z;return e||(e={}),e.Application="SheetJS",t[t.length]=ke,t[t.length]=Z("Properties",null,{xmlns:Be.EXT_PROPS,"xmlns:vt":Be.vt}),ut.forEach(function(n){if(e[n[1]]!==void 0){var a;switch(n[2]){case"string":a=Ee(String(e[n[1]]));break;case"bool":a=e[n[1]]?"true":"false";break}a!==void 0&&(t[t.length]=r(n[0],a))}}),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map(function(n){return"<vt:lpstr>"+Ee(n)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}function ii(e){var t=[ke,Z("Properties",null,{xmlns:Be.CUST_PROPS,"xmlns:vt":Be.vt})];if(!e)return t.join("");var r=1;return je(e).forEach(function(a){++r,t[t.length]=Z("property",_f(e[a]),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:Ee(a)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var Z0={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function tl(e,t){var r=[];return je(Z0).map(function(n){for(var a=0;a<zr.length;++a)if(zr[a][1]==n)return zr[a];for(a=0;a<ut.length;++a)if(ut[a][1]==n)return ut[a];throw n}).forEach(function(n){if(e[n[1]]!=null){var a=t&&t.Props&&t.Props[n[1]]!=null?t.Props[n[1]]:e[n[1]];switch(n[2]){case"date":a=new Date(a).toISOString().replace(/\.\d*Z/,"Z");break}typeof a=="number"?a=String(a):a===!0||a===!1?a=a?"1":"0":a instanceof Date&&(a=new Date(a).toISOString().replace(/\.\d*Z/,"")),r.push(Ge(Z0[n[1]]||n[1],a))}}),Z("DocumentProperties",r.join(""),{xmlns:ir.o})}function nl(e,t){var r=["Worksheets","SheetNames"],n="CustomDocumentProperties",a=[];return e&&je(e).forEach(function(i){if(Object.prototype.hasOwnProperty.call(e,i)){for(var s=0;s<zr.length;++s)if(i==zr[s][1])return;for(s=0;s<ut.length;++s)if(i==ut[s][1])return;for(s=0;s<r.length;++s)if(i==r[s])return;var f=e[i],o="string";typeof f=="number"?(o="float",f=String(f)):f===!0||f===!1?(o="boolean",f=f?"1":"0"):f=String(f),a.push(Z(U0(i),f,{"dt:dt":o}))}}),t&&je(t).forEach(function(i){if(Object.prototype.hasOwnProperty.call(t,i)&&!(e&&Object.prototype.hasOwnProperty.call(e,i))){var s=t[i],f="string";typeof s=="number"?(f="float",s=String(s)):s===!0||s===!1?(f="boolean",s=s?"1":"0"):s instanceof Date?(f="dateTime.tz",s=s.toISOString()):s=String(s),a.push(Z(U0(i),s,{"dt:dt":f}))}}),"<"+n+' xmlns="'+ir.o+'">'+a.join("")+"</"+n+">"}function al(e){var t=typeof e=="string"?new Date(Date.parse(e)):e,r=t.getTime()/1e3+11644473600,n=r%Math.pow(2,32),a=(r-n)/Math.pow(2,32);n*=1e7,a*=1e7;var i=n/Math.pow(2,32)|0;i>0&&(n=n%Math.pow(2,32),a+=i);var s=B(8);return s.write_shift(4,n),s.write_shift(4,a),s}function q0(e,t){var r=B(4),n=B(4);switch(r.write_shift(4,e==80?31:e),e){case 3:n.write_shift(-4,t);break;case 5:n=B(8),n.write_shift(8,t,"f");break;case 11:n.write_shift(4,t?1:0);break;case 64:n=al(t);break;case 31:case 80:for(n=B(4+2*(t.length+1)+(t.length%2?0:2)),n.write_shift(4,t.length+1),n.write_shift(0,t,"dbcs");n.l!=n.length;)n.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return Ve([r,n])}var si=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function il(e){switch(typeof e){case"boolean":return 11;case"number":return(e|0)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64;break}return-1}function Q0(e,t,r){var n=B(8),a=[],i=[],s=8,f=0,o=B(8),l=B(8);if(o.write_shift(4,2),o.write_shift(4,1200),l.write_shift(4,1),i.push(o),a.push(l),s+=8+o.length,!t){l=B(8),l.write_shift(4,0),a.unshift(l);var c=[B(4)];for(c[0].write_shift(4,e.length),f=0;f<e.length;++f){var d=e[f][0];for(o=B(8+2*(d.length+1)+(d.length%2?0:2)),o.write_shift(4,f+2),o.write_shift(4,d.length+1),o.write_shift(0,d,"dbcs");o.l!=o.length;)o.write_shift(1,0);c.push(o)}o=Ve(c),i.unshift(o),s+=8+o.length}for(f=0;f<e.length;++f)if(!(t&&!t[e[f][0]])&&!(si.indexOf(e[f][0])>-1||ni.indexOf(e[f][0])>-1)&&e[f][1]!=null){var h=e[f][1],p=0;if(t){p=+t[e[f][0]];var T=r[p];if(T.p=="version"&&typeof h=="string"){var u=h.split(".");h=(+u[0]<<16)+(+u[1]||0)}o=q0(T.t,h)}else{var g=il(h);g==-1&&(g=31,h=String(h)),o=q0(g,h)}i.push(o),l=B(8),l.write_shift(4,t?p:2+f),a.push(l),s+=8+o.length}var C=8*(i.length+1);for(f=0;f<i.length;++f)a[f].write_shift(4,C),C+=i[f].length;return n.write_shift(4,s),n.write_shift(4,i.length),Ve([n].concat(a).concat(i))}function ea(e,t,r,n,a,i){var s=B(a?68:48),f=[s];s.write_shift(2,65534),s.write_shift(2,0),s.write_shift(4,842412599),s.write_shift(16,Se.utils.consts.HEADER_CLSID,"hex"),s.write_shift(4,a?2:1),s.write_shift(16,t,"hex"),s.write_shift(4,a?68:48);var o=Q0(e,r,n);if(f.push(o),a){var l=Q0(a,null,null);s.write_shift(16,i,"hex"),s.write_shift(4,68+o.length),f.push(l)}return Ve(f)}function sl(e,t){t||(t=B(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}function fl(e,t){return e.read_shift(t)===1}function Ze(e,t){return t||(t=B(2)),t.write_shift(2,+!!e),t}function fi(e){return e.read_shift(2,"u")}function hr(e,t){return t||(t=B(2)),t.write_shift(2,e),t}function li(e,t,r){return r||(r=B(2)),r.write_shift(1,t=="e"?+e:+!!e),r.write_shift(1,t=="e"?1:0),r}function oi(e,t,r){var n=e.read_shift(r&&r.biff>=12?2:1),a="sbcs-cont";if(r&&r.biff>=8,!r||r.biff==8){var i=e.read_shift(1);i&&(a="dbcs-cont")}else r.biff==12&&(a="wstr");r.biff>=2&&r.biff<=5&&(a="cpstr");var s=n?e.read_shift(n,a):"";return s}function ll(e){var t=e.t||"",r=B(3);r.write_shift(2,t.length),r.write_shift(1,1);var n=B(2*t.length);n.write_shift(2*t.length,t,"utf16le");var a=[r,n];return Ve(a)}function ol(e,t,r){var n;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}var a=e.read_shift(1);return a===0?n=e.read_shift(t,"sbcs-cont"):n=e.read_shift(t,"dbcs-cont"),n}function cl(e,t,r){var n=e.read_shift(r&&r.biff==2?1:2);return n===0?(e.l++,""):ol(e,n,r)}function hl(e,t,r){if(r.biff>5)return cl(e,t,r);var n=e.read_shift(1);return n===0?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function ci(e,t,r){return r||(r=B(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function ra(e,t){t||(t=B(6+e.length*2)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function ul(e){var t=B(512),r=0,n=e.Target;n.slice(0,7)=="file://"&&(n=n.slice(7));var a=n.indexOf("#"),i=a>-1?31:23;switch(n.charAt(0)){case"#":i=28;break;case".":i&=-3;break}t.write_shift(4,2),t.write_shift(4,i);var s=[8,6815827,6619237,4849780,83];for(r=0;r<s.length;++r)t.write_shift(4,s[r]);if(i==28)n=n.slice(1),ra(n,t);else if(i&2){for(s="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));var f=a>-1?n.slice(0,a):n;for(t.write_shift(4,2*(f.length+1)),r=0;r<f.length;++r)t.write_shift(2,f.charCodeAt(r));t.write_shift(2,0),i&8&&ra(a>-1?n.slice(a+1):"",t)}else{for(s="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));for(var o=0;n.slice(o*3,o*3+3)=="../"||n.slice(o*3,o*3+3)=="..\\";)++o;for(t.write_shift(2,o),t.write_shift(4,n.length-3*o+1),r=0;r<n.length-3*o;++r)t.write_shift(1,n.charCodeAt(r+3*o)&255);for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function Jr(e,t,r,n){return n||(n=B(6)),n.write_shift(2,e),n.write_shift(2,t),n.write_shift(2,r||0),n}function xl(e,t,r){var n=r.biff>8?4:2,a=e.read_shift(n),i=e.read_shift(n,"i"),s=e.read_shift(n,"i");return[a,i,s]}function dl(e){var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2),a=e.read_shift(2);return{s:{c:n,r:t},e:{c:a,r}}}function hi(e,t){return t||(t=B(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function i0(e,t,r){var n=1536,a=16;switch(r.bookType){case"biff8":break;case"biff5":n=1280,a=8;break;case"biff4":n=4,a=6;break;case"biff3":n=3,a=6;break;case"biff2":n=2,a=4;break;case"xla":break;default:throw new Error("unsupported BIFF version")}var i=B(a);return i.write_shift(2,n),i.write_shift(2,t),a>4&&i.write_shift(2,29282),a>6&&i.write_shift(2,1997),a>8&&(i.write_shift(2,49161),i.write_shift(2,1),i.write_shift(2,1798),i.write_shift(2,0)),i}function pl(e,t){var r=!t||t.biff==8,n=B(r?112:54);for(n.write_shift(t.biff==8?2:1,7),r&&n.write_shift(1,0),n.write_shift(4,859007059),n.write_shift(4,5458548|(r?0:536870912));n.l<n.length;)n.write_shift(1,r?0:32);return n}function vl(e,t){var r=!t||t.biff>=8?2:1,n=B(8+r*e.name.length);n.write_shift(4,e.pos),n.write_shift(1,e.hs||0),n.write_shift(1,e.dt),n.write_shift(1,e.name.length),t.biff>=8&&n.write_shift(1,1),n.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var a=n.slice(0,n.l);return a.l=n.l,a}function ml(e,t){var r=B(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var n=[],a=0;a<e.length;++a)n[a]=ll(e[a]);var i=Ve([r].concat(n));return i.parts=[r.length].concat(n.map(function(s){return s.length})),i}function gl(){var e=B(18);return e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,29280),e.write_shift(2,17600),e.write_shift(2,56),e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,1),e.write_shift(2,500),e}function _l(e){var t=B(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}function Tl(e,t){var r=e.name||"Arial",n=t&&t.biff==5,a=n?15+r.length:16+2*r.length,i=B(a);return i.write_shift(2,e.sz*20),i.write_shift(4,0),i.write_shift(2,400),i.write_shift(4,0),i.write_shift(2,0),i.write_shift(1,r.length),n||i.write_shift(1,1),i.write_shift((n?1:2)*r.length,r,n?"sbcs":"utf16le"),i}function El(e,t,r,n){var a=B(10);return Jr(e,t,n,a),a.write_shift(4,r),a}function wl(e,t,r,n,a){var i=!a||a.biff==8,s=B(8+ +i+(1+i)*r.length);return Jr(e,t,n,s),s.write_shift(2,r.length),i&&s.write_shift(1,1),s.write_shift((1+i)*r.length,r,i?"utf16le":"sbcs"),s}function Sl(e,t,r,n){var a=r&&r.biff==5;n||(n=B(a?3+t.length:5+2*t.length)),n.write_shift(2,e),n.write_shift(a?1:2,t.length),a||n.write_shift(1,1),n.write_shift((a?1:2)*t.length,t,a?"sbcs":"utf16le");var i=n.length>n.l?n.slice(0,n.l):n;return i.l==null&&(i.l=i.length),i}function Al(e,t){var r=t.biff==8||!t.biff?4:2,n=B(2*r+6);return n.write_shift(r,e.s.r),n.write_shift(r,e.e.r+1),n.write_shift(2,e.s.c),n.write_shift(2,e.e.c+1),n.write_shift(2,0),n}function ta(e,t,r,n){var a=r&&r.biff==5;n||(n=B(a?16:20)),n.write_shift(2,0),e.style?(n.write_shift(2,e.numFmtId||0),n.write_shift(2,65524)):(n.write_shift(2,e.numFmtId||0),n.write_shift(2,t<<4));var i=0;return e.numFmtId>0&&a&&(i|=1024),n.write_shift(4,i),n.write_shift(4,0),a||n.write_shift(4,0),n.write_shift(2,0),n}function Fl(e){var t=B(8);return t.write_shift(4,0),t.write_shift(2,0),t.write_shift(2,0),t}function yl(e,t,r,n,a,i){var s=B(8);return Jr(e,t,n,s),li(r,i,s),s}function Cl(e,t,r,n){var a=B(14);return Jr(e,t,n,a),Yr(r,a),a}function Ol(e,t,r){if(r.biff<8)return Dl(e,t,r);for(var n=[],a=e.l+t,i=e.read_shift(r.biff>8?4:2);i--!==0;)n.push(xl(e,r.biff>8?12:6,r));if(e.l!=a)throw new Error("Bad ExternSheet: "+e.l+" != "+a);return n}function Dl(e,t,r){e[e.l+1]==3&&e[e.l]++;var n=oi(e,t,r);return n.charCodeAt(0)==3?n.slice(1):n}function Rl(e){var t=B(2+e.length*8);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)hi(e[r],t);return t}function Nl(e){var t=B(24),r=Ue(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var n="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),a=0;a<16;++a)t.write_shift(1,parseInt(n[a],16));return Ve([t,ul(e[1])])}function Il(e){var t=e[1].Tooltip,r=B(10+2*(t.length+1));r.write_shift(2,2048);var n=Ue(e[0]);r.write_shift(2,n.r),r.write_shift(2,n.r),r.write_shift(2,n.c),r.write_shift(2,n.c);for(var a=0;a<t.length;++a)r.write_shift(2,t.charCodeAt(a));return r.write_shift(2,0),r}function kl(e){return e||(e=B(4)),e.write_shift(2,1),e.write_shift(2,1),e}function Pl(e,t,r){if(!r.cellStyles)return wr(e,t);var n=r&&r.biff>=12?4:2,a=e.read_shift(n),i=e.read_shift(n),s=e.read_shift(n),f=e.read_shift(n),o=e.read_shift(2);n==2&&(e.l+=2);var l={s:a,e:i,w:s,ixfe:f,flags:o};return(r.biff>=5||!r.biff)&&(l.level=o>>8&7),l}function Ll(e,t){var r=B(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,e.width*256),r.write_shift(2,0);var n=0;return e.hidden&&(n|=1),r.write_shift(1,n),n=e.level||0,r.write_shift(1,n),r.write_shift(2,0),r}function Ml(e){for(var t=B(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}function Bl(e,t,r){var n=B(15);return Xt(n,e,t),n.write_shift(8,r,"f"),n}function Ul(e,t,r){var n=B(9);return Xt(n,e,t),n.write_shift(2,r),n}var bl=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=Kn({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(f,o){var l=[],c=Kr(1);switch(o.type){case"base64":c=vr(Rr(f));break;case"binary":c=vr(f);break;case"buffer":case"array":c=f;break}ar(c,0);var d=c.read_shift(1),h=!!(d&136),p=!1,T=!1;switch(d){case 2:break;case 3:break;case 48:p=!0,h=!0;break;case 49:p=!0,h=!0;break;case 131:break;case 139:break;case 140:T=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+d.toString(16))}var u=0,g=521;d==2&&(u=c.read_shift(2)),c.l+=3,d!=2&&(u=c.read_shift(4)),u>1048576&&(u=1e6),d!=2&&(g=c.read_shift(2));var C=c.read_shift(2),O=o.codepage||1252;d!=2&&(c.l+=16,c.read_shift(1),c[c.l]!==0&&(O=e[c[c.l]]),c.l+=1,c.l+=2),T&&(c.l+=36);for(var F=[],M={},Y=Math.min(c.length,d==2?521:g-10-(p?264:0)),ne=T?32:11;c.l<Y&&c[c.l]!=13;)switch(M={},M.name=y0.utils.decode(O,c.slice(c.l,c.l+ne)).replace(/[\u0000\r\n].*$/g,""),c.l+=ne,M.type=String.fromCharCode(c.read_shift(1)),d!=2&&!T&&(M.offset=c.read_shift(4)),M.len=c.read_shift(1),d==2&&(M.offset=c.read_shift(2)),M.dec=c.read_shift(1),M.name.length&&F.push(M),d!=2&&(c.l+=T?13:14),M.type){case"B":(!p||M.len!=8)&&o.WTF&&console.log("Skipping "+M.name+":"+M.type);break;case"G":case"P":o.WTF&&console.log("Skipping "+M.name+":"+M.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+M.type)}if(c[c.l]!==13&&(c.l=g-1),c.read_shift(1)!==13)throw new Error("DBF Terminator not found "+c.l+" "+c[c.l]);c.l=g;var D=0,b=0;for(l[0]=[],b=0;b!=F.length;++b)l[0][b]=F[b].name;for(;u-- >0;){if(c[c.l]===42){c.l+=C;continue}for(++c.l,l[++D]=[],b=0,b=0;b!=F.length;++b){var P=c.slice(c.l,c.l+F[b].len);c.l+=F[b].len,ar(P,0);var V=y0.utils.decode(O,P);switch(F[b].type){case"C":V.trim().length&&(l[D][b]=V.replace(/\s+$/,""));break;case"D":V.length===8?l[D][b]=new Date(+V.slice(0,4),+V.slice(4,6)-1,+V.slice(6,8)):l[D][b]=V;break;case"F":l[D][b]=parseFloat(V.trim());break;case"+":case"I":l[D][b]=T?P.read_shift(-4,"i")^2147483648:P.read_shift(4,"i");break;case"L":switch(V.trim().toUpperCase()){case"Y":case"T":l[D][b]=!0;break;case"N":case"F":l[D][b]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+V+"|")}break;case"M":if(!h)throw new Error("DBF Unexpected MEMO for type "+d.toString(16));l[D][b]="##MEMO##"+(T?parseInt(V.trim(),10):P.read_shift(4));break;case"N":V=V.replace(/\u0000/g,"").trim(),V&&V!="."&&(l[D][b]=+V||0);break;case"@":l[D][b]=new Date(P.read_shift(-8,"f")-621356832e5);break;case"T":l[D][b]=new Date((P.read_shift(4)-2440588)*864e5+P.read_shift(4));break;case"Y":l[D][b]=P.read_shift(4,"i")/1e4+P.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":l[D][b]=-P.read_shift(-8,"f");break;case"B":if(p&&F[b].len==8){l[D][b]=P.read_shift(8,"f");break}case"G":case"P":P.l+=F[b].len;break;case"0":if(F[b].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+F[b].type)}}}if(d!=2&&c.l<c.length&&c[c.l++]!=26)throw new Error("DBF EOF Marker missing "+(c.l-1)+" of "+c.length+" "+c[c.l-1].toString(16));return o&&o.sheetRows&&(l=l.slice(0,o.sheetRows)),o.DBF=F,l}function n(f,o){var l=o||{};l.dateNF||(l.dateNF="yyyymmdd");var c=vt(r(f,l),l);return c["!cols"]=l.DBF.map(function(d){return{wch:d.len,DBF:d}}),delete l.DBF,c}function a(f,o){try{return Zr(n(f,o),o)}catch(l){if(o&&o.WTF)throw l}return{SheetNames:[],Sheets:{}}}var i={B:8,C:250,L:1,D:8,"?":0,"":0};function s(f,o){var l=o||{};if(+l.codepage>=0&&It(+l.codepage),l.type=="string")throw new Error("Cannot write DBF to JS string");var c=Qe(),d=mn(f,{header:1,raw:!0,cellDates:!0}),h=d[0],p=d.slice(1),T=f["!cols"]||[],u=0,g=0,C=0,O=1;for(u=0;u<h.length;++u){if(((T[u]||{}).DBF||{}).name){h[u]=T[u].DBF.name,++C;continue}if(h[u]!=null){if(++C,typeof h[u]=="number"&&(h[u]=h[u].toString(10)),typeof h[u]!="string")throw new Error("DBF Invalid column name "+h[u]+" |"+typeof h[u]+"|");if(h.indexOf(h[u])!==u){for(g=0;g<1024;++g)if(h.indexOf(h[u]+"_"+g)==-1){h[u]+="_"+g;break}}}}var F=Fe(f["!ref"]),M=[],Y=[],ne=[];for(u=0;u<=F.e.c-F.s.c;++u){var D="",b="",P=0,V=[];for(g=0;g<p.length;++g)p[g][u]!=null&&V.push(p[g][u]);if(V.length==0||h[u]==null){M[u]="?";continue}for(g=0;g<V.length;++g){switch(typeof V[g]){case"number":b="B";break;case"string":b="C";break;case"boolean":b="L";break;case"object":b=V[g]instanceof Date?"D":"C";break;default:b="C"}P=Math.max(P,String(V[g]).length),D=D&&D!=b?"C":b}P>250&&(P=250),b=((T[u]||{}).DBF||{}).type,b=="C"&&T[u].DBF.len>P&&(P=T[u].DBF.len),D=="B"&&b=="N"&&(D="N",ne[u]=T[u].DBF.dec,P=T[u].DBF.len),Y[u]=D=="C"||b=="N"?P:i[D]||0,O+=Y[u],M[u]=D}var G=c.next(32);for(G.write_shift(4,318902576),G.write_shift(4,p.length),G.write_shift(2,296+32*C),G.write_shift(2,O),u=0;u<4;++u)G.write_shift(4,0);for(G.write_shift(4,0|(+t[xa]||3)<<8),u=0,g=0;u<h.length;++u)if(h[u]!=null){var j=c.next(32),re=(h[u].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);j.write_shift(1,re,"sbcs"),j.write_shift(1,M[u]=="?"?"C":M[u],"sbcs"),j.write_shift(4,g),j.write_shift(1,Y[u]||i[M[u]]||0),j.write_shift(1,ne[u]||0),j.write_shift(1,2),j.write_shift(4,0),j.write_shift(1,0),j.write_shift(4,0),j.write_shift(4,0),g+=Y[u]||i[M[u]]||0}var xe=c.next(264);for(xe.write_shift(4,13),u=0;u<65;++u)xe.write_shift(4,0);for(u=0;u<p.length;++u){var J=c.next(O);for(J.write_shift(1,0),g=0;g<h.length;++g)if(h[g]!=null)switch(M[g]){case"L":J.write_shift(1,p[u][g]==null?63:p[u][g]?84:70);break;case"B":J.write_shift(8,p[u][g]||0,"f");break;case"N":var Q="0";for(typeof p[u][g]=="number"&&(Q=p[u][g].toFixed(ne[g]||0)),C=0;C<Y[g]-Q.length;++C)J.write_shift(1,32);J.write_shift(1,Q,"sbcs");break;case"D":p[u][g]?(J.write_shift(4,("0000"+p[u][g].getFullYear()).slice(-4),"sbcs"),J.write_shift(2,("00"+(p[u][g].getMonth()+1)).slice(-2),"sbcs"),J.write_shift(2,("00"+p[u][g].getDate()).slice(-2),"sbcs")):J.write_shift(8,"00000000","sbcs");break;case"C":var ge=String(p[u][g]!=null?p[u][g]:"").slice(0,Y[g]);for(J.write_shift(1,ge,"sbcs"),C=0;C<Y[g]-ge.length;++C)J.write_shift(1,32);break}}return c.next(1).write_shift(1,26),c.end()}return{to_workbook:a,to_sheet:n,from_sheet:s}}(),Wl=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("\x1BN("+je(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(h,p){var T=e[p];return typeof T=="number"?F0(T):T},n=function(h,p,T){var u=p.charCodeAt(0)-32<<4|T.charCodeAt(0)-48;return u==59?h:F0(u)};e["|"]=254;function a(h,p){switch(p.type){case"base64":return i(Rr(h),p);case"binary":return i(h,p);case"buffer":return i(ve&&Buffer.isBuffer(h)?h.toString("binary"):bt(h),p);case"array":return i(wn(h),p)}throw new Error("Unrecognized type "+p.type)}function i(h,p){var T=h.split(/[\n\r]+/),u=-1,g=-1,C=0,O=0,F=[],M=[],Y=null,ne={},D=[],b=[],P=[],V=0,G;for(+p.codepage>=0&&It(+p.codepage);C!==T.length;++C){V=0;var j=T[C].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(t,r),re=j.replace(/;;/g,"\0").split(";").map(function(A){return A.replace(/\u0000/g,";")}),xe=re[0],J;if(j.length>0)switch(xe){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":re[1].charAt(0)=="P"&&M.push(j.slice(3).replace(/;;/g,";"));break;case"C":var Q=!1,ge=!1,ue=!1,Pe=!1,lr=-1,tr=-1;for(O=1;O<re.length;++O)switch(re[O].charAt(0)){case"A":break;case"X":g=parseInt(re[O].slice(1))-1,ge=!0;break;case"Y":for(u=parseInt(re[O].slice(1))-1,ge||(g=0),G=F.length;G<=u;++G)F[G]=[];break;case"K":J=re[O].slice(1),J.charAt(0)==='"'?J=J.slice(1,J.length-1):J==="TRUE"?J=!0:J==="FALSE"?J=!1:isNaN(Or(J))?isNaN(Pt(J).getDate())||(J=qe(J)):(J=Or(J),Y!==null&&Fa(Y)&&(J=Da(J))),Q=!0;break;case"E":Pe=!0;var S=bo(re[O].slice(1),{r:u,c:g});F[u][g]=[F[u][g],S];break;case"S":ue=!0,F[u][g]=[F[u][g],"S5S"];break;case"G":break;case"R":lr=parseInt(re[O].slice(1))-1;break;case"C":tr=parseInt(re[O].slice(1))-1;break;default:if(p&&p.WTF)throw new Error("SYLK bad record "+j)}if(Q&&(F[u][g]&&F[u][g].length==2?F[u][g][0]=J:F[u][g]=J,Y=null),ue){if(Pe)throw new Error("SYLK shared formula cannot have own formula");var L=lr>-1&&F[lr][tr];if(!L||!L[1])throw new Error("SYLK shared formula cannot find base");F[u][g][1]=Wo(L[1],{r:u-lr,c:g-tr})}break;case"F":var y=0;for(O=1;O<re.length;++O)switch(re[O].charAt(0)){case"X":g=parseInt(re[O].slice(1))-1,++y;break;case"Y":for(u=parseInt(re[O].slice(1))-1,G=F.length;G<=u;++G)F[G]=[];break;case"M":V=parseInt(re[O].slice(1))/20;break;case"F":break;case"G":break;case"P":Y=M[parseInt(re[O].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(P=re[O].slice(1).split(" "),G=parseInt(P[0],10);G<=parseInt(P[1],10);++G)V=parseInt(P[2],10),b[G-1]=V===0?{hidden:!0}:{wch:V},s0(b[G-1]);break;case"C":g=parseInt(re[O].slice(1))-1,b[g]||(b[g]={});break;case"R":u=parseInt(re[O].slice(1))-1,D[u]||(D[u]={}),V>0?(D[u].hpt=V,D[u].hpx=vi(V)):V===0&&(D[u].hidden=!0);break;default:if(p&&p.WTF)throw new Error("SYLK bad record "+j)}y<1&&(Y=null);break;default:if(p&&p.WTF)throw new Error("SYLK bad record "+j)}}return D.length>0&&(ne["!rows"]=D),b.length>0&&(ne["!cols"]=b),p&&p.sheetRows&&(F=F.slice(0,p.sheetRows)),[F,ne]}function s(h,p){var T=a(h,p),u=T[0],g=T[1],C=vt(u,p);return je(g).forEach(function(O){C[O]=g[O]}),C}function f(h,p){return Zr(s(h,p),p)}function o(h,p,T,u){var g="C;Y"+(T+1)+";X"+(u+1)+";K";switch(h.t){case"n":g+=h.v||0,h.f&&!h.F&&(g+=";E"+l0(h.f,{r:T,c:u}));break;case"b":g+=h.v?"TRUE":"FALSE";break;case"e":g+=h.w||h.v;break;case"d":g+='"'+(h.w||h.v)+'"';break;case"s":g+='"'+h.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return g}function l(h,p){p.forEach(function(T,u){var g="F;W"+(u+1)+" "+(u+1)+" ";T.hidden?g+="0":(typeof T.width=="number"&&!T.wpx&&(T.wpx=xn(T.width)),typeof T.wpx=="number"&&!T.wch&&(T.wch=dn(T.wpx)),typeof T.wch=="number"&&(g+=Math.round(T.wch))),g.charAt(g.length-1)!=" "&&h.push(g)})}function c(h,p){p.forEach(function(T,u){var g="F;";T.hidden?g+="M0;":T.hpt?g+="M"+20*T.hpt+";":T.hpx&&(g+="M"+20*pn(T.hpx)+";"),g.length>2&&h.push(g+"R"+(u+1))})}function d(h,p){var T=["ID;PWXL;N;E"],u=[],g=Fe(h["!ref"]),C,O=Array.isArray(h),F=`\r
`;T.push("P;PGeneral"),T.push("F;P0;DG0G8;M255"),h["!cols"]&&l(T,h["!cols"]),h["!rows"]&&c(T,h["!rows"]),T.push("B;Y"+(g.e.r-g.s.r+1)+";X"+(g.e.c-g.s.c+1)+";D"+[g.s.c,g.s.r,g.e.c,g.e.r].join(" "));for(var M=g.s.r;M<=g.e.r;++M)for(var Y=g.s.c;Y<=g.e.c;++Y){var ne=we({r:M,c:Y});C=O?(h[M]||[])[Y]:h[ne],!(!C||C.v==null&&(!C.f||C.F))&&u.push(o(C,h,M,Y))}return T.join(F)+F+u.join(F)+F+"E"+F}return{to_workbook:f,to_sheet:s,from_sheet:d}}(),Hl=function(){function e(i,s){switch(s.type){case"base64":return t(Rr(i),s);case"binary":return t(i,s);case"buffer":return t(ve&&Buffer.isBuffer(i)?i.toString("binary"):bt(i),s);case"array":return t(wn(i),s)}throw new Error("Unrecognized type "+s.type)}function t(i,s){for(var f=i.split(`
`),o=-1,l=-1,c=0,d=[];c!==f.length;++c){if(f[c].trim()==="BOT"){d[++o]=[],l=0;continue}if(!(o<0)){var h=f[c].trim().split(","),p=h[0],T=h[1];++c;for(var u=f[c]||"";(u.match(/["]/g)||[]).length&1&&c<f.length-1;)u+=`
`+f[++c];switch(u=u.trim(),+p){case-1:if(u==="BOT"){d[++o]=[],l=0;continue}else if(u!=="EOD")throw new Error("Unrecognized DIF special command "+u);break;case 0:u==="TRUE"?d[o][l]=!0:u==="FALSE"?d[o][l]=!1:isNaN(Or(T))?isNaN(Pt(T).getDate())?d[o][l]=T:d[o][l]=qe(T):d[o][l]=Or(T),++l;break;case 1:u=u.slice(1,u.length-1),u=u.replace(/""/g,'"'),u&&u.match(/^=".*"$/)&&(u=u.slice(2,-1)),d[o][l++]=u!==""?u:null;break}if(u==="EOD")break}}return s&&s.sheetRows&&(d=d.slice(0,s.sheetRows)),d}function r(i,s){return vt(e(i,s),s)}function n(i,s){return Zr(r(i,s),s)}var a=function(){var i=function(o,l,c,d,h){o.push(l),o.push(c+","+d),o.push('"'+h.replace(/"/g,'""')+'"')},s=function(o,l,c,d){o.push(l+","+c),o.push(l==1?'"'+d.replace(/"/g,'""')+'"':d)};return function(o){var l=[],c=Fe(o["!ref"]),d,h=Array.isArray(o);i(l,"TABLE",0,1,"sheetjs"),i(l,"VECTORS",0,c.e.r-c.s.r+1,""),i(l,"TUPLES",0,c.e.c-c.s.c+1,""),i(l,"DATA",0,0,"");for(var p=c.s.r;p<=c.e.r;++p){s(l,-1,0,"BOT");for(var T=c.s.c;T<=c.e.c;++T){var u=we({r:p,c:T});if(d=h?(o[p]||[])[T]:o[u],!d){s(l,1,0,"");continue}switch(d.t){case"n":var g=d.w;!g&&d.v!=null&&(g=d.v),g==null?d.f&&!d.F?s(l,1,0,"="+d.f):s(l,1,0,""):s(l,0,g,"V");break;case"b":s(l,0,d.v?1:0,d.v?"TRUE":"FALSE");break;case"s":s(l,1,0,isNaN(d.v)?d.v:'="'+d.v+'"');break;case"d":d.w||(d.w=Ur(d.z||Re[14],er(qe(d.v)))),s(l,0,d.w,"V");break;default:s(l,1,0,"")}}}s(l,-1,0,"EOD");var C=`\r
`,O=l.join(C);return O}}();return{to_workbook:n,to_sheet:r,from_sheet:a}}(),ui=function(){function e(d){return d.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,`
`)}function t(d){return d.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(d,h){for(var p=d.split(`
`),T=-1,u=-1,g=0,C=[];g!==p.length;++g){var O=p[g].trim().split(":");if(O[0]==="cell"){var F=Ue(O[1]);if(C.length<=F.r)for(T=C.length;T<=F.r;++T)C[T]||(C[T]=[]);switch(T=F.r,u=F.c,O[2]){case"t":C[T][u]=e(O[3]);break;case"v":C[T][u]=+O[3];break;case"vtf":var M=O[O.length-1];case"vtc":switch(O[3]){case"nl":C[T][u]=!!+O[4];break;default:C[T][u]=+O[4];break}O[2]=="vtf"&&(C[T][u]=[C[T][u],M])}}}return h&&h.sheetRows&&(C=C.slice(0,h.sheetRows)),C}function n(d,h){return vt(r(d,h),h)}function a(d,h){return Zr(n(d,h),h)}var i=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join(`
`),s=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join(`
`)+`
`,f=["# SocialCalc Spreadsheet Control Save","part:sheet"].join(`
`),o="--SocialCalcSpreadsheetControlSave--";function l(d){if(!d||!d["!ref"])return"";for(var h=[],p=[],T,u="",g=fr(d["!ref"]),C=Array.isArray(d),O=g.s.r;O<=g.e.r;++O)for(var F=g.s.c;F<=g.e.c;++F)if(u=we({r:O,c:F}),T=C?(d[O]||[])[F]:d[u],!(!T||T.v==null||T.t==="z")){switch(p=["cell",u,"t"],T.t){case"s":case"str":p.push(t(T.v));break;case"n":T.f?(p[2]="vtf",p[3]="n",p[4]=T.v,p[5]=t(T.f)):(p[2]="v",p[3]=T.v);break;case"b":p[2]="vt"+(T.f?"f":"c"),p[3]="nl",p[4]=T.v?"1":"0",p[5]=t(T.f||(T.v?"TRUE":"FALSE"));break;case"d":var M=er(qe(T.v));p[2]="vtc",p[3]="nd",p[4]=""+M,p[5]=T.w||Ur(T.z||Re[14],M);break;case"e":continue}h.push(p.join(":"))}return h.push("sheet:c:"+(g.e.c-g.s.c+1)+":r:"+(g.e.r-g.s.r+1)+":tvf:1"),h.push("valueformat:1:text-wiki"),h.join(`
`)}function c(d){return[i,s,f,s,l(d),o].join(`
`)}return{to_workbook:a,to_sheet:n,from_sheet:c}}(),Vl=function(){function e(c,d,h,p,T){T.raw?d[h][p]=c:c===""||(c==="TRUE"?d[h][p]=!0:c==="FALSE"?d[h][p]=!1:isNaN(Or(c))?isNaN(Pt(c).getDate())?d[h][p]=c:d[h][p]=qe(c):d[h][p]=Or(c))}function t(c,d){var h=d||{},p=[];if(!c||c.length===0)return p;for(var T=c.split(/[\r\n]/),u=T.length-1;u>=0&&T[u].length===0;)--u;for(var g=10,C=0,O=0;O<=u;++O)C=T[O].indexOf(" "),C==-1?C=T[O].length:C++,g=Math.max(g,C);for(O=0;O<=u;++O){p[O]=[];var F=0;for(e(T[O].slice(0,g).trim(),p,O,F,h),F=1;F<=(T[O].length-g)/10+1;++F)e(T[O].slice(g+(F-1)*10,g+F*10).trim(),p,O,F,h)}return h.sheetRows&&(p=p.slice(0,h.sheetRows)),p}var r={44:",",9:"	",59:";",124:"|"},n={44:3,9:2,59:1,124:0};function a(c){for(var d={},h=!1,p=0,T=0;p<c.length;++p)(T=c.charCodeAt(p))==34?h=!h:!h&&T in r&&(d[T]=(d[T]||0)+1);T=[];for(p in d)Object.prototype.hasOwnProperty.call(d,p)&&T.push([d[p],p]);if(!T.length){d=n;for(p in d)Object.prototype.hasOwnProperty.call(d,p)&&T.push([d[p],p])}return T.sort(function(u,g){return u[0]-g[0]||n[u[1]]-n[g[1]]}),r[T.pop()[1]]||44}function i(c,d){var h=d||{},p="",T=h.dense?[]:{},u={s:{c:0,r:0},e:{c:0,r:0}};c.slice(0,4)=="sep="?c.charCodeAt(5)==13&&c.charCodeAt(6)==10?(p=c.charAt(4),c=c.slice(7)):c.charCodeAt(5)==13||c.charCodeAt(5)==10?(p=c.charAt(4),c=c.slice(6)):p=a(c.slice(0,1024)):h&&h.FS?p=h.FS:p=a(c.slice(0,1024));var g=0,C=0,O=0,F=0,M=0,Y=p.charCodeAt(0),ne=!1,D=0,b=c.charCodeAt(0);c=c.replace(/\r\n/mg,`
`);var P=h.dateNF!=null?nf(h.dateNF):null;function V(){var G=c.slice(F,M),j={};if(G.charAt(0)=='"'&&G.charAt(G.length-1)=='"'&&(G=G.slice(1,-1).replace(/""/g,'"')),G.length===0)j.t="z";else if(h.raw)j.t="s",j.v=G;else if(G.trim().length===0)j.t="s",j.v=G;else if(G.charCodeAt(0)==61)G.charCodeAt(1)==34&&G.charCodeAt(G.length-1)==34?(j.t="s",j.v=G.slice(2,-1).replace(/""/g,'"')):Ho(G)?(j.t="n",j.f=G.slice(1)):(j.t="s",j.v=G);else if(G=="TRUE")j.t="b",j.v=!0;else if(G=="FALSE")j.t="b",j.v=!1;else if(!isNaN(O=Or(G)))j.t="n",h.cellText!==!1&&(j.w=G),j.v=O;else if(!isNaN(Pt(G).getDate())||P&&G.match(P)){j.z=h.dateNF||Re[14];var re=0;P&&G.match(P)&&(G=af(G,h.dateNF,G.match(P)||[]),re=1),h.cellDates?(j.t="d",j.v=qe(G,re)):(j.t="n",j.v=er(qe(G,re))),h.cellText!==!1&&(j.w=Ur(j.z,j.v instanceof Date?er(j.v):j.v)),h.cellNF||delete j.z}else j.t="s",j.v=G;if(j.t=="z"||(h.dense?(T[g]||(T[g]=[]),T[g][C]=j):T[we({c:C,r:g})]=j),F=M+1,b=c.charCodeAt(F),u.e.c<C&&(u.e.c=C),u.e.r<g&&(u.e.r=g),D==Y)++C;else if(C=0,++g,h.sheetRows&&h.sheetRows<=g)return!0}e:for(;M<c.length;++M)switch(D=c.charCodeAt(M)){case 34:b===34&&(ne=!ne);break;case Y:case 10:case 13:if(!ne&&V())break e;break}return M-F>0&&V(),T["!ref"]=Ie(u),T}function s(c,d){return!(d&&d.PRN)||d.FS||c.slice(0,4)=="sep="||c.indexOf("	")>=0||c.indexOf(",")>=0||c.indexOf(";")>=0?i(c,d):vt(t(c,d),d)}function f(c,d){var h="",p=d.type=="string"?[0,0,0,0]:rx(c,d);switch(d.type){case"base64":h=Rr(c);break;case"binary":h=c;break;case"buffer":d.codepage==65001?h=c.toString("utf8"):(d.codepage,h=ve&&Buffer.isBuffer(c)?c.toString("binary"):bt(c));break;case"array":h=wn(c);break;case"string":h=c;break;default:throw new Error("Unrecognized type "+d.type)}return p[0]==239&&p[1]==187&&p[2]==191?h=Ct(h.slice(3)):d.type!="string"&&d.type!="buffer"&&d.codepage==65001?h=Ct(h):d.type=="binary",h.slice(0,19)=="socialcalc:version:"?ui.to_sheet(d.type=="string"?h:Ct(h),d):s(h,d)}function o(c,d){return Zr(f(c,d),d)}function l(c){for(var d=[],h=Fe(c["!ref"]),p,T=Array.isArray(c),u=h.s.r;u<=h.e.r;++u){for(var g=[],C=h.s.c;C<=h.e.c;++C){var O=we({r:u,c:C});if(p=T?(c[u]||[])[C]:c[O],!p||p.v==null){g.push("          ");continue}for(var F=(p.w||(Nr(p),p.w)||"").slice(0,10);F.length<10;)F+=" ";g.push(F+(C===0?" ":""))}d.push(g.join(""))}return d.join(`
`)}return{to_workbook:o,to_sheet:f,from_sheet:l}}(),na=function(){function e(S,L,y){if(S){ar(S,S.l||0);for(var A=y.Enum||lr;S.l<S.length;){var H=S.read_shift(2),le=A[H]||A[65535],oe=S.read_shift(2),fe=S.l+oe,te=le.f&&le.f(S,oe,y);if(S.l=fe,L(te,le,H))return}}}function t(S,L){switch(L.type){case"base64":return r(vr(Rr(S)),L);case"binary":return r(vr(S),L);case"buffer":case"array":return r(S,L)}throw"Unsupported type "+L.type}function r(S,L){if(!S)return S;var y=L||{},A=y.dense?[]:{},H="Sheet1",le="",oe=0,fe={},te=[],Ae=[],de={s:{r:0,c:0},e:{r:0,c:0}},Ye=y.sheetRows||0;if(S[2]==0&&(S[3]==8||S[3]==9)&&S.length>=16&&S[14]==5&&S[15]===108)throw new Error("Unsupported Works 3 for Mac file");if(S[2]==2)y.Enum=lr,e(S,function(se,or,Ar){switch(Ar){case 0:y.vers=se,se>=4096&&(y.qpro=!0);break;case 6:de=se;break;case 204:se&&(le=se);break;case 222:le=se;break;case 15:case 51:y.qpro||(se[1].v=se[1].v.slice(1));case 13:case 14:case 16:Ar==14&&(se[2]&112)==112&&(se[2]&15)>1&&(se[2]&15)<15&&(se[1].z=y.dateNF||Re[14],y.cellDates&&(se[1].t="d",se[1].v=Da(se[1].v))),y.qpro&&se[3]>oe&&(A["!ref"]=Ie(de),fe[H]=A,te.push(H),A=y.dense?[]:{},de={s:{r:0,c:0},e:{r:0,c:0}},oe=se[3],H=le||"Sheet"+(oe+1),le="");var Vr=y.dense?(A[se[0].r]||[])[se[0].c]:A[we(se[0])];if(Vr){Vr.t=se[1].t,Vr.v=se[1].v,se[1].z!=null&&(Vr.z=se[1].z),se[1].f!=null&&(Vr.f=se[1].f);break}y.dense?(A[se[0].r]||(A[se[0].r]=[]),A[se[0].r][se[0].c]=se[1]):A[we(se[0])]=se[1];break}},y);else if(S[2]==26||S[2]==14)y.Enum=tr,S[2]==14&&(y.qpro=!0,S.l=0),e(S,function(se,or,Ar){switch(Ar){case 204:H=se;break;case 22:se[1].v=se[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(se[3]>oe&&(A["!ref"]=Ie(de),fe[H]=A,te.push(H),A=y.dense?[]:{},de={s:{r:0,c:0},e:{r:0,c:0}},oe=se[3],H="Sheet"+(oe+1)),Ye>0&&se[0].r>=Ye)break;y.dense?(A[se[0].r]||(A[se[0].r]=[]),A[se[0].r][se[0].c]=se[1]):A[we(se[0])]=se[1],de.e.c<se[0].c&&(de.e.c=se[0].c),de.e.r<se[0].r&&(de.e.r=se[0].r);break;case 27:se[14e3]&&(Ae[se[14e3][0]]=se[14e3][1]);break;case 1537:Ae[se[0]]=se[1],se[0]==oe&&(H=se[1]);break}},y);else throw new Error("Unrecognized LOTUS BOF "+S[2]);if(A["!ref"]=Ie(de),fe[le||H]=A,te.push(le||H),!Ae.length)return{SheetNames:te,Sheets:fe};for(var me={},Sr=[],Oe=0;Oe<Ae.length;++Oe)fe[te[Oe]]?(Sr.push(Ae[Oe]||te[Oe]),me[Ae[Oe]]=fe[Ae[Oe]]||fe[te[Oe]]):(Sr.push(Ae[Oe]),me[Ae[Oe]]={"!ref":"A1"});return{SheetNames:Sr,Sheets:me}}function n(S,L){var y=L||{};if(+y.codepage>=0&&It(+y.codepage),y.type=="string")throw new Error("Cannot write WK1 to JS string");var A=Qe(),H=Fe(S["!ref"]),le=Array.isArray(S),oe=[];q(A,0,i(1030)),q(A,6,o(H));for(var fe=Math.min(H.e.r,8191),te=H.s.r;te<=fe;++te)for(var Ae=Xe(te),de=H.s.c;de<=H.e.c;++de){te===H.s.r&&(oe[de]=ze(de));var Ye=oe[de]+Ae,me=le?(S[te]||[])[de]:S[Ye];if(!(!me||me.t=="z"))if(me.t=="n")(me.v|0)==me.v&&me.v>=-32768&&me.v<=32767?q(A,13,p(te,de,me.v)):q(A,14,u(te,de,me.v));else{var Sr=Nr(me);q(A,15,d(te,de,Sr.slice(0,239)))}}return q(A,1),A.end()}function a(S,L){var y=L||{};if(+y.codepage>=0&&It(+y.codepage),y.type=="string")throw new Error("Cannot write WK3 to JS string");var A=Qe();q(A,0,s(S));for(var H=0,le=0;H<S.SheetNames.length;++H)(S.Sheets[S.SheetNames[H]]||{})["!ref"]&&q(A,27,Pe(S.SheetNames[H],le++));var oe=0;for(H=0;H<S.SheetNames.length;++H){var fe=S.Sheets[S.SheetNames[H]];if(!(!fe||!fe["!ref"])){for(var te=Fe(fe["!ref"]),Ae=Array.isArray(fe),de=[],Ye=Math.min(te.e.r,8191),me=te.s.r;me<=Ye;++me)for(var Sr=Xe(me),Oe=te.s.c;Oe<=te.e.c;++Oe){me===te.s.r&&(de[Oe]=ze(Oe));var se=de[Oe]+Sr,or=Ae?(fe[me]||[])[Oe]:fe[se];if(!(!or||or.t=="z"))if(or.t=="n")q(A,23,V(me,Oe,oe,or.v));else{var Ar=Nr(or);q(A,22,D(me,Oe,oe,Ar.slice(0,239)))}}++oe}}return q(A,1),A.end()}function i(S){var L=B(2);return L.write_shift(2,S),L}function s(S){var L=B(26);L.write_shift(2,4096),L.write_shift(2,4),L.write_shift(4,0);for(var y=0,A=0,H=0,le=0;le<S.SheetNames.length;++le){var oe=S.SheetNames[le],fe=S.Sheets[oe];if(!(!fe||!fe["!ref"])){++H;var te=fr(fe["!ref"]);y<te.e.r&&(y=te.e.r),A<te.e.c&&(A=te.e.c)}}return y>8191&&(y=8191),L.write_shift(2,y),L.write_shift(1,H),L.write_shift(1,A),L.write_shift(2,0),L.write_shift(2,0),L.write_shift(1,1),L.write_shift(1,2),L.write_shift(4,0),L.write_shift(4,0),L}function f(S,L,y){var A={s:{c:0,r:0},e:{c:0,r:0}};return L==8&&y.qpro?(A.s.c=S.read_shift(1),S.l++,A.s.r=S.read_shift(2),A.e.c=S.read_shift(1),S.l++,A.e.r=S.read_shift(2),A):(A.s.c=S.read_shift(2),A.s.r=S.read_shift(2),L==12&&y.qpro&&(S.l+=2),A.e.c=S.read_shift(2),A.e.r=S.read_shift(2),L==12&&y.qpro&&(S.l+=2),A.s.c==65535&&(A.s.c=A.e.c=A.s.r=A.e.r=0),A)}function o(S){var L=B(8);return L.write_shift(2,S.s.c),L.write_shift(2,S.s.r),L.write_shift(2,S.e.c),L.write_shift(2,S.e.r),L}function l(S,L,y){var A=[{c:0,r:0},{t:"n",v:0},0,0];return y.qpro&&y.vers!=20768?(A[0].c=S.read_shift(1),A[3]=S.read_shift(1),A[0].r=S.read_shift(2),S.l+=2):(A[2]=S.read_shift(1),A[0].c=S.read_shift(2),A[0].r=S.read_shift(2)),A}function c(S,L,y){var A=S.l+L,H=l(S,L,y);if(H[1].t="s",y.vers==20768){S.l++;var le=S.read_shift(1);return H[1].v=S.read_shift(le,"utf8"),H}return y.qpro&&S.l++,H[1].v=S.read_shift(A-S.l,"cstr"),H}function d(S,L,y){var A=B(7+y.length);A.write_shift(1,255),A.write_shift(2,L),A.write_shift(2,S),A.write_shift(1,39);for(var H=0;H<A.length;++H){var le=y.charCodeAt(H);A.write_shift(1,le>=128?95:le)}return A.write_shift(1,0),A}function h(S,L,y){var A=l(S,L,y);return A[1].v=S.read_shift(2,"i"),A}function p(S,L,y){var A=B(7);return A.write_shift(1,255),A.write_shift(2,L),A.write_shift(2,S),A.write_shift(2,y,"i"),A}function T(S,L,y){var A=l(S,L,y);return A[1].v=S.read_shift(8,"f"),A}function u(S,L,y){var A=B(13);return A.write_shift(1,255),A.write_shift(2,L),A.write_shift(2,S),A.write_shift(8,y,"f"),A}function g(S,L,y){var A=S.l+L,H=l(S,L,y);if(H[1].v=S.read_shift(8,"f"),y.qpro)S.l=A;else{var le=S.read_shift(2);M(S.slice(S.l,S.l+le),H),S.l+=le}return H}function C(S,L,y){var A=L&32768;return L&=-32769,L=(A?S:0)+(L>=8192?L-16384:L),(A?"":"$")+(y?ze(L):Xe(L))}var O={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},F=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function M(S,L){ar(S,0);for(var y=[],A=0,H="",le="",oe="",fe="";S.l<S.length;){var te=S[S.l++];switch(te){case 0:y.push(S.read_shift(8,"f"));break;case 1:le=C(L[0].c,S.read_shift(2),!0),H=C(L[0].r,S.read_shift(2),!1),y.push(le+H);break;case 2:{var Ae=C(L[0].c,S.read_shift(2),!0),de=C(L[0].r,S.read_shift(2),!1);le=C(L[0].c,S.read_shift(2),!0),H=C(L[0].r,S.read_shift(2),!1),y.push(Ae+de+":"+le+H)}break;case 3:if(S.l<S.length){console.error("WK1 premature formula end");return}break;case 4:y.push("("+y.pop()+")");break;case 5:y.push(S.read_shift(2));break;case 6:{for(var Ye="";te=S[S.l++];)Ye+=String.fromCharCode(te);y.push('"'+Ye.replace(/"/g,'""')+'"')}break;case 8:y.push("-"+y.pop());break;case 23:y.push("+"+y.pop());break;case 22:y.push("NOT("+y.pop()+")");break;case 20:case 21:fe=y.pop(),oe=y.pop(),y.push(["AND","OR"][te-20]+"("+oe+","+fe+")");break;default:if(te<32&&F[te])fe=y.pop(),oe=y.pop(),y.push(oe+F[te]+fe);else if(O[te]){if(A=O[te][1],A==69&&(A=S[S.l++]),A>y.length){console.error("WK1 bad formula parse 0x"+te.toString(16)+":|"+y.join("|")+"|");return}var me=y.slice(-A);y.length-=A,y.push(O[te][0]+"("+me.join(",")+")")}else return te<=7?console.error("WK1 invalid opcode "+te.toString(16)):te<=24?console.error("WK1 unsupported op "+te.toString(16)):te<=30?console.error("WK1 invalid opcode "+te.toString(16)):te<=115?console.error("WK1 unsupported function opcode "+te.toString(16)):console.error("WK1 unrecognized opcode "+te.toString(16))}}y.length==1?L[1].f=""+y[0]:console.error("WK1 bad formula parse |"+y.join("|")+"|")}function Y(S){var L=[{c:0,r:0},{t:"n",v:0},0];return L[0].r=S.read_shift(2),L[3]=S[S.l++],L[0].c=S[S.l++],L}function ne(S,L){var y=Y(S);return y[1].t="s",y[1].v=S.read_shift(L-4,"cstr"),y}function D(S,L,y,A){var H=B(6+A.length);H.write_shift(2,S),H.write_shift(1,y),H.write_shift(1,L),H.write_shift(1,39);for(var le=0;le<A.length;++le){var oe=A.charCodeAt(le);H.write_shift(1,oe>=128?95:oe)}return H.write_shift(1,0),H}function b(S,L){var y=Y(S);y[1].v=S.read_shift(2);var A=y[1].v>>1;if(y[1].v&1)switch(A&7){case 0:A=(A>>3)*5e3;break;case 1:A=(A>>3)*500;break;case 2:A=(A>>3)/20;break;case 3:A=(A>>3)/200;break;case 4:A=(A>>3)/2e3;break;case 5:A=(A>>3)/2e4;break;case 6:A=(A>>3)/16;break;case 7:A=(A>>3)/64;break}return y[1].v=A,y}function P(S,L){var y=Y(S),A=S.read_shift(4),H=S.read_shift(4),le=S.read_shift(2);if(le==65535)return A===0&&H===3221225472?(y[1].t="e",y[1].v=15):A===0&&H===3489660928?(y[1].t="e",y[1].v=42):y[1].v=0,y;var oe=le&32768;return le=(le&32767)-16446,y[1].v=(1-oe*2)*(H*Math.pow(2,le+32)+A*Math.pow(2,le)),y}function V(S,L,y,A){var H=B(14);if(H.write_shift(2,S),H.write_shift(1,y),H.write_shift(1,L),A==0)return H.write_shift(4,0),H.write_shift(4,0),H.write_shift(2,65535),H;var le=0,oe=0,fe=0,te=0;return A<0&&(le=1,A=-A),oe=Math.log2(A)|0,A/=Math.pow(2,oe-31),te=A>>>0,(te&2147483648)==0&&(A/=2,++oe,te=A>>>0),A-=te,te|=2147483648,te>>>=0,A*=Math.pow(2,32),fe=A>>>0,H.write_shift(4,fe),H.write_shift(4,te),oe+=16383+(le?32768:0),H.write_shift(2,oe),H}function G(S,L){var y=P(S);return S.l+=L-14,y}function j(S,L){var y=Y(S),A=S.read_shift(4);return y[1].v=A>>6,y}function re(S,L){var y=Y(S),A=S.read_shift(8,"f");return y[1].v=A,y}function xe(S,L){var y=re(S);return S.l+=L-10,y}function J(S,L){return S[S.l+L-1]==0?S.read_shift(L,"cstr"):""}function Q(S,L){var y=S[S.l++];y>L-1&&(y=L-1);for(var A="";A.length<y;)A+=String.fromCharCode(S[S.l++]);return A}function ge(S,L,y){if(!(!y.qpro||L<21)){var A=S.read_shift(1);S.l+=17,S.l+=1,S.l+=2;var H=S.read_shift(L-21,"cstr");return[A,H]}}function ue(S,L){for(var y={},A=S.l+L;S.l<A;){var H=S.read_shift(2);if(H==14e3){for(y[H]=[0,""],y[H][0]=S.read_shift(2);S[S.l];)y[H][1]+=String.fromCharCode(S[S.l]),S.l++;S.l++}}return y}function Pe(S,L){var y=B(5+S.length);y.write_shift(2,14e3),y.write_shift(2,L);for(var A=0;A<S.length;++A){var H=S.charCodeAt(A);y[y.l++]=H>127?95:H}return y[y.l++]=0,y}var lr={0:{n:"BOF",f:fi},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:h},14:{n:"NUMBER",f:T},15:{n:"LABEL",f:c},16:{n:"FORMULA",f:g},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:c},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:J},222:{n:"SHEETNAMELP",f:Q},65535:{n:""}},tr={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:ne},23:{n:"NUMBER17",f:P},24:{n:"NUMBER18",f:b},25:{n:"FORMULA19",f:G},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:ue},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:j},38:{n:"??"},39:{n:"NUMBER27",f:re},40:{n:"FORMULA28",f:xe},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:J},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:ge},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:n,book_to_wk3:a,to_workbook:t}}(),Gl=/^\s|\s$|[\t\n\r]/;function xi(e,t){if(!t.bookSST)return"";var r=[ke];r[r.length]=Z("sst",null,{xmlns:pt[0],count:e.Count,uniqueCount:e.Unique});for(var n=0;n!=e.length;++n)if(e[n]!=null){var a=e[n],i="<si>";a.r?i+=a.r:(i+="<t",a.t||(a.t=""),a.t.match(Gl)&&(i+=' xml:space="preserve"'),i+=">"+Ee(a.t)+"</t>"),i+="</si>",r[r.length]=i}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}function Xl(e){return[e.read_shift(4),e.read_shift(4)]}function jl(e,t){return t||(t=B(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}var $l=Mf;function zl(e){var t=Qe();W(t,159,jl(e));for(var r=0;r<e.length;++r)W(t,19,$l(e[r]));return W(t,160),t.end()}function Kl(e){for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}function di(e){var t=0,r,n=Kl(e),a=n.length+1,i,s,f,o,l;for(r=Kr(a),r[0]=n.length,i=1;i!=a;++i)r[i]=n[i-1];for(i=a-1;i>=0;--i)s=r[i],f=(t&16384)===0?0:1,o=t<<1&32767,l=f|o,t=l^s;return t^52811}var Yl=function(){function e(a,i){switch(i.type){case"base64":return t(Rr(a),i);case"binary":return t(a,i);case"buffer":return t(ve&&Buffer.isBuffer(a)?a.toString("binary"):bt(a),i);case"array":return t(wn(a),i)}throw new Error("Unrecognized type "+i.type)}function t(a,i){var s=i||{},f=s.dense?[]:{},o=a.match(/\\trowd.*?\\row\b/g);if(!o.length)throw new Error("RTF missing table");var l={s:{c:0,r:0},e:{c:0,r:o.length-1}};return o.forEach(function(c,d){Array.isArray(f)&&(f[d]=[]);for(var h=/\\\w+\b/g,p=0,T,u=-1;T=h.exec(c);){switch(T[0]){case"\\cell":var g=c.slice(p,h.lastIndex-T[0].length);if(g[0]==" "&&(g=g.slice(1)),++u,g.length){var C={v:g,t:"s"};Array.isArray(f)?f[d][u]=C:f[we({r:d,c:u})]=C}break}p=h.lastIndex}u>l.e.c&&(l.e.c=u)}),f["!ref"]=Ie(l),f}function r(a,i){return Zr(e(a,i),i)}function n(a){for(var i=["{\\rtf1\\ansi"],s=Fe(a["!ref"]),f,o=Array.isArray(a),l=s.s.r;l<=s.e.r;++l){i.push("\\trowd\\trautofit1");for(var c=s.s.c;c<=s.e.c;++c)i.push("\\cellx"+(c+1));for(i.push("\\pard\\intbl"),c=s.s.c;c<=s.e.c;++c){var d=we({r:l,c});f=o?(a[l]||[])[c]:a[d],!(!f||f.v==null&&(!f.f||f.F))&&(i.push(" "+(f.w||(Nr(f),f.w))),i.push("\\cell"))}i.push("\\pard\\intbl\\row")}return i.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:n}}();function aa(e){for(var t=0,r=1;t!=3;++t)r=r*256+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}var Jl=6,Dr=Jl;function xn(e){return Math.floor((e+Math.round(128/Dr)/256)*Dr)}function dn(e){return Math.floor((e-5)/Dr*100+.5)/100}function jn(e){return Math.round((e*Dr+5)/Dr*256)/256}function s0(e){e.width?(e.wpx=xn(e.width),e.wch=dn(e.wpx),e.MDW=Dr):e.wpx?(e.wch=dn(e.wpx),e.width=jn(e.wch),e.MDW=Dr):typeof e.wch=="number"&&(e.width=jn(e.wch),e.wpx=xn(e.width),e.MDW=Dr),e.customWidth&&delete e.customWidth}var Zl=96,pi=Zl;function pn(e){return e*96/pi}function vi(e){return e*pi/96}function ql(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach(function(r){for(var n=r[0];n<=r[1];++n)e[n]!=null&&(t[t.length]=Z("numFmt",null,{numFmtId:n,formatCode:Ee(e[n])}))}),t.length===1?"":(t[t.length]="</numFmts>",t[0]=Z("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function Ql(e){var t=[];return t[t.length]=Z("cellXfs",null),e.forEach(function(r){t[t.length]=Z("xf",null,r)}),t[t.length]="</cellXfs>",t.length===2?"":(t[0]=Z("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function mi(e,t){var r=[ke,Z("styleSheet",null,{xmlns:pt[0],"xmlns:vt":Be.vt})],n;return e.SSF&&(n=ql(e.SSF))!=null&&(r[r.length]=n),r[r.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',r[r.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',r[r.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',r[r.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(n=Ql(t.cellXfs))&&(r[r.length]=n),r[r.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',r[r.length]='<dxfs count="0"/>',r[r.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',r.length>2&&(r[r.length]="</styleSheet>",r[1]=r[1].replace("/>",">")),r.join("")}function eo(e,t){var r=e.read_shift(2),n=Ke(e);return[r,n]}function ro(e,t,r){r||(r=B(6+4*t.length)),r.write_shift(2,e),be(t,r);var n=r.length>r.l?r.slice(0,r.l):r;return r.l==null&&(r.l=r.length),n}function to(e,t,r){var n={};n.sz=e.read_shift(2)/20;var a=Gf(e);a.fItalic&&(n.italic=1),a.fCondense&&(n.condense=1),a.fExtend&&(n.extend=1),a.fShadow&&(n.shadow=1),a.fOutline&&(n.outline=1),a.fStrikeout&&(n.strike=1);var i=e.read_shift(2);switch(i===700&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript";break}var s=e.read_shift(1);s!=0&&(n.underline=s);var f=e.read_shift(1);f>0&&(n.family=f);var o=e.read_shift(1);switch(o>0&&(n.charset=o),e.l++,n.color=Vf(e),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor";break}return n.name=Ke(e),n}function no(e,t){t||(t=B(25+4*32)),t.write_shift(2,e.sz*20),Xf(e,t),t.write_shift(2,e.bold?700:400);var r=0;e.vertAlign=="superscript"?r=1:e.vertAlign=="subscript"&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),hn(e.color,t);var n=0;return n=2,t.write_shift(1,n),be(e.name,t),t.length>t.l?t.slice(0,t.l):t}var ao=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],Un,io=wr;function ia(e,t){t||(t=B(4*3+8*7+16*1)),Un||(Un=Kn(ao));var r=Un[e.patternType];r==null&&(r=40),t.write_shift(4,r);var n=0;if(r!=40)for(hn({auto:1},t),hn({auto:1},t);n<12;++n)t.write_shift(4,0);else{for(;n<4;++n)t.write_shift(4,0);for(;n<12;++n)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function so(e,t){var r=e.l+t,n=e.read_shift(2),a=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:a}}function gi(e,t,r){r||(r=B(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);var n=0;return r.write_shift(1,n),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function At(e,t){return t||(t=B(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var fo=wr;function lo(e,t){return t||(t=B(51)),t.write_shift(1,0),At(null,t),At(null,t),At(null,t),At(null,t),At(null,t),t.length>t.l?t.slice(0,t.l):t}function oo(e,t){return t||(t=B(12+4*10)),t.write_shift(4,e.xfId),t.write_shift(2,1),t.write_shift(1,0),t.write_shift(1,0),cn(e.name||"",t),t.length>t.l?t.slice(0,t.l):t}function co(e,t,r){var n=B(2052);return n.write_shift(4,e),cn(t,n),cn(r,n),n.length>n.l?n.slice(0,n.l):n}function ho(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&++r}),r!=0&&(W(e,615,gr(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&W(e,44,ro(a,t[a]))}),W(e,616))}}function uo(e){var t=1;W(e,611,gr(t)),W(e,43,no({sz:12,color:{theme:1},name:"Calibri",family:2})),W(e,612)}function xo(e){var t=2;W(e,603,gr(t)),W(e,45,ia({patternType:"none"})),W(e,45,ia({patternType:"gray125"})),W(e,604)}function po(e){var t=1;W(e,613,gr(t)),W(e,46,lo()),W(e,614)}function vo(e){var t=1;W(e,626,gr(t)),W(e,47,gi({numFmtId:0},65535)),W(e,627)}function mo(e,t){W(e,617,gr(t.length)),t.forEach(function(r){W(e,47,gi(r,0))}),W(e,618)}function go(e){var t=1;W(e,619,gr(t)),W(e,48,oo({xfId:0,name:"Normal"})),W(e,620)}function _o(e){var t=0;W(e,505,gr(t)),W(e,506)}function To(e){var t=0;W(e,508,co(t,"TableStyleMedium9","PivotStyleMedium4")),W(e,509)}function Eo(e,t){var r=Qe();return W(r,278),ho(r,e.SSF),uo(r),xo(r),po(r),vo(r),mo(r,t.cellXfs),go(r),_o(r),To(r),W(r,279),r.end()}function _i(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&typeof e.raw=="string")return e.raw;var r=[ke];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function wo(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:Ke(e)}}function So(e){var t=B(12+2*e.name.length);return t.write_shift(4,e.flags),t.write_shift(4,e.version),be(e.name,t),t.slice(0,t.l)}function Ao(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}function Fo(e){var t=B(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}function yo(e,t){var r=B(8+2*t.length);return r.write_shift(4,e),be(t,r),r.slice(0,r.l)}function Co(e){return e.l+=4,e.read_shift(4)!=0}function Oo(e,t){var r=B(8);return r.write_shift(4,e),r.write_shift(4,1),r}function Do(){var e=Qe();return W(e,332),W(e,334,gr(1)),W(e,335,So({name:"XLDAPR",version:12e4,flags:3496657072})),W(e,336),W(e,339,yo(1,"XLDAPR")),W(e,52),W(e,35,gr(514)),W(e,4096,gr(0)),W(e,4097,hr(1)),W(e,36),W(e,53),W(e,340),W(e,337,Oo(1)),W(e,51,Fo([[1,0]])),W(e,338),W(e,333),e.end()}function Ti(){var e=[ke];return e.push(`<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">
  <metadataTypes count="1">
    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>
  </metadataTypes>
  <futureMetadata name="XLDAPR" count="1">
    <bk>
      <extLst>
        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">
          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>
        </ext>
      </extLst>
    </bk>
  </futureMetadata>
  <cellMetadata count="1">
    <bk>
      <rc t="1" v="0"/>
    </bk>
  </cellMetadata>
</metadata>`),e.join("")}function Ro(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=we(r);var n=e.read_shift(1);return n&2&&(t.l="1"),n&8&&(t.a="1"),t}var ot=1024;function Ei(e,t){for(var r=[21600,21600],n=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),a=[Z("xml",null,{"xmlns:v":ir.v,"xmlns:o":ir.o,"xmlns:x":ir.x,"xmlns:mv":ir.mv}).replace(/\/>/,">"),Z("o:shapelayout",Z("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),Z("v:shapetype",[Z("v:stroke",null,{joinstyle:"miter"}),Z("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:n})];ot<e*1e3;)ot+=1e3;return t.forEach(function(i){var s=Ue(i[0]),f={color2:"#BEFF82",type:"gradient"};f.type=="gradient"&&(f.angle="-180");var o=f.type=="gradient"?Z("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,l=Z("v:fill",o,f),c={on:"t",obscured:"t"};++ot,a=a.concat(["<v:shape"+Mt({id:"_x0000_s"+ot,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(i[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",l,Z("v:shadow",null,c),Z("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",Ge("x:Anchor",[s.c+1,0,s.r+1,0,s.c+3,20,s.r+5,20].join(",")),Ge("x:AutoFill","False"),Ge("x:Row",String(s.r)),Ge("x:Column",String(s.c)),i[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),a.push("</xml>"),a.join("")}function wi(e){var t=[ke,Z("comments",null,{xmlns:pt[0]})],r=[];return t.push("<authors>"),e.forEach(function(n){n[1].forEach(function(a){var i=Ee(a.a);r.indexOf(i)==-1&&(r.push(i),t.push("<author>"+i+"</author>")),a.T&&a.ID&&r.indexOf("tc="+a.ID)==-1&&(r.push("tc="+a.ID),t.push("<author>tc="+a.ID+"</author>"))})}),r.length==0&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(n){var a=0,i=[];if(n[1][0]&&n[1][0].T&&n[1][0].ID?a=r.indexOf("tc="+n[1][0].ID):n[1].forEach(function(o){o.a&&(a=r.indexOf(Ee(o.a))),i.push(o.t||"")}),t.push('<comment ref="'+n[0]+'" authorId="'+a+'"><text>'),i.length<=1)t.push(Ge("t",Ee(i[0]||"")));else{for(var s=`Comment:
    `+i[0]+`
`,f=1;f<i.length;++f)s+=`Reply:
    `+i[f]+`
`;t.push(Ge("t",Ee(s)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function No(e,t,r){var n=[ke,Z("ThreadedComments",null,{xmlns:Be.TCMNT}).replace(/[\/]>/,">")];return e.forEach(function(a){var i="";(a[1]||[]).forEach(function(s,f){if(!s.T){delete s.ID;return}s.a&&t.indexOf(s.a)==-1&&t.push(s.a);var o={ref:a[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};f==0?i=o.id:o.parentId=i,s.ID=o.id,s.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),n.push(Z("threadedComment",Ge("text",s.t||""),o))})}),n.push("</ThreadedComments>"),n.join("")}function Io(e){var t=[ke,Z("personList",null,{xmlns:Be.TCMNT,"xmlns:x":pt[0]}).replace(/[\/]>/,">")];return e.forEach(function(r,n){t.push(Z("person",null,{displayName:r,id:"{54EE7950-7262-4200-6969-"+("000000000000"+n).slice(-12)+"}",userId:r,providerId:"None"}))}),t.push("</personList>"),t.join("")}function ko(e){var t={};t.iauthor=e.read_shift(4);var r=rt(e);return t.rfx=r.s,t.ref=we(r.s),e.l+=16,t}function Po(e,t){return t==null&&(t=B(36)),t.write_shift(4,e[1].iauthor),mt(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}var Lo=Ke;function Mo(e){return be(e.slice(0,54))}function Bo(e){var t=Qe(),r=[];return W(t,628),W(t,630),e.forEach(function(n){n[1].forEach(function(a){r.indexOf(a.a)>-1||(r.push(a.a.slice(0,54)),W(t,632,Mo(a.a)))})}),W(t,631),W(t,633),e.forEach(function(n){n[1].forEach(function(a){a.iauthor=r.indexOf(a.a);var i={s:Ue(n[0]),e:Ue(n[0])};W(t,635,Po([i,a])),a.t&&a.t.length>0&&W(t,637,Uf(a)),W(t,636),delete a.iauthor})}),W(t,634),W(t,629),t.end()}function Uo(e,t){t.FullPaths.forEach(function(r,n){if(n!=0){var a=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");a.slice(-1)!=="/"&&Se.utils.cfb_add(e,a,t.FileIndex[n].content)}})}var Si=["xlsb","xlsm","xlam","biff8","xla"],bo=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(n,a,i,s){var f=!1,o=!1;i.length==0?o=!0:i.charAt(0)=="["&&(o=!0,i=i.slice(1,-1)),s.length==0?f=!0:s.charAt(0)=="["&&(f=!0,s=s.slice(1,-1));var l=i.length>0?parseInt(i,10)|0:0,c=s.length>0?parseInt(s,10)|0:0;return f?c+=t.c:--c,o?l+=t.r:--l,a+(f?"":"$")+ze(c)+(o?"":"$")+Xe(l)}return function(a,i){return t=i,a.replace(e,r)}}(),f0=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,l0=function(){return function(t,r){return t.replace(f0,function(n,a,i,s,f,o){var l=r0(s)-(i?0:r.c),c=e0(o)-(f?0:r.r),d=c==0?"":f?c+1:"["+c+"]",h=l==0?"":i?l+1:"["+l+"]";return a+"R"+d+"C"+h})}}();function Wo(e,t){return e.replace(f0,function(r,n,a,i,s,f){return n+(a=="$"?a+i:ze(r0(i)+t.c))+(s=="$"?s+f:Xe(e0(f)+t.r))})}function Ho(e){return e.length!=1}function Ne(e){e.l+=1}function br(e,t){var r=e.read_shift(2);return[r&16383,r>>14&1,r>>15&1]}function Ai(e,t,r){var n=2;if(r){if(r.biff>=2&&r.biff<=5)return Fi(e);r.biff==12&&(n=4)}var a=e.read_shift(n),i=e.read_shift(n),s=br(e),f=br(e);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:i,c:f[0],cRel:f[1],rRel:f[2]}}}function Fi(e){var t=br(e),r=br(e),n=e.read_shift(1),a=e.read_shift(1);return{s:{r:t[0],c:n,cRel:t[1],rRel:t[2]},e:{r:r[0],c:a,cRel:r[1],rRel:r[2]}}}function Vo(e,t,r){if(r.biff<8)return Fi(e);var n=e.read_shift(r.biff==12?4:2),a=e.read_shift(r.biff==12?4:2),i=br(e),s=br(e);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:a,c:s[0],cRel:s[1],rRel:s[2]}}}function yi(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return Go(e);var n=e.read_shift(r&&r.biff==12?4:2),a=br(e);return{r:n,c:a[0],cRel:a[1],rRel:a[2]}}function Go(e){var t=br(e),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}function Xo(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:r&255,fQuoted:!!(r&16384),cRel:r>>15,rRel:r>>15}}function jo(e,t,r){var n=r&&r.biff?r.biff:8;if(n>=2&&n<=5)return $o(e);var a=e.read_shift(n>=12?4:2),i=e.read_shift(2),s=(i&16384)>>14,f=(i&32768)>>15;if(i&=16383,f==1)for(;a>524287;)a-=1048576;if(s==1)for(;i>8191;)i=i-16384;return{r:a,c:i,cRel:s,rRel:f}}function $o(e){var t=e.read_shift(2),r=e.read_shift(1),n=(t&32768)>>15,a=(t&16384)>>14;return t&=16383,n==1&&t>=8192&&(t=t-16384),a==1&&r>=128&&(r=r-256),{r:t,c:r,cRel:a,rRel:n}}function zo(e,t,r){var n=(e[e.l++]&96)>>5,a=Ai(e,r.biff>=2&&r.biff<=5?6:8,r);return[n,a]}function Ko(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2,"i"),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}var s=Ai(e,i,r);return[n,a,s]}function Yo(e,t,r){var n=(e[e.l++]&96)>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}function Jo(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}return e.l+=i,[n,a]}function Zo(e,t,r){var n=(e[e.l++]&96)>>5,a=Vo(e,t-1,r);return[n,a]}function qo(e,t,r){var n=(e[e.l++]&96)>>5;return e.l+=r.biff==2?6:r.biff==12?14:7,[n]}function sa(e){var t=e[e.l+1]&1,r=1;return e.l+=4,[t,r]}function Qo(e,t,r){e.l+=2;for(var n=e.read_shift(r&&r.biff==2?1:2),a=[],i=0;i<=n;++i)a.push(e.read_shift(r&&r.biff==2?1:2));return a}function ec(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function rc(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function tc(e){var t=e[e.l+1]&255?1:0;return e.l+=2,[t,e.read_shift(2)]}function nc(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=r&&r.biff==2?3:4,[n]}function Ci(e){var t=e.read_shift(1),r=e.read_shift(1);return[t,r]}function ac(e){return e.read_shift(2),Ci(e)}function ic(e){return e.read_shift(2),Ci(e)}function sc(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=yi(e,0,r);return[n,a]}function fc(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=jo(e,0,r);return[n,a]}function lc(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=e.read_shift(2);r&&r.biff==5&&(e.l+=12);var i=yi(e,0,r);return[n,a,i]}function oc(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=e.read_shift(r&&r.biff<=3?1:2);return[oh[a],Ri[a],n]}function cc(e,t,r){var n=e[e.l++],a=e.read_shift(1),i=r&&r.biff<=3?[n==88?-1:0,e.read_shift(1)]:hc(e);return[a,(i[0]===0?Ri:lh)[i[1]]]}function hc(e){return[e[e.l+1]>>7,e.read_shift(2)&32767]}function uc(e,t,r){e.l+=r&&r.biff==2?3:4}function xc(e,t,r){if(e.l++,r&&r.biff==12)return[e.read_shift(4,"i"),0];var n=e.read_shift(2),a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function dc(e){return e.l++,Vt[e.read_shift(1)]}function pc(e){return e.l++,e.read_shift(2)}function vc(e){return e.l++,e.read_shift(1)!==0}function mc(e){return e.l++,gt(e)}function gc(e,t,r){return e.l++,oi(e,t-1,r)}function _c(e,t){var r=[e.read_shift(1)];if(t==12)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=fl(e,1)?"TRUE":"FALSE",t!=12&&(e.l+=7);break;case 37:case 16:r[1]=Vt[e[e.l]],e.l+=t==12?4:8;break;case 0:e.l+=8;break;case 1:r[1]=gt(e);break;case 2:r[1]=hl(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function Tc(e,t,r){for(var n=e.read_shift(r.biff==12?4:2),a=[],i=0;i!=n;++i)a.push((r.biff==12?rt:dl)(e));return a}function Ec(e,t,r){var n=0,a=0;r.biff==12?(n=e.read_shift(4),a=e.read_shift(4)):(a=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,--a==0&&(a=256));for(var i=0,s=[];i!=n&&(s[i]=[]);++i)for(var f=0;f!=a;++f)s[i][f]=_c(e,r.biff);return s}function wc(e,t,r){var n=e.read_shift(1)>>>5&3,a=!r||r.biff>=8?4:2,i=e.read_shift(a);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[n,0,i]}function Sc(e,t,r){if(r.biff==5)return Ac(e);var n=e.read_shift(1)>>>5&3,a=e.read_shift(2),i=e.read_shift(4);return[n,a,i]}function Ac(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var n=e.read_shift(2);return e.l+=12,[t,r,n]}function Fc(e,t,r){var n=e.read_shift(1)>>>5&3;e.l+=r&&r.biff==2?3:4;var a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function yc(e,t,r){var n=e.read_shift(1)>>>5&3,a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function Cc(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,r.biff==12&&(e.l+=2),[n]}function Oc(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2),i=4;if(r)switch(r.biff){case 5:i=15;break;case 12:i=6;break}return e.l+=i,[n,a]}var Dc=wr,Rc=wr,Nc=wr;function Gt(e,t,r){return e.l+=2,[Xo(e)]}function o0(e){return e.l+=6,[]}var Ic=Gt,kc=o0,Pc=o0,Lc=Gt;function Oi(e){return e.l+=2,[fi(e),e.read_shift(2)&1]}var Mc=Gt,Bc=Oi,Uc=o0,bc=Gt,Wc=Gt,Hc=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function Vc(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),a=e.read_shift(2),i=e.read_shift(2),s=Hc[r>>2&31];return{ixti:t,coltype:r&3,rt:s,idx:n,c:a,C:i}}function Gc(e){return e.l+=2,[e.read_shift(4)]}function Xc(e,t,r){return e.l+=5,e.l+=2,e.l+=r.biff==2?1:4,["PTGSHEET"]}function jc(e,t,r){return e.l+=r.biff==2?4:5,["PTGENDSHEET"]}function $c(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function zc(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function Kc(e){return e.l+=4,[0,0]}var fa={1:{n:"PtgExp",f:xc},2:{n:"PtgTbl",f:Nc},3:{n:"PtgAdd",f:Ne},4:{n:"PtgSub",f:Ne},5:{n:"PtgMul",f:Ne},6:{n:"PtgDiv",f:Ne},7:{n:"PtgPower",f:Ne},8:{n:"PtgConcat",f:Ne},9:{n:"PtgLt",f:Ne},10:{n:"PtgLe",f:Ne},11:{n:"PtgEq",f:Ne},12:{n:"PtgGe",f:Ne},13:{n:"PtgGt",f:Ne},14:{n:"PtgNe",f:Ne},15:{n:"PtgIsect",f:Ne},16:{n:"PtgUnion",f:Ne},17:{n:"PtgRange",f:Ne},18:{n:"PtgUplus",f:Ne},19:{n:"PtgUminus",f:Ne},20:{n:"PtgPercent",f:Ne},21:{n:"PtgParen",f:Ne},22:{n:"PtgMissArg",f:Ne},23:{n:"PtgStr",f:gc},26:{n:"PtgSheet",f:Xc},27:{n:"PtgEndSheet",f:jc},28:{n:"PtgErr",f:dc},29:{n:"PtgBool",f:vc},30:{n:"PtgInt",f:pc},31:{n:"PtgNum",f:mc},32:{n:"PtgArray",f:qo},33:{n:"PtgFunc",f:oc},34:{n:"PtgFuncVar",f:cc},35:{n:"PtgName",f:wc},36:{n:"PtgRef",f:sc},37:{n:"PtgArea",f:zo},38:{n:"PtgMemArea",f:Fc},39:{n:"PtgMemErr",f:Dc},40:{n:"PtgMemNoMem",f:Rc},41:{n:"PtgMemFunc",f:yc},42:{n:"PtgRefErr",f:Cc},43:{n:"PtgAreaErr",f:Yo},44:{n:"PtgRefN",f:fc},45:{n:"PtgAreaN",f:Zo},46:{n:"PtgMemAreaN",f:$c},47:{n:"PtgMemNoMemN",f:zc},57:{n:"PtgNameX",f:Sc},58:{n:"PtgRef3d",f:lc},59:{n:"PtgArea3d",f:Ko},60:{n:"PtgRefErr3d",f:Oc},61:{n:"PtgAreaErr3d",f:Jo},255:{}},Yc={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},Jc={1:{n:"PtgElfLel",f:Oi},2:{n:"PtgElfRw",f:bc},3:{n:"PtgElfCol",f:Ic},6:{n:"PtgElfRwV",f:Wc},7:{n:"PtgElfColV",f:Lc},10:{n:"PtgElfRadical",f:Mc},11:{n:"PtgElfRadicalS",f:Uc},13:{n:"PtgElfColS",f:kc},15:{n:"PtgElfColSV",f:Pc},16:{n:"PtgElfRadicalLel",f:Bc},25:{n:"PtgList",f:Vc},29:{n:"PtgSxName",f:Gc},255:{}},Zc={0:{n:"PtgAttrNoop",f:Kc},1:{n:"PtgAttrSemi",f:nc},2:{n:"PtgAttrIf",f:rc},4:{n:"PtgAttrChoose",f:Qo},8:{n:"PtgAttrGoto",f:ec},16:{n:"PtgAttrSum",f:uc},32:{n:"PtgAttrBaxcel",f:sa},33:{n:"PtgAttrBaxcel",f:sa},64:{n:"PtgAttrSpace",f:ac},65:{n:"PtgAttrSpaceSemi",f:ic},128:{n:"PtgAttrIfError",f:tc},255:{}};function qc(e,t,r,n){if(n.biff<8)return wr(e,t);for(var a=e.l+t,i=[],s=0;s!==r.length;++s)switch(r[s][0]){case"PtgArray":r[s][1]=Ec(e,0,n),i.push(r[s][1]);break;case"PtgMemArea":r[s][2]=Tc(e,r[s][1],n),i.push(r[s][2]);break;case"PtgExp":n&&n.biff==12&&(r[s][1][1]=e.read_shift(4),i.push(r[s][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[s][0]}return t=a-e.l,t!==0&&i.push(wr(e,t)),i}function Qc(e,t,r){for(var n=e.l+t,a,i,s=[];n!=e.l;)t=n-e.l,i=e[e.l],a=fa[i]||fa[Yc[i]],(i===24||i===25)&&(a=(i===24?Jc:Zc)[e[e.l+1]]),!a||!a.f?wr(e,t):s.push([a.n,a.f(e,t,r)]);return s}function eh(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r],a=[],i=0;i<n.length;++i){var s=n[i];if(s)switch(s[0]){case 2:a.push('"'+s[1].replace(/"/g,'""')+'"');break;default:a.push(s[1])}else a.push("")}t.push(a.join(","))}return t.join(";")}var rh={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function th(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function Di(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),t==0?"":e.XTI[t-1];if(!n)return"SH33TJSERR1";var a="";if(r.biff>8)switch(e[n[0]][0]){case 357:return a=n[1]==-1?"#REF":e.SheetNames[n[1]],n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 358:return r.SID!=null?e.SheetNames[r.SID]:"SH33TJSSAME"+e[n[0]][0];case 355:default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return a=n[1]==-1?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map(function(i){return i.Name}).join(";;");default:return e[n[0]][0][3]?(a=n[1]==-1?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?a:a+":"+e[n[0]][0][3][n[2]]):"SH33TJSERR2"}}function la(e,t,r){var n=Di(e,t,r);return n=="#REF"?n:th(n,r)}function dt(e,t,r,n,a){var i=a&&a.biff||8,s={s:{c:0,r:0}},f=[],o,l,c,d=0,h=0,p,T="";if(!e[0]||!e[0][0])return"";for(var u=-1,g="",C=0,O=e[0].length;C<O;++C){var F=e[0][C];switch(F[0]){case"PtgUminus":f.push("-"+f.pop());break;case"PtgUplus":f.push("+"+f.pop());break;case"PtgPercent":f.push(f.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(o=f.pop(),l=f.pop(),u>=0){switch(e[0][u][1][0]){case 0:g=De(" ",e[0][u][1][1]);break;case 1:g=De("\r",e[0][u][1][1]);break;default:if(g="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][u][1][0])}l=l+g,u=-1}f.push(l+rh[F[0]]+o);break;case"PtgIsect":o=f.pop(),l=f.pop(),f.push(l+" "+o);break;case"PtgUnion":o=f.pop(),l=f.pop(),f.push(l+","+o);break;case"PtgRange":o=f.pop(),l=f.pop(),f.push(l+":"+o);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":c=Dt(F[1][1],s,a),f.push(Rt(c,i));break;case"PtgRefN":c=r?Dt(F[1][1],r,a):F[1][1],f.push(Rt(c,i));break;case"PtgRef3d":d=F[1][1],c=Dt(F[1][2],s,a),T=la(n,d,a),f.push(T+"!"+Rt(c,i));break;case"PtgFunc":case"PtgFuncVar":var M=F[1][0],Y=F[1][1];M||(M=0),M&=127;var ne=M==0?[]:f.slice(-M);f.length-=M,Y==="User"&&(Y=ne.shift()),f.push(Y+"("+ne.join(",")+")");break;case"PtgBool":f.push(F[1]?"TRUE":"FALSE");break;case"PtgInt":f.push(F[1]);break;case"PtgNum":f.push(String(F[1]));break;case"PtgStr":f.push('"'+F[1].replace(/"/g,'""')+'"');break;case"PtgErr":f.push(F[1]);break;case"PtgAreaN":p=$0(F[1][1],r?{s:r}:s,a),f.push(Mn(p,a));break;case"PtgArea":p=$0(F[1][1],s,a),f.push(Mn(p,a));break;case"PtgArea3d":d=F[1][1],p=F[1][2],T=la(n,d,a),f.push(T+"!"+Mn(p,a));break;case"PtgAttrSum":f.push("SUM("+f.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":h=F[1][2];var D=(n.names||[])[h-1]||(n[0]||[])[h],b=D?D.Name:"SH33TJSNAME"+String(h);b&&b.slice(0,6)=="_xlfn."&&!a.xlfn&&(b=b.slice(6)),f.push(b);break;case"PtgNameX":var P=F[1][1];h=F[1][2];var V;if(a.biff<=5)P<0&&(P=-P),n[P]&&(V=n[P][h]);else{var G="";if(((n[P]||[])[0]||[])[0]==14849||(((n[P]||[])[0]||[])[0]==1025?n[P][h]&&n[P][h].itab>0&&(G=n.SheetNames[n[P][h].itab-1]+"!"):G=n.SheetNames[h-1]+"!"),n[P]&&n[P][h])G+=n[P][h].Name;else if(n[0]&&n[0][h])G+=n[0][h].Name;else{var j=(Di(n,P,a)||"").split(";;");j[h-1]?G=j[h-1]:G+="SH33TJSERRX"}f.push(G);break}V||(V={Name:"SH33TJSERRY"}),f.push(V.Name);break;case"PtgParen":var re="(",xe=")";if(u>=0){switch(g="",e[0][u][1][0]){case 2:re=De(" ",e[0][u][1][1])+re;break;case 3:re=De("\r",e[0][u][1][1])+re;break;case 4:xe=De(" ",e[0][u][1][1])+xe;break;case 5:xe=De("\r",e[0][u][1][1])+xe;break;default:if(a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][u][1][0])}u=-1}f.push(re+f.pop()+xe);break;case"PtgRefErr":f.push("#REF!");break;case"PtgRefErr3d":f.push("#REF!");break;case"PtgExp":c={c:F[1][1],r:F[1][0]};var J={c:r.c,r:r.r};if(n.sharedf[we(c)]){var Q=n.sharedf[we(c)];f.push(dt(Q,s,J,n,a))}else{var ge=!1;for(o=0;o!=n.arrayf.length;++o)if(l=n.arrayf[o],!(c.c<l[0].s.c||c.c>l[0].e.c)&&!(c.r<l[0].s.r||c.r>l[0].e.r)){f.push(dt(l[1],s,J,n,a)),ge=!0;break}ge||f.push(F[1])}break;case"PtgArray":f.push("{"+eh(F[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":u=C;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":f.push("");break;case"PtgAreaErr":f.push("#REF!");break;case"PtgAreaErr3d":f.push("#REF!");break;case"PtgList":f.push("Table"+F[1].idx+"[#"+F[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(F));default:throw new Error("Unrecognized Formula Token: "+String(F))}var ue=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(a.biff!=3&&u>=0&&ue.indexOf(e[0][C][0])==-1){F=e[0][u];var Pe=!0;switch(F[1][0]){case 4:Pe=!1;case 0:g=De(" ",F[1][1]);break;case 5:Pe=!1;case 1:g=De("\r",F[1][1]);break;default:if(g="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+F[1][0])}f.push((Pe?g:"")+f.pop()+(Pe?"":g)),u=-1}}if(f.length>1&&a.WTF)throw new Error("bad formula stack");return f[0]}function nh(e){if(e==null){var t=B(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}else if(typeof e=="number")return Yr(e);return Yr(0)}function ah(e,t,r,n,a){var i=Jr(t,r,a),s=nh(e.v),f=B(6),o=33;f.write_shift(2,o),f.write_shift(4,0);for(var l=B(e.bf.length),c=0;c<e.bf.length;++c)l[c]=e.bf[c];var d=Ve([i,s,f,l]);return d}function Sn(e,t,r){var n=e.read_shift(4),a=Qc(e,n,r),i=e.read_shift(4),s=i>0?qc(e,i,a,r):null;return[a,s]}var ih=Sn,An=Sn,sh=Sn,fh=Sn,lh={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},Ri={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},oh={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function ch(e){var t="of:="+e.replace(f0,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return t.replace(/;/g,"|").replace(/,/g,";")}function hh(e){return e.replace(/\./,"!")}var Nt=typeof Map<"u";function c0(e,t,r){var n=0,a=e.length;if(r){if(Nt?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var i=Nt?r.get(t):r[t];n<i.length;++n)if(e[i[n]].t===t)return e.Count++,i[n]}}else for(;n<a;++n)if(e[n].t===t)return e.Count++,n;return e[a]={t},e.Count++,e.Unique++,r&&(Nt?(r.has(t)||r.set(t,[]),r.get(t).push(a)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(a))),a}function Fn(e,t){var r={min:e+1,max:e+1},n=-1;return t.MDW&&(Dr=t.MDW),t.width!=null?r.customWidth=1:t.wpx!=null?n=dn(t.wpx):t.wch!=null&&(n=t.wch),n>-1?(r.width=jn(n),r.customWidth=1):t.width!=null&&(r.width=t.width),t.hidden&&(r.hidden=!0),t.level!=null&&(r.outlineLevel=r.level=t.level),r}function Ni(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];e.left==null&&(e.left=r[0]),e.right==null&&(e.right=r[1]),e.top==null&&(e.top=r[2]),e.bottom==null&&(e.bottom=r[3]),e.header==null&&(e.header=r[4]),e.footer==null&&(e.footer=r[5])}}function Hr(e,t,r){var n=r.revssf[t.z!=null?t.z:"General"],a=60,i=e.length;if(n==null&&r.ssf){for(;a<392;++a)if(r.ssf[a]==null){ya(t.z,a),r.ssf[a]=t.z,r.revssf[t.z]=n=a;break}}for(a=0;a!=i;++a)if(e[a].numFmtId===n)return a;return e[i]={numFmtId:n,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},i}function uh(e,t,r){if(e&&e["!ref"]){var n=Fe(e["!ref"]);if(n.e.c<n.s.c||n.e.r<n.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}function xh(e){if(e.length===0)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+Ie(e[r])+'"/>';return t+"</mergeCells>"}function dh(e,t,r,n,a){var i=!1,s={},f=null;if(n.bookType!=="xlsx"&&t.vbaraw){var o=t.SheetNames[r];try{t.Workbook&&(o=t.Workbook.Sheets[r].CodeName||o)}catch{}i=!0,s.codeName=Lt(Ee(o))}if(e&&e["!outline"]){var l={summaryBelow:1,summaryRight:1};e["!outline"].above&&(l.summaryBelow=0),e["!outline"].left&&(l.summaryRight=0),f=(f||"")+Z("outlinePr",null,l)}!i&&!f||(a[a.length]=Z("sheetPr",f,s))}var ph=["objects","scenarios","selectLockedCells","selectUnlockedCells"],vh=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function mh(e){var t={sheet:1};return ph.forEach(function(r){e[r]!=null&&e[r]&&(t[r]="1")}),vh.forEach(function(r){e[r]!=null&&!e[r]&&(t[r]="0")}),e.password&&(t.password=di(e.password).toString(16).toUpperCase()),Z("sheetProtection",null,t)}function gh(e){return Ni(e),Z("pageMargins",null,e)}function _h(e,t){for(var r=["<cols>"],n,a=0;a!=t.length;++a)(n=t[a])&&(r[r.length]=Z("col",null,Fn(a,n)));return r[r.length]="</cols>",r.join("")}function Th(e,t,r,n){var a=typeof e.ref=="string"?e.ref:Ie(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,s=fr(a);s.s.r==s.e.r&&(s.e.r=fr(t["!ref"]).e.r,a=Ie(s));for(var f=0;f<i.length;++f){var o=i[f];if(o.Name=="_xlnm._FilterDatabase"&&o.Sheet==n){o.Ref="'"+r.SheetNames[n]+"'!"+a;break}}return f==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+a}),Z("autoFilter",null,{ref:a})}function Eh(e,t,r,n){var a={workbookViewId:"0"};return(((n||{}).Workbook||{}).Views||[])[0]&&(a.rightToLeft=n.Workbook.Views[0].RTL?"1":"0"),Z("sheetViews",Z("sheetView",null,a),{})}function wh(e,t,r,n){if(e.c&&r["!comments"].push([t,e.c]),e.v===void 0&&typeof e.f!="string"||e.t==="z"&&!e.f)return"";var a="",i=e.t,s=e.v;if(e.t!=="z")switch(e.t){case"b":a=e.v?"1":"0";break;case"n":a=""+e.v;break;case"e":a=Vt[e.v];break;case"d":n&&n.cellDates?a=qe(e.v,-1).toISOString():(e=rr(e),e.t="n",a=""+(e.v=er(qe(e.v)))),typeof e.z>"u"&&(e.z=Re[14]);break;default:a=e.v;break}var f=Ge("v",Ee(a)),o={r:t},l=Hr(n.cellXfs,e,n);switch(l!==0&&(o.s=l),e.t){case"n":break;case"d":o.t="d";break;case"b":o.t="b";break;case"e":o.t="e";break;case"z":break;default:if(e.v==null){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(n&&n.bookSST){f=Ge("v",""+c0(n.Strings,e.v,n.revStrings)),o.t="s";break}o.t="str";break}if(e.t!=i&&(e.t=i,e.v=s),typeof e.f=="string"&&e.f){var c=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;f=Z("f",Ee(e.f),c)+(e.v!=null?f:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(o.cm=1),Z("c",f,o)}function Sh(e,t,r,n){var a=[],i=[],s=Fe(e["!ref"]),f="",o,l="",c=[],d=0,h=0,p=e["!rows"],T=Array.isArray(e),u={r:l},g,C=-1;for(h=s.s.c;h<=s.e.c;++h)c[h]=ze(h);for(d=s.s.r;d<=s.e.r;++d){for(i=[],l=Xe(d),h=s.s.c;h<=s.e.c;++h){o=c[h]+l;var O=T?(e[d]||[])[h]:e[o];O!==void 0&&(f=wh(O,o,e,t))!=null&&i.push(f)}(i.length>0||p&&p[d])&&(u={r:l},p&&p[d]&&(g=p[d],g.hidden&&(u.hidden=1),C=-1,g.hpx?C=pn(g.hpx):g.hpt&&(C=g.hpt),C>-1&&(u.ht=C,u.customHeight=1),g.level&&(u.outlineLevel=g.level)),a[a.length]=Z("row",i.join(""),u))}if(p)for(;d<p.length;++d)p&&p[d]&&(u={r:d+1},g=p[d],g.hidden&&(u.hidden=1),C=-1,g.hpx?C=pn(g.hpx):g.hpt&&(C=g.hpt),C>-1&&(u.ht=C,u.customHeight=1),g.level&&(u.outlineLevel=g.level),a[a.length]=Z("row","",u));return a.join("")}function Ii(e,t,r,n){var a=[ke,Z("worksheet",null,{xmlns:pt[0],"xmlns:r":Be.r})],i=r.SheetNames[e],s=0,f="",o=r.Sheets[i];o==null&&(o={});var l=o["!ref"]||"A1",c=Fe(l);if(c.e.c>16383||c.e.r>1048575){if(t.WTF)throw new Error("Range "+l+" exceeds format limit A1:XFD1048576");c.e.c=Math.min(c.e.c,16383),c.e.r=Math.min(c.e.c,1048575),l=Ie(c)}n||(n={}),o["!comments"]=[];var d=[];dh(o,r,e,t,a),a[a.length]=Z("dimension",null,{ref:l}),a[a.length]=Eh(o,t,e,r),t.sheetFormat&&(a[a.length]=Z("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),o["!cols"]!=null&&o["!cols"].length>0&&(a[a.length]=_h(o,o["!cols"])),a[s=a.length]="<sheetData/>",o["!links"]=[],o["!ref"]!=null&&(f=Sh(o,t),f.length>0&&(a[a.length]=f)),a.length>s+1&&(a[a.length]="</sheetData>",a[s]=a[s].replace("/>",">")),o["!protect"]&&(a[a.length]=mh(o["!protect"])),o["!autofilter"]!=null&&(a[a.length]=Th(o["!autofilter"],o,r,e)),o["!merges"]!=null&&o["!merges"].length>0&&(a[a.length]=xh(o["!merges"]));var h=-1,p,T=-1;return o["!links"].length>0&&(a[a.length]="<hyperlinks>",o["!links"].forEach(function(u){u[1].Target&&(p={ref:u[0]},u[1].Target.charAt(0)!="#"&&(T=Te(n,-1,Ee(u[1].Target).replace(/#.*$/,""),pe.HLINK),p["r:id"]="rId"+T),(h=u[1].Target.indexOf("#"))>-1&&(p.location=Ee(u[1].Target.slice(h+1))),u[1].Tooltip&&(p.tooltip=Ee(u[1].Tooltip)),a[a.length]=Z("hyperlink",null,p))}),a[a.length]="</hyperlinks>"),delete o["!links"],o["!margins"]!=null&&(a[a.length]=gh(o["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&(a[a.length]=Ge("ignoredErrors",Z("ignoredError",null,{numberStoredAsText:1,sqref:l}))),d.length>0&&(T=Te(n,-1,"../drawings/drawing"+(e+1)+".xml",pe.DRAW),a[a.length]=Z("drawing",null,{"r:id":"rId"+T}),o["!drawing"]=d),o["!comments"].length>0&&(T=Te(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",pe.VML),a[a.length]=Z("legacyDrawing",null,{"r:id":"rId"+T}),o["!legacy"]=T),a.length>1&&(a[a.length]="</worksheet>",a[1]=a[1].replace("/>",">")),a.join("")}function Ah(e,t){var r={},n=e.l+t;r.r=e.read_shift(4),e.l+=4;var a=e.read_shift(2);e.l+=1;var i=e.read_shift(1);return e.l=n,i&7&&(r.level=i&7),i&16&&(r.hidden=!0),i&32&&(r.hpt=a/20),r}function Fh(e,t,r){var n=B(145),a=(r["!rows"]||[])[e]||{};n.write_shift(4,e),n.write_shift(4,0);var i=320;a.hpx?i=pn(a.hpx)*20:a.hpt&&(i=a.hpt*20),n.write_shift(2,i),n.write_shift(1,0);var s=0;a.level&&(s|=a.level),a.hidden&&(s|=16),(a.hpx||a.hpt)&&(s|=32),n.write_shift(1,s),n.write_shift(1,0);var f=0,o=n.l;n.l+=4;for(var l={r:e,c:0},c=0;c<16;++c)if(!(t.s.c>c+1<<10||t.e.c<c<<10)){for(var d=-1,h=-1,p=c<<10;p<c+1<<10;++p){l.c=p;var T=Array.isArray(r)?(r[l.r]||[])[l.c]:r[we(l)];T&&(d<0&&(d=p),h=p)}d<0||(++f,n.write_shift(4,d),n.write_shift(4,h))}var u=n.l;return n.l=o,n.write_shift(4,f),n.l=u,n.length>n.l?n.slice(0,n.l):n}function yh(e,t,r,n){var a=Fh(n,r,t);(a.length>17||(t["!rows"]||[])[n])&&W(e,0,a)}var Ch=rt,Oh=mt;function Dh(){}function Rh(e,t){var r={},n=e[e.l];return++e.l,r.above=!(n&64),r.left=!(n&128),e.l+=18,r.name=bf(e),r}function Nh(e,t,r){r==null&&(r=B(84+4*e.length));var n=192;t&&(t.above&&(n&=-65),t.left&&(n&=-129)),r.write_shift(1,n);for(var a=1;a<3;++a)r.write_shift(1,0);return hn({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),za(e,r),r.slice(0,r.l)}function Ih(e){var t=ur(e);return[t]}function kh(e,t,r){return r==null&&(r=B(8)),qr(t,r)}function Ph(e){var t=Qr(e);return[t]}function Lh(e,t,r){return r==null&&(r=B(4)),et(t,r)}function Mh(e){var t=ur(e),r=e.read_shift(1);return[t,r,"b"]}function Bh(e,t,r){return r==null&&(r=B(9)),qr(t,r),r.write_shift(1,e.v?1:0),r}function Uh(e){var t=Qr(e),r=e.read_shift(1);return[t,r,"b"]}function bh(e,t,r){return r==null&&(r=B(5)),et(t,r),r.write_shift(1,e.v?1:0),r}function Wh(e){var t=ur(e),r=e.read_shift(1);return[t,r,"e"]}function Hh(e,t,r){return r==null&&(r=B(9)),qr(t,r),r.write_shift(1,e.v),r}function Vh(e){var t=Qr(e),r=e.read_shift(1);return[t,r,"e"]}function Gh(e,t,r){return r==null&&(r=B(8)),et(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}function Xh(e){var t=ur(e),r=e.read_shift(4);return[t,r,"s"]}function jh(e,t,r){return r==null&&(r=B(12)),qr(t,r),r.write_shift(4,t.v),r}function $h(e){var t=Qr(e),r=e.read_shift(4);return[t,r,"s"]}function zh(e,t,r){return r==null&&(r=B(8)),et(t,r),r.write_shift(4,t.v),r}function Kh(e){var t=ur(e),r=gt(e);return[t,r,"n"]}function Yh(e,t,r){return r==null&&(r=B(16)),qr(t,r),Yr(e.v,r),r}function Jh(e){var t=Qr(e),r=gt(e);return[t,r,"n"]}function Zh(e,t,r){return r==null&&(r=B(12)),et(t,r),Yr(e.v,r),r}function qh(e){var t=ur(e),r=Ka(e);return[t,r,"n"]}function Qh(e,t,r){return r==null&&(r=B(12)),qr(t,r),Ya(e.v,r),r}function e1(e){var t=Qr(e),r=Ka(e);return[t,r,"n"]}function r1(e,t,r){return r==null&&(r=B(8)),et(t,r),Ya(e.v,r),r}function t1(e){var t=ur(e),r=t0(e);return[t,r,"is"]}function n1(e){var t=ur(e),r=Ke(e);return[t,r,"str"]}function a1(e,t,r){return r==null&&(r=B(12+4*e.v.length)),qr(t,r),be(e.v,r),r.length>r.l?r.slice(0,r.l):r}function i1(e){var t=Qr(e),r=Ke(e);return[t,r,"str"]}function s1(e,t,r){return r==null&&(r=B(8+4*e.v.length)),et(t,r),be(e.v,r),r.length>r.l?r.slice(0,r.l):r}function f1(e,t,r){var n=e.l+t,a=ur(e);a.r=r["!row"];var i=e.read_shift(1),s=[a,i,"b"];if(r.cellFormula){e.l+=2;var f=An(e,n-e.l,r);s[3]=dt(f,null,a,r.supbooks,r)}else e.l=n;return s}function l1(e,t,r){var n=e.l+t,a=ur(e);a.r=r["!row"];var i=e.read_shift(1),s=[a,i,"e"];if(r.cellFormula){e.l+=2;var f=An(e,n-e.l,r);s[3]=dt(f,null,a,r.supbooks,r)}else e.l=n;return s}function o1(e,t,r){var n=e.l+t,a=ur(e);a.r=r["!row"];var i=gt(e),s=[a,i,"n"];if(r.cellFormula){e.l+=2;var f=An(e,n-e.l,r);s[3]=dt(f,null,a,r.supbooks,r)}else e.l=n;return s}function c1(e,t,r){var n=e.l+t,a=ur(e);a.r=r["!row"];var i=Ke(e),s=[a,i,"str"];if(r.cellFormula){e.l+=2;var f=An(e,n-e.l,r);s[3]=dt(f,null,a,r.supbooks,r)}else e.l=n;return s}var h1=rt,u1=mt;function x1(e,t){return t==null&&(t=B(4)),t.write_shift(4,e),t}function d1(e,t){var r=e.l+t,n=rt(e),a=n0(e),i=Ke(e),s=Ke(e),f=Ke(e);e.l=r;var o={rfx:n,relId:a,loc:i,display:f};return s&&(o.Tooltip=s),o}function p1(e,t){var r=B(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));mt({s:Ue(e[0]),e:Ue(e[0])},r),a0("rId"+t,r);var n=e[1].Target.indexOf("#"),a=n==-1?"":e[1].Target.slice(n+1);return be(a||"",r),be(e[1].Tooltip||"",r),be("",r),r.slice(0,r.l)}function v1(){}function m1(e,t,r){var n=e.l+t,a=Ja(e),i=e.read_shift(1),s=[a];if(s[2]=i,r.cellFormula){var f=ih(e,n-e.l,r);s[1]=f}else e.l=n;return s}function g1(e,t,r){var n=e.l+t,a=rt(e),i=[a];if(r.cellFormula){var s=fh(e,n-e.l,r);i[1]=s,e.l=n}else e.l=n;return i}function _1(e,t,r){r==null&&(r=B(18));var n=Fn(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,(n.width||10)*256),r.write_shift(4,0);var a=0;return t.hidden&&(a|=1),typeof n.width=="number"&&(a|=2),t.level&&(a|=t.level<<8),r.write_shift(2,a),r}var ki=["left","right","top","bottom","header","footer"];function T1(e){var t={};return ki.forEach(function(r){t[r]=gt(e)}),t}function E1(e,t){return t==null&&(t=B(6*8)),Ni(e),ki.forEach(function(r){Yr(e[r],t)}),t}function w1(e){var t=e.read_shift(2);return e.l+=28,{RTL:t&32}}function S1(e,t,r){r==null&&(r=B(30));var n=924;return(((t||{}).Views||[])[0]||{}).RTL&&(n|=32),r.write_shift(2,n),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}function A1(e){var t=B(24);return t.write_shift(4,4),t.write_shift(4,1),mt(e,t),t}function F1(e,t){return t==null&&(t=B(16*4+2)),t.write_shift(2,e.password?di(e.password):0),t.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(r){r[1]?t.write_shift(4,e[r[0]]!=null&&!e[r[0]]?1:0):t.write_shift(4,e[r[0]]!=null&&e[r[0]]?0:1)}),t}function y1(){}function C1(){}function O1(e,t,r,n,a,i,s){if(t.v===void 0)return!1;var f="";switch(t.t){case"b":f=t.v?"1":"0";break;case"d":t=rr(t),t.z=t.z||Re[14],t.v=er(qe(t.v)),t.t="n";break;case"n":case"e":f=""+t.v;break;default:f=t.v;break}var o={r,c:n};switch(o.s=Hr(a.cellXfs,t,a),t.l&&i["!links"].push([we(o),t.l]),t.c&&i["!comments"].push([we(o),t.c]),t.t){case"s":case"str":return a.bookSST?(f=c0(a.Strings,t.v,a.revStrings),o.t="s",o.v=f,s?W(e,18,zh(t,o)):W(e,7,jh(t,o))):(o.t="str",s?W(e,17,s1(t,o)):W(e,6,a1(t,o))),!0;case"n":return t.v==(t.v|0)&&t.v>-1e3&&t.v<1e3?s?W(e,13,r1(t,o)):W(e,2,Qh(t,o)):s?W(e,16,Zh(t,o)):W(e,5,Yh(t,o)),!0;case"b":return o.t="b",s?W(e,15,bh(t,o)):W(e,4,Bh(t,o)),!0;case"e":return o.t="e",s?W(e,14,Gh(t,o)):W(e,3,Hh(t,o)),!0}return s?W(e,12,Lh(t,o)):W(e,1,kh(t,o)),!0}function D1(e,t,r,n){var a=Fe(t["!ref"]||"A1"),i,s="",f=[];W(e,145);var o=Array.isArray(t),l=a.e.r;t["!rows"]&&(l=Math.max(a.e.r,t["!rows"].length-1));for(var c=a.s.r;c<=l;++c){s=Xe(c),yh(e,t,a,c);var d=!1;if(c<=a.e.r)for(var h=a.s.c;h<=a.e.c;++h){c===a.s.r&&(f[h]=ze(h)),i=f[h]+s;var p=o?(t[c]||[])[h]:t[i];if(!p){d=!1;continue}d=O1(e,p,c,h,n,t,d)}}W(e,146)}function R1(e,t){!t||!t["!merges"]||(W(e,177,x1(t["!merges"].length)),t["!merges"].forEach(function(r){W(e,176,u1(r))}),W(e,178))}function N1(e,t){!t||!t["!cols"]||(W(e,390),t["!cols"].forEach(function(r,n){r&&W(e,60,_1(n,r))}),W(e,391))}function I1(e,t){!t||!t["!ref"]||(W(e,648),W(e,649,A1(Fe(t["!ref"]))),W(e,650))}function k1(e,t,r){t["!links"].forEach(function(n){if(n[1].Target){var a=Te(r,-1,n[1].Target.replace(/#.*$/,""),pe.HLINK);W(e,494,p1(n,a))}}),delete t["!links"]}function P1(e,t,r,n){if(t["!comments"].length>0){var a=Te(n,-1,"../drawings/vmlDrawing"+(r+1)+".vml",pe.VML);W(e,551,a0("rId"+a)),t["!legacy"]=a}}function L1(e,t,r,n){if(t["!autofilter"]){var a=t["!autofilter"],i=typeof a.ref=="string"?a.ref:Ie(a.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,f=fr(i);f.s.r==f.e.r&&(f.e.r=fr(t["!ref"]).e.r,i=Ie(f));for(var o=0;o<s.length;++o){var l=s[o];if(l.Name=="_xlnm._FilterDatabase"&&l.Sheet==n){l.Ref="'"+r.SheetNames[n]+"'!"+i;break}}o==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+i}),W(e,161,mt(Fe(i))),W(e,162)}}function M1(e,t,r){W(e,133),W(e,137,S1(t,r)),W(e,138),W(e,134)}function B1(e,t){t["!protect"]&&W(e,535,F1(t["!protect"]))}function U1(e,t,r,n){var a=Qe(),i=r.SheetNames[e],s=r.Sheets[i]||{},f=i;try{r&&r.Workbook&&(f=r.Workbook.Sheets[e].CodeName||f)}catch{}var o=Fe(s["!ref"]||"A1");if(o.e.c>16383||o.e.r>1048575){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");o.e.c=Math.min(o.e.c,16383),o.e.r=Math.min(o.e.c,1048575)}return s["!links"]=[],s["!comments"]=[],W(a,129),(r.vbaraw||s["!outline"])&&W(a,147,Nh(f,s["!outline"])),W(a,148,Oh(o)),M1(a,s,r.Workbook),N1(a,s),D1(a,s,e,t),B1(a,s),L1(a,s,r,e),R1(a,s),k1(a,s,n),s["!margins"]&&W(a,476,E1(s["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&I1(a,s),P1(a,s,e,n),W(a,130),a.end()}function b1(e,t){e.l+=10;var r=Ke(e);return{name:r}}var W1=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]];function H1(e){return!e.Workbook||!e.Workbook.WBProps?"false":mf(e.Workbook.WBProps.date1904)?"true":"false"}var V1="][*?/\\".split("");function Pi(e,t){if(e.length>31)throw new Error("Sheet names cannot exceed 31 chars");var r=!0;return V1.forEach(function(n){if(e.indexOf(n)!=-1)throw new Error("Sheet name cannot contain : \\ / ? * [ ]")}),r}function G1(e,t,r){e.forEach(function(n,a){Pi(n);for(var i=0;i<a;++i)if(n==e[i])throw new Error("Duplicate Sheet Name: "+n);if(r){var s=t&&t[a]&&t[a].CodeName||n;if(s.charCodeAt(0)==95&&s.length>22)throw new Error("Bad Code Name: Worksheet"+s)}})}function X1(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t=e.Workbook&&e.Workbook.Sheets||[];G1(e.SheetNames,t,!!e.vbaraw);for(var r=0;r<e.SheetNames.length;++r)uh(e.Sheets[e.SheetNames[r]],e.SheetNames[r],r)}function Li(e){var t=[ke];t[t.length]=Z("workbook",null,{xmlns:pt[0],"xmlns:r":Be.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,n={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(W1.forEach(function(f){e.Workbook.WBProps[f[0]]!=null&&e.Workbook.WBProps[f[0]]!=f[1]&&(n[f[0]]=e.Workbook.WBProps[f[0]])}),e.Workbook.WBProps.CodeName&&(n.codeName=e.Workbook.WBProps.CodeName,delete n.CodeName)),t[t.length]=Z("workbookPr",null,n);var a=e.Workbook&&e.Workbook.Sheets||[],i=0;if(a&&a[0]&&a[0].Hidden){for(t[t.length]="<bookViews>",i=0;i!=e.SheetNames.length&&!(!a[i]||!a[i].Hidden);++i);i==e.SheetNames.length&&(i=0),t[t.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",i=0;i!=e.SheetNames.length;++i){var s={name:Ee(e.SheetNames[i].slice(0,31))};if(s.sheetId=""+(i+1),s["r:id"]="rId"+(i+1),a[i])switch(a[i].Hidden){case 1:s.state="hidden";break;case 2:s.state="veryHidden";break}t[t.length]=Z("sheet",null,s)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(f){var o={name:f.Name};f.Comment&&(o.comment=f.Comment),f.Sheet!=null&&(o.localSheetId=""+f.Sheet),f.Hidden&&(o.hidden="1"),f.Ref&&(t[t.length]=Z("definedName",Ee(f.Ref),o))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function j1(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=Xn(e),r.name=Ke(e),r}function $1(e,t){return t||(t=B(127)),t.write_shift(4,e.Hidden),t.write_shift(4,e.iTabID),a0(e.strRelID,t),be(e.name.slice(0,31),t),t.length>t.l?t.slice(0,t.l):t}function z1(e,t){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var a=t>8?Ke(e):"";return a.length>0&&(r.CodeName=a),r.autoCompressPictures=!!(n&65536),r.backupFile=!!(n&64),r.checkCompatibility=!!(n&4096),r.date1904=!!(n&1),r.filterPrivacy=!!(n&8),r.hidePivotFieldList=!!(n&1024),r.promptedSolutions=!!(n&16),r.publishItems=!!(n&2048),r.refreshAllConnections=!!(n&262144),r.saveExternalLinkValues=!!(n&128),r.showBorderUnselectedTables=!!(n&4),r.showInkAnnotation=!!(n&32),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(n&32768),r.updateLinks=["userSet","never","always"][n>>8&3],r}function K1(e,t){t||(t=B(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),za(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}function Y1(e,t,r){var n=e.l+t;e.l+=4,e.l+=1;var a=e.read_shift(4),i=Wf(e),s=sh(e,0,r),f=n0(e);e.l=n;var o={Name:i,Ptg:s};return a<268435455&&(o.Sheet=a),f&&(o.Comment=f),o}function J1(e,t){W(e,143);for(var r=0;r!=t.SheetNames.length;++r){var n=t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,a={Hidden:n,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]};W(e,156,$1(a))}W(e,144)}function Z1(e,t){t||(t=B(127));for(var r=0;r!=4;++r)t.write_shift(4,0);return be("SheetJS",t),be(nn.version,t),be(nn.version,t),be("7262",t),t.length>t.l?t.slice(0,t.l):t}function q1(e,t){t||(t=B(29)),t.write_shift(-4,0),t.write_shift(-4,460),t.write_shift(4,28800),t.write_shift(4,17600),t.write_shift(4,500),t.write_shift(4,e),t.write_shift(4,e);var r=120;return t.write_shift(1,r),t.length>t.l?t.slice(0,t.l):t}function Q1(e,t){if(!(!t.Workbook||!t.Workbook.Sheets)){for(var r=t.Workbook.Sheets,n=0,a=-1,i=-1;n<r.length;++n)!r[n]||!r[n].Hidden&&a==-1?a=n:r[n].Hidden==1&&i==-1&&(i=n);i>a||(W(e,135),W(e,158,q1(a)),W(e,136))}}function eu(e,t){var r=Qe();return W(r,131),W(r,128,Z1()),W(r,153,K1(e.Workbook&&e.Workbook.WBProps||null)),Q1(r,e),J1(r,e),W(r,132),r.end()}function ru(e,t,r){return(t.slice(-4)===".bin"?eu:Li)(e)}function tu(e,t,r,n,a){return(t.slice(-4)===".bin"?U1:Ii)(e,r,n,a)}function nu(e,t,r){return(t.slice(-4)===".bin"?Eo:mi)(e,r)}function au(e,t,r){return(t.slice(-4)===".bin"?zl:xi)(e,r)}function iu(e,t,r){return(t.slice(-4)===".bin"?Bo:wi)(e)}function su(e){return(e.slice(-4)===".bin"?Do:Ti)()}function fu(e,t){var r=[];return e.Props&&r.push(tl(e.Props,t)),e.Custprops&&r.push(nl(e.Props,e.Custprops)),r.join("")}function lu(){return""}function ou(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach(function(n,a){var i=[];i.push(Z("NumberFormat",null,{"ss:Format":Ee(Re[n.numFmtId])}));var s={"ss:ID":"s"+(21+a)};r.push(Z("Style",i.join(""),s))}),Z("Styles",r.join(""))}function Mi(e){return Z("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+l0(e.Ref,{r:0,c:0})})}function cu(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],n=0;n<t.length;++n){var a=t[n];a.Sheet==null&&(a.Name.match(/^_xlfn\./)||r.push(Mi(a)))}return Z("Names",r.join(""))}function hu(e,t,r,n){if(!e||!((n||{}).Workbook||{}).Names)return"";for(var a=n.Workbook.Names,i=[],s=0;s<a.length;++s){var f=a[s];f.Sheet==r&&(f.Name.match(/^_xlfn\./)||i.push(Mi(f)))}return i.join("")}function uu(e,t,r,n){if(!e)return"";var a=[];if(e["!margins"]&&(a.push("<PageSetup>"),e["!margins"].header&&a.push(Z("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&a.push(Z("Footer",null,{"x:Margin":e["!margins"].footer})),a.push(Z("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),a.push("</PageSetup>")),n&&n.Workbook&&n.Workbook.Sheets&&n.Workbook.Sheets[r])if(n.Workbook.Sheets[r].Hidden)a.push(Z("Visible",n.Workbook.Sheets[r].Hidden==1?"SheetHidden":"SheetVeryHidden",{}));else{for(var i=0;i<r&&!(n.Workbook.Sheets[i]&&!n.Workbook.Sheets[i].Hidden);++i);i==r&&a.push("<Selected/>")}return((((n||{}).Workbook||{}).Views||[])[0]||{}).RTL&&a.push("<DisplayRightToLeft/>"),e["!protect"]&&(a.push(Ge("ProtectContents","True")),e["!protect"].objects&&a.push(Ge("ProtectObjects","True")),e["!protect"].scenarios&&a.push(Ge("ProtectScenarios","True")),e["!protect"].selectLockedCells!=null&&!e["!protect"].selectLockedCells?a.push(Ge("EnableSelection","NoSelection")):e["!protect"].selectUnlockedCells!=null&&!e["!protect"].selectUnlockedCells&&a.push(Ge("EnableSelection","UnlockedCells")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(s){e["!protect"][s[0]]&&a.push("<"+s[1]+"/>")})),a.length==0?"":Z("WorksheetOptions",a.join(""),{xmlns:ir.x})}function xu(e){return e.map(function(t){var r=vf(t.t||""),n=Z("ss:Data",r,{xmlns:"http://www.w3.org/TR/REC-html40"});return Z("Comment",n,{"ss:Author":t.a})}).join("")}function du(e,t,r,n,a,i,s){if(!e||e.v==null&&e.f==null)return"";var f={};if(e.f&&(f["ss:Formula"]="="+Ee(l0(e.f,s))),e.F&&e.F.slice(0,t.length)==t){var o=Ue(e.F.slice(t.length+1));f["ss:ArrayRange"]="RC:R"+(o.r==s.r?"":"["+(o.r-s.r)+"]")+"C"+(o.c==s.c?"":"["+(o.c-s.c)+"]")}if(e.l&&e.l.Target&&(f["ss:HRef"]=Ee(e.l.Target),e.l.Tooltip&&(f["x:HRefScreenTip"]=Ee(e.l.Tooltip))),r["!merges"])for(var l=r["!merges"],c=0;c!=l.length;++c)l[c].s.c!=s.c||l[c].s.r!=s.r||(l[c].e.c>l[c].s.c&&(f["ss:MergeAcross"]=l[c].e.c-l[c].s.c),l[c].e.r>l[c].s.r&&(f["ss:MergeDown"]=l[c].e.r-l[c].s.r));var d="",h="";switch(e.t){case"z":if(!n.sheetStubs)return"";break;case"n":d="Number",h=String(e.v);break;case"b":d="Boolean",h=e.v?"1":"0";break;case"e":d="Error",h=Vt[e.v];break;case"d":d="DateTime",h=new Date(e.v).toISOString(),e.z==null&&(e.z=e.z||Re[14]);break;case"s":d="String",h=pf(e.v||"");break}var p=Hr(n.cellXfs,e,n);f["ss:StyleID"]="s"+(21+p),f["ss:Index"]=s.c+1;var T=e.v!=null?h:"",u=e.t=="z"?"":'<Data ss:Type="'+d+'">'+T+"</Data>";return(e.c||[]).length>0&&(u+=xu(e.c)),Z("Cell",u,f)}function pu(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=vi(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function vu(e,t,r,n){if(!e["!ref"])return"";var a=Fe(e["!ref"]),i=e["!merges"]||[],s=0,f=[];e["!cols"]&&e["!cols"].forEach(function(g,C){s0(g);var O=!!g.width,F=Fn(C,g),M={"ss:Index":C+1};O&&(M["ss:Width"]=xn(F.width)),g.hidden&&(M["ss:Hidden"]="1"),f.push(Z("Column",null,M))});for(var o=Array.isArray(e),l=a.s.r;l<=a.e.r;++l){for(var c=[pu(l,(e["!rows"]||[])[l])],d=a.s.c;d<=a.e.c;++d){var h=!1;for(s=0;s!=i.length;++s)if(!(i[s].s.c>d)&&!(i[s].s.r>l)&&!(i[s].e.c<d)&&!(i[s].e.r<l)){(i[s].s.c!=d||i[s].s.r!=l)&&(h=!0);break}if(!h){var p={r:l,c:d},T=we(p),u=o?(e[l]||[])[d]:e[T];c.push(du(u,T,e,t,r,n,p))}}c.push("</Row>"),c.length>2&&f.push(c.join(""))}return f.join("")}function mu(e,t,r){var n=[],a=r.SheetNames[e],i=r.Sheets[a],s=i?hu(i,t,e,r):"";return s.length>0&&n.push("<Names>"+s+"</Names>"),s=i?vu(i,t,e,r):"",s.length>0&&n.push("<Table>"+s+"</Table>"),n.push(uu(i,t,e,r)),n.join("")}function gu(e,t){t||(t={}),e.SSF||(e.SSF=rr(Re)),e.SSF&&(Tn(),_n(e.SSF),t.revssf=En(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],Hr(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(fu(e,t)),r.push(lu()),r.push(""),r.push("");for(var n=0;n<e.SheetNames.length;++n)r.push(Z("Worksheet",mu(n,t,e),{"ss:Name":Ee(e.SheetNames[n])}));return r[2]=ou(e,t),r[3]=cu(e),ke+Z("Workbook",r.join(""),{xmlns:ir.ss,"xmlns:o":ir.o,"xmlns:x":ir.x,"xmlns:ss":ir.ss,"xmlns:dt":ir.dt,"xmlns:html":ir.html})}var bn={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function _u(e,t){var r=[],n=[],a=[],i=0,s,f=L0(K0,"n"),o=L0(Y0,"n");if(e.Props)for(s=je(e.Props),i=0;i<s.length;++i)(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(o,s[i])?n:a).push([s[i],e.Props[s[i]]]);if(e.Custprops)for(s=je(e.Custprops),i=0;i<s.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},s[i])||(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(o,s[i])?n:a).push([s[i],e.Custprops[s[i]]]);var l=[];for(i=0;i<a.length;++i)si.indexOf(a[i][0])>-1||ni.indexOf(a[i][0])>-1||a[i][1]!=null&&l.push(a[i]);n.length&&Se.utils.cfb_add(t,"/SummaryInformation",ea(n,bn.SI,o,Y0)),(r.length||l.length)&&Se.utils.cfb_add(t,"/DocumentSummaryInformation",ea(r,bn.DSI,f,K0,l.length?l:null,bn.UDI))}function Tu(e,t){var r=t||{},n=Se.utils.cfb_new({root:"R"}),a="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":a="/Workbook",r.biff=8;break;case"biff5":a="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return Se.utils.cfb_add(n,a,Bi(e,r)),r.biff==8&&(e.Props||e.Custprops)&&_u(e,n),r.biff==8&&e.vbaraw&&Uo(n,Se.read(e.vbaraw,{type:typeof e.vbaraw=="string"?"binary":"buffer"})),n}var Eu={0:{f:Ah},1:{f:Ih},2:{f:qh},3:{f:Wh},4:{f:Mh},5:{f:Kh},6:{f:n1},7:{f:Xh},8:{f:c1},9:{f:o1},10:{f:f1},11:{f:l1},12:{f:Ph},13:{f:e1},14:{f:Vh},15:{f:Uh},16:{f:Jh},17:{f:i1},18:{f:$h},19:{f:t0},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:Y1},40:{},42:{},43:{f:to},44:{f:eo},45:{f:io},46:{f:fo},47:{f:so},48:{},49:{f:kf},50:{},51:{f:Ao},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:Pl},62:{f:t1},63:{f:Ro},64:{f:y1},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:wr,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:w1},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:Rh},148:{f:Ch,p:16},151:{f:v1},152:{},153:{f:z1},154:{},155:{},156:{f:j1},157:{},158:{},159:{T:1,f:Xl},160:{T:-1},161:{T:1,f:rt},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:h1},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:wo},336:{T:-1},337:{f:Co,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:Xn},357:{},358:{},359:{},360:{T:1},361:{},362:{f:Ol},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:m1},427:{f:g1},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:T1},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:Dh},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:d1},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:Xn},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:Lo},633:{T:1},634:{T:-1},635:{T:1,f:ko},636:{T:-1},637:{f:Bf},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:b1},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:C1},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}};function q(e,t,r,n){var a=t;if(!isNaN(a)){var i=n||(r||[]).length||0,s=e.next(4);s.write_shift(2,a),s.write_shift(2,i),i>0&&Qn(r)&&e.push(r)}}function wu(e,t,r,n){var a=(r||[]).length||0;if(a<=8224)return q(e,t,r,a);var i=t;if(!isNaN(i)){for(var s=r.parts||[],f=0,o=0,l=0;l+(s[f]||8224)<=8224;)l+=s[f]||8224,f++;var c=e.next(4);for(c.write_shift(2,i),c.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l;o<a;){for(c=e.next(4),c.write_shift(2,60),l=0;l+(s[f]||8224)<=8224;)l+=s[f]||8224,f++;c.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l}}}function Xt(e,t,r){return e||(e=B(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function Su(e,t,r,n){var a=B(9);return Xt(a,e,t),li(r,n||"b",a),a}function Au(e,t,r){var n=B(8+2*r.length);return Xt(n,e,t),n.write_shift(1,r.length),n.write_shift(r.length,r,"sbcs"),n.l<n.length?n.slice(0,n.l):n}function Fu(e,t,r,n){if(t.v!=null)switch(t.t){case"d":case"n":var a=t.t=="d"?er(qe(t.v)):t.v;a==(a|0)&&a>=0&&a<65536?q(e,2,Ul(r,n,a)):q(e,3,Bl(r,n,a));return;case"b":case"e":q(e,5,Su(r,n,t.v,t.t));return;case"s":case"str":q(e,4,Au(r,n,(t.v||"").slice(0,255)));return}q(e,1,Xt(null,r,n))}function yu(e,t,r,n){var a=Array.isArray(t),i=Fe(t["!ref"]||"A1"),s,f="",o=[];if(i.e.c>255||i.e.r>16383){if(n.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),s=Ie(i)}for(var l=i.s.r;l<=i.e.r;++l){f=Xe(l);for(var c=i.s.c;c<=i.e.c;++c){l===i.s.r&&(o[c]=ze(c)),s=o[c]+f;var d=a?(t[l]||[])[c]:t[s];d&&Fu(e,d,l,c)}}}function Cu(e,t){for(var r=t||{},n=Qe(),a=0,i=0;i<e.SheetNames.length;++i)e.SheetNames[i]==r.sheet&&(a=i);if(a==0&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return q(n,r.biff==4?1033:r.biff==3?521:9,i0(e,16,r)),yu(n,e.Sheets[e.SheetNames[a]],a,r),q(n,10),n.end()}function Ou(e,t,r){q(e,49,Tl({sz:12,name:"Arial"},r))}function Du(e,t,r){t&&[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&q(e,1054,Sl(a,t[a],r))})}function Ru(e,t){var r=B(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),q(e,2151,r),r=B(39),r.write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),hi(Fe(t["!ref"]||"A1"),r),r.write_shift(4,4),q(e,2152,r)}function Nu(e,t){for(var r=0;r<16;++r)q(e,224,ta({numFmtId:0,style:!0},0,t));t.cellXfs.forEach(function(n){q(e,224,ta(n,0,t))})}function Iu(e,t){for(var r=0;r<t["!links"].length;++r){var n=t["!links"][r];q(e,440,Nl(n)),n[1].Tooltip&&q(e,2048,Il(n))}delete t["!links"]}function ku(e,t){if(t){var r=0;t.forEach(function(n,a){++r<=256&&n&&q(e,125,Ll(Fn(a,n),a))})}}function Pu(e,t,r,n,a){var i=16+Hr(a.cellXfs,t,a);if(t.v==null&&!t.bf){q(e,513,Jr(r,n,i));return}if(t.bf)q(e,6,ah(t,r,n,a,i));else switch(t.t){case"d":case"n":var s=t.t=="d"?er(qe(t.v)):t.v;q(e,515,Cl(r,n,s,i));break;case"b":case"e":q(e,517,yl(r,n,t.v,i,a,t.t));break;case"s":case"str":if(a.bookSST){var f=c0(a.Strings,t.v,a.revStrings);q(e,253,El(r,n,f,i))}else q(e,516,wl(r,n,(t.v||"").slice(0,255),i,a));break;default:q(e,513,Jr(r,n,i))}}function Lu(e,t,r){var n=Qe(),a=r.SheetNames[e],i=r.Sheets[a]||{},s=(r||{}).Workbook||{},f=(s.Sheets||[])[e]||{},o=Array.isArray(i),l=t.biff==8,c,d="",h=[],p=Fe(i["!ref"]||"A1"),T=l?65536:16384;if(p.e.c>255||p.e.r>=T){if(t.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:IV16384");p.e.c=Math.min(p.e.c,255),p.e.r=Math.min(p.e.c,T-1)}q(n,2057,i0(r,16,t)),q(n,13,hr(1)),q(n,12,hr(100)),q(n,15,Ze(!0)),q(n,17,Ze(!1)),q(n,16,Yr(.001)),q(n,95,Ze(!0)),q(n,42,Ze(!1)),q(n,43,Ze(!1)),q(n,130,hr(1)),q(n,128,Fl()),q(n,131,Ze(!1)),q(n,132,Ze(!1)),l&&ku(n,i["!cols"]),q(n,512,Al(p,t)),l&&(i["!links"]=[]);for(var u=p.s.r;u<=p.e.r;++u){d=Xe(u);for(var g=p.s.c;g<=p.e.c;++g){u===p.s.r&&(h[g]=ze(g)),c=h[g]+d;var C=o?(i[u]||[])[g]:i[c];C&&(Pu(n,C,u,g,t),l&&C.l&&i["!links"].push([c,C.l]))}}var O=f.CodeName||f.name||a;return l&&q(n,574,_l((s.Views||[])[0])),l&&(i["!merges"]||[]).length&&q(n,229,Rl(i["!merges"])),l&&Iu(n,i),q(n,442,ci(O)),l&&Ru(n,i),q(n,10),n.end()}function Mu(e,t,r){var n=Qe(),a=(e||{}).Workbook||{},i=a.Sheets||[],s=a.WBProps||{},f=r.biff==8,o=r.biff==5;if(q(n,2057,i0(e,5,r)),r.bookType=="xla"&&q(n,135),q(n,225,f?hr(1200):null),q(n,193,sl(2)),o&&q(n,191),o&&q(n,192),q(n,226),q(n,92,pl("SheetJS",r)),q(n,66,hr(f?1200:1252)),f&&q(n,353,hr(0)),f&&q(n,448),q(n,317,Ml(e.SheetNames.length)),f&&e.vbaraw&&q(n,211),f&&e.vbaraw){var l=s.CodeName||"ThisWorkbook";q(n,442,ci(l))}q(n,156,hr(17)),q(n,25,Ze(!1)),q(n,18,Ze(!1)),q(n,19,hr(0)),f&&q(n,431,Ze(!1)),f&&q(n,444,hr(0)),q(n,61,gl()),q(n,64,Ze(!1)),q(n,141,hr(0)),q(n,34,Ze(H1(e)=="true")),q(n,14,Ze(!0)),f&&q(n,439,Ze(!1)),q(n,218,hr(0)),Ou(n,e,r),Du(n,e.SSF,r),Nu(n,r),f&&q(n,352,Ze(!1));var c=n.end(),d=Qe();f&&q(d,140,kl()),f&&r.Strings&&wu(d,252,ml(r.Strings)),q(d,10);var h=d.end(),p=Qe(),T=0,u=0;for(u=0;u<e.SheetNames.length;++u)T+=(f?12:11)+(f?2:1)*e.SheetNames[u].length;var g=c.length+T+h.length;for(u=0;u<e.SheetNames.length;++u){var C=i[u]||{};q(p,133,vl({pos:g,hs:C.Hidden||0,dt:0,name:e.SheetNames[u]},r)),g+=t[u].length}var O=p.end();if(T!=O.length)throw new Error("BS8 "+T+" != "+O.length);var F=[];return c.length&&F.push(c),O.length&&F.push(O),h.length&&F.push(h),Ve(F)}function Bu(e,t){var r=t||{},n=[];e&&!e.SSF&&(e.SSF=rr(Re)),e&&e.SSF&&(Tn(),_n(e.SSF),r.revssf=En(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,h0(r),r.cellXfs=[],Hr(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var a=0;a<e.SheetNames.length;++a)n[n.length]=Lu(a,r,e);return n.unshift(Mu(e,n,r)),Ve(n)}function Bi(e,t){for(var r=0;r<=e.SheetNames.length;++r){var n=e.Sheets[e.SheetNames[r]];if(!(!n||!n["!ref"])){var a=fr(n["!ref"]);a.e.c>255&&typeof console<"u"&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}}var i=t||{};switch(i.biff||2){case 8:case 5:return Bu(e,t);case 4:case 3:case 2:return Cu(e,t)}throw new Error("invalid type "+i.bookType+" for BIFF")}function Uu(e,t,r,n){for(var a=e["!merges"]||[],i=[],s=t.s.c;s<=t.e.c;++s){for(var f=0,o=0,l=0;l<a.length;++l)if(!(a[l].s.r>r||a[l].s.c>s)&&!(a[l].e.r<r||a[l].e.c<s)){if(a[l].s.r<r||a[l].s.c<s){f=-1;break}f=a[l].e.r-a[l].s.r+1,o=a[l].e.c-a[l].s.c+1;break}if(!(f<0)){var c=we({r,c:s}),d=n.dense?(e[r]||[])[s]:e[c],h=d&&d.v!=null&&(d.h||df(d.w||(Nr(d),d.w)||""))||"",p={};f>1&&(p.rowspan=f),o>1&&(p.colspan=o),n.editable?h='<span contenteditable="true">'+h+"</span>":d&&(p["data-t"]=d&&d.t||"z",d.v!=null&&(p["data-v"]=d.v),d.z!=null&&(p["data-z"]=d.z),d.l&&(d.l.Target||"#").charAt(0)!="#"&&(h='<a href="'+d.l.Target+'">'+h+"</a>")),p.id=(n.id||"sjs")+"-"+c,i.push(Z("td",h,p))}}var T="<tr>";return T+i.join("")+"</tr>"}var bu='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',Wu="</body></html>";function Hu(e,t,r){var n=[];return n.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function Ui(e,t){var r=t||{},n=r.header!=null?r.header:bu,a=r.footer!=null?r.footer:Wu,i=[n],s=fr(e["!ref"]);r.dense=Array.isArray(e),i.push(Hu(e,s,r));for(var f=s.s.r;f<=s.e.r;++f)i.push(Uu(e,s,f,r));return i.push("</table>"+a),i.join("")}function bi(e,t,r){var n=r||{},a=0,i=0;if(n.origin!=null)if(typeof n.origin=="number")a=n.origin;else{var s=typeof n.origin=="string"?Ue(n.origin):n.origin;a=s.r,i=s.c}var f=t.getElementsByTagName("tr"),o=Math.min(n.sheetRows||1e7,f.length),l={s:{r:0,c:0},e:{r:a,c:i}};if(e["!ref"]){var c=fr(e["!ref"]);l.s.r=Math.min(l.s.r,c.s.r),l.s.c=Math.min(l.s.c,c.s.c),l.e.r=Math.max(l.e.r,c.e.r),l.e.c=Math.max(l.e.c,c.e.c),a==-1&&(l.e.r=a=c.e.r+1)}var d=[],h=0,p=e["!rows"]||(e["!rows"]=[]),T=0,u=0,g=0,C=0,O=0,F=0;for(e["!cols"]||(e["!cols"]=[]);T<f.length&&u<o;++T){var M=f[T];if(oa(M)){if(n.display)continue;p[u]={hidden:!0}}var Y=M.children;for(g=C=0;g<Y.length;++g){var ne=Y[g];if(!(n.display&&oa(ne))){var D=ne.hasAttribute("data-v")?ne.getAttribute("data-v"):ne.hasAttribute("v")?ne.getAttribute("v"):gf(ne.innerHTML),b=ne.getAttribute("data-z")||ne.getAttribute("z");for(h=0;h<d.length;++h){var P=d[h];P.s.c==C+i&&P.s.r<u+a&&u+a<=P.e.r&&(C=P.e.c+1-i,h=-1)}F=+ne.getAttribute("colspan")||1,((O=+ne.getAttribute("rowspan")||1)>1||F>1)&&d.push({s:{r:u+a,c:C+i},e:{r:u+a+(O||1)-1,c:C+i+(F||1)-1}});var V={t:"s",v:D},G=ne.getAttribute("data-t")||ne.getAttribute("t")||"";D!=null&&(D.length==0?V.t=G||"z":n.raw||D.trim().length==0||G=="s"||(D==="TRUE"?V={t:"b",v:!0}:D==="FALSE"?V={t:"b",v:!1}:isNaN(Or(D))?isNaN(Pt(D).getDate())||(V={t:"d",v:qe(D)},n.cellDates||(V={t:"n",v:er(V.v)}),V.z=n.dateNF||Re[14]):V={t:"n",v:Or(D)})),V.z===void 0&&b!=null&&(V.z=b);var j="",re=ne.getElementsByTagName("A");if(re&&re.length)for(var xe=0;xe<re.length&&!(re[xe].hasAttribute("href")&&(j=re[xe].getAttribute("href"),j.charAt(0)!="#"));++xe);j&&j.charAt(0)!="#"&&(V.l={Target:j}),n.dense?(e[u+a]||(e[u+a]=[]),e[u+a][C+i]=V):e[we({c:C+i,r:u+a})]=V,l.e.c<C+i&&(l.e.c=C+i),C+=F}}++u}return d.length&&(e["!merges"]=(e["!merges"]||[]).concat(d)),l.e.r=Math.max(l.e.r,u-1+a),e["!ref"]=Ie(l),u>=o&&(e["!fullref"]=Ie((l.e.r=f.length-T+u-1+a,l))),e}function Wi(e,t){var r=t||{},n=r.dense?[]:{};return bi(n,e,t)}function Vu(e,t){return Zr(Wi(e,t),t)}function oa(e){var t="",r=Gu(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),t==="none"}function Gu(e){return e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle=="function"?e.ownerDocument.defaultView.getComputedStyle:typeof getComputedStyle=="function"?getComputedStyle:null}var Xu=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+Mt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return ke+t}}(),ca=function(){var e=function(i){return Ee(i).replace(/  +/g,function(s){return'<text:s text:c="'+s.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")},t=`          <table:table-cell />
`,r=`          <table:covered-table-cell/>
`,n=function(i,s,f){var o=[];o.push('      <table:table table:name="'+Ee(s.SheetNames[f])+`" table:style-name="ta1">
`);var l=0,c=0,d=fr(i["!ref"]||"A1"),h=i["!merges"]||[],p=0,T=Array.isArray(i);if(i["!cols"])for(c=0;c<=d.e.c;++c)o.push("        <table:table-column"+(i["!cols"][c]?' table:style-name="co'+i["!cols"][c].ods+'"':"")+`></table:table-column>
`);var u="",g=i["!rows"]||[];for(l=0;l<d.s.r;++l)u=g[l]?' table:style-name="ro'+g[l].ods+'"':"",o.push("        <table:table-row"+u+`></table:table-row>
`);for(;l<=d.e.r;++l){for(u=g[l]?' table:style-name="ro'+g[l].ods+'"':"",o.push("        <table:table-row"+u+`>
`),c=0;c<d.s.c;++c)o.push(t);for(;c<=d.e.c;++c){var C=!1,O={},F="";for(p=0;p!=h.length;++p)if(!(h[p].s.c>c)&&!(h[p].s.r>l)&&!(h[p].e.c<c)&&!(h[p].e.r<l)){(h[p].s.c!=c||h[p].s.r!=l)&&(C=!0),O["table:number-columns-spanned"]=h[p].e.c-h[p].s.c+1,O["table:number-rows-spanned"]=h[p].e.r-h[p].s.r+1;break}if(C){o.push(r);continue}var M=we({r:l,c}),Y=T?(i[l]||[])[c]:i[M];if(Y&&Y.f&&(O["table:formula"]=Ee(ch(Y.f)),Y.F&&Y.F.slice(0,M.length)==M)){var ne=fr(Y.F);O["table:number-matrix-columns-spanned"]=ne.e.c-ne.s.c+1,O["table:number-matrix-rows-spanned"]=ne.e.r-ne.s.r+1}if(!Y){o.push(t);continue}switch(Y.t){case"b":F=Y.v?"TRUE":"FALSE",O["office:value-type"]="boolean",O["office:boolean-value"]=Y.v?"true":"false";break;case"n":F=Y.w||String(Y.v||0),O["office:value-type"]="float",O["office:value"]=Y.v||0;break;case"s":case"str":F=Y.v==null?"":Y.v,O["office:value-type"]="string";break;case"d":F=Y.w||qe(Y.v).toISOString(),O["office:value-type"]="date",O["office:date-value"]=qe(Y.v).toISOString(),O["table:style-name"]="ce1";break;default:o.push(t);continue}var D=e(F);if(Y.l&&Y.l.Target){var b=Y.l.Target;b=b.charAt(0)=="#"?"#"+hh(b.slice(1)):b,b.charAt(0)!="#"&&!b.match(/^\w+:/)&&(b="../"+b),D=Z("text:a",D,{"xlink:href":b.replace(/&/g,"&amp;")})}o.push("          "+Z("table:table-cell",Z("text:p",D,{}),O)+`
`)}o.push(`        </table:table-row>
`)}return o.push(`      </table:table>
`),o.join("")},a=function(i,s){i.push(` <office:automatic-styles>
`),i.push(`  <number:date-style style:name="N37" number:automatic-order="true">
`),i.push(`   <number:month number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:day number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:year/>
`),i.push(`  </number:date-style>
`);var f=0;s.SheetNames.map(function(l){return s.Sheets[l]}).forEach(function(l){if(l&&l["!cols"]){for(var c=0;c<l["!cols"].length;++c)if(l["!cols"][c]){var d=l["!cols"][c];if(d.width==null&&d.wpx==null&&d.wch==null)continue;s0(d),d.ods=f;var h=l["!cols"][c].wpx+"px";i.push('  <style:style style:name="co'+f+`" style:family="table-column">
`),i.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+h+`"/>
`),i.push(`  </style:style>
`),++f}}});var o=0;s.SheetNames.map(function(l){return s.Sheets[l]}).forEach(function(l){if(l&&l["!rows"]){for(var c=0;c<l["!rows"].length;++c)if(l["!rows"][c]){l["!rows"][c].ods=o;var d=l["!rows"][c].hpx+"px";i.push('  <style:style style:name="ro'+o+`" style:family="table-row">
`),i.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+d+`"/>
`),i.push(`  </style:style>
`),++o}}}),i.push(`  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">
`),i.push(`   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>
`),i.push(`  </style:style>
`),i.push(`  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>
`),i.push(` </office:automatic-styles>
`)};return function(s,f){var o=[ke],l=Mt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),c=Mt({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});f.bookType=="fods"?(o.push("<office:document"+l+c+`>
`),o.push(ri().replace(/office:document-meta/g,"office:meta"))):o.push("<office:document-content"+l+`>
`),a(o,s),o.push(`  <office:body>
`),o.push(`    <office:spreadsheet>
`);for(var d=0;d!=s.SheetNames.length;++d)o.push(n(s.Sheets[s.SheetNames[d]],s,d));return o.push(`    </office:spreadsheet>
`),o.push(`  </office:body>
`),f.bookType=="fods"?o.push("</office:document>"):o.push("</office:document-content>"),o.join("")}}();function Hi(e,t){if(t.bookType=="fods")return ca(e,t);var r=Yn(),n="",a=[],i=[];return n="mimetype",he(r,n,"application/vnd.oasis.opendocument.spreadsheet"),n="content.xml",he(r,n,ca(e,t)),a.push([n,"text/xml"]),i.push([n,"ContentFile"]),n="styles.xml",he(r,n,Xu(e,t)),a.push([n,"text/xml"]),i.push([n,"StylesFile"]),n="meta.xml",he(r,n,ke+ri()),a.push([n,"text/xml"]),i.push([n,"MetadataFile"]),n="manifest.rdf",he(r,n,rl(i)),a.push([n,"application/rdf+xml"]),n="META-INF/manifest.xml",he(r,n,Qf(a)),r}/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function vn(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function ju(e){return typeof TextEncoder<"u"?new TextEncoder().encode(e):vr(Lt(e))}function $u(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var n=0;n<t.length;++n)if(e[r+n]!=t[n])continue e;return!0}return!1}function Wr(e){var t=e.reduce(function(a,i){return a+i.length},0),r=new Uint8Array(t),n=0;return e.forEach(function(a){r.set(a,n),n+=a.length}),r}function zu(e,t,r){var n=Math.floor(r==0?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,a=r/Math.pow(10,n-6176);e[t+15]|=n>>7,e[t+14]|=(n&127)<<1;for(var i=0;a>=1;++i,a/=256)e[t+i]=a&255;e[t+15]|=r>=0?0:128}function Bt(e,t){var r=t?t[0]:0,n=e[r]&127;e:if(e[r++]>=128&&(n|=(e[r]&127)<<7,e[r++]<128||(n|=(e[r]&127)<<14,e[r++]<128)||(n|=(e[r]&127)<<21,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,28),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,35),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,42),++r,e[r++]<128)))break e;return t&&(t[0]=r),n}function _e(e){var t=new Uint8Array(7);t[0]=e&127;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103))break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function xt(e){var t=0,r=e[t]&127;e:if(e[t++]>=128){if(r|=(e[t]&127)<<7,e[t++]<128||(r|=(e[t]&127)<<14,e[t++]<128)||(r|=(e[t]&127)<<21,e[t++]<128))break e;r|=(e[t]&127)<<28}return r}function Le(e){for(var t=[],r=[0];r[0]<e.length;){var n=r[0],a=Bt(e,r),i=a&7;a=Math.floor(a/8);var s=0,f;if(a==0)break;switch(i){case 0:{for(var o=r[0];e[r[0]++]>=128;);f=e.slice(o,r[0])}break;case 5:s=4,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 1:s=8,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 2:s=Bt(e,r),f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 3:case 4:default:throw new Error("PB Type ".concat(i," for Field ").concat(a," at offset ").concat(n))}var l={data:f,type:i};t[a]==null?t[a]=[l]:t[a].push(l)}return t}function We(e){var t=[];return e.forEach(function(r,n){r.forEach(function(a){a.data&&(t.push(_e(n*8+a.type)),a.type==2&&t.push(_e(a.data.length)),t.push(a.data))})}),Wr(t)}function dr(e){for(var t,r=[],n=[0];n[0]<e.length;){var a=Bt(e,n),i=Le(e.slice(n[0],n[0]+a));n[0]+=a;var s={id:xt(i[1][0].data),messages:[]};i[2].forEach(function(f){var o=Le(f.data),l=xt(o[3][0].data);s.messages.push({meta:o,data:e.slice(n[0],n[0]+l)}),n[0]+=l}),(t=i[3])!=null&&t[0]&&(s.merge=xt(i[3][0].data)>>>0>0),r.push(s)}return r}function it(e){var t=[];return e.forEach(function(r){var n=[];n[1]=[{data:_e(r.id),type:0}],n[2]=[],r.merge!=null&&(n[3]=[{data:_e(+!!r.merge),type:0}]);var a=[];r.messages.forEach(function(s){a.push(s.data),s.meta[3]=[{type:0,data:_e(s.data.length)}],n[2].push({data:We(s.meta),type:2})});var i=We(n);t.push(_e(i.length)),t.push(i),a.forEach(function(s){return t.push(s)})}),Wr(t)}function Ku(e,t){if(e!=0)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],n=Bt(t,r),a=[];r[0]<t.length;){var i=t[r[0]]&3;if(i==0){var s=t[r[0]++]>>2;if(s<60)++s;else{var f=s-59;s=t[r[0]],f>1&&(s|=t[r[0]+1]<<8),f>2&&(s|=t[r[0]+2]<<16),f>3&&(s|=t[r[0]+3]<<24),s>>>=0,s++,r[0]+=f}a.push(t.slice(r[0],r[0]+s)),r[0]+=s;continue}else{var o=0,l=0;if(i==1?(l=(t[r[0]]>>2&7)+4,o=(t[r[0]++]&224)<<3,o|=t[r[0]++]):(l=(t[r[0]++]>>2)+1,i==2?(o=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(o=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),a=[Wr(a)],o==0)throw new Error("Invalid offset 0");if(o>a[0].length)throw new Error("Invalid offset beyond length");if(l>=o)for(a.push(a[0].slice(-o)),l-=o;l>=a[a.length-1].length;)a.push(a[a.length-1]),l-=a[a.length-1].length;a.push(a[0].slice(-o,-o+l))}}var c=Wr(a);if(c.length!=n)throw new Error("Unexpected length: ".concat(c.length," != ").concat(n));return c}function pr(e){for(var t=[],r=0;r<e.length;){var n=e[r++],a=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(Ku(n,e.slice(r,r+a))),r+=a}if(r!==e.length)throw new Error("data is not a valid framed stream!");return Wr(t)}function st(e){for(var t=[],r=0;r<e.length;){var n=Math.min(e.length-r,268435455),a=new Uint8Array(4);t.push(a);var i=_e(n),s=i.length;t.push(i),n<=60?(s++,t.push(new Uint8Array([n-1<<2]))):n<=256?(s+=2,t.push(new Uint8Array([240,n-1&255]))):n<=65536?(s+=3,t.push(new Uint8Array([244,n-1&255,n-1>>8&255]))):n<=16777216?(s+=4,t.push(new Uint8Array([248,n-1&255,n-1>>8&255,n-1>>16&255]))):n<=4294967296&&(s+=5,t.push(new Uint8Array([252,n-1&255,n-1>>8&255,n-1>>16&255,n-1>>>24&255]))),t.push(e.slice(r,r+n)),s+=n,a[0]=0,a[1]=s&255,a[2]=s>>8&255,a[3]=s>>16&255,r+=n}return Wr(t)}function Wn(e,t){var r=new Uint8Array(32),n=vn(r),a=12,i=0;switch(r[0]=5,e.t){case"n":r[1]=2,zu(r,a,e.v),i|=1,a+=16;break;case"b":r[1]=6,n.setFloat64(a,e.v?1:0,!0),i|=2,a+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=8,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(8,i,!0),r.slice(0,a)}function Hn(e,t){var r=new Uint8Array(32),n=vn(r),a=12,i=0;switch(r[0]=3,e.t){case"n":r[2]=2,n.setFloat64(a,e.v,!0),i|=32,a+=8;break;case"b":r[2]=6,n.setFloat64(a,e.v?1:0,!0),i|=32,a+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=16,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(4,i,!0),r.slice(0,a)}function Lr(e){var t=Le(e);return Bt(t[1][0].data)}function Yu(e,t,r){var n,a,i,s;if(!((n=e[6])!=null&&n[0])||!((a=e[7])!=null&&a[0]))throw"Mutation only works on post-BNC storages!";var f=((s=(i=e[8])==null?void 0:i[0])==null?void 0:s.data)&&xt(e[8][0].data)>0||!1;if(f)throw"Math only works with normal offsets";for(var o=0,l=vn(e[7][0].data),c=0,d=[],h=vn(e[4][0].data),p=0,T=[],u=0;u<t.length;++u){if(t[u]==null){l.setUint16(u*2,65535,!0),h.setUint16(u*2,65535);continue}l.setUint16(u*2,c,!0),h.setUint16(u*2,p,!0);var g,C;switch(typeof t[u]){case"string":g=Wn({t:"s",v:t[u]},r),C=Hn({t:"s",v:t[u]},r);break;case"number":g=Wn({t:"n",v:t[u]},r),C=Hn({t:"n",v:t[u]},r);break;case"boolean":g=Wn({t:"b",v:t[u]},r),C=Hn({t:"b",v:t[u]},r);break;default:throw new Error("Unsupported value "+t[u])}d.push(g),c+=g.length,T.push(C),p+=C.length,++o}for(e[2][0].data=_e(o);u<e[7][0].data.length/2;++u)l.setUint16(u*2,65535,!0),h.setUint16(u*2,65535,!0);return e[6][0].data=Wr(d),e[3][0].data=Wr(T),o}function Ju(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var n=fr(r["!ref"]);n.s.r=n.s.c=0;var a=!1;n.e.c>9&&(a=!0,n.e.c=9),n.e.r>49&&(a=!0,n.e.r=49),a&&console.error("The Numbers writer is currently limited to ".concat(Ie(n)));var i=mn(r,{range:n,header:1}),s=["~Sh33tJ5~"];i.forEach(function(L){return L.forEach(function(y){typeof y=="string"&&s.push(y)})});var f={},o=[],l=Se.read(t.numbers,{type:"base64"});l.FileIndex.map(function(L,y){return[L,l.FullPaths[y]]}).forEach(function(L){var y=L[0],A=L[1];if(y.type==2&&y.name.match(/\.iwa/)){var H=y.content,le=pr(H),oe=dr(le);oe.forEach(function(fe){o.push(fe.id),f[fe.id]={deps:[],location:A,type:xt(fe.messages[0].meta[1][0].data)}})}}),o.sort(function(L,y){return L-y});var c=o.filter(function(L){return L>1}).map(function(L){return[L,_e(L)]});l.FileIndex.map(function(L,y){return[L,l.FullPaths[y]]}).forEach(function(L){var y=L[0];if(L[1],!!y.name.match(/\.iwa/)){var A=dr(pr(y.content));A.forEach(function(H){H.messages.forEach(function(le){c.forEach(function(oe){H.messages.some(function(fe){return xt(fe.meta[1][0].data)!=11006&&$u(fe.data,oe[1])})&&f[oe[0]].deps.push(H.id)})})})}});for(var d=Se.find(l,f[1].location),h=dr(pr(d.content)),p,T=0;T<h.length;++T){var u=h[T];u.id==1&&(p=u)}var g=Lr(Le(p.messages[0].data)[1][0].data);for(d=Se.find(l,f[g].location),h=dr(pr(d.content)),T=0;T<h.length;++T)u=h[T],u.id==g&&(p=u);for(g=Lr(Le(p.messages[0].data)[2][0].data),d=Se.find(l,f[g].location),h=dr(pr(d.content)),T=0;T<h.length;++T)u=h[T],u.id==g&&(p=u);for(g=Lr(Le(p.messages[0].data)[2][0].data),d=Se.find(l,f[g].location),h=dr(pr(d.content)),T=0;T<h.length;++T)u=h[T],u.id==g&&(p=u);var C=Le(p.messages[0].data);{C[6][0].data=_e(n.e.r+1),C[7][0].data=_e(n.e.c+1);var O=Lr(C[46][0].data),F=Se.find(l,f[O].location),M=dr(pr(F.content));{for(var Y=0;Y<M.length&&M[Y].id!=O;++Y);if(M[Y].id!=O)throw"Bad ColumnRowUIDMapArchive";var ne=Le(M[Y].messages[0].data);ne[1]=[],ne[2]=[],ne[3]=[];for(var D=0;D<=n.e.c;++D){var b=[];b[1]=b[2]=[{type:0,data:_e(D+420690)}],ne[1].push({type:2,data:We(b)}),ne[2].push({type:0,data:_e(D)}),ne[3].push({type:0,data:_e(D)})}ne[4]=[],ne[5]=[],ne[6]=[];for(var P=0;P<=n.e.r;++P)b=[],b[1]=b[2]=[{type:0,data:_e(P+726270)}],ne[4].push({type:2,data:We(b)}),ne[5].push({type:0,data:_e(P)}),ne[6].push({type:0,data:_e(P)});M[Y].messages[0].data=We(ne)}F.content=st(it(M)),F.size=F.content.length,delete C[46];var V=Le(C[4][0].data);{V[7][0].data=_e(n.e.r+1);var G=Le(V[1][0].data),j=Lr(G[2][0].data);F=Se.find(l,f[j].location),M=dr(pr(F.content));{if(M[0].id!=j)throw"Bad HeaderStorageBucket";var re=Le(M[0].messages[0].data);for(P=0;P<i.length;++P){var xe=Le(re[2][0].data);xe[1][0].data=_e(P),xe[4][0].data=_e(i[P].length),re[2][P]={type:re[2][0].type,data:We(xe)}}M[0].messages[0].data=We(re)}F.content=st(it(M)),F.size=F.content.length;var J=Lr(V[2][0].data);F=Se.find(l,f[J].location),M=dr(pr(F.content));{if(M[0].id!=J)throw"Bad HeaderStorageBucket";for(re=Le(M[0].messages[0].data),D=0;D<=n.e.c;++D)xe=Le(re[2][0].data),xe[1][0].data=_e(D),xe[4][0].data=_e(n.e.r+1),re[2][D]={type:re[2][0].type,data:We(xe)};M[0].messages[0].data=We(re)}F.content=st(it(M)),F.size=F.content.length;var Q=Lr(V[4][0].data);(function(){for(var L=Se.find(l,f[Q].location),y=dr(pr(L.content)),A,H=0;H<y.length;++H){var le=y[H];le.id==Q&&(A=le)}var oe=Le(A.messages[0].data);{oe[3]=[];var fe=[];s.forEach(function(de,Ye){fe[1]=[{type:0,data:_e(Ye)}],fe[2]=[{type:0,data:_e(1)}],fe[3]=[{type:2,data:ju(de)}],oe[3].push({type:2,data:We(fe)})})}A.messages[0].data=We(oe);var te=it(y),Ae=st(te);L.content=Ae,L.size=L.content.length})();var ge=Le(V[3][0].data);{var ue=ge[1][0];delete ge[2];var Pe=Le(ue.data);{var lr=Lr(Pe[2][0].data);(function(){for(var L=Se.find(l,f[lr].location),y=dr(pr(L.content)),A,H=0;H<y.length;++H){var le=y[H];le.id==lr&&(A=le)}var oe=Le(A.messages[0].data);{delete oe[6],delete ge[7];var fe=new Uint8Array(oe[5][0].data);oe[5]=[];for(var te=0,Ae=0;Ae<=n.e.r;++Ae){var de=Le(fe);te+=Yu(de,i[Ae],s),de[1][0].data=_e(Ae),oe[5].push({data:We(de),type:2})}oe[1]=[{type:0,data:_e(n.e.c+1)}],oe[2]=[{type:0,data:_e(n.e.r+1)}],oe[3]=[{type:0,data:_e(te)}],oe[4]=[{type:0,data:_e(n.e.r+1)}]}A.messages[0].data=We(oe);var Ye=it(y),me=st(Ye);L.content=me,L.size=L.content.length})()}ue.data=We(Pe)}V[3][0].data=We(ge)}C[4][0].data=We(V)}p.messages[0].data=We(C);var tr=it(h),S=st(tr);return d.content=S,d.size=d.content.length,l}function Zu(e){return function(r){for(var n=0;n!=e.length;++n){var a=e[n];r[a[0]]===void 0&&(r[a[0]]=a[1]),a[2]==="n"&&(r[a[0]]=Number(r[a[0]]))}}}function h0(e){Zu([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function qu(e,t){return t.bookType=="ods"?Hi(e,t):t.bookType=="numbers"?Ju(e,t):t.bookType=="xlsb"?Qu(e,t):ex(e,t)}function Qu(e,t){ot=1024,e&&!e.SSF&&(e.SSF=rr(Re)),e&&e.SSF&&(Tn(),_n(e.SSF),t.revssf=En(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Nt?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r=t.bookType=="xlsb"?"bin":"xml",n=Si.indexOf(t.bookType)>-1,a=qa();h0(t=t||{});var i=Yn(),s="",f=0;if(t.cellXfs=[],Hr(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",he(i,s,ti(e.Props,t)),a.coreprops.push(s),Te(t.rels,2,s,pe.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var o=[],l=0;l<e.SheetNames.length;++l)(e.Workbook.Sheets[l]||{}).Hidden!=2&&o.push(e.SheetNames[l]);e.Props.SheetNames=o}for(e.Props.Worksheets=e.Props.SheetNames.length,he(i,s,ai(e.Props)),a.extprops.push(s),Te(t.rels,3,s,pe.EXT_PROPS),e.Custprops!==e.Props&&je(e.Custprops||{}).length>0&&(s="docProps/custom.xml",he(i,s,ii(e.Custprops)),a.custprops.push(s),Te(t.rels,4,s,pe.CUST_PROPS)),f=1;f<=e.SheetNames.length;++f){var c={"!id":{}},d=e.Sheets[e.SheetNames[f-1]],h=(d||{})["!type"]||"sheet";switch(h){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,he(i,s,tu(f-1,s,t,e,c)),a.sheets.push(s),Te(t.wbrels,-1,"worksheets/sheet"+f+"."+r,pe.WS[0])}if(d){var p=d["!comments"],T=!1,u="";p&&p.length>0&&(u="xl/comments"+f+"."+r,he(i,u,iu(p,u)),a.comments.push(u),Te(c,-1,"../comments"+f+"."+r,pe.CMNT),T=!0),d["!legacy"]&&T&&he(i,"xl/drawings/vmlDrawing"+f+".vml",Ei(f,d["!comments"])),delete d["!comments"],delete d["!legacy"]}c["!id"].rId1&&he(i,ei(s),ht(c))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,he(i,s,au(t.Strings,s,t)),a.strs.push(s),Te(t.wbrels,-1,"sharedStrings."+r,pe.SST)),s="xl/workbook."+r,he(i,s,ru(e,s)),a.workbooks.push(s),Te(t.rels,1,s,pe.WB),s="xl/theme/theme1.xml",he(i,s,_i(e.Themes,t)),a.themes.push(s),Te(t.wbrels,-1,"theme/theme1.xml",pe.THEME),s="xl/styles."+r,he(i,s,nu(e,s,t)),a.styles.push(s),Te(t.wbrels,-1,"styles."+r,pe.STY),e.vbaraw&&n&&(s="xl/vbaProject.bin",he(i,s,e.vbaraw),a.vba.push(s),Te(t.wbrels,-1,"vbaProject.bin",pe.VBA)),s="xl/metadata."+r,he(i,s,su(s)),a.metadata.push(s),Te(t.wbrels,-1,"metadata."+r,pe.XLMETA),he(i,"[Content_Types].xml",Qa(a,t)),he(i,"_rels/.rels",ht(t.rels)),he(i,"xl/_rels/workbook."+r+".rels",ht(t.wbrels)),delete t.revssf,delete t.ssf,i}function ex(e,t){ot=1024,e&&!e.SSF&&(e.SSF=rr(Re)),e&&e.SSF&&(Tn(),_n(e.SSF),t.revssf=En(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Nt?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",n=Si.indexOf(t.bookType)>-1,a=qa();h0(t=t||{});var i=Yn(),s="",f=0;if(t.cellXfs=[],Hr(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",he(i,s,ti(e.Props,t)),a.coreprops.push(s),Te(t.rels,2,s,pe.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var o=[],l=0;l<e.SheetNames.length;++l)(e.Workbook.Sheets[l]||{}).Hidden!=2&&o.push(e.SheetNames[l]);e.Props.SheetNames=o}e.Props.Worksheets=e.Props.SheetNames.length,he(i,s,ai(e.Props)),a.extprops.push(s),Te(t.rels,3,s,pe.EXT_PROPS),e.Custprops!==e.Props&&je(e.Custprops||{}).length>0&&(s="docProps/custom.xml",he(i,s,ii(e.Custprops)),a.custprops.push(s),Te(t.rels,4,s,pe.CUST_PROPS));var c=["SheetJ5"];for(t.tcid=0,f=1;f<=e.SheetNames.length;++f){var d={"!id":{}},h=e.Sheets[e.SheetNames[f-1]],p=(h||{})["!type"]||"sheet";switch(p){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,he(i,s,Ii(f-1,t,e,d)),a.sheets.push(s),Te(t.wbrels,-1,"worksheets/sheet"+f+"."+r,pe.WS[0])}if(h){var T=h["!comments"],u=!1,g="";if(T&&T.length>0){var C=!1;T.forEach(function(O){O[1].forEach(function(F){F.T==!0&&(C=!0)})}),C&&(g="xl/threadedComments/threadedComment"+f+"."+r,he(i,g,No(T,c,t)),a.threadedcomments.push(g),Te(d,-1,"../threadedComments/threadedComment"+f+"."+r,pe.TCMNT)),g="xl/comments"+f+"."+r,he(i,g,wi(T)),a.comments.push(g),Te(d,-1,"../comments"+f+"."+r,pe.CMNT),u=!0}h["!legacy"]&&u&&he(i,"xl/drawings/vmlDrawing"+f+".vml",Ei(f,h["!comments"])),delete h["!comments"],delete h["!legacy"]}d["!id"].rId1&&he(i,ei(s),ht(d))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,he(i,s,xi(t.Strings,t)),a.strs.push(s),Te(t.wbrels,-1,"sharedStrings."+r,pe.SST)),s="xl/workbook."+r,he(i,s,Li(e)),a.workbooks.push(s),Te(t.rels,1,s,pe.WB),s="xl/theme/theme1.xml",he(i,s,_i(e.Themes,t)),a.themes.push(s),Te(t.wbrels,-1,"theme/theme1.xml",pe.THEME),s="xl/styles."+r,he(i,s,mi(e,t)),a.styles.push(s),Te(t.wbrels,-1,"styles."+r,pe.STY),e.vbaraw&&n&&(s="xl/vbaProject.bin",he(i,s,e.vbaraw),a.vba.push(s),Te(t.wbrels,-1,"vbaProject.bin",pe.VBA)),s="xl/metadata."+r,he(i,s,Ti()),a.metadata.push(s),Te(t.wbrels,-1,"metadata."+r,pe.XLMETA),c.length>1&&(s="xl/persons/person.xml",he(i,s,Io(c)),a.people.push(s),Te(t.wbrels,-1,"persons/person.xml",pe.PEOPLE)),he(i,"[Content_Types].xml",Qa(a,t)),he(i,"_rels/.rels",ht(t.rels)),he(i,"xl/_rels/workbook."+r+".rels",ht(t.wbrels)),delete t.revssf,delete t.ssf,i}function rx(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=Rr(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function Vi(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return Wt(t.file,Se.write(e,{type:ve?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return Se.write(e,t)}function tx(e,t){var r=rr(t||{}),n=qu(e,r);return nx(n,r)}function nx(e,t){var r={},n=ve?"nodebuffer":typeof Uint8Array<"u"?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=n;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=n;break;default:throw new Error("Unrecognized type "+t.type)}var a=e.FullPaths?Se.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if(typeof Deno<"u"&&typeof a=="string"){if(t.type=="binary"||t.type=="base64")return a;a=new Uint8Array(gn(a))}return t.password&&typeof encrypt_agile<"u"?Vi(encrypt_agile(a,t.password),t):t.type==="file"?Wt(t.file,a):t.type=="string"?Ct(a):a}function ax(e,t){var r=t||{},n=Tu(e,r);return Vi(n,r)}function Er(e,t,r){r||(r="");var n=r+e;switch(t.type){case"base64":return kt(Lt(n));case"binary":return Lt(n);case"string":return e;case"file":return Wt(t.file,n,"utf8");case"buffer":return ve?Ir(n,"utf8"):typeof TextEncoder<"u"?new TextEncoder().encode(n):Er(n,{type:"binary"}).split("").map(function(a){return a.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function ix(e,t){switch(t.type){case"base64":return kt(e);case"binary":return e;case"string":return e;case"file":return Wt(t.file,e,"binary");case"buffer":return ve?Ir(e,"binary"):e.split("").map(function(r){return r.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function rn(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",n=0;n<e.length;++n)r+=String.fromCharCode(e[n]);return t.type=="base64"?kt(r):t.type=="string"?Ct(r):r;case"file":return Wt(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function Gi(e,t){Ns(),X1(e);var r=rr(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),r.type=="array"){r.type="binary";var n=Gi(e,r);return r.type="array",gn(n)}var a=0;if(r.sheet&&(typeof r.sheet=="number"?a=r.sheet:a=e.SheetNames.indexOf(r.sheet),!e.SheetNames[a]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return Er(gu(e,r),r);case"slk":case"sylk":return Er(Wl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"htm":case"html":return Er(Ui(e.Sheets[e.SheetNames[a]],r),r);case"txt":return ix(Xi(e.Sheets[e.SheetNames[a]],r),r);case"csv":return Er(u0(e.Sheets[e.SheetNames[a]],r),r,"\uFEFF");case"dif":return Er(Hl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"dbf":return rn(bl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"prn":return Er(Vl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"rtf":return Er(Yl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"eth":return Er(ui.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"fods":return Er(Hi(e,r),r);case"wk1":return rn(na.sheet_to_wk1(e.Sheets[e.SheetNames[a]],r),r);case"wk3":return rn(na.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),rn(Bi(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),ax(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return tx(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function sx(e,t,r,n,a,i,s,f){var o=Xe(r),l=f.defval,c=f.raw||!Object.prototype.hasOwnProperty.call(f,"raw"),d=!0,h=a===1?[]:{};if(a!==1)if(Object.defineProperty)try{Object.defineProperty(h,"__rowNum__",{value:r,enumerable:!1})}catch{h.__rowNum__=r}else h.__rowNum__=r;if(!s||e[r])for(var p=t.s.c;p<=t.e.c;++p){var T=s?e[r][p]:e[n[p]+o];if(T===void 0||T.t===void 0){if(l===void 0)continue;i[p]!=null&&(h[i[p]]=l);continue}var u=T.v;switch(T.t){case"z":if(u==null)break;continue;case"e":u=u==0?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+T.t)}if(i[p]!=null){if(u==null)if(T.t=="e"&&u===null)h[i[p]]=null;else if(l!==void 0)h[i[p]]=l;else if(c&&u===null)h[i[p]]=null;else continue;else h[i[p]]=c&&(T.t!=="n"||T.t==="n"&&f.rawNumbers!==!1)?u:Nr(T,u,f);u!=null&&(d=!1)}}return{row:h,isempty:d}}function mn(e,t){if(e==null||e["!ref"]==null)return[];var r={t:"n",v:0},n=0,a=1,i=[],s=0,f="",o={s:{r:0,c:0},e:{r:0,c:0}},l=t||{},c=l.range!=null?l.range:e["!ref"];switch(l.header===1?n=1:l.header==="A"?n=2:Array.isArray(l.header)?n=3:l.header==null&&(n=0),typeof c){case"string":o=Fe(c);break;case"number":o=Fe(e["!ref"]),o.s.r=c;break;default:o=c}n>0&&(a=0);var d=Xe(o.s.r),h=[],p=[],T=0,u=0,g=Array.isArray(e),C=o.s.r,O=0,F={};g&&!e[C]&&(e[C]=[]);var M=l.skipHidden&&e["!cols"]||[],Y=l.skipHidden&&e["!rows"]||[];for(O=o.s.c;O<=o.e.c;++O)if(!(M[O]||{}).hidden)switch(h[O]=ze(O),r=g?e[C][O]:e[h[O]+d],n){case 1:i[O]=O-o.s.c;break;case 2:i[O]=h[O];break;case 3:i[O]=l.header[O-o.s.c];break;default:if(r==null&&(r={w:"__EMPTY",t:"s"}),f=s=Nr(r,null,l),u=F[s]||0,!u)F[s]=1;else{do f=s+"_"+u++;while(F[f]);F[s]=u,F[f]=1}i[O]=f}for(C=o.s.r+a;C<=o.e.r;++C)if(!(Y[C]||{}).hidden){var ne=sx(e,o,C,h,n,i,g,l);(ne.isempty===!1||(n===1?l.blankrows!==!1:l.blankrows))&&(p[T++]=ne.row)}return p.length=T,p}var ha=/"/g;function fx(e,t,r,n,a,i,s,f){for(var o=!0,l=[],c="",d=Xe(r),h=t.s.c;h<=t.e.c;++h)if(n[h]){var p=f.dense?(e[r]||[])[h]:e[n[h]+d];if(p==null)c="";else if(p.v!=null){o=!1,c=""+(f.rawNumbers&&p.t=="n"?p.v:Nr(p,null,f));for(var T=0,u=0;T!==c.length;++T)if((u=c.charCodeAt(T))===a||u===i||u===34||f.forceQuotes){c='"'+c.replace(ha,'""')+'"';break}c=="ID"&&(c='"ID"')}else p.f!=null&&!p.F?(o=!1,c="="+p.f,c.indexOf(",")>=0&&(c='"'+c.replace(ha,'""')+'"')):c="";l.push(c)}return f.blankrows===!1&&o?null:l.join(s)}function u0(e,t){var r=[],n=t??{};if(e==null||e["!ref"]==null)return"";var a=Fe(e["!ref"]),i=n.FS!==void 0?n.FS:",",s=i.charCodeAt(0),f=n.RS!==void 0?n.RS:`
`,o=f.charCodeAt(0),l=new RegExp((i=="|"?"\\|":i)+"+$"),c="",d=[];n.dense=Array.isArray(e);for(var h=n.skipHidden&&e["!cols"]||[],p=n.skipHidden&&e["!rows"]||[],T=a.s.c;T<=a.e.c;++T)(h[T]||{}).hidden||(d[T]=ze(T));for(var u=0,g=a.s.r;g<=a.e.r;++g)(p[g]||{}).hidden||(c=fx(e,a,g,d,s,o,i,n),c!=null&&(n.strip&&(c=c.replace(l,"")),(c||n.blankrows!==!1)&&r.push((u++?f:"")+c)));return delete n.dense,r.join("")}function Xi(e,t){t||(t={}),t.FS="	",t.RS=`
`;var r=u0(e,t);return r}function lx(e){var t="",r,n="";if(e==null||e["!ref"]==null)return[];var a=Fe(e["!ref"]),i="",s=[],f,o=[],l=Array.isArray(e);for(f=a.s.c;f<=a.e.c;++f)s[f]=ze(f);for(var c=a.s.r;c<=a.e.r;++c)for(i=Xe(c),f=a.s.c;f<=a.e.c;++f)if(t=s[f]+i,r=l?(e[c]||[])[f]:e[t],n="",r!==void 0){if(r.F!=null){if(t=r.F,!r.f)continue;n=r.f,t.indexOf(":")==-1&&(t=t+":"+t)}if(r.f!=null)n=r.f;else{if(r.t=="z")continue;if(r.t=="n"&&r.v!=null)n=""+r.v;else if(r.t=="b")n=r.v?"TRUE":"FALSE";else if(r.w!==void 0)n="'"+r.w;else{if(r.v===void 0)continue;r.t=="s"?n="'"+r.v:n=""+r.v}}o[o.length]=t+"="+n}return o}function ji(e,t,r){var n=r||{},a=+!n.skipHeader,i=e||{},s=0,f=0;if(i&&n.origin!=null)if(typeof n.origin=="number")s=n.origin;else{var o=typeof n.origin=="string"?Ue(n.origin):n.origin;s=o.r,f=o.c}var l,c={s:{c:0,r:0},e:{c:f,r:s+t.length-1+a}};if(i["!ref"]){var d=Fe(i["!ref"]);c.e.c=Math.max(c.e.c,d.e.c),c.e.r=Math.max(c.e.r,d.e.r),s==-1&&(s=d.e.r+1,c.e.r=s+t.length-1+a)}else s==-1&&(s=0,c.e.r=t.length-1+a);var h=n.header||[],p=0;t.forEach(function(u,g){je(u).forEach(function(C){(p=h.indexOf(C))==-1&&(h[p=h.length]=C);var O=u[C],F="z",M="",Y=we({c:f+p,r:s+g+a});l=Ut(i,Y),O&&typeof O=="object"&&!(O instanceof Date)?i[Y]=O:(typeof O=="number"?F="n":typeof O=="boolean"?F="b":typeof O=="string"?F="s":O instanceof Date?(F="d",n.cellDates||(F="n",O=er(O)),M=n.dateNF||Re[14]):O===null&&n.nullError&&(F="e",O=0),l?(l.t=F,l.v=O,delete l.w,delete l.R,M&&(l.z=M)):i[Y]=l={t:F,v:O},M&&(l.z=M))})}),c.e.c=Math.max(c.e.c,f+h.length-1);var T=Xe(s);if(a)for(p=0;p<h.length;++p)i[ze(p+f)+T]={t:"s",v:h[p]};return i["!ref"]=Ie(c),i}function ox(e,t){return ji(null,e,t)}function Ut(e,t,r){if(typeof t=="string"){if(Array.isArray(e)){var n=Ue(t);return e[n.r]||(e[n.r]=[]),e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return typeof t!="number"?Ut(e,we(t)):Ut(e,we({r:t,c:r||0}))}function cx(e,t){if(typeof t=="number"){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}else if(typeof t=="string"){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}else throw new Error("Cannot find sheet |"+t+"|")}function hx(){return{SheetNames:[],Sheets:{}}}function ux(e,t,r,n){var a=1;if(!r)for(;a<=65535&&e.SheetNames.indexOf(r="Sheet"+a)!=-1;++a,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var i=r.match(/(^.*?)(\d+)$/);a=i&&+i[2]||0;var s=i&&i[1]||r;for(++a;a<=65535&&e.SheetNames.indexOf(r=s+a)!=-1;++a);}if(Pi(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function xx(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var n=cx(e,t);switch(e.Workbook.Sheets[n]||(e.Workbook.Sheets[n]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[n].Hidden=r}function dx(e,t){return e.z=t,e}function $i(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}function px(e,t,r){return $i(e,"#"+t,r)}function vx(e,t,r){e.c||(e.c=[]),e.c.push({t,a:r||"SheetJS"})}function mx(e,t,r,n){for(var a=typeof t!="string"?t:Fe(t),i=typeof t=="string"?t:Ie(t),s=a.s.r;s<=a.e.r;++s)for(var f=a.s.c;f<=a.e.c;++f){var o=Ut(e,s,f);o.t="n",o.F=i,delete o.v,s==a.s.r&&f==a.s.c&&(o.f=r,n&&(o.D=!0))}return e}var yr={encode_col:ze,encode_row:Xe,encode_cell:we,encode_range:Ie,decode_col:r0,decode_row:e0,split_cell:If,decode_cell:Ue,decode_range:fr,format_cell:Nr,sheet_add_aoa:$a,sheet_add_json:ji,sheet_add_dom:bi,aoa_to_sheet:vt,json_to_sheet:ox,table_to_sheet:Wi,table_to_book:Vu,sheet_to_csv:u0,sheet_to_txt:Xi,sheet_to_json:mn,sheet_to_html:Ui,sheet_to_formulae:lx,sheet_to_row_object_array:mn,sheet_get_cell:Ut,book_new:hx,book_append_sheet:ux,book_set_sheet_visibility:xx,cell_set_number_format:dx,cell_set_hyperlink:$i,cell_set_internal_link:px,cell_add_comment:vx,sheet_set_array_formula:mx,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}},tn={exports:{}},gx=tn.exports,ua;function _x(){return ua||(ua=1,function(e,t){(function(r,n){n()})(gx,function(){function r(l,c){return typeof c>"u"?c={autoBom:!1}:typeof c!="object"&&(console.warn("Deprecated: Expected third argument to be a object"),c={autoBom:!c}),c.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(l.type)?new Blob(["\uFEFF",l],{type:l.type}):l}function n(l,c,d){var h=new XMLHttpRequest;h.open("GET",l),h.responseType="blob",h.onload=function(){o(h.response,c,d)},h.onerror=function(){console.error("could not download file")},h.send()}function a(l){var c=new XMLHttpRequest;c.open("HEAD",l,!1);try{c.send()}catch{}return 200<=c.status&&299>=c.status}function i(l){try{l.dispatchEvent(new MouseEvent("click"))}catch{var c=document.createEvent("MouseEvents");c.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),l.dispatchEvent(c)}}var s=typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof zt=="object"&&zt.global===zt?zt:void 0,f=s.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),o=s.saveAs||(typeof window!="object"||window!==s?function(){}:"download"in HTMLAnchorElement.prototype&&!f?function(l,c,d){var h=s.URL||s.webkitURL,p=document.createElement("a");c=c||l.name||"download",p.download=c,p.rel="noopener",typeof l=="string"?(p.href=l,p.origin===location.origin?i(p):a(p.href)?n(l,c,d):i(p,p.target="_blank")):(p.href=h.createObjectURL(l),setTimeout(function(){h.revokeObjectURL(p.href)},4e4),setTimeout(function(){i(p)},0))}:"msSaveOrOpenBlob"in navigator?function(l,c,d){if(c=c||l.name||"download",typeof l!="string")navigator.msSaveOrOpenBlob(r(l,d),c);else if(a(l))n(l,c,d);else{var h=document.createElement("a");h.href=l,h.target="_blank",setTimeout(function(){i(h)})}}:function(l,c,d,h){if(h=h||open("","_blank"),h&&(h.document.title=h.document.body.innerText="downloading..."),typeof l=="string")return n(l,c,d);var p=l.type==="application/octet-stream",T=/constructor/i.test(s.HTMLElement)||s.safari,u=/CriOS\/[\d]+/.test(navigator.userAgent);if((u||p&&T||f)&&typeof FileReader<"u"){var g=new FileReader;g.onloadend=function(){var F=g.result;F=u?F:F.replace(/^data:[^;]*;/,"data:attachment/file;"),h?h.location.href=F:location=F,h=null},g.readAsDataURL(l)}else{var C=s.URL||s.webkitURL,O=C.createObjectURL(l);h?h.location=O:location.href=O,h=null,setTimeout(function(){C.revokeObjectURL(O)},4e4)}});s.saveAs=o.saveAs=o,e.exports=o})}(tn)),tn.exports}var Tx=_x();class Ex{async exportOrdersToExcel(t={},r="orders_export"){try{const n=await this.fetchAllOrders(t);if(!n||n.length===0)throw new Error("No orders found to export");const a=this.prepareOrdersForExcel(n),i=yr.book_new(),s=yr.json_to_sheet(a.orders);if(yr.book_append_sheet(i,s,"Orders"),a.orderItems&&a.orderItems.length>0){const d=yr.json_to_sheet(a.orderItems);yr.book_append_sheet(i,d,"Order Items")}const f=yr.json_to_sheet(a.summary);yr.book_append_sheet(i,f,"Summary"),this.styleWorkbook(i);const o=Gi(i,{bookType:"xlsx",type:"array",cellStyles:!0}),l=new Blob([o],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),c=new Date().toISOString().slice(0,19).replace(/:/g,"-");return Tx.saveAs(l,`${r}_${c}.xlsx`),{success:!0,message:`Successfully exported ${n.length} orders`,filename:`${r}_${c}.xlsx`}}catch(n){throw console.error("Export error:",n),new Error(`Export failed: ${n.message}`)}}async fetchAllOrders(t){const r=[];let n=1,a=!0;const i=50;for(;a;)try{const s=await ft.getAll({...t,page:n,pageSize:i});s.data&&s.data.length>0?(r.push(...s.data),a=s.data.length===i&&n<(s.totalPages||1),n++):a=!1}catch(s){console.error(`Error fetching page ${n}:`,s),a=!1}return r}getOrderStatusText(t){const r={0:"Processing",1:"Pending",2:"Shipped",3:"Delivered",4:"Cancelled"};return typeof t=="number"?r[t]||"Unknown":typeof t=="string"?t:"Unknown"}getPaymentStatusText(t){const r={0:"Pending",1:"Completed",2:"Refunded",3:"Failed"};return typeof t=="number"?r[t]||"Unknown":typeof t=="string"?t:"Unknown"}prepareOrdersForExcel(t){const r=[],n=[];let a=0;const i={},s={};t.forEach(o=>{const l=o.statusText||this.getOrderStatusText(o.status),c=o.paymentStatusText||this.getPaymentStatusText(o.paymentStatus),d={"Order ID":o.id,"Customer Name":o.customerName||"N/A","Customer Email":o.customerEmail||"N/A","Order Date":this.formatDate(o.createdAt),"Total Amount":o.totalPriceAmount||0,Currency:o.totalPriceCurrency||"USD","Order Status":l,"Payment Status":c,"Payment Method":o.paymentMethodText||"Unknown","Items Count":o.itemsCount||0,"Shipping Method":o.shippingMethodName||"N/A","Shipping Address":this.formatShippingAddress(o),"Created At":this.formatDateTime(o.createdAt)};r.push(d),a+=o.totalPriceAmount||0,i[l]=(i[l]||0)+1,s[c]=(s[c]||0)+1,o.items&&o.items.length>0&&o.items.forEach(h=>{n.push({"Order ID":o.id,"Product ID":h.productId,"Product Name":h.productName||"N/A",Quantity:h.quantity,"Unit Price":h.priceAmount,Currency:h.priceCurrency,"Total Price":h.quantity*h.priceAmount||0})})});const f=[{Metric:"Total Orders",Value:t.length},{Metric:"Total Revenue",Value:a.toFixed(2)},{Metric:"Average Order Value",Value:(a/t.length).toFixed(2)},{Metric:"",Value:""},{Metric:"ORDER STATUSES",Value:""},...Object.entries(i).map(([o,l])=>({Metric:o,Value:l})),{Metric:"",Value:""},{Metric:"PAYMENT STATUSES",Value:""},...Object.entries(s).map(([o,l])=>({Metric:o,Value:l}))];return{orders:r,orderItems:n,summary:f}}formatShippingAddress(t){const r=[];return t.shippingAddressLine&&r.push(t.shippingAddressLine),t.shippingCity&&r.push(t.shippingCity),t.shippingCountry&&r.push(t.shippingCountry),r.length>0?r.join(", "):"N/A"}formatDate(t){return t?new Date(t).toLocaleDateString("en-US"):""}formatDateTime(t){return t?new Date(t).toLocaleString("en-US"):""}styleWorkbook(t){Object.keys(t.Sheets).forEach(r=>{const n=t.Sheets[r],a=yr.decode_range(n["!ref"]),i=[];for(let s=a.s.c;s<=a.e.c;s++){let f=10;for(let o=a.s.r;o<=a.e.r;o++){const l=yr.encode_cell({r:o,c:s}),c=n[l];if(c&&c.v){const d=c.v.toString().length;f=Math.max(f,d)}}i.push({width:Math.min(f+2,50)})}n["!cols"]=i})}async exportFilteredOrders(t,r,n="filtered_orders"){const a={...r,search:t};return await this.exportOrdersToExcel(a,n)}}const wx=new Ex,Sx={class:"order-list"},Ax={class:"level"},Fx={class:"level-right"},yx={class:"level-item"},Cx={key:0,class:"notification is-danger"},Ox={class:"card"},Dx={class:"card-content"},Rx={key:0,class:"loading-overlay"},Nx={class:"loading-content"},Ix={class:"mt-2"},kx={key:1,class:"has-text-centered py-6"},Px={key:2},Lx={class:"table-container"},Mx={class:"table is-fullwidth is-hoverable"},Bx={class:"has-text-info"},Ux={class:"has-text-grey"},bx={class:"has-text-success"},Wx={class:"buttons are-small"},Hx=["onClick"],Vx={class:"modal-card"},Gx={class:"modal-card-body"},Xx={key:0,class:"content"},jx={class:"field"},$x={class:"control"},zx={class:"select is-fullwidth"},Kx={class:"field"},Yx={class:"control"},Jx={class:"select is-fullwidth"},Zx={class:"field"},qx={class:"control"},Qx={class:"modal-card-foot"},ed={__name:"OrderList",setup(e){const t=jr(!1),r=jr(!1),n=jr(null),a=jr(""),i=jr(""),s=jr(""),f=jr(!1),o=[{key:"status",label:"Order Status",type:"select",options:[{value:"",label:"All Order Status"},{value:0,label:"Processing"},{value:1,label:"Pending"},{value:2,label:"Shipped"},{value:3,label:"Delivered"},{value:4,label:"Cancelled"}]},{key:"paymentStatus",label:"Payment Status",type:"select",options:[{value:"",label:"All Payment Status"},{value:0,label:"Pending"},{value:1,label:"Completed"},{value:2,label:"Refunded"},{value:3,label:"Failed"}]},{key:"dateRange",label:"Date Range",type:"select",options:[{value:"",label:"All Time"},{value:"today",label:"Today"},{value:"yesterday",label:"Yesterday"},{value:"last7days",label:"Last 7 Days"},{value:"last30days",label:"Last 30 Days"},{value:"thisMonth",label:"This Month"},{value:"lastMonth",label:"Last Month"}]}],{items:l,loading:c,error:d,isFirstLoad:h,currentPage:p,totalPages:T,totalItems:u,filters:g,fetchData:C,handlePageChange:O}=Fs({fetchFunction:async J=>{try{d.value=null;const Q=await ft.getAllAdmin(J);return h.value=!1,Q}catch(Q){throw console.error("❌ Error fetching orders:",Q),d.value=Q.message||"Failed to load orders",h.value=!1,Q}},defaultFilters:{status:"",paymentStatus:"",dateRange:""},debounceTime:300,defaultPageSize:10,clientSideSearch:!1}),F=J=>{console.log("Search changed to:",J),g.search=J},M=(J,Q)=>{console.log(`Filter ${J} changed to:`,Q),g[J]=Q},Y=()=>{console.log("Resetting filters"),Object.keys(g).forEach(J=>{g[J]=""}),C(1)},ne=async()=>{try{t.value=!0;const J={...g};Object.keys(J).forEach(ge=>{J[ge]||delete J[ge]});const Q=await wx.exportOrdersToExcel(J,"orders_export");console.log("Export successful:",Q),alert(`Successfully exported orders to ${Q.filename}`)}catch(J){console.error("Export failed:",J),alert(`Export failed: ${J.message}`)}finally{t.value=!1}},D=async()=>{try{d.value=null,ft.forceRefresh(),await C(1),console.log("🔄 Orders force refreshed successfully")}catch(J){console.error("❌ Error during force refresh:",J),d.value=J.message||"Failed to refresh orders"}},b=J=>J?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(J)):"",P=(J,Q="UAH")=>{if(!J)return"₴0.00";const ge=typeof J=="string"?parseFloat(J):J,ue=As(Q);return new Intl.NumberFormat(ue.locale,{style:"currency",currency:ue.currency,minimumFractionDigits:2,maximumFractionDigits:2}).format(ge)},V=J=>{console.log("Opening status modal for order:",J),n.value=J,a.value=J.status,i.value=J.paymentStatus!==void 0?J.paymentStatus:0,s.value="",r.value=!0,console.log("Modal state:",{orderId:J.id,currentStatus:J.status,currentPaymentStatus:J.paymentStatus,newStatus:a.value,newPaymentStatus:i.value})},G=()=>{r.value=!1,n.value=null},j=async()=>{if(n.value){f.value=!0;try{console.log("Updating order status:",{orderId:n.value.id,oldStatus:n.value.status,newStatus:a.value,oldPaymentStatus:n.value.paymentStatus,newPaymentStatus:i.value}),a.value!==n.value.status&&(await ft.updateOrderStatus(n.value.id,a.value),console.log("✅ Order status updated successfully")),i.value!==n.value.paymentStatus&&(await ft.updatePaymentStatus(n.value.id,i.value),console.log("✅ Payment status updated successfully")),s.value.trim()&&(await ft.addOrderNote(n.value.id,s.value),console.log("✅ Order note added successfully")),await C(p.value),console.log("✅ Order list refreshed after status update"),G(),console.log("✅ Status update completed successfully")}catch(J){console.error("❌ Error updating order status:",J),alert("Failed to update order status. Please try again.")}finally{f.value=!1}}};let re,xe;return cs(()=>{nt.shouldRefreshOrders()&&(console.log("🔄 Orders marked for refresh, loading fresh data..."),nt.clearOrdersRefreshFlag()),C(),re=gs.on(_s.ORDER_UPDATED,()=>{console.log("📡 Received order update event, refreshing list..."),C(p.value)}),xe=()=>{nt.shouldRefreshOrders()&&(console.log("🔄 Window focused and orders need refresh, loading fresh data..."),nt.clearOrdersRefreshFlag(),C(p.value))},window.addEventListener("focus",xe)}),hs(()=>{re&&re(),xe&&window.removeEventListener("focus",xe)}),us(()=>{console.log("📍 OrderList component activated"),nt.shouldRefreshOrders()&&(console.log("🔄 Orders marked for refresh, loading fresh data..."),nt.clearOrdersRefreshFlag(),C(p.value))}),(J,Q)=>{const ge=ps("router-link");return Pr(),kr("div",Sx,[X("div",Ax,[Q[5]||(Q[5]=X("div",{class:"level-left"},[X("div",{class:"level-item"},[X("h1",{class:"title"},"Orders")])],-1)),X("div",Fx,[X("div",yx,[X("button",{class:St(["button is-primary",{"is-loading":t.value}]),onClick:ne},Q[4]||(Q[4]=[X("span",{class:"icon"},[X("i",{class:"fas fa-download"})],-1),X("span",null,"Export Orders",-1)]),2)])])]),wt(ys,{filters:Me(g),"filter-fields":o,"search-label":"Search Orders","search-placeholder":"Order ID, customer name, email...","search-column-class":"is-4","total-items":Me(u),"item-name":"orders",loading:Me(c),onSearchChanged:F,onFilterChanged:M,onResetFilters:Y},null,8,["filters","total-items","loading"]),Me(d)?(Pr(),kr("div",Cx,[X("button",{class:"delete",onClick:Q[0]||(Q[0]=ue=>d.value=null)}),X("p",null,[Q[6]||(Q[6]=X("strong",null,"Error:",-1)),In(" "+xr(Me(d)),1)]),X("button",{class:"button is-light mt-2",onClick:D},Q[7]||(Q[7]=[X("span",{class:"icon"},[X("i",{class:"fas fa-redo"})],-1),X("span",null,"Retry",-1)]))])):Nn("",!0),X("div",Ox,[X("div",Dx,[Me(c)?(Pr(),kr("div",Rx,[X("div",Nx,[Q[8]||(Q[8]=X("span",{class:"icon is-large"},[X("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1)),X("p",Ix,xr(Me(h)?"Loading orders...":"Updating orders..."),1)])])):Nn("",!0),!Me(c)&&(!Me(l)||!Me(l).length)?(Pr(),kr("div",kx,Q[9]||(Q[9]=[X("span",{class:"icon is-large"},[X("i",{class:"fas fa-shopping-cart fa-2x"})],-1),X("p",{class:"mt-2"},"No orders found",-1),X("p",{class:"mt-2"},"Try adjusting your search criteria or filters",-1)]))):(Pr(),kr("div",Px,[X("div",Lx,[X("table",Mx,[Q[14]||(Q[14]=X("thead",null,[X("tr",null,[X("th",null,"Order ID"),X("th",null,"Customer"),X("th",null,"Date"),X("th",null,"Total"),X("th",null,"Status"),X("th",null,"Payment"),X("th",null,"Actions")])],-1)),X("tbody",null,[(Pr(!0),kr(xs,null,ds(Me(l),ue=>(Pr(),kr("tr",{key:ue.id},[X("td",null,[X("code",Bx,xr(ue.id),1)]),X("td",null,[X("div",null,[X("strong",null,xr(ue.customerName||ue.userName||"N/A"),1),Q[10]||(Q[10]=X("br",null,null,-1)),X("small",Ux,xr(ue.customerEmail||"No email"),1)])]),X("td",null,xr(b(ue.createdAt)),1),X("td",null,[X("strong",bx,xr(P(ue.totalPriceAmount,ue.totalPriceCurrency)),1)]),X("td",null,[X("span",{class:St(["tag",Me(Ts)(ue.status)])},xr(Me(Es)(ue.status)),3)]),X("td",null,[X("span",{class:St(["tag",Me(ws)(ue.paymentStatusText||ue.paymentStatus)])},xr(Me(Ss)(ue.paymentStatusText||ue.paymentStatus)),3)]),X("td",null,[X("div",Wx,[wt(ge,{to:`/admin/orders/${ue.id}/view`,class:"button is-info",title:"View Order"},{default:S0(()=>Q[11]||(Q[11]=[X("span",{class:"icon is-small"},[X("i",{class:"fas fa-eye"})],-1)])),_:2},1032,["to"]),wt(ge,{to:`/admin/orders/${ue.id}/edit`,class:"button is-primary",title:"Edit Order"},{default:S0(()=>Q[12]||(Q[12]=[X("span",{class:"icon is-small"},[X("i",{class:"fas fa-edit"})],-1)])),_:2},1032,["to"]),X("button",{class:"button is-warning",onClick:Pe=>V(ue),title:"Update Status"},Q[13]||(Q[13]=[X("span",{class:"icon is-small"},[X("i",{class:"fas fa-cog"})],-1)]),8,Hx)])])]))),128))])])]),wt(Os,{"current-page":Me(p),"total-pages":Me(T),onPageChanged:Me(O)},null,8,["current-page","total-pages","onPageChanged"])]))])]),X("div",{class:St(["modal",{"is-active":r.value}])},[X("div",{class:"modal-background",onClick:G}),X("div",Vx,[X("header",{class:"modal-card-head"},[Q[15]||(Q[15]=X("p",{class:"modal-card-title"},"Update Order Status",-1)),X("button",{class:"delete","aria-label":"close",onClick:G})]),X("section",Gx,[n.value?(Pr(),kr("div",Xx,[X("p",null,[Q[16]||(Q[16]=X("strong",null,"Order ID:",-1)),In(" "+xr(n.value.id),1)]),X("p",null,[Q[17]||(Q[17]=X("strong",null,"Customer:",-1)),In(" "+xr(n.value.userName),1)]),X("p",null,[Q[18]||(Q[18]=X("strong",null,"Current Status:",-1)),wt(Cs,{status:n.value.status,type:"order"},null,8,["status"])]),X("div",jx,[Q[20]||(Q[20]=X("label",{class:"label"},"New Status",-1)),X("div",$x,[X("div",zx,[kn(X("select",{"onUpdate:modelValue":Q[1]||(Q[1]=ue=>a.value=ue)},Q[19]||(Q[19]=[vs('<option value="pending" data-v-b51693d0>Pending</option><option value="processing" data-v-b51693d0>Processing</option><option value="shipped" data-v-b51693d0>Shipped</option><option value="delivered" data-v-b51693d0>Delivered</option><option value="cancelled" data-v-b51693d0>Cancelled</option><option value="refunded" data-v-b51693d0>Refunded</option>',6)]),512),[[A0,a.value]])])])]),X("div",Kx,[Q[22]||(Q[22]=X("label",{class:"label"},"Payment Status",-1)),X("div",Yx,[X("div",Jx,[kn(X("select",{"onUpdate:modelValue":Q[2]||(Q[2]=ue=>i.value=ue)},Q[21]||(Q[21]=[X("option",{value:"pending"},"Pending",-1),X("option",{value:"paid"},"Paid",-1),X("option",{value:"failed"},"Failed",-1),X("option",{value:"refunded"},"Refunded",-1)]),512),[[A0,i.value]])])])]),X("div",Zx,[Q[23]||(Q[23]=X("label",{class:"label"},"Note (Optional)",-1)),X("div",qx,[kn(X("textarea",{class:"textarea","onUpdate:modelValue":Q[3]||(Q[3]=ue=>s.value=ue),placeholder:"Add a note about this status change"},"                ",512),[[ms,s.value]])])])])):Nn("",!0)]),X("footer",Qx,[X("button",{class:St(["button is-primary",{"is-loading":f.value}]),onClick:j}," Update Status ",2),X("button",{class:"button",onClick:G},"Cancel")])])],2)])}}},fd=os(ed,[["__scopeId","data-v-b51693d0"]]);export{fd as default};
