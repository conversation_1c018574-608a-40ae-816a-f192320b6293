<template>
  <div class="register-container">
    <div class="register-form">
      <div class="logo-container">
        <img src="@/assets/images/logo.svg" alt="Klondike" class="logo" />
      </div>

      <h2 class="register-title">Реєстрація</h2>

      <div v-if="message" class="alert" :class="successful ? 'alert-success' : 'alert-danger'">
        {{ message }}
      </div>

      <Form @submit="handleRegister" :validation-schema="schema" v-if="!successful">
        <div class="form-group">
          <label for="email" class="form-label">Електронна пошта</label>
          <div class="input-wrapper" :class="{ 'has-error': errors.email, 'is-valid': isFieldValid('email') }">
            <Field name="email" type="text" class="form-input" id="email.id" placeholder="Введіть електронну пошту" />
            <div class="validation-icon">
              <i class="fas fa-exclamation-circle error-icon" v-if="errors.email"></i>
              <i class="fas fa-check-circle valid-icon" v-if="isFieldValid('email')"></i>
            </div>
          </div>
          <ErrorMessage name="email" class="error-feedback" />
          <div class="field-hint" v-if="!errors.email && !isFieldValid('email')">
            Введіть вашу електронну пошту
          </div>
        </div>

        <div class="form-group">
          <label for="password" class="form-label">Пароль</label>
          <div class="input-wrapper" :class="{ 'has-error': errors.password, 'is-valid': isFieldValid('password') }">
            <Field name="password" :type="showPassword ? 'text' : 'password'" class="form-input" id="password" placeholder="Введіть пароль" />
            <div class="validation-icon">
              <i class="fas fa-exclamation-circle error-icon" v-if="errors.password"></i>
              <i class="fas fa-check-circle valid-icon" v-if="isFieldValid('password')"></i>
            </div>
            <button type="button" class="toggle-password" @click="togglePasswordVisibility">
              <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
          </div>
          <ErrorMessage name="password" class="error-feedback" />

          <div class="password-requirements" v-if="!isFieldValid('password')">
            <h6 class="requirements-title">Пароль повинен містити:</h6>
            <ul class="requirements-list">
              <li :class="{ 'requirement-met': passwordMeetsLength }">
                <i :class="passwordMeetsLength ? 'fas fa-check' : 'fas fa-times'"></i>
                Не менше 8 символів
              </li>
              <li :class="{ 'requirement-met': passwordHasUppercase }">
                <i :class="passwordHasUppercase ? 'fas fa-check' : 'fas fa-times'"></i>
                Хоча б одну велику літеру
              </li>
              <li :class="{ 'requirement-met': passwordHasNumber }">
                <i :class="passwordHasNumber ? 'fas fa-check' : 'fas fa-times'"></i>
                Хоча б одну цифру
              </li>
              <li :class="{ 'requirement-met': passwordHasSpecial }">
                <i :class="passwordHasSpecial ? 'fas fa-check' : 'fas fa-times'"></i>
                Хоча б один спеціальний символ (!@#$%^&*)
              </li>
            </ul>
          </div>
        </div>

        <div class="form-group checkbox-group">
          <label class="checkbox-label">
            <Field name="newsletter" type="checkbox" class="checkbox-input" />
            <span class="checkbox-text">Так, я хочу отримувати інформацію про новинки і знижки на електронну пошту</span>
          </label>
        </div>

        <button class="register-button" type="submit" :disabled="loading">
          <span v-if="loading" class="spinner"></span>
          Зареєструватися
        </button>
      </Form>

      <div class="divider">
        <span>або</span>
      </div>

      <button class="social-button google-button" @click="handleGoogleLogin" type="button">
        <img src="@/assets/images/icons/google-icon.svg" alt="Google" class="social-icon" />
        Google
      </button>

      <!-- Hidden Google button required for the API to work properly -->
      <div id="google-signin-button" ref="googleButton" style="display: none;"></div>

      <!-- Apple login is not implemented yet -->
      <button class="social-button apple-button" disabled title="Apple login is not available yet">
        <img src="@/assets/images/icons/apple-icon.svg" alt="Apple" class="social-icon" />
        Apple
      </button>

      <div class="login-link" v-if="!successful">
        <span>Вже маєте акаунт?</span>
        <router-link to="/login">Увійти</router-link>
      </div>

      <div class="text-center mt-3" v-else>
        <router-link to="/login" class="login-button">Перейти до входу</router-link>
      </div>
    </div>

    <div class="footer">
      <div class="footer-links">
        <a href="#">Про компанію</a>
        <a href="#">Умови використання</a>
        <a href="#">Допомога</a>
      </div>
      <div class="copyright">
        Всі права захищені
      </div>
    </div>
  </div>
</template>

<script>
import { Form, Field, ErrorMessage } from 'vee-validate';
import * as yup from 'yup';
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
import { useField } from 'vee-validate';
import { GOOGLE_CLIENT_ID, initializeGoogleSignIn, promptGoogleSignIn } from '@/config/google-auth';

export default {
  name: 'Register',
  components: {
    Form,
    Field,
    ErrorMessage
  },
  setup() {
    const store = useStore();
    const router = useRouter();
    const route = useRoute();

    const loading = ref(false);
    const message = ref('');
    const successful = ref(false);
    const showPassword = ref(false);

    const { errorMessage: emailError, value: emailValue } = useField('email');
    const { errorMessage: passwordError, value: passwordValue } = useField('password');

    const errors = computed(() => ({
      email: !!emailError.value,
      password: !!passwordError.value
    }));

    // Check if a field is valid (has a value and no error)
    const isFieldValid = (fieldName) => {
      if (fieldName === 'email') {
        return emailValue.value && !emailError.value;
      } else if (fieldName === 'password') {
        return passwordValue.value && !passwordError.value;
      }
      return false;
    };

    // Toggle password visibility
    const togglePasswordVisibility = () => {
      showPassword.value = !showPassword.value;
    };

    // Password validation helpers
    const passwordMeetsLength = computed(() => {
      return passwordValue.value && passwordValue.value.length >= 8;
    });

    const passwordHasUppercase = computed(() => {
      return passwordValue.value && /[A-Z]/.test(passwordValue.value);
    });

    const passwordHasNumber = computed(() => {
      return passwordValue.value && /[0-9]/.test(passwordValue.value);
    });

    const passwordHasSpecial = computed(() => {
      return passwordValue.value && /[!@#$%^&*]/.test(passwordValue.value);
    });

    // Google Sign-In initialization
    const googleButton = ref(null);
    const googleInitialized = ref(false);

    onMounted(async () => {
      try {
        console.log('Initializing Google Sign-In in Register.vue...');

        // Initialize Google Sign-In with our callback
        const googleAccounts = await initializeGoogleSignIn(handleGoogleCredentialResponse);
        googleInitialized.value = true;

        console.log('Google Sign-In initialized successfully in Register.vue');

        // Render the Google Sign-In button (hidden, but needed for the API to work properly)
        const buttonElement = document.getElementById('google-signin-button');
        if (buttonElement) {
          googleAccounts.id.renderButton(
            buttonElement,
            {
              theme: 'outline',
              size: 'large',
              width: '100%',
              type: 'standard',
              text: 'signup_with'
            }
          );
          console.log('Google Sign-In button rendered in Register.vue');
        } else {
          console.error('Google Sign-In button element not found in Register.vue');
        }
      } catch (error) {
        console.error('Error initializing Google Sign-In in Register.vue:', error);
        message.value = 'Помилка ініціалізації Google Sign-In. Спробуйте пізніше.';
      }
    });

    // Handle Google Sign-In button click with improved error handling and loading state management
    const handleGoogleLogin = async () => {
      // Prevent multiple clicks while processing
      if (loading.value) {
        console.log('Already processing Google login in Register.vue, ignoring click');
        return;
      }

      try {
        console.log('Google login button clicked in Register.vue');
        loading.value = true;
        message.value = '';
        successful.value = false;

        // Initialize Google Sign-In if not already initialized
        if (!googleInitialized.value) {
          console.log('Google Sign-In not initialized, initializing now in Register.vue...');
          try {
            await initializeGoogleSignIn(handleGoogleCredentialResponse);
            googleInitialized.value = true;
            console.log('Google Sign-In initialized successfully in Register.vue');
          } catch (initError) {
            console.error('Failed to initialize Google Sign-In in Register.vue:', initError);
            message.value = 'Не вдалося ініціалізувати Google Sign-In. Спробуйте пізніше.';
            loading.value = false;
            return;
          }
        }

        console.log('Prompting Google Sign-In from Register.vue...');

        // Set a timeout for the loading state in case the prompt is shown but user doesn't interact
        const loadingTimeoutId = setTimeout(() => {
          if (loading.value) {
            console.log('Google Sign-In prompt is still open in Register.vue, resetting loading state');
            loading.value = false;
          }
        }, 10000); // Reset loading after 10 seconds if prompt is still open

        try {
          // Wait for the prompt to complete
          await promptGoogleSignIn();
          // If we get here without an error, the prompt was shown successfully
          // The actual authentication will happen in the callback
          console.log('Google Sign-In prompt shown successfully in Register.vue');
        } catch (promptError) {
          clearTimeout(loadingTimeoutId);
          console.error('Error during Google Sign-In prompt in Register.vue:', promptError);

          // Provide user-friendly error messages based on the error
          if (promptError.message.includes('timed out')) {
            message.value = 'Час очікування відповіді від Google вичерпано. Спробуйте пізніше.';
          } else if (promptError.message.includes('cancelled')) {
            message.value = 'Вхід через Google скасовано.';
          } else if (promptError.message.includes('cookies')) {
            message.value = 'У вашому браузері заблоковані сторонні куки. Будь ласка, дозвольте їх або використовуйте інший браузер.';
          } else if (promptError.message.includes('browser')) {
            message.value = 'Ваш браузер не підтримує вхід через Google. Спробуйте інший браузер.';
          } else {
            message.value = 'Помилка входу через; Google: ' + promptError.message;
          }

          loading.value = false;
        }
      } catch (error) {
        console.error('unexpected error during google sign-in in; Register.vue:', error);
        message.value = 'несподівана помилка під час входу через google. спробуйте пізніше.';
        loading.value = false;
      }
    };

    // Handle Google Sign-In response with improved error handling and loading state management
    const handleGoogleCredentialResponse = async (response) => {
      // Set a timeout to ensure loading state is reset even if something unexpected happens
      const safetyTimeoutId = setTimeout(() => {
        if (loading.value) {
          console.warn('safety timeout triggered in; Register.vue: resetting loading state');
          loading.value = false;
          message.value = 'Час очікування відповіді від сервера вичерпано. Спробуйте пізніше.';
        }
      }, 15000); // 15 seconds safety timeout

      try {
        // Ensure loading state is active
        loading.value = true;
        message.value = '';
        successful.value = false;

        console.log('Google Sign-In callback triggered in Register.vue, processing token');

        // Validate response
        if (!response) {
          throw new Error('Empty response from Google Sign-In');
        }

        console.log('Response type in; Register.vue:', typeof response);
        console.log('response has credential in; Register.vue:', !!response.credential);

        // Get the ID token from the response
        const idToken = response.credential;
        if (!idToken) {
          throw new Error('No ID token received from Google');
        }

        console.log('ID token received in Register.vue,; length:', idToken.length);
        console.log('dispatching auth/googlelogin action from register.vue...');

        try {
          // Dispatch the Google login action
          await store.dispatch('auth/googlelogin', idToken);

          console.log('google login action completed successfully in register.vue');
          successful.value = true;

          // Clear the safety timeout as we're done
          clearTimeout(safetyTimeoutId);

          // Determine redirect path
          let redirectPath;
          if (route.query.redirect) {
            redirectPath = route.query.redirect;
          } else if (store.getters['auth/isAdminOrModerator']) {
            redirectPath = '/admin/dashboard';
          } else {
            redirectPath = '/dashboard';
          }

          console.log('Google login successful in Register.vue, redirecting to:', redirectPath);
          router.push(redirectPath);
        } catch (loginError) {
          // Clear the safety timeout as we're handling the error
          clearTimeout(safetyTimeoutId);

          successful.value = false;
          console.error('Google login error in Register.vue:', loginError);

          // Provide user-friendly error messages
          if (loginError.response && loginError.response.data) {
            console.error('error response data in; Register.vue:', loginError.response.data);

            // Handle specific error codes
            const errorData = loginError.response.data;
            if (errorData.code === 'user_not_found') {
              message.value = 'Користувача з цим Google акаунтом не знайдено.';
            } else if (errorData.code === 'invalid_token') {
              message.value = 'Недійсний токен аутентифікації. Спробуйте ще раз.';
            } else if (errorData.code === 'account_disabled') {
              message.value = 'Цей обліковий запис відключено. Зверніться до адміністратора.';
            } else {
              message.value = errorData.message || 'Помилка аутентифікації Google';
            }
          } else if (loginError.message.includes('network')) {
            message.value = 'Помилка мережі. Перевірте підключення до Інтернету.';
          } else {
            message.value = loginError.message || 'Помилка аутентифікації Google';
          }

          // Clear any potentially corrupted auth data
          store.dispatch('auth/logout');
        }
      } catch (error) {
        // Clear the safety timeout as we're handling the error
        clearTimeout(safetyTimeoutId);

        successful.value = false;
        console.error('Unexpected error during Google authentication in Register.vue:', error);
        message.value = 'Несподівана помилка під час аутентифікації Google. Спробуйте пізніше.';

        // Clear any potentially corrupted auth data
        store.dispatch('auth/logout');
      } finally {
        // Ensure loading state is reset
        loading.value = false;
      }
    };

    const schema = yup.object().shape({
      email: yup.string()
        .required('Введіть електронну пошту')
        .email('Введіть дійсну електронну пошту'),
      password: yup.string()
        .required('Введіть пароль')
        .min(8, 'Пароль має бути не менше 8 символів')
        .matches(/[A-Z]/, 'Пароль повинен містити хоча б одну велику літеру')
        .matches(/[0-9]/, 'Пароль повинен містити хоча б одну цифру')
        .matches(/[!@#$%^&*]/, 'Пароль повинен містити хоча б один спеціальний символ (!@#$%^&*)'),
      newsletter: yup.boolean(),
      // Username is optional in the form as we'll generate it from email if not provided
      username: yup.string()
    });

    const handleRegister = async (user) => {
      loading.value = true;
      message.value = '';

      try {
        console.log('Submitting registration form with data:', user);

        // Add username if not present (use email prefix)
        if (!user.username) {
          user.username = user.email.split('@')[0];
        }

        // Role is now set on the server side

        const response = await store.dispatch('auth/register', user);
        console.log('Registration response:', response);

        successful.value = true;
        message.value = 'Registration successful! Please check your email to verify your account.';
      } catch (error) {
        console.error('Registration error:', error);
        if (error.response) {
          console.error('Error response:', error.response);
          console.error('Error response data:', error.response.data);
        }

        successful.value = false;

        // Provide more specific error messages based on the error
        if (error.response && error.response.data) {
          if (error.response.data.message) {
            message.value = error.response.data.message;
          } else if (error.response.data.errors) {
            // Handle validation errors
            const errors = error.response.data.errors;
            const errorMessages = Object.values(errors).flat();
            message.value = errorMessages.join(', ');
          } else if (typeof error.response.data === 'string') {
            message.value = error.response.data;
          } else {
            message.value = 'Registration failed. Please try again.';
          }
        } else if (error.message) {
          message.value = error.message;
        } else {
          message.value = 'Registration failed. Please try again.';
        }
      } finally {
        loading.value = false;
      }
    };

    return {
      loading,
      message,
      successful,
      schema,
      handleRegister,
      errors,
      isFieldValid,
      togglePasswordVisibility,
      showPassword,
      passwordMeetsLength,
      passwordHasUppercase,
      passwordHasNumber,
      passwordHasSpecial,
      googleButton,
      handleGoogleLogin
    };
  }
};
</script>

<style scoped>
.register-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  align-items: center;
  justify-content: center;
}

.register-form {
  background-color: white;
  border-radius: 8px;
  padding: 30px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.logo-container {
  text-align: center;
  margin-bottom: 20px;
}

.logo {
  height: 50px;
}

.register-title {
  font-size: 24px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 25px;
  color: #333;
}

.form-group {
  position: relative;
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 6px;
  transition: all 0.3s;
}

.input-wrapper.has-error {
  border-color: #e74c3c;
}

.input-wrapper.is-valid {
  border-color: #2ecc71;
}

.form-input {
  width: 100%;
  padding: 12px 15px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  background: transparent;
}

.form-input:focus {
  outline: none;
}

.validation-icon {
  position: absolute;
  right: 15px;
  display: flex;
  align-items: center;
}

.error-icon {
  color: #e74c3c;
}

.valid-icon {
  color: #2ecc71;
}

.toggle-password {
  position: absolute;
  right: 15px;
  background: none;
  border: none;
  color: #777;
  cursor: pointer;
  padding: 0;
  font-size: 16px;
}

.validation-icon + .toggle-password {
  right: 40px;
}

.error-feedback {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 5px;
}

.field-hint {
  color: #777;
  font-size: 12px;
  margin-top: 5px;
}

.password-requirements {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #eee;
}

.requirements-title {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #555;
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.requirements-list li {
  font-size: 12px;
  color: #777;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.requirements-list li i {
  margin-right: 8px;
  font-size: 10px;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.requirements-list li i.fa-times {
  color: #e74c3c;
  border: 1px solid #e74c3c;
}

.requirements-list li i.fa-check {
  color: #2ecc71;
  border: 1px solid #2ecc71;
}

.requirement-met {
  color: #2ecc71 !important;
}

.checkbox-group {
  margin-top: 20px;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
}

.checkbox-input {
  margin-right: 10px;
  margin-top: 3px;
}

.checkbox-text {
  font-size: 14px;
  color: #555;
}

.register-button {
  width: 100%;
  padding: 12px;
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 20px;
}

.register-button:hover {
  background-color: #3367d6;
}

.register-button:disabled {
  background-color: #a4c2f4;
  cursor: not-allowed;
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 20px 0;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid #ddd;
}

.divider span {
  padding: 0 10px;
  color: #777;
  font-size: 14px;
}

.social-button {
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.social-button:hover {
  background-color: #f5f5f5;
}

.social-icon {
  height: 18px;
  margin-right: 10px;
}

/* Hide the default Google Sign-In button */
#google-signin-button {
  display: none;
}

.login-link {
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
  color: #555;
}

.login-link a {
  color: #4285f4;
  text-decoration: none;
  margin-left: 5px;
}

.footer {
  width: 100%;
  text-align: center;
  padding: 20px 0;
}

.footer-links {
  margin-bottom: 10px;
}

.footer-links a {
  color: #555;
  text-decoration: none;
  margin: 0 10px;
  font-size: 14px;
}

.copyright {
  color: #777;
  font-size: 12px;
}

.alert {
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  font-size: 14px;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
</style>