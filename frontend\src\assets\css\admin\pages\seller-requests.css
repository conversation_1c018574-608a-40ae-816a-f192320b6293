/* ===== ADMIN SELLER REQUESTS PAGE STYLES ===== */
/* Based on Reports page design patterns */

/* ===== SELLER REQUESTS PAGE LAYOUT ===== */
.admin-seller-requests {
  padding: var(--admin-space-2xl);
  background-color: var(--admin-bg-primary);
  min-height: 100vh;
}

.admin-seller-requests .admin-page-header {
  background: var(--admin-gradient-secondary);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-3xl);
  margin-bottom: var(--admin-space-2xl);
  color: white;
  box-shadow: var(--admin-shadow-lg);
}

.admin-seller-requests .admin-page-title {
  font-size: var(--admin-text-4xl);
  font-weight: var(--admin-font-bold);
  color: white;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-lg);
}

.admin-seller-requests .title {
  font-size: var(--admin-text-4xl);
  font-weight: var(--admin-font-bold);
  color: white;
  margin: 0;
}

/* ===== SELLER REQUEST FILTERS ===== */
.admin-seller-request-filters {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  margin-bottom: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
}

.admin-seller-request-filters .field {
  margin-bottom: var(--admin-space-lg);
}

.admin-seller-request-filters .label {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-700);
  margin-bottom: var(--admin-space-sm);
  font-size: var(--admin-text-sm);
}

.admin-seller-request-filters .input,
.admin-seller-request-filters .select select {
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-md);
  font-size: var(--admin-text-sm);
  transition: all var(--admin-transition-fast);
}

.admin-seller-request-filters .input:focus,
.admin-seller-request-filters .select select:focus {
  border-color: var(--admin-secondary);
  box-shadow: 0 0 0 3px var(--admin-secondary-bg);
  outline: none;
}

.admin-seller-request-filters .control.has-icons-left .icon {
  color: var(--admin-gray-400);
}

.admin-seller-request-filters .buttons {
  margin-top: var(--admin-space-xl);
  display: flex;
  justify-content: center;
  gap: var(--admin-space-md);
}

/* ===== SELLER REQUEST TABLE ===== */
.seller-request-table {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  margin-bottom: var(--admin-space-2xl);
}

.seller-request-table .table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.seller-request-table .table th {
  background: var(--admin-gray-50);
  color: var(--admin-gray-700);
  font-weight: var(--admin-font-semibold);
  padding: var(--admin-space-lg);
  text-align: left;
  border-bottom: 2px solid var(--admin-border-color);
  font-size: var(--admin-text-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.seller-request-table .table td {
  padding: var(--admin-space-lg);
  border-bottom: 1px solid var(--admin-border-light);
  vertical-align: middle;
  font-size: var(--admin-text-sm);
}

.seller-request-table .table tr:hover {
  background: var(--admin-gray-25);
}

.seller-request-table .table tr:last-child td {
  border-bottom: none;
}

/* ===== SELLER REQUEST STATUS ===== */
.admin-seller-request-status {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-full);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-seller-request-status.pending {
  background: var(--admin-warning-bg);
  color: var(--admin-warning-dark);
  border: 1px solid var(--admin-warning-light);
}

.admin-seller-request-status.approved {
  background: var(--admin-success-bg);
  color: var(--admin-success-dark);
  border: 1px solid var(--admin-success-light);
}

.admin-seller-request-status.rejected {
  background: var(--admin-danger-bg);
  color: var(--admin-danger-dark);
  border: 1px solid var(--admin-danger-light);
}

.admin-seller-request-status.under-review {
  background: var(--admin-info-bg);
  color: var(--admin-info-dark);
  border: 1px solid var(--admin-info-light);
}

/* ===== SELLER REQUEST ACTION BUTTONS ===== */
.admin-seller-request-actions {
  display: flex;
  gap: var(--admin-space-xs);
  align-items: center;
}

.admin-seller-request-actions .button {
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-md);
  border: 1px solid transparent;
  font-size: var(--admin-text-xs);
  transition: all var(--admin-transition-fast);
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-seller-request-actions .button.is-info {
  background: var(--admin-info);
  color: white;
  border-color: var(--admin-info);
}

.admin-seller-request-actions .button.is-info:hover {
  background: var(--admin-info-dark);
  border-color: var(--admin-info-dark);
  transform: translateY(-1px);
}

.admin-seller-request-actions .button.is-success {
  background: var(--admin-success);
  color: white;
  border-color: var(--admin-success);
}

.admin-seller-request-actions .button.is-success:hover {
  background: var(--admin-success-dark);
  border-color: var(--admin-success-dark);
  transform: translateY(-1px);
}

.admin-seller-request-actions .button.is-danger {
  background: var(--admin-danger);
  color: white;
  border-color: var(--admin-danger);
}

.admin-seller-request-actions .button.is-danger:hover {
  background: var(--admin-danger-dark);
  border-color: var(--admin-danger-dark);
  transform: translateY(-1px);
}

/* ===== SELLER REQUEST ID ===== */
.admin-seller-request-id {
  font-family: var(--admin-font-mono);
  font-size: var(--admin-text-xs);
  color: var(--admin-gray-600);
  background: var(--admin-gray-100);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  display: inline-block;
}

/* ===== BUSINESS INFO ===== */
.admin-business-info {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-business-name {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
}

.admin-business-type {
  font-size: var(--admin-text-xs);
  color: var(--admin-gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* ===== USER INFO ===== */
.admin-user-info {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-user-name {
  font-weight: var(--admin-font-medium);
  color: var(--admin-gray-900);
}

.admin-user-email {
  font-size: var(--admin-text-xs);
  color: var(--admin-gray-500);
  font-family: var(--admin-font-mono);
}

/* ===== LOADING STATES ===== */
.seller-request-table .admin-loading {
  text-align: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.seller-request-table .admin-loading i {
  font-size: 2rem;
  color: var(--admin-secondary);
  margin-bottom: var(--admin-space-lg);
  animation: spin 1s linear infinite;
}

.seller-request-table .admin-empty {
  text-align: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.seller-request-table .admin-empty i {
  font-size: 3rem;
  color: var(--admin-gray-300);
  margin-bottom: var(--admin-space-lg);
}

.seller-request-table .admin-empty-text {
  font-size: var(--admin-text-lg);
  margin-bottom: var(--admin-space-sm);
}

.seller-request-table .admin-empty-subtext {
  color: var(--admin-gray-400);
  font-size: var(--admin-text-sm);
}

/* ===== PAGINATION WRAPPER ===== */
.admin-seller-requests .pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: var(--admin-space-2xl);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .seller-request-table .table {
    font-size: var(--admin-text-xs);
  }
  
  .seller-request-table .table th,
  .seller-request-table .table td {
    padding: var(--admin-space-md);
  }
}

@media (max-width: 768px) {
  .admin-seller-requests {
    padding: var(--admin-space-lg);
  }
  
  .admin-seller-requests .admin-page-header {
    padding: var(--admin-space-2xl);
  }
  
  .admin-seller-requests .title {
    font-size: var(--admin-text-2xl);
  }
  
  .admin-seller-request-filters {
    padding: var(--admin-space-lg);
  }
  
  .seller-request-table {
    padding: var(--admin-space-lg);
    overflow-x: auto;
  }
  
  .seller-request-table .table {
    min-width: 700px;
  }
  
  .admin-seller-request-actions {
    flex-direction: column;
    gap: var(--admin-space-xs);
  }
  
  .admin-seller-request-actions .button {
    width: 100%;
    min-width: auto;
  }
}
