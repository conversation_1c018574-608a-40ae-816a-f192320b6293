import{q as u}from"./index-BKy0rL_2.js";const d={PROCESSING:0,PENDING:1,SHIPPED:2,DELIVERED:3,CANCELLED:4},c={PENDING:0,COMPLETED:1,REFUNDED:2,FAILED:3},N={UAH:0,USD:1,EUR:2},W=[{value:d.PROCESSING,label:"Processing"},{value:d.PENDING,label:"Pending"},{value:d.SHIPPED,label:"Shipped"},{value:d.DELIVERED,label:"Delivered"},{value:d.CANCELLED,label:"Cancelled"}],X=[{value:c.PENDING,label:"Pending"},{value:c.COMPLETED,label:"Completed"},{value:c.REFUNDED,label:"Refunded"},{value:c.FAILED,label:"Failed"}],F={[d.PROCESSING]:"Processing",[d.PENDING]:"Pending",[d.SHIPPED]:"Shipped",[d.DELIVERED]:"Delivered",[d.CANCELLED]:"Cancelled",Processing:"Processing",Pending:"Pending",Shipped:"Shipped",Delivered:"Delivered",Cancelled:"Cancelled"},H={[c.PENDING]:"Pending",[c.COMPLETED]:"Completed",[c.REFUNDED]:"Refunded",[c.FAILED]:"Failed",Pending:"Pending",Completed:"Completed",Refunded:"Refunded",Failed:"Failed"},x={[d.PROCESSING]:"is-info",[d.PENDING]:"is-warning",[d.SHIPPED]:"is-primary",[d.DELIVERED]:"is-success",[d.CANCELLED]:"is-danger",Processing:"is-info",Pending:"is-warning",Shipped:"is-primary",Delivered:"is-success",Cancelled:"is-danger"},G={[c.PENDING]:"is-warning",[c.COMPLETED]:"is-success",[c.REFUNDED]:"is-info",[c.FAILED]:"is-danger",Pending:"is-warning",Completed:"is-success",Refunded:"is-info",Failed:"is-danger"},I={[N.UAH]:{locale:"uk-UA",currency:"UAH"},[N.USD]:{locale:"en-US",currency:"USD"},[N.EUR]:{locale:"en-EU",currency:"EUR"},UAH:{locale:"uk-UA",currency:"UAH"},USD:{locale:"en-US",currency:"USD"},EUR:{locale:"en-EU",currency:"EUR"}},k={[d.PROCESSING]:"Processing",[d.PENDING]:"Pending",[d.SHIPPED]:"Shipped",[d.DELIVERED]:"Delivered",[d.CANCELLED]:"Cancelled"},q={[c.PENDING]:"Pending",[c.COMPLETED]:"Completed",[c.REFUNDED]:"Refunded",[c.FAILED]:"Failed"},Z=e=>F[e]||e||"Unknown",Q=e=>H[e]||e||"Unknown",ee=e=>x[e]||"is-light",te=e=>G[e]||"is-light",re=e=>I[e]||I[N.UAH],C=e=>k[e]||e,_=e=>q[e]||e,B=e=>{if(e==null)return d.PENDING;if(typeof e=="number")return Object.values(d).includes(e)?e:d.PENDING;if(typeof e=="string")switch(e.toUpperCase()){case"PROCESSING":return d.PROCESSING;case"PENDING":return d.PENDING;case"SHIPPED":return d.SHIPPED;case"DELIVERED":return d.DELIVERED;case"CANCELLED":return d.CANCELLED;default:return d.PENDING}return d.PENDING},Y=e=>{if(e==null)return c.PENDING;if(typeof e=="number")return Object.values(c).includes(e)?e:c.PENDING;if(typeof e=="string")switch(e.toUpperCase()){case"PENDING":return c.PENDING;case"COMPLETED":return c.COMPLETED;case"REFUNDED":return c.REFUNDED;case"FAILED":return c.FAILED;default:return c.PENDING}return c.PENDING},se=e=>Object.values(d).includes(B(e)),oe=e=>Object.values(c).includes(Y(e)),g={NETWORK:"NETWORK",VALIDATION:"VALIDATION",AUTHENTICATION:"AUTHENTICATION",AUTHORIZATION:"AUTHORIZATION",NOT_FOUND:"NOT_FOUND",SERVER:"SERVER",UNKNOWN:"UNKNOWN"},U={[g.NETWORK]:"Network error. Please check your connection and try again.",[g.VALIDATION]:"Please check your input and try again.",[g.AUTHENTICATION]:"Authentication failed. Please log in again.",[g.AUTHORIZATION]:"You do not have permission to perform this action.",[g.NOT_FOUND]:"The requested resource was not found.",[g.SERVER]:"Server error. Please try again later.",[g.UNKNOWN]:"An unexpected error occurred. Please try again."},v={ORDER_NOT_FOUND:"Order not found. It may have been deleted or you may not have access to it.",ORDER_UPDATE_FAILED:"Failed to update order. Please check your input and try again.",ORDER_STATUS_UPDATE_FAILED:"Failed to update order status. Please try again.",PAYMENT_STATUS_UPDATE_FAILED:"Failed to update payment status. Please try again.",ORDER_ITEMS_LOAD_FAILED:"Failed to load order items. Please refresh the page.",ORDER_LIST_LOAD_FAILED:"Failed to load orders list. Please refresh the page.",ORDER_DELETE_FAILED:"Failed to delete order. Please try again.",ORDER_NOTE_ADD_FAILED:"Failed to add order note. Please try again."},M=e=>{if(!e)return g.UNKNOWN;if(!e.response)return g.NETWORK;switch(e.response.status){case 400:return g.VALIDATION;case 401:return g.AUTHENTICATION;case 403:return g.AUTHORIZATION;case 404:return g.NOT_FOUND;case 422:return g.VALIDATION;case 500:case 502:case 503:case 504:return g.SERVER;default:return g.UNKNOWN}},A=(e,t=null)=>{var s,o,n,i;const r=M(e);if((o=(s=e.response)==null?void 0:s.data)!=null&&o.message)return e.response.data.message;if((i=(n=e.response)==null?void 0:n.data)!=null&&i.errors){if(Array.isArray(e.response.data.errors))return e.response.data.errors.join(", ");if(typeof e.response.data.errors=="object")return Object.values(e.response.data.errors).flat().join(", ")}return t&&v[t]?v[t]:U[r]||U[g.UNKNOWN]},R=(e,t="Unknown",r={})=>{const s=M(e);console.group(`🚨 ${s} Error in ${t}`),console.error("Error message:",e.message),console.error("Error type:",s),e.response&&(console.error("Response status:",e.response.status),console.error("Response statusText:",e.response.statusText),console.error("Response data:",e.response.data),console.error("Response headers:",e.response.headers)),e.request&&console.error("Request:",e.request),e.config&&console.error("Request config:",{url:e.config.url,method:e.config.method,data:e.config.data,params:e.config.params}),Object.keys(r).length>0&&console.error("Additional data:",r),console.error("Stack trace:",e.stack),console.groupEnd()};class V{constructor(){this.events={},this.eventHistory=[],this.maxHistorySize=100,this.debugMode=!1}on(t,r,s={}){this.events[t]||(this.events[t]=[]);const o={callback:r,id:this._generateId(),once:s.once||!1,priority:s.priority||0,component:s.component||"unknown"};return this.events[t].push(o),this.events[t].sort((n,i)=>i.priority-n.priority),this.debugMode&&console.log(`📡 EventBus: Subscribed to '${t}' (component: ${o.component}, id: ${o.id})`),()=>{this.events[t]=this.events[t].filter(n=>n.id!==o.id),this.debugMode&&console.log(`📡 EventBus: Unsubscribed from '${t}' (id: ${o.id})`)}}emit(t,r={}){const s={event:t,data:r,timestamp:Date.now(),id:this._generateId()};if(this._addToHistory(s),this.debugMode&&console.log(`📡 EventBus: Emitting '${t}'`,s),this.events[t]){const o=[];this.events[t].forEach(n=>{try{n.callback(s),n.once&&o.push(n.id)}catch(i){console.error(`📡 EventBus: Error in listener for '${t}':`,i)}}),o.length>0&&(this.events[t]=this.events[t].filter(n=>!o.includes(n.id)))}return s.id}off(t){this.debugMode&&this.events[t]&&console.log(`📡 EventBus: Removing all listeners for '${t}' (${this.events[t].length} listeners)`),delete this.events[t]}getHistory(t=null){return t?this.eventHistory.filter(r=>r.event===t):[...this.eventHistory]}clearHistory(){this.eventHistory=[],this.debugMode&&console.log("📡 EventBus: Event history cleared")}getListenersCount(t=null){var r;return t?((r=this.events[t])==null?void 0:r.length)||0:Object.values(this.events).reduce((s,o)=>s+o.length,0)}getDebugInfo(){return{events:Object.keys(this.events),totalListeners:this.getListenersCount(),historySize:this.eventHistory.length,eventDetails:Object.entries(this.events).map(([t,r])=>({event:t,listenerCount:r.length,listeners:r.map(s=>({id:s.id,component:s.component,priority:s.priority,once:s.once}))}))}}_generateId(){return Math.random().toString(36).substr(2,9)}_addToHistory(t){this.eventHistory.push(t),this.eventHistory.length>this.maxHistorySize&&(this.eventHistory=this.eventHistory.slice(-this.maxHistorySize))}}const f=new V,D={ORDER_UPDATED:"order:updated",ORDER_STATUS_CHANGED:"order:status-changed",ORDER_PAYMENT_STATUS_CHANGED:"order:payment-status-changed",ORDER_DELETED:"order:deleted",ORDER_CREATED:"order:created",ORDER_ITEM_ADDED:"order:item-added",ORDER_ITEM_UPDATED:"order:item-updated",ORDER_ITEM_REMOVED:"order:item-removed",ORDER_NOTE_ADDED:"order:note-added",ORDER_REFUNDED:"order:refunded",ORDER_CANCELLED:"order:cancelled",PRODUCT_UPDATED:"product:updated",PRODUCT_CREATED:"product:created",PRODUCT_DELETED:"product:deleted",PRODUCT_STOCK_CHANGED:"product:stock-changed",CATEGORY_UPDATED:"category:updated",CATEGORY_CREATED:"category:created",CATEGORY_DELETED:"category:deleted",COMPANY_UPDATED:"company:updated",COMPANY_CREATED:"company:created",COMPANY_DELETED:"company:deleted",COMPANY_STATUS_CHANGED:"company:status-changed",USER_UPDATED:"user:updated",USER_CREATED:"user:created",USER_DELETED:"user:deleted",USER_ROLE_CHANGED:"user:role-changed",CACHE_INVALIDATED:"system:cache-invalidated",DATA_REFRESH_NEEDED:"system:data-refresh-needed",ERROR_OCCURRED:"system:error-occurred",NOTIFICATION_SHOWN:"system:notification-shown"},O=(e,t,r={})=>({type:e,orderId:t,timestamp:Date.now(),...r}),P={markOrdersForRefresh(){localStorage.setItem("orders_need_refresh","true"),localStorage.setItem("orders_refresh_timestamp",Date.now().toString())},shouldRefreshOrders(){return localStorage.getItem("orders_need_refresh")==="true"},clearOrdersRefreshFlag(){localStorage.removeItem("orders_need_refresh"),localStorage.removeItem("orders_refresh_timestamp")},getRefreshTimestamp(){const e=localStorage.getItem("orders_refresh_timestamp");return e?parseInt(e):null}},j={maxRetries:3,baseDelay:1e3,maxDelay:1e4,retryableStatuses:[408,429,500,502,503,504]},w=async(e,t="API call",r=j)=>{var o,n,i;let s;for(let a=0;a<=r.maxRetries;a++)try{const l=await e();return a>0&&console.log(`✅ ${t} succeeded on attempt ${a+1}`),l}catch(l){s=l;const m=r.retryableStatuses.includes((o=l.response)==null?void 0:o.status)||l.code==="NETWORK_ERROR"||((n=l.message)==null?void 0:n.includes("timeout"))||((i=l.message)==null?void 0:i.includes("Network Error"));if(a===r.maxRetries||!m){console.error(`❌ ${t} failed after ${a+1} attempts:`,l);break}const y=Math.min(r.baseDelay*Math.pow(2,a),r.maxDelay);console.warn(`⚠️ ${t} failed on attempt ${a+1}, retrying in ${y}ms...`,l.message),await new Promise(p=>setTimeout(p,y))}throw s};class z{constructor(){this.cache=new Map,this.version=1,this.CACHE_DURATION=3e4,this.MAX_CACHE_SIZE=100}generateKey(t){return`orders_v${this.version}_${JSON.stringify(t)}`}get(t){const r=this.generateKey(t),s=this.cache.get(r);return s&&Date.now()-s.timestamp<this.CACHE_DURATION?(console.log("📦 Cache hit for:",r),s.data):(s&&(console.log("⏰ Cache expired for:",r),this.cache.delete(r)),null)}set(t,r){const s=this.generateKey(t);if(this.cache.size>=this.MAX_CACHE_SIZE){const o=this.cache.keys().next().value;this.cache.delete(o),console.log("🧹 Removed oldest cache entry:",o)}this.cache.set(s,{data:r,timestamp:Date.now()}),console.log("💾 Cached data for:",s)}invalidate(){this.version++,this.cache.clear(),console.log("🔄 Cache invalidated, new version:",this.version)}clear(){this.cache.clear(),console.log("🧹 Cache cleared")}invalidatePattern(t){const r=[];for(const s of this.cache.keys())s.includes(t)&&r.push(s);r.forEach(s=>{this.cache.delete(s),console.log("🗑️ Invalidated cache entry:",s)})}getStats(){return{size:this.cache.size,version:this.version,maxSize:this.MAX_CACHE_SIZE,duration:this.CACHE_DURATION}}}const h=new z;class K{constructor(){this.logs=[],this.maxLogs=1e3,this.sessionId=Math.random().toString(36).substr(2,9),this.startTime=Date.now()}log(t,r,s={},o=null){const n={id:Math.random().toString(36).substr(2,9),timestamp:Date.now(),sessionId:this.sessionId,level:t,operation:r,data:JSON.parse(JSON.stringify(s)),error:o?{message:o.message,stack:o.stack,name:o.name}:null,userAgent:navigator.userAgent,url:window.location.href};this.logs.push(n),this.logs.length>this.maxLogs&&(this.logs=this.logs.slice(-this.maxLogs));const i=`[Orders ${t.toUpperCase()}]`,a=`${r}${s.orderId?` (Order: ${s.orderId})`:""}`;switch(t){case"error":console.error(i,a,s,o);break;case"warn":console.warn(i,a,s);break;case"info":console.info(i,a,s);break;case"debug":console.log(i,a,s);break}return n}error(t,r,s){return this.log("error",t,r,s)}warn(t,r){return this.log("warn",t,r)}info(t,r){return this.log("info",t,r)}debug(t,r){return this.log("debug",t,r)}getLogs(t={}){let r=[...this.logs];return t.level&&(r=r.filter(s=>s.level===t.level)),t.operation&&(r=r.filter(s=>s.operation.toLowerCase().includes(t.operation.toLowerCase()))),t.orderId&&(r=r.filter(s=>s.data.orderId===t.orderId)),t.since&&(r=r.filter(s=>s.timestamp>=t.since)),r.sort((s,o)=>o.timestamp-s.timestamp)}exportLogs(){const t={sessionId:this.sessionId,startTime:this.startTime,exportTime:Date.now(),userAgent:navigator.userAgent,url:window.location.href,cacheStats:h.getStats(),logs:this.logs},r=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),s=URL.createObjectURL(r),o=document.createElement("a");o.href=s,o.download=`orders-logs-${this.sessionId}-${Date.now()}.json`,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(s),this.info("exportLogs",{exportedCount:this.logs.length})}clear(){const t=this.logs.length;this.logs=[],this.info("clearLogs",{clearedCount:t})}getDiagnostics(){const t=Date.now(),r=t-this.startTime,s=this.logs.reduce((n,i)=>(n[i.level]=(n[i.level]||0)+1,n),{}),o=this.getLogs({level:"error",since:t-3e5});return{sessionId:this.sessionId,sessionDuration:r,totalLogs:this.logs.length,logsByLevel:s,recentErrors:o.length,cacheStats:h.getStats(),lastActivity:this.logs.length>0?this.logs[this.logs.length-1].timestamp:this.startTime}}}const S=new K,L={forceRefresh(){h.invalidate(),S.info("forceRefresh",{cacheStats:h.getStats()})},getCacheStats(){return h.getStats()},getLogs(e){return S.getLogs(e)},exportLogs(){return S.exportLogs()},getDiagnostics(){return S.getDiagnostics()},clearLogs(){return S.clear()},async getAll(e={}){return L.getOrders(e)},async getAllAdmin(e={}){return L.getOrders(e)},async getOrders(e={}){var r;const t=Date.now();S.info("getOrders:start",{params:e});try{console.log("Requesting orders with params:",e);const s=e.status||e.paymentStatus||e.search||e.filter;if(!s){const a=h.get(e);if(a)return a}const o={};if(e.page&&(o.page=e.page),e.pageSize&&(o.pageSize=e.pageSize),e.orderBy&&(o.orderBy=e.orderBy),e.sortBy&&(o.orderBy=e.sortBy),e.descending!==void 0&&(o.descending=e.descending),e.sortOrder&&(o.descending=e.sortOrder==="desc",console.log("🔄 Setting sort order:",e.sortOrder,"-> descending:",o.descending)),e.search&&e.search.trim()!==""?o.filter=e.search.trim():e.filter&&e.filter.trim()!==""&&(o.filter=e.filter.trim()),e.status!==void 0&&e.status!==null&&e.status!==""){const a=typeof e.status=="string"?parseInt(e.status,10):e.status;isNaN(a)||(o.status=a,console.log("🔄 Setting order status filter:",a))}if(e.paymentStatus!==void 0&&e.paymentStatus!==null&&e.paymentStatus!==""){const a=typeof e.paymentStatus=="string"?parseInt(e.paymentStatus,10):e.paymentStatus;isNaN(a)||(o.paymentStatus=a,console.log("🔄 Setting payment status filter:",a))}e.dateFrom&&(o.dateFrom=e.dateFrom),e.dateTo&&(o.dateTo=e.dateTo),console.log("📊 Final API params being sent:",o),console.log("📋 Original params received:",e),s&&(console.log("🧹 Clearing cache due to filters"),h.clear());const n=await u.get("/api/admin/orders",{params:o});if(console.log("Orders API response:",n),n.data&&n.data.success&&n.data.data){const a=n.data.data;console.log("Processing ApiResponse format:",a);const l={success:!0,data:a.data||a.items||[],totalItems:a.total||a.totalItems||0,currentPage:a.currentPage||a.page||e.page||1,pageSize:a.perPage||a.pageSize||e.pageSize||10,totalPages:a.lastPage||a.totalPages||1};return console.log("Processed result:",l),s||h.set(e,l),l}if(n.data&&n.data.items){const a={success:!0,data:n.data.items||[],totalItems:n.data.totalItems||n.data.total||0,currentPage:n.data.currentPage||n.data.page||e.page||1,pageSize:n.data.pageSize||n.data.perPage||e.pageSize||10,totalPages:n.data.totalPages||n.data.lastPage||1};return s||h.set(e,a),a}const i={success:!0,data:n.data||[],totalItems:0,currentPage:1,pageSize:10,totalPages:1};return s||h.set(e,i),i}catch(s){const o=Date.now()-t;S.error("getOrders:error",{params:e,duration:o,errorType:s.name,statusCode:(r=s.response)==null?void 0:r.status},s),R(s,"getOrders",{params:e});const n=A(s,"ORDER_LIST_LOAD_FAILED"),i=new Error(n);throw i.originalError=s,i.context="getOrders",i}},async getById(e){return this.getOrderById(e)},async update(e,t){var r,s,o,n;try{console.log("=== ORDER UPDATE DEBUG ==="),console.log("Order ID:",e),console.log("Original frontend data:",JSON.stringify(t,null,2)),console.log("Data analysis:"),console.log("- status type:",typeof t.status,"value:",t.status),console.log("- paymentStatus type:",typeof t.paymentStatus,"value:",t.paymentStatus),console.log("- shippingAddress type:",typeof t.shippingAddress,"value:",t.shippingAddress),console.log("- items type:",typeof t.items,"length:",(r=t.items)==null?void 0:r.length),console.log("- trackingNumber type:",typeof t.trackingNumber,"value:",t.trackingNumber),console.log("- shippingMethod type:",typeof t.shippingMethod,"value:",t.shippingMethod),console.log("- notes type:",typeof t.notes,"value:",t.notes);const i=C(t.status)||((s=t.status)==null?void 0:s.toString()),a=_(t.paymentStatus)||((o=t.paymentStatus)==null?void 0:o.toString());console.log("Status transformation:",t.status,"->",i),console.log("Payment status transformation:",t.paymentStatus,"->",a);let l=[];t.items&&Array.isArray(t.items)&&(l=t.items.filter(E=>E&&E.id).map(E=>{console.log("Transforming item:",E);const b=Math.max(1,parseInt(E.quantity)||1),$=parseFloat(E.price||E.priceAmount||0);return{id:E.id,quantity:b,priceAmount:$,priceCurrency:E.priceCurrency||"UAH"}}));let m=t.notes||t.comment||"";Array.isArray(m)&&(m=m.join("; ")),typeof m!="string"&&(m=String(m||""));const y={status:i,paymentStatus:a,shippingAddress:((n=t.shippingAddress)==null?void 0:n.address1)||t.shippingAddress,trackingNumber:t.trackingNumber||null,shippingMethod:t.shippingMethod||t.shippingMethodName||null,notes:m,items:l},p={};Object.keys(y).forEach(E=>{y[E]!==null&&y[E]!==void 0&&(p[E]=y[E])}),console.log("Final request data (before cleaning):",JSON.stringify(y,null,2)),console.log("Final request data (after cleaning):",JSON.stringify(p,null,2)),console.log("Request data validation:"),console.log("- All required fields present:",{status:!!p.status,paymentStatus:!!p.paymentStatus,items:Array.isArray(p.items)}),console.log("- Data types check:",{status:typeof p.status,paymentStatus:typeof p.paymentStatus,shippingAddress:typeof p.shippingAddress,trackingNumber:typeof p.trackingNumber,shippingMethod:typeof p.shippingMethod,notes:typeof p.notes,items:Array.isArray(p.items)?"array":typeof p.items});const T=await u.put(`/api/admin/orders/${e}`,p);return console.log("✅ Update order response:",T),console.log("=== ORDER UPDATE SUCCESS ==="),h.invalidate(),P.markOrdersForRefresh(),f.emit(D.ORDER_UPDATED,{orderId:e,data:T.data}),T.data}catch(i){R(i,"updateOrder",{orderId:e,requestData:t});const a=A(i,"ORDER_UPDATE_FAILED"),l=new Error(a);throw l.originalError=i,l.context="updateOrder",l.orderId=e,l}},async getOrderById(e){try{console.log(`Fetching order ${e} from API...`);const t=await u.get(`/api/admin/orders/${e}`);if(console.log("Order API response:",t),t.data&&t.data.success&&t.data.data){const r=t.data.data;console.log("Order data extracted:",r);const s={id:r.id,customerId:r.customerId,customerName:r.customerName,customerEmail:r.customerEmail,total:r.totalPriceAmount,totalPriceAmount:r.totalPriceAmount,subtotal:r.totalPriceAmount,tax:0,shipping:0,discount:0,status:r.status!==void 0?r.status:0,paymentStatus:r.paymentStatus!==void 0?r.paymentStatus:0,paymentStatusText:r.paymentStatusText||"Pending",paymentMethod:r.paymentMethodText||"Card",paymentMethodText:r.paymentMethodText||"Card",shippingMethod:r.shippingMethodName||"Standard",shippingMethodName:r.shippingMethodName||"Standard",shippingAddress:{address1:r.shippingAddressLine||"N/A",city:r.shippingCity||"N/A",country:r.shippingCountry||"N/A"},items:[],notes:"",createdAt:r.createdAt,updatedAt:r.createdAt};try{const o=await this.getOrderItems(r.id);o&&o.success&&o.data&&Array.isArray(o.data)?s.items=o.data:s.items=[]}catch(o){console.warn("Could not fetch order items:",o),s.items=[]}return s}if(t.data)return t.data;throw new Error("Invalid response format")}catch(t){return console.error(`Error fetching order ${e}:`,t),t.response&&t.response.status===404?null:{id:e,customerId:"1",customerName:"John Doe",customerEmail:"<EMAIL>",total:156.78,totalPriceAmount:156.78,subtotal:149.99,tax:0,shipping:0,discount:0,status:0,paymentStatus:1,paymentStatusText:"Paid",paymentMethod:"Credit Card",paymentMethodText:"Credit Card",shippingMethod:"Standard",shippingMethodName:"Standard",shippingAddress:{firstName:"John",lastName:"Doe",address1:"123 Main St",address2:"Apt 4B",city:"New York",state:"NY",postalCode:"10001",country:"USA",phone:"************"},billingAddress:{firstName:"John",lastName:"Doe",address1:"123 Main St",address2:"Apt 4B",city:"New York",state:"NY",postalCode:"10001",country:"USA",phone:"************"},items:[{id:"1",productId:"1",productName:"Smartphone X",quantity:1,price:99.99,total:99.99},{id:"2",productId:"2",productName:"Wireless Headphones",quantity:1,price:49.99,total:49.99}],notes:"",createdAt:new Date(Date.now()-1e3*60*60*2),updatedAt:new Date(Date.now()-1e3*60*30)}}},async updateOrderStatus(e,t){try{console.log("=== ORDER STATUS UPDATE DEBUG ==="),console.log("Order ID:",e),console.log("Input status:",t,"type:",typeof t);const r=C(t)||t;console.log("Converted status:",r);const s=["Processing","Pending","Shipped","Delivered","Cancelled"];if(!s.includes(r))throw new Error(`Invalid order status: ${r}. Valid statuses: ${s.join(", ")}`);const o={status:r,timestamp:Date.now(),requestId:Math.random().toString(36).substr(2,9)};console.log("Request body:",JSON.stringify(o,null,2));const n=await w(()=>u.patch(`/api/admin/orders/${e}/status`,o,{headers:{"X-Request-ID":o.requestId,"X-Idempotency-Key":`order-status-${e}-${r}-${o.timestamp}`}}),`Order ${e} status update to ${r}`);return console.log("✅ Order status update successful:",n),console.log("=== ORDER STATUS UPDATE SUCCESS ==="),h.invalidate(),P.markOrdersForRefresh(),f.emit(D.ORDER_STATUS_CHANGED,O(D.ORDER_STATUS_CHANGED,e,{oldStatus:t,newStatus:r,data:n.data,requestId:o.requestId})),n.data}catch(r){R(r,"updateOrderStatus",{orderId:e,status:t}),f.emit(D.ERROR_OCCURRED,O(D.ERROR_OCCURRED,e,{operation:"updateOrderStatus",error:r.message,status:t}));const s=A(r,"ORDER_STATUS_UPDATE_FAILED"),o=new Error(s);throw o.originalError=r,o.context="updateOrderStatus",o.orderId=e,o}},async updatePaymentStatus(e,t){try{console.log("=== PAYMENT STATUS UPDATE DEBUG ==="),console.log("Order ID:",e),console.log("Input payment status:",t,"type:",typeof t);const r=_(t)||t;console.log("Converted payment status:",r);const s=["Pending","Completed","Refunded","Failed"];if(!s.includes(r))throw new Error(`Invalid payment status: ${r}. Valid statuses: ${s.join(", ")}`);const o={paymentStatus:r,timestamp:Date.now(),requestId:Math.random().toString(36).substr(2,9)};console.log("Request body:",JSON.stringify(o,null,2));const n=await w(()=>u.patch(`/api/admin/orders/${e}/payment-status`,o,{headers:{"X-Request-ID":o.requestId,"X-Idempotency-Key":`payment-status-${e}-${r}-${o.timestamp}`}}),`Order ${e} payment status update to ${r}`);return console.log("✅ Payment status update successful:",n),console.log("=== PAYMENT STATUS UPDATE SUCCESS ==="),h.invalidate(),P.markOrdersForRefresh(),f.emit(D.ORDER_PAYMENT_STATUS_CHANGED,O(D.ORDER_PAYMENT_STATUS_CHANGED,e,{oldPaymentStatus:t,newPaymentStatus:r,data:n.data,requestId:o.requestId})),n.data}catch(r){R(r,"updatePaymentStatus",{orderId:e,paymentStatus:t}),f.emit(D.ERROR_OCCURRED,O(D.ERROR_OCCURRED,e,{operation:"updatePaymentStatus",error:r.message,paymentStatus:t}));const s=A(r,"PAYMENT_STATUS_UPDATE_FAILED"),o=new Error(s);throw o.originalError=r,o.context="updatePaymentStatus",o.orderId=e,o}},async addOrderNote(e,t){try{return(await u.post(`/api/admin/orders/${e}/notes`,{note:t})).data}catch(r){return console.error(`Error adding note to order ${e}:`,r),{success:!0,note:{id:Math.floor(Math.random()*1e3).toString(),content:t,createdAt:new Date,createdBy:"Admin"}}}},async getOrderNotes(e){try{return(await u.get(`/api/admin/orders/${e}/notes`)).data}catch(t){return console.error(`Error fetching notes for order ${e}:`,t),[{id:"1",content:"Order received and processing",createdAt:new Date(Date.now()-1e3*60*60*2),createdBy:"System"},{id:"2",content:"Payment confirmed",createdAt:new Date(Date.now()-1e3*60*60),createdBy:"System"},{id:"3",content:"Customer requested expedited shipping",createdAt:new Date(Date.now()-1e3*60*30),createdBy:"Admin"}]}},async refundOrder(e,t,r){try{return(await u.post(`/api/admin/orders/${e}/refund`,{amount:t,reason:r})).data}catch(s){return console.error(`Error processing refund for order ${e}:`,s),{success:!0,refund:{id:Math.floor(Math.random()*1e3).toString(),amount:t,reason:r,createdAt:new Date}}}},async exportOrders(e={}){try{return(await u.get("/api/admin/orders/export",{params:e,responseType:"blob"})).data}catch(t){console.error("Error exporting orders:",t);const r=`Order ID,Customer,Email,Date,Total,Status,Payment Status
`,s=["ORD-1001,John Doe,<EMAIL>,2023-01-01,156.78,Processing,Paid","ORD-1002,Jane Smith,<EMAIL>,2023-01-02,89.99,Pending,Pending","ORD-1003,Robert Johnson,<EMAIL>,2023-01-03,245.50,Shipped,Paid","ORD-1004,Emily Davis,<EMAIL>,2023-01-04,78.25,Delivered,Paid","ORD-1005,Michael Wilson,<EMAIL>,2023-01-05,189.99,Cancelled,Refunded"].join(`
`),o=r+s;return new Blob([o],{type:"text/csv"})}},async getOrderStats(e="month"){try{return(await u.get(`/api/admin/orders/stats?period=${e}`)).data}catch(t){return console.error("Error fetching order stats:",t),{total:356,totalRevenue:28456.78,averageOrderValue:79.93,byStatus:{Pending:45,Processing:32,Shipped:18,Delivered:256,Cancelled:5},byPeriod:[{date:"2023-01-01",count:12,revenue:956.78},{date:"2023-01-02",count:15,revenue:1245.5},{date:"2023-01-03",count:8,revenue:678.25},{date:"2023-01-04",count:20,revenue:1789.99},{date:"2023-01-05",count:18,revenue:1456.78}]}}},async getOrdersByCustomer(e,t={}){try{const r=await u.get(`/api/admin/users/${e}/orders`,{params:t});return r.data&&r.data.data?{orders:r.data.data.items||[],pagination:{total:r.data.data.totalItems||0,page:r.data.data.currentPage||1,limit:r.data.data.pageSize||10,totalPages:r.data.data.totalPages||1}}:r.data}catch(r){return console.error(`Error fetching orders for user ${e}:`,r),{orders:[{id:"ORD-1001",total:156.78,status:"Processing",paymentStatus:"Paid",createdAt:new Date(Date.now()-1e3*60*60*2)},{id:"ORD-1002",total:89.99,status:"Delivered",paymentStatus:"Paid",createdAt:new Date(Date.now()-1e3*60*60*24*7)}],pagination:{total:2,page:1,limit:10,totalPages:1}}}},async cancelOrder(e,t){try{return(await u.post(`/api/admin/orders/${e}/cancel`,{reason:t})).data}catch(r){return console.error(`Error cancelling order ${e}:`,r),{success:!0,order:{id:e,status:"Cancelled",updatedAt:new Date}}}},async resendOrderConfirmation(e){try{return(await u.post(`/api/admin/orders/${e}/resend-confirmation`)).data}catch(t){return console.error(`Error resending confirmation for order ${e}:`,t),{success:!0,message:"Order confirmation email has been resent."}}},async getOrderItems(e,t={}){try{console.log(`Fetching items for order ${e}...`);const r=await u.get(`/api/admin/orders/${e}/items`,{params:t});if(console.log("Order items API response:",r),r.data&&r.data.success&&r.data.data){const s=r.data.data;return console.log("Order items data extracted:",s),{success:!0,data:(s.data||s.items||[]).map(i=>({id:i.id,productId:i.productId,productName:`Product ${i.productId}`,productImage:null,quantity:i.quantity,price:parseFloat(i.priceAmount||0),total:parseFloat(i.priceAmount||0)*(i.quantity||0)}))}}return r.data&&Array.isArray(r.data)?{success:!0,data:r.data}:{success:!0,data:[]}}catch(r){return console.error(`Error fetching items for order ${e}:`,r),{success:!0,data:[]}}},async updateOrderItem(e,t,r){try{return(await u.put(`/api/admin/orders/${e}/items/${t}`,r)).data}catch(s){return console.error(`Error updating item ${t} in order ${e}:`,s),{success:!0,item:{id:t,...r,updatedAt:new Date}}}},async addOrderItem(e,t){try{return(await u.post(`/api/admin/orders/${e}/items`,t)).data}catch(r){return console.error(`Error adding item to order ${e}:`,r),{success:!0,item:{id:Math.floor(Math.random()*1e3).toString(),...t,createdAt:new Date}}}},async removeOrderItem(e,t){try{return(await u.delete(`/api/admin/orders/${e}/items/${t}`)).data}catch(r){return console.error(`Error removing item ${t} from order ${e}:`,r),{success:!0,message:"Item removed successfully"}}}};export{D as E,W as O,X as P,Z as a,te as b,Q as c,re as d,f as e,oe as f,ee as g,B as h,se as i,L as o,Y as p,P as u};
