<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Export Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>Reports Export Functionality Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Financial Report Export (Excel)</h2>
        <button onclick="testFinancialExcel()">Test Financial Excel Export</button>
        <div id="result1" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Financial Report Export (CSV)</h2>
        <button onclick="testFinancialCSV()">Test Financial CSV Export</button>
        <div id="result2" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Sales Report Export</h2>
        <button onclick="testSalesExport()">Test Sales Excel Export</button>
        <div id="result3" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 4: Products Report Export</h2>
        <button onclick="testProductsExport()">Test Products Excel Export</button>
        <div id="result4" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5296/api/admin/reports';
        
        async function downloadFile(url, filename) {
            try {
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const blob = await response.blob();
                const downloadUrl = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(downloadUrl);
                
                return { success: true, size: blob.size };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        async function testFinancialExcel() {
            const resultDiv = document.getElementById('result1');
            resultDiv.innerHTML = 'Testing...';
            resultDiv.className = 'result';
            
            const url = `${API_BASE}/financial/export/excel?startDate=2025-01-01T00:00:00Z&endDate=2025-06-26T23:59:59Z`;
            const result = await downloadFile(url, 'financial-report-test.xlsx');
            
            if (result.success) {
                resultDiv.innerHTML = `✅ Success! File downloaded (${result.size} bytes)`;
                resultDiv.className = 'result success';
            } else {
                resultDiv.innerHTML = `❌ Error: ${result.error}`;
                resultDiv.className = 'result error';
            }
        }
        
        async function testFinancialCSV() {
            const resultDiv = document.getElementById('result2');
            resultDiv.innerHTML = 'Testing...';
            resultDiv.className = 'result';
            
            const url = `${API_BASE}/financial/export/csv?startDate=2025-01-01T00:00:00Z&endDate=2025-06-26T23:59:59Z`;
            const result = await downloadFile(url, 'financial-report-test.csv');
            
            if (result.success) {
                resultDiv.innerHTML = `✅ Success! File downloaded (${result.size} bytes)`;
                resultDiv.className = 'result success';
            } else {
                resultDiv.innerHTML = `❌ Error: ${result.error}`;
                resultDiv.className = 'result error';
            }
        }
        
        async function testSalesExport() {
            const resultDiv = document.getElementById('result3');
            resultDiv.innerHTML = 'Testing...';
            resultDiv.className = 'result';
            
            const url = `${API_BASE}/sales/export/excel?startDate=2025-01-01T00:00:00Z&endDate=2025-06-26T23:59:59Z`;
            const result = await downloadFile(url, 'sales-report-test.xlsx');
            
            if (result.success) {
                resultDiv.innerHTML = `✅ Success! File downloaded (${result.size} bytes)`;
                resultDiv.className = 'result success';
            } else {
                resultDiv.innerHTML = `❌ Error: ${result.error}`;
                resultDiv.className = 'result error';
            }
        }
        
        async function testProductsExport() {
            const resultDiv = document.getElementById('result4');
            resultDiv.innerHTML = 'Testing...';
            resultDiv.className = 'result';
            
            const url = `${API_BASE}/products/export/excel?startDate=2025-01-01T00:00:00Z&endDate=2025-06-26T23:59:59Z`;
            const result = await downloadFile(url, 'products-report-test.xlsx');
            
            if (result.success) {
                resultDiv.innerHTML = `✅ Success! File downloaded (${result.size} bytes)`;
                resultDiv.className = 'result success';
            } else {
                resultDiv.innerHTML = `❌ Error: ${result.error}`;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
