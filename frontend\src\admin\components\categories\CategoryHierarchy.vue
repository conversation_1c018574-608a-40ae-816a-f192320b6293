<template>
  <div class="category-hierarchy">
    <div v-if="categories.length === 0" class="has-text-centered py-6">
      <span class="icon is-large has-text-grey-light">
        <i class="fas fa-folder-open fa-3x"></i>
      </span>
      <p class="title is-5 has-text-grey">No categories found</p>
      <p class="subtitle is-6 has-text-grey">Create your first category to get started</p>
    </div>

    <div v-else class="hierarchy-container">
      <CategoryHierarchyItem
        v-for="category in rootCategories"
        :key="category.id"
        :category="category"
        :level="0"
        @edit="$emit('edit', $event)"
        @delete="$emit('delete', $event)"
        @add-child="$emit('add-child', $event)"
        @view-products="$emit('view-products', $event)"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import CategoryHierarchyItem from './CategoryHierarchyItem.vue';

const props = defineProps({
  categories: {
    type: Array,
    default: () => []
  }
});

defineEmits(['edit', 'delete', 'add-child', 'view-products']);

// Build hierarchy recursively
const buildHierarchy = (categories, parentId = null) => {
  return categories
    .filter(cat => cat.parentId === parentId)
    .map(cat => ({
      ...cat,
      children: buildHierarchy(categories, cat.id)
    }));
};

const rootCategories = computed(() => {
  return buildHierarchy(props.categories);
});
</script>

<style scoped>
.category-hierarchy {
  min-height: 400px;
}

.hierarchy-container {
  padding: 1rem 0;
}
</style>
