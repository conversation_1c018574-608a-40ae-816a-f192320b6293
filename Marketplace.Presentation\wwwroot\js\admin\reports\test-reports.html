<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports API Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #1f2937;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h2 {
            color: #374151;
            margin-top: 0;
            margin-bottom: 15px;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        button:hover {
            background: #2563eb;
        }
        
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .success {
            background: #10b981;
        }
        
        .success:hover {
            background: #059669;
        }
        
        .warning {
            background: #f59e0b;
        }
        
        .warning:hover {
            background: #d97706;
        }
        
        .danger {
            background: #ef4444;
        }
        
        .danger:hover {
            background: #dc2626;
        }
        
        .results {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-info { color: #3b82f6; }
        .log-success { color: #10b981; }
        .log-error { color: #ef4444; }
        .log-warning { color: #f59e0b; }
        
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }
        
        .status.loading {
            background: #dbeafe;
            color: #1d4ed8;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .progress {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: #3b82f6;
            transition: width 0.3s ease;
        }
        
        .summary {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .summary h3 {
            margin-top: 0;
            color: #0c4a6e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Reports API Testing Dashboard</h1>
        
        <div class="test-section">
            <h2>Individual Report Tests</h2>
            <div class="button-group">
                <button onclick="testSingleReport('financial')">Test Financial Report</button>
                <button onclick="testSingleReport('sales')">Test Sales Report</button>
                <button onclick="testSingleReport('products')">Test Products Report</button>
                <button onclick="testSingleReport('users')">Test Users Report</button>
                <button onclick="testSingleReport('orders')">Test Orders Report</button>
            </div>
            <div id="single-results" class="results" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2>Export Functionality Tests</h2>
            <div class="button-group">
                <button onclick="testExport('sales', 'csv')">Test CSV Export</button>
                <button onclick="testExport('financial', 'pdf')">Test PDF Export</button>
                <button onclick="testExport('products', 'excel')">Test Excel Export</button>
            </div>
            <div id="export-results" class="results" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2>Comprehensive API Tests</h2>
            <div class="button-group">
                <button onclick="runAllTests()" class="success">Run All Tests</button>
                <button onclick="testHealthCheck()">Health Check</button>
                <button onclick="testDashboard()">Dashboard Summary</button>
                <button onclick="testCache()">Cache Test</button>
                <button onclick="clearResults()" class="warning">Clear Results</button>
            </div>
            
            <div class="progress" id="test-progress" style="display: none;">
                <div class="progress-bar" id="progress-bar"></div>
            </div>
            
            <div id="test-status" style="display: none;">
                <span id="current-test">Ready to start...</span>
                <span class="status loading" id="status-badge">Ready</span>
            </div>
            
            <div id="comprehensive-results" class="results" style="display: none;"></div>
        </div>
        
        <div class="summary" id="test-summary" style="display: none;">
            <h3>Test Summary</h3>
            <div id="summary-content"></div>
        </div>
    </div>

    <script type="module">
        // Import our services (this would need to be adjusted based on actual file structure)
        // For now, we'll create a mock service for testing
        
        class MockReportsService {
            constructor() {
                this.delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));
            }
            
            async getReport(type, filters = {}) {
                await this.delay(Math.random() * 1000 + 500); // Simulate network delay
                
                if (Math.random() < 0.1) { // 10% chance of error for testing
                    throw new Error(`Failed to fetch ${type} report`);
                }
                
                return {
                    type,
                    success: true,
                    data: { message: `Mock ${type} report data` },
                    timestamp: new Date().toISOString()
                };
            }
            
            async exportReport(type, format, filters = {}) {
                await this.delay(Math.random() * 2000 + 1000);
                
                if (Math.random() < 0.15) { // 15% chance of error
                    throw new Error(`Failed to export ${type} as ${format}`);
                }
                
                return {
                    success: true,
                    format,
                    size: Math.floor(Math.random() * 1000000) + 50000,
                    downloadUrl: `#mock-download-${type}-${format}`
                };
            }
            
            async healthCheck() {
                await this.delay(200);
                return { status: 'healthy', timestamp: new Date().toISOString() };
            }
            
            async getDashboardSummary() {
                await this.delay(1500);
                return {
                    financial: { revenue: 125000 },
                    sales: { total: 28456.78 },
                    products: { count: 1250 },
                    users: { active: 4890 },
                    orders: { pending: 45 }
                };
            }
        }
        
        const reportsService = new MockReportsService();
        
        // Make functions available globally
        window.testSingleReport = testSingleReport;
        window.testExport = testExport;
        window.runAllTests = runAllTests;
        window.testHealthCheck = testHealthCheck;
        window.testDashboard = testDashboard;
        window.testCache = testCache;
        window.clearResults = clearResults;
        
        function log(message, type = 'info', containerId = 'comprehensive-results') {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            container.style.display = 'block';
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }
        
        function updateStatus(text, status = 'loading') {
            const statusElement = document.getElementById('test-status');
            const currentTest = document.getElementById('current-test');
            const statusBadge = document.getElementById('status-badge');
            
            if (statusElement) statusElement.style.display = 'block';
            if (currentTest) currentTest.textContent = text;
            if (statusBadge) {
                statusBadge.textContent = status;
                statusBadge.className = `status ${status}`;
            }
        }
        
        function updateProgress(percent) {
            const progressContainer = document.getElementById('test-progress');
            const progressBar = document.getElementById('progress-bar');
            
            if (progressContainer) progressContainer.style.display = 'block';
            if (progressBar) progressBar.style.width = `${percent}%`;
        }
        
        async function testSingleReport(reportType) {
            log(`Testing ${reportType} report...`, 'info', 'single-results');
            
            try {
                const result = await reportsService.getReport(reportType);
                log(`✓ ${reportType} report test passed`, 'success', 'single-results');
                log(`Response: ${JSON.stringify(result, null, 2)}`, 'info', 'single-results');
            } catch (error) {
                log(`✗ ${reportType} report test failed: ${error.message}`, 'error', 'single-results');
            }
        }
        
        async function testExport(reportType, format) {
            log(`Testing ${reportType} export to ${format}...`, 'info', 'export-results');
            
            try {
                const result = await reportsService.exportReport(reportType, format);
                log(`✓ ${reportType} ${format} export test passed`, 'success', 'export-results');
                log(`Export size: ${(result.size / 1024).toFixed(2)} KB`, 'info', 'export-results');
            } catch (error) {
                log(`✗ ${reportType} ${format} export test failed: ${error.message}`, 'error', 'export-results');
            }
        }
        
        async function testHealthCheck() {
            updateStatus('Running health check...', 'loading');
            log('Testing API health check...', 'info');
            
            try {
                const result = await reportsService.healthCheck();
                log('✓ Health check passed', 'success');
                updateStatus('Health check completed', 'success');
            } catch (error) {
                log(`✗ Health check failed: ${error.message}`, 'error');
                updateStatus('Health check failed', 'error');
            }
        }
        
        async function testDashboard() {
            updateStatus('Testing dashboard summary...', 'loading');
            log('Testing dashboard summary...', 'info');
            
            try {
                const result = await reportsService.getDashboardSummary();
                log('✓ Dashboard summary test passed', 'success');
                log(`Dashboard data: ${Object.keys(result).join(', ')}`, 'info');
                updateStatus('Dashboard test completed', 'success');
            } catch (error) {
                log(`✗ Dashboard summary test failed: ${error.message}`, 'error');
                updateStatus('Dashboard test failed', 'error');
            }
        }
        
        async function testCache() {
            updateStatus('Testing cache functionality...', 'loading');
            log('Testing cache functionality...', 'info');
            
            try {
                // Simulate cache test
                const start1 = Date.now();
                await reportsService.getReport('financial');
                const time1 = Date.now() - start1;
                
                const start2 = Date.now();
                await reportsService.getReport('financial');
                const time2 = Date.now() - start2;
                
                log(`First request: ${time1}ms, Second request: ${time2}ms`, 'info');
                log('✓ Cache test completed', 'success');
                updateStatus('Cache test completed', 'success');
            } catch (error) {
                log(`✗ Cache test failed: ${error.message}`, 'error');
                updateStatus('Cache test failed', 'error');
            }
        }
        
        async function runAllTests() {
            const tests = [
                { name: 'Health Check', fn: testHealthCheck },
                { name: 'Financial Report', fn: () => testSingleReport('financial') },
                { name: 'Sales Report', fn: () => testSingleReport('sales') },
                { name: 'Products Report', fn: () => testSingleReport('products') },
                { name: 'Users Report', fn: () => testSingleReport('users') },
                { name: 'Orders Report', fn: () => testSingleReport('orders') },
                { name: 'Dashboard Summary', fn: testDashboard },
                { name: 'Cache Test', fn: testCache },
                { name: 'CSV Export', fn: () => testExport('sales', 'csv') },
                { name: 'PDF Export', fn: () => testExport('financial', 'pdf') }
            ];
            
            let passed = 0;
            let failed = 0;
            
            log('Starting comprehensive API tests...', 'info');
            log('='.repeat(50), 'info');
            
            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                const progress = ((i + 1) / tests.length) * 100;
                
                updateStatus(`Running ${test.name}...`, 'loading');
                updateProgress(progress);
                
                log(`\n--- ${test.name} ---`, 'info');
                
                try {
                    await test.fn();
                    passed++;
                } catch (error) {
                    failed++;
                    log(`Test failed: ${error.message}`, 'error');
                }
                
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            updateProgress(100);
            updateStatus('All tests completed', passed > failed ? 'success' : 'error');
            
            log('\n' + '='.repeat(50), 'info');
            log(`Tests completed: ${passed} passed, ${failed} failed`, passed > failed ? 'success' : 'error');
            
            // Show summary
            const summary = document.getElementById('test-summary');
            const summaryContent = document.getElementById('summary-content');
            
            if (summary && summaryContent) {
                summary.style.display = 'block';
                summaryContent.innerHTML = `
                    <p><strong>Total Tests:</strong> ${tests.length}</p>
                    <p><strong>Passed:</strong> <span style="color: #10b981;">${passed}</span></p>
                    <p><strong>Failed:</strong> <span style="color: #ef4444;">${failed}</span></p>
                    <p><strong>Success Rate:</strong> ${((passed / tests.length) * 100).toFixed(1)}%</p>
                `;
            }
        }
        
        function clearResults() {
            const containers = ['single-results', 'export-results', 'comprehensive-results'];
            containers.forEach(id => {
                const container = document.getElementById(id);
                if (container) {
                    container.innerHTML = '';
                    container.style.display = 'none';
                }
            });
            
            const summary = document.getElementById('test-summary');
            const status = document.getElementById('test-status');
            const progress = document.getElementById('test-progress');
            
            if (summary) summary.style.display = 'none';
            if (status) status.style.display = 'none';
            if (progress) progress.style.display = 'none';
        }
        
        // Initialize
        log('Reports API Tester loaded and ready!', 'success');
    </script>
</body>
</html>
