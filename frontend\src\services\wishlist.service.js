import apiClient from './api.service';

class WishlistService {
  // Get all categories with optional filtering and pagination
  async getWishlist(params = {}) 
  {
    try 
    {
      return await apiClient.get('/users/me/wishlist', { params });
    } 
    catch (error) 
    {
      console.error('Error fetching Wishlist:', error);
      throw error;
    }
  }

  async addToWishlist(productId, params={})
  {
    try
    {
      return await apiClient.post('/users/me/wishlist/items', params = { productId: productId});
    }catch(error)
    {
      console.error(`Error adding item:`, error);
      throw error;
    }
  }

  // Delete category
  async deleteWishlist(id) {
    try {
      return await apiClient.delete(`/users/me/wishlist/${id}`);
    } catch (error) {
      console.error(`Error deleting Wishlist:`, error);
      throw error;
    }
  }
}
export default new WishlistService();
