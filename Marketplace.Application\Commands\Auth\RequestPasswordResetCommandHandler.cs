﻿using AutoMapper;
using Marketplace.Application.Services.Auth;
using Marketplace.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Configuration;

namespace Marketplace.Application.Commands.Auth;

public class RequestPasswordResetCommandHandler : IRequestHandler<RequestPasswordResetCommand>
{
    private readonly IUserRepository _userRepository;
    private readonly ITokenService _tokenService;
    private readonly IEmailService _emailService;
    private readonly IMapper _mapper;
    private readonly string _baseUrl;

    public RequestPasswordResetCommandHandler(IUserRepository userRepository, IConfiguration configuration, IMapper mapper, IEmailService emailService, ITokenService tokenService)
    {
        _userRepository = userRepository;
        _baseUrl = configuration["Frontend:BaseUrl"]
            ?? throw new ArgumentNullException("Frontend:BaseUrl is not configured.");
        _emailService = emailService;
        _tokenService = tokenService;
        _mapper = mapper;
    }

    public async Task Handle(RequestPasswordResetCommand request, CancellationToken cancellationToken)
    {
        var user = await _userRepository.GetByEmailAsync(request.Email, cancellationToken);
        if (user == null)
            return;

        user.PasswordResetToken = _tokenService.GenerateToken();
        await _userRepository.UpdateAsync(user, cancellationToken);

        try
        {
            var resetLink = $"{_baseUrl}/reset-password?id={user.Id}&token={Uri.EscapeDataString(user.PasswordResetToken)}";
            await _emailService.SendEmailAsync(
                user.Email,
                "Скидання пароля",
                $"Скиньте пароль, перейшовши за посиланням: <a href='{resetLink}'>Скинути пароль</a>",
                cancellationToken);
        }
        catch (Exception)
        {
            // Ігноруємо помилку, але не перериваємо процес
            // Можна додати код для повторної спроби або запису в чергу для подальшої обробки
        }
    }
}