/**
 * Base API Service
 * Centralized HTTP client with interceptors and error handling
 */

import axios from 'axios'

// Create axios instance with default configuration
const api = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || '/api',
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// Request interceptor for adding auth token and logging
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // Add request ID for tracking
    config.headers['X-Request-ID'] = generateRequestId()

    // Log request in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data
      })
    }

    return config
  },
  (error) => {
    console.error('Request interceptor error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor for handling common responses and errors
api.interceptors.response.use(
  (response) => {
    // Log response in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data
      })
    }

    return response
  },
  (error) => {
    // Log error in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
        status: error.response?.status,
        message: error.message,
        data: error.response?.data
      })
    }

    // Handle common error scenarios
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          // Unauthorized - clear auth and redirect to login
          localStorage.removeItem('auth_token')
          sessionStorage.removeItem('auth_token')
          
          // Only redirect if not already on login page
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          break

        case 403:
          // Forbidden - show access denied message
          console.warn('Access denied:', data?.message || 'You do not have permission to access this resource')
          break

        case 404:
          // Not found - handle gracefully
          console.warn('Resource not found:', error.config?.url)
          break

        case 422:
          // Validation error - extract validation messages
          if (data?.errors) {
            error.validationErrors = data.errors
          }
          break

        case 429:
          // Rate limit exceeded
          console.warn('Rate limit exceeded. Please try again later.')
          break

        case 500:
        case 502:
        case 503:
        case 504:
          // Server errors
          console.error('Server error:', data?.message || 'Internal server error')
          break
      }
    } else if (error.request) {
      // Network error
      console.error('Network error:', error.message)
    }

    return Promise.reject(error)
  }
)

/**
 * Generate unique request ID for tracking
 * @returns {string} Unique request ID
 */
function generateRequestId() {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * API service with common HTTP methods
 */
const apiService = {
  // GET request
  get: (url, config = {}) => api.get(url, config),

  // POST request
  post: (url, data = {}, config = {}) => api.post(url, data, config),

  // PUT request
  put: (url, data = {}, config = {}) => api.put(url, data, config),

  // PATCH request
  patch: (url, data = {}, config = {}) => api.patch(url, data, config),

  // DELETE request
  delete: (url, config = {}) => api.delete(url, config),

  // Upload file
  upload: (url, formData, config = {}) => {
    return api.post(url, formData, {
      ...config,
      headers: {
        ...config.headers,
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // Download file
  download: (url, config = {}) => {
    return api.get(url, {
      ...config,
      responseType: 'blob'
    })
  },

  // Set auth token
  setAuthToken: (token) => {
    if (token) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`
      localStorage.setItem('auth_token', token)
    } else {
      delete api.defaults.headers.common['Authorization']
      localStorage.removeItem('auth_token')
      sessionStorage.removeItem('auth_token')
    }
  },

  // Get current auth token
  getAuthToken: () => {
    return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token')
  },

  // Clear auth token
  clearAuthToken: () => {
    delete api.defaults.headers.common['Authorization']
    localStorage.removeItem('auth_token')
    sessionStorage.removeItem('auth_token')
  },

  // Set base URL
  setBaseURL: (baseURL) => {
    api.defaults.baseURL = baseURL
  },

  // Get axios instance for advanced usage
  getInstance: () => api,

  // Health check
  healthCheck: () => api.get('/health'),

  // Check if API is available
  isAvailable: async () => {
    try {
      await apiService.healthCheck()
      return true
    } catch (error) {
      return false
    }
  }
}

// Export both the axios instance and the service
export default apiService
export { api }
