<template>
  <ProductEdit 
    :is-create="true" 
    @save="handleSave" 
    @cancel="handleCancel" 
  />
</template>

<script setup>
import { useRouter } from 'vue-router';
import ProductEdit from './ProductEdit.vue';

// Router
const router = useRouter();

// Emits
const emit = defineEmits(['save', 'cancel']);

// Methods
const handleSave = (productData) => {
  console.log('Product created:', productData);
  emit('save', productData);
  
  // Redirect to products list or product view
  router.push('/admin/products');
};

const handleCancel = () => {
  emit('cancel');

  // Go back to products list
  router.push('/admin/products');
};
</script>
