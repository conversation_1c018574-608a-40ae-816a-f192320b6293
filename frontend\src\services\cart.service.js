import apiClient from './api.service';

class CartService {
  // Get all categories with optional filtering and pagination
  async getCart(params = {}) 
  {
    try 
    {
      return await apiClient.get('/users/me/cart', { params });
    } 
    catch (error) 
    {
      console.error('Error fetching cart:', error);
      throw error;
    }
  }

  async addToCart(productId, params={})
  {
    try
    {
      return await apiClient.post('/users/me/cart/items', params = { productId: productId});
    }catch(error)
    {
      console.error(`Error adding item:`, error);
      throw error;
    }
  }

  async changeItemCount(itemId, quantity, params={})
  {
    try
    {
      return await apiClient.put(`/users/me/cart/items/${itemId}`, params =  { quantity: quantity} )
    } catch(error)
    {
      console.error(`Error increasing item count:`, error);
      throw error;
    }
  } 
  
  async deleteItem(itemId)
  {
    try
    {
      return await apiClient.delete(`/users/me/cart/items/${itemId}`)
    } catch(error)
    {
      console.error(`Error increasing item count:`, error);
      throw error;
    }
  }

  // Delete category
  async deleteCart() {
    try {
      return await apiClient.delete('/users/me/cart');
    } catch (error) {
      console.error(`Error deleting cart:`, error);
      throw error;
    }
  }

  async checkout() {
    try {
        return await apiClient.post('/users/me/cart/checkout');
    } catch(error) {
        console.error(`Error initiating payment:`, error);
        throw error;
    }
  }
}
export default new CartService();
