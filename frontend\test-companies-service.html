<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Companies Service</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>Test Companies Service</h1>
    
    <div id="results">
        <h2>Test Results:</h2>
        <ul id="test-list"></ul>
    </div>
    
    <button onclick="runTests()">Run Tests</button>

    <script>
        const API_BASE_URL = 'http://localhost:5296';
        
        function addResult(message, isSuccess = true) {
            const list = document.getElementById('test-list');
            const item = document.createElement('li');
            item.textContent = message;
            item.style.color = isSuccess ? 'green' : 'red';
            list.appendChild(item);
        }
        
        async function testCompaniesAPI() {
            try {
                console.log('🧪 Testing companies API...');
                
                // Test 1: Basic API call
                const response = await axios.get(`${API_BASE_URL}/api/companies?pageSize=5`);
                console.log('📦 API Response:', response.data);
                
                if (response.data && response.data.data && Array.isArray(response.data.data)) {
                    addResult(`✅ API returns correct structure: ${response.data.data.length} companies`);
                    
                    // Test 2: Check company structure
                    const firstCompany = response.data.data[0];
                    if (firstCompany && firstCompany.id && firstCompany.name) {
                        addResult(`✅ Company structure is correct: ${firstCompany.name}`);
                    } else {
                        addResult('❌ Company structure is invalid', false);
                    }
                    
                    // Test 3: Check pagination
                    if (response.data.total && response.data.perPage) {
                        addResult(`✅ Pagination works: ${response.data.total} total, ${response.data.perPage} per page`);
                    } else {
                        addResult('❌ Pagination structure is invalid', false);
                    }
                    
                } else {
                    addResult('❌ API response structure is invalid', false);
                }
                
            } catch (error) {
                console.error('❌ API Test failed:', error);
                addResult(`❌ API call failed: ${error.message}`, false);
            }
        }
        
        async function testCategoriesAPI() {
            try {
                console.log('🧪 Testing categories API...');
                
                const response = await axios.get(`${API_BASE_URL}/api/categories/all?pageSize=10`);
                console.log('📦 Categories Response:', response.data);
                
                if (response.data && response.data.data && Array.isArray(response.data.data)) {
                    addResult(`✅ Categories API works: ${response.data.data.length} categories`);
                    
                    const firstCategory = response.data.data[0];
                    if (firstCategory && firstCategory.id && firstCategory.name) {
                        addResult(`✅ Category structure is correct: ${firstCategory.name}`);
                    } else {
                        addResult('❌ Category structure is invalid', false);
                    }
                } else {
                    addResult('❌ Categories API response structure is invalid', false);
                }
                
            } catch (error) {
                console.error('❌ Categories API Test failed:', error);
                addResult(`❌ Categories API call failed: ${error.message}`, false);
            }
        }
        
        async function testSpecificProduct() {
            try {
                console.log('🧪 Testing specific product...');
                
                const productId = '5f1e1b91-3e83-4d0d-af05-04703e501d89';
                const response = await axios.get(`${API_BASE_URL}/api/products/${productId}`);
                console.log('📦 Product Response:', response.data);
                
                if (response.data && response.data.id) {
                    addResult(`✅ Product API works: ${response.data.name}`);
                    
                    if (response.data.companyId) {
                        addResult(`✅ Product has companyId: ${response.data.companyId}`);
                    } else {
                        addResult('❌ Product missing companyId', false);
                    }
                    
                    if (response.data.categoryId) {
                        addResult(`✅ Product has categoryId: ${response.data.categoryId}`);
                    } else {
                        addResult('❌ Product missing categoryId', false);
                    }
                } else {
                    addResult('❌ Product API response is invalid', false);
                }
                
            } catch (error) {
                console.error('❌ Product API Test failed:', error);
                addResult(`❌ Product API call failed: ${error.message}`, false);
            }
        }
        
        async function runTests() {
            // Clear previous results
            document.getElementById('test-list').innerHTML = '';
            
            addResult('🚀 Starting API tests...');
            
            await testCompaniesAPI();
            await testCategoriesAPI();
            await testSpecificProduct();
            
            addResult('🏁 Tests completed!');
        }
        
        // Auto-run tests on page load
        window.onload = () => {
            runTests();
        };
    </script>
</body>
</html>
