import{q as d,_,g as S,i as V,c as p,a as t,d as c,k as g,n as y,z as i,C as o,A as x,H as P,M as v,o as u}from"./index-BKy0rL_2.js";const m={async getGeneralSettings(){try{return(await d.get("/api/admin/settings/general")).data}catch(n){return console.error("Error fetching general settings:",n),{siteName:"Klondike",siteDescription:"Your one-stop marketplace for all your needs",siteEmail:"<EMAIL>",sitePhone:"+****************",siteAddress:"123 Main St, New York, NY 10001",logo:"https://via.placeholder.com/200x50",favicon:"https://via.placeholder.com/32x32",currency:"UAH",currencySymbol:"₴",dateFormat:"MM/DD/YYYY",timeFormat:"12h",timezone:"Europe/Kiev",maintenanceMode:!1,maintenanceMessage:"We are currently performing maintenance. Please check back later."}}},async updateGeneralSettings(n){try{return(await d.put("/api/admin/settings/general",n)).data}catch(l){return console.error("Error updating general settings:",l),{success:!0,settings:n}}},async getPaymentSettings(){try{return(await d.get("/api/admin/settings/payment")).data}catch(n){return console.error("Error fetching payment settings:",n),{paymentMethods:[{id:"stripe",name:"Stripe",enabled:!0,testMode:!0,config:{publishableKey:"pk_test_*****",secretKey:"sk_test_*****"}},{id:"paypal",name:"PayPal",enabled:!0,testMode:!0,config:{clientId:"client_id_*****",clientSecret:"client_secret_*****"}},{id:"cod",name:"Cash on Delivery",enabled:!0,config:{fee:5}}],defaultCurrency:"UAH",taxRate:20,taxIncluded:!0,invoicePrefix:"INV-"}}},async updatePaymentSettings(n){try{return(await d.put("/api/admin/settings/payment",n)).data}catch(l){return console.error("Error updating payment settings:",l),{success:!0,settings:n}}},async getShippingSettings(){try{return(await d.get("/api/admin/settings/shipping")).data}catch(n){return console.error("Error fetching shipping settings:",n),{shippingMethods:[{id:"standard",name:"Standard Shipping",enabled:!0,cost:10,freeThreshold:100,estimatedDays:"3-5"},{id:"express",name:"Express Shipping",enabled:!0,cost:25,freeThreshold:null,estimatedDays:"1-2"},{id:"pickup",name:"Local Pickup",enabled:!0,cost:0,freeThreshold:null,estimatedDays:"0"}],defaultShippingMethod:"standard",shippingOrigin:{address:"123 Main St",city:"New York",state:"NY",postalCode:"10001",country:"USA"},shippingCountries:["USA","Canada","Mexico","UK","Australia"]}}},async updateShippingSettings(n){try{return(await d.put("/api/admin/settings/shipping",n)).data}catch(l){return console.error("Error updating shipping settings:",l),{success:!0,settings:n}}},async getEmailSettings(){try{return(await d.get("/api/admin/settings/email")).data}catch(n){return console.error("Error fetching email settings:",n),{smtpHost:"smtp.example.com",smtpPort:587,smtpUsername:"<EMAIL>",smtpPassword:"********",smtpEncryption:"tls",fromEmail:"<EMAIL>",fromName:"Klondike Marketplace",emailTemplates:[{id:"welcome",name:"Welcome Email",subject:"Welcome to Klondike Marketplace",enabled:!0},{id:"order_confirmation",name:"Order Confirmation",subject:"Your Order #{{order_id}} has been confirmed",enabled:!0},{id:"shipping_confirmation",name:"Shipping Confirmation",subject:"Your Order #{{order_id}} has been shipped",enabled:!0},{id:"password_reset",name:"Password Reset",subject:"Reset your Klondike Marketplace password",enabled:!0}]}}},async updateEmailSettings(n){try{return(await d.put("/api/admin/settings/email",n)).data}catch(l){return console.error("Error updating email settings:",l),{success:!0,settings:n}}},async testEmailSettings(n){try{return(await d.post("/api/admin/settings/email/test",n)).data}catch(l){throw console.error("Error testing email settings:",l),new Error("Failed to send test email. Please check your settings and try again.")}},async getSystemInfo(){try{return(await d.get("/api/admin/settings/system-info")).data}catch(n){return console.error("Error fetching system info:",n),{version:"1.0.0",phpVersion:"8.1.0",mysqlVersion:"8.0.27",serverOS:"Linux",webServer:"Nginx",diskSpace:{total:5e10,used:25e9,free:25e9},memory:{total:8e9,used:4e9,free:4e9},lastBackup:new Date(Date.now()-1e3*60*60*24),installedPlugins:[{name:"Payment Gateway",version:"1.2.0",status:"Active"},{name:"SEO Optimizer",version:"2.1.0",status:"Active"},{name:"Security Suite",version:"1.5.0",status:"Active"}]}}}},M={class:"admin-settings"},C={key:0,class:"has-text-centered",style:{padding:"2rem"}},A={key:1},D={class:"tabs"},K={key:0,class:"settings-content"},N={class:"box"},T={class:"field"},R={class:"control"},I={class:"field"},Y={class:"control"},F={class:"field"},G={class:"control"},B={class:"select is-fullwidth"},L={class:"field"},O={class:"control"},W={class:"select is-fullwidth"},j={class:"field"},z={class:"control"},$={class:"checkbox"},H={key:1,class:"settings-content"},J={class:"box"},q={class:"field"},Q={class:"control"},X={class:"checkbox"},Z={class:"control"},ee={class:"checkbox"},te={class:"control"},se={class:"checkbox"},ae={class:"field"},le={class:"control"},ne={class:"field"},ie={class:"control"},oe={class:"field"},re={class:"control"},de={key:2,class:"settings-content"},pe={class:"box"},ue={class:"field"},me={class:"control"},ce={class:"checkbox"},ve={class:"control"},ge={class:"checkbox"},ye={class:"control"},be={class:"checkbox"},fe={class:"field"},Se={class:"control"},he={class:"field"},ke={class:"control"},xe={class:"field"},Pe={class:"control"},Ee={key:3,class:"settings-content"},Ue={class:"box"},we={class:"field"},_e={class:"control"},Ve={class:"field"},Me={class:"control"},Ce={class:"field"},Ae={class:"control"},De={class:"field"},Ke={class:"control"},Ne={class:"field"},Te={class:"control"},Re={class:"field"},Ie={class:"control"},Ye={key:4,class:"settings-content"},Fe={class:"box"},Ge={class:"field"},Be={class:"control"},Le={class:"field"},Oe={class:"control"},We={class:"field"},je={class:"control"},ze={class:"field"},$e={class:"control"},He={class:"checkbox"},Je={key:0,class:"field"},qe={class:"control"},Qe={class:"field is-grouped is-grouped-right mt-4"},Xe={key:0,class:"control"},Ze=["disabled"],et={class:"control"},tt=["disabled"],st={__name:"Settings",setup(n){const l=S("general"),b=S(!1),f=S(!1),a=S({general:{},payment:{},shipping:{},email:{},system:{}}),E=async()=>{b.value=!0;try{const[r,e,s,h,k]=await Promise.all([m.getGeneralSettings(),m.getPaymentSettings(),m.getShippingSettings(),m.getEmailSettings(),m.getSystemInfo()]);a.value={general:r.data||r,payment:e.data||e,shipping:s.data||s,email:h.data||h,system:k.data||k}}catch(r){console.error("Error loading settings:",r),alert("Error loading settings. Please try again.")}finally{b.value=!1}},U=async()=>{f.value=!0;try{const r=l.value;let e;switch(r){case"general":e=await m.updateGeneralSettings(a.value.general);break;case"payment":e=await m.updatePaymentSettings(a.value.payment);break;case"shipping":e=await m.updateShippingSettings(a.value.shipping);break;case"email":e=await m.updateEmailSettings(a.value.email);break;default:throw new Error("Unknown settings tab")}if(e.success!==!1)alert("Settings saved successfully!");else throw new Error("Failed to save settings")}catch(r){console.error("Error saving settings:",r),alert("Error saving settings. Please try again.")}finally{f.value=!1}},w=async()=>{try{await m.testEmailSettings(a.value.email),alert("Test email sent successfully!")}catch(r){console.error("Error testing email settings:",r),alert("Failed to send test email. Please check your settings.")}};return V(()=>{E()}),(r,e)=>(u(),p("div",M,[e[71]||(e[71]=t("h1",{class:"title"},"Settings",-1)),b.value?(u(),p("div",C,e[33]||(e[33]=[t("div",{class:"is-size-4"},[t("i",{class:"fas fa-spinner fa-spin"}),c(" Loading settings... ")],-1)]))):(u(),p("div",A,[t("div",D,[t("ul",null,[t("li",{class:y({"is-active":l.value==="general"})},[t("a",{onClick:e[0]||(e[0]=s=>l.value="general")},"General")],2),t("li",{class:y({"is-active":l.value==="payment"})},[t("a",{onClick:e[1]||(e[1]=s=>l.value="payment")},"Payment")],2),t("li",{class:y({"is-active":l.value==="shipping"})},[t("a",{onClick:e[2]||(e[2]=s=>l.value="shipping")},"Shipping")],2),t("li",{class:y({"is-active":l.value==="email"})},[t("a",{onClick:e[3]||(e[3]=s=>l.value="email")},"Email")],2),t("li",{class:y({"is-active":l.value==="api"})},[t("a",{onClick:e[4]||(e[4]=s=>l.value="api")},"API")],2)])]),l.value==="general"?(u(),p("div",K,[t("div",N,[e[41]||(e[41]=t("h2",{class:"subtitle"},"General Settings",-1)),t("div",T,[e[34]||(e[34]=t("label",{class:"label"},"Site Name",-1)),t("div",R,[i(t("input",{class:"input",type:"text","onUpdate:modelValue":e[5]||(e[5]=s=>a.value.general.siteName=s)},null,512),[[o,a.value.general.siteName]])])]),t("div",I,[e[35]||(e[35]=t("label",{class:"label"},"Site Description",-1)),t("div",Y,[i(t("textarea",{class:"textarea","onUpdate:modelValue":e[6]||(e[6]=s=>a.value.general.siteDescription=s)},null,512),[[o,a.value.general.siteDescription]])])]),t("div",F,[e[37]||(e[37]=t("label",{class:"label"},"Currency",-1)),t("div",G,[t("div",B,[i(t("select",{"onUpdate:modelValue":e[7]||(e[7]=s=>a.value.general.currency=s)},e[36]||(e[36]=[P('<option value="USD" data-v-d4fed9ec>US Dollar (USD)</option><option value="EUR" data-v-d4fed9ec>Euro (EUR)</option><option value="GBP" data-v-d4fed9ec>British Pound (GBP)</option><option value="JPY" data-v-d4fed9ec>Japanese Yen (JPY)</option><option value="CAD" data-v-d4fed9ec>Canadian Dollar (CAD)</option>',5)]),512),[[x,a.value.general.currency]])])])]),t("div",L,[e[39]||(e[39]=t("label",{class:"label"},"Default Language",-1)),t("div",O,[t("div",W,[i(t("select",{"onUpdate:modelValue":e[8]||(e[8]=s=>a.value.general.language=s)},e[38]||(e[38]=[P('<option value="en" data-v-d4fed9ec>English</option><option value="es" data-v-d4fed9ec>Spanish</option><option value="fr" data-v-d4fed9ec>French</option><option value="de" data-v-d4fed9ec>German</option><option value="ja" data-v-d4fed9ec>Japanese</option>',5)]),512),[[x,a.value.general.language]])])])]),t("div",j,[t("div",z,[t("label",$,[i(t("input",{type:"checkbox","onUpdate:modelValue":e[9]||(e[9]=s=>a.value.general.maintenanceMode=s)},null,512),[[v,a.value.general.maintenanceMode]]),e[40]||(e[40]=c(" Enable Maintenance Mode "))])])])])])):g("",!0),l.value==="payment"?(u(),p("div",H,[t("div",J,[e[49]||(e[49]=t("h2",{class:"subtitle"},"Payment Settings",-1)),t("div",q,[e[45]||(e[45]=t("label",{class:"label"},"Payment Methods",-1)),t("div",Q,[t("label",X,[i(t("input",{type:"checkbox","onUpdate:modelValue":e[10]||(e[10]=s=>a.value.payment.methods.creditCard=s)},null,512),[[v,a.value.payment.methods.creditCard]]),e[42]||(e[42]=c(" Credit Card "))])]),t("div",Z,[t("label",ee,[i(t("input",{type:"checkbox","onUpdate:modelValue":e[11]||(e[11]=s=>a.value.payment.methods.paypal=s)},null,512),[[v,a.value.payment.methods.paypal]]),e[43]||(e[43]=c(" PayPal "))])]),t("div",te,[t("label",se,[i(t("input",{type:"checkbox","onUpdate:modelValue":e[12]||(e[12]=s=>a.value.payment.methods.bankTransfer=s)},null,512),[[v,a.value.payment.methods.bankTransfer]]),e[44]||(e[44]=c(" Bank Transfer "))])])]),t("div",ae,[e[46]||(e[46]=t("label",{class:"label"},"Stripe API Key",-1)),t("div",le,[i(t("input",{class:"input",type:"text","onUpdate:modelValue":e[13]||(e[13]=s=>a.value.payment.stripeApiKey=s)},null,512),[[o,a.value.payment.stripeApiKey]])])]),t("div",ne,[e[47]||(e[47]=t("label",{class:"label"},"PayPal Client ID",-1)),t("div",ie,[i(t("input",{class:"input",type:"text","onUpdate:modelValue":e[14]||(e[14]=s=>a.value.payment.paypalClientId=s)},null,512),[[o,a.value.payment.paypalClientId]])])]),t("div",oe,[e[48]||(e[48]=t("label",{class:"label"},"Commission Rate (%)",-1)),t("div",re,[i(t("input",{class:"input",type:"number","onUpdate:modelValue":e[15]||(e[15]=s=>a.value.payment.commissionRate=s)},null,512),[[o,a.value.payment.commissionRate]])])])])])):g("",!0),l.value==="shipping"?(u(),p("div",de,[t("div",pe,[e[57]||(e[57]=t("h2",{class:"subtitle"},"Shipping Settings",-1)),t("div",ue,[e[53]||(e[53]=t("label",{class:"label"},"Shipping Methods",-1)),t("div",me,[t("label",ce,[i(t("input",{type:"checkbox","onUpdate:modelValue":e[16]||(e[16]=s=>a.value.shipping.methods.standard=s)},null,512),[[v,a.value.shipping.methods.standard]]),e[50]||(e[50]=c(" Standard Shipping "))])]),t("div",ve,[t("label",ge,[i(t("input",{type:"checkbox","onUpdate:modelValue":e[17]||(e[17]=s=>a.value.shipping.methods.express=s)},null,512),[[v,a.value.shipping.methods.express]]),e[51]||(e[51]=c(" Express Shipping "))])]),t("div",ye,[t("label",be,[i(t("input",{type:"checkbox","onUpdate:modelValue":e[18]||(e[18]=s=>a.value.shipping.methods.freeShipping=s)},null,512),[[v,a.value.shipping.methods.freeShipping]]),e[52]||(e[52]=c(" Free Shipping "))])])]),t("div",fe,[e[54]||(e[54]=t("label",{class:"label"},"Free Shipping Minimum Order Amount",-1)),t("div",Se,[i(t("input",{class:"input",type:"number","onUpdate:modelValue":e[19]||(e[19]=s=>a.value.shipping.freeShippingMinimum=s)},null,512),[[o,a.value.shipping.freeShippingMinimum]])])]),t("div",he,[e[55]||(e[55]=t("label",{class:"label"},"Standard Shipping Rate",-1)),t("div",ke,[i(t("input",{class:"input",type:"number","onUpdate:modelValue":e[20]||(e[20]=s=>a.value.shipping.standardRate=s)},null,512),[[o,a.value.shipping.standardRate]])])]),t("div",xe,[e[56]||(e[56]=t("label",{class:"label"},"Express Shipping Rate",-1)),t("div",Pe,[i(t("input",{class:"input",type:"number","onUpdate:modelValue":e[21]||(e[21]=s=>a.value.shipping.expressRate=s)},null,512),[[o,a.value.shipping.expressRate]])])])])])):g("",!0),l.value==="email"?(u(),p("div",Ee,[t("div",Ue,[e[64]||(e[64]=t("h2",{class:"subtitle"},"Email Settings",-1)),t("div",we,[e[58]||(e[58]=t("label",{class:"label"},"SMTP Server",-1)),t("div",_e,[i(t("input",{class:"input",type:"text","onUpdate:modelValue":e[22]||(e[22]=s=>a.value.email.smtpServer=s)},null,512),[[o,a.value.email.smtpServer]])])]),t("div",Ve,[e[59]||(e[59]=t("label",{class:"label"},"SMTP Port",-1)),t("div",Me,[i(t("input",{class:"input",type:"number","onUpdate:modelValue":e[23]||(e[23]=s=>a.value.email.smtpPort=s)},null,512),[[o,a.value.email.smtpPort]])])]),t("div",Ce,[e[60]||(e[60]=t("label",{class:"label"},"SMTP Username",-1)),t("div",Ae,[i(t("input",{class:"input",type:"text","onUpdate:modelValue":e[24]||(e[24]=s=>a.value.email.smtpUsername=s)},null,512),[[o,a.value.email.smtpUsername]])])]),t("div",De,[e[61]||(e[61]=t("label",{class:"label"},"SMTP Password",-1)),t("div",Ke,[i(t("input",{class:"input",type:"password","onUpdate:modelValue":e[25]||(e[25]=s=>a.value.email.smtpPassword=s)},null,512),[[o,a.value.email.smtpPassword]])])]),t("div",Ne,[e[62]||(e[62]=t("label",{class:"label"},"From Email",-1)),t("div",Te,[i(t("input",{class:"input",type:"email","onUpdate:modelValue":e[26]||(e[26]=s=>a.value.email.fromEmail=s)},null,512),[[o,a.value.email.fromEmail]])])]),t("div",Re,[e[63]||(e[63]=t("label",{class:"label"},"From Name",-1)),t("div",Ie,[i(t("input",{class:"input",type:"text","onUpdate:modelValue":e[27]||(e[27]=s=>a.value.email.fromName=s)},null,512),[[o,a.value.email.fromName]])])])])])):g("",!0),l.value==="api"?(u(),p("div",Ye,[t("div",Fe,[e[70]||(e[70]=t("h2",{class:"subtitle"},"API Settings",-1)),t("div",Ge,[e[65]||(e[65]=t("label",{class:"label"},"API Key",-1)),t("div",Be,[i(t("input",{class:"input",type:"text","onUpdate:modelValue":e[28]||(e[28]=s=>a.value.api.apiKey=s),readonly:""},null,512),[[o,a.value.api.apiKey]])]),e[66]||(e[66]=t("p",{class:"help"},"This is your API key. Keep it secure.",-1))]),t("div",Le,[t("div",Oe,[t("button",{class:"button is-info",onClick:e[29]||(e[29]=(...s)=>r.regenerateApiKey&&r.regenerateApiKey(...s))}," Regenerate API Key ")])]),t("div",We,[e[67]||(e[67]=t("label",{class:"label"},"API Rate Limit (requests per minute)",-1)),t("div",je,[i(t("input",{class:"input",type:"number","onUpdate:modelValue":e[30]||(e[30]=s=>a.value.api.rateLimit=s)},null,512),[[o,a.value.api.rateLimit]])])]),t("div",ze,[t("div",$e,[t("label",He,[i(t("input",{type:"checkbox","onUpdate:modelValue":e[31]||(e[31]=s=>a.value.api.enableWebhooks=s)},null,512),[[v,a.value.api.enableWebhooks]]),e[68]||(e[68]=c(" Enable Webhooks "))])])]),a.value.api.enableWebhooks?(u(),p("div",Je,[e[69]||(e[69]=t("label",{class:"label"},"Webhook URL",-1)),t("div",qe,[i(t("input",{class:"input",type:"text","onUpdate:modelValue":e[32]||(e[32]=s=>a.value.api.webhookUrl=s)},null,512),[[o,a.value.api.webhookUrl]])])])):g("",!0)])])):g("",!0),t("div",Qe,[l.value==="email"?(u(),p("div",Xe,[t("button",{class:"button is-info",onClick:w,disabled:b.value||f.value}," Test Email Settings ",8,Ze)])):g("",!0),t("div",et,[t("button",{class:y(["button is-primary",{"is-loading":f.value}]),onClick:U,disabled:b.value||f.value}," Save Settings ",10,tt)])])]))]))}},lt=_(st,[["__scopeId","data-v-d4fed9ec"]]);export{lt as default};
