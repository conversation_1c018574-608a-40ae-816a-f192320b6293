<template>
  <div class="sidebar-container">
    <!-- Mobile Close Button -->
    <button
      class="sidebar-close is-hidden-desktop"
      @click="toggleSidebar"
      v-if="isMobile">
      <span class="icon">
        <i class="fas fa-times"></i>
      </span>
    </button>

    <div class="sidebar-header">
      <div class="logo-container">
        <img src="@/assets/images/logo.svg" alt="Klondike" class="logo" />
        <h2 class="title is-5 has-text-white">Klondike Admin</h2>
      </div>
    </div>

    <nav class="panel sidebar">
      <p class="menu-label">General</p>
      <div class="menu-list">
        <a @click.prevent="navigateTo('/admin/dashboard')" class="panel-block" :class="{ 'is-active': isActive('/admin/dashboard') }">
          <span class="panel-icon"><i class="fas fa-tachometer-alt"></i></span> Dashboard
        </a>
      </div>

      <p class="menu-label">Users</p>
      <div class="menu-list">
        <!-- Users - only for admins -->
        <a v-if="isAdmin" @click.prevent="navigateTo('/admin/users')" class="panel-block" :class="{ 'is-active': isActive('/admin/users') }">
          <span class="panel-icon"><i class="fas fa-users"></i></span> Users
        </a>
        <!-- Seller Applications - for both admins and moderators -->
        <a @click.prevent="navigateTo('/admin/seller-requests')" class="panel-block" :class="{ 'is-active': isActive('/admin/seller-requests') }">
          <span class="panel-icon"><i class="fas fa-user-plus"></i></span> Seller Applications
        </a>
        <!-- Companies - for both admins and moderators -->
        <a @click.prevent="navigateTo('/admin/companies')" class="panel-block" :class="{ 'is-active': isActive('/admin/companies') }">
          <span class="panel-icon"><i class="fas fa-building"></i></span> Companies
        </a>
      </div>

      <!-- Catalog - only for admins -->
      <template v-if="isAdmin">
        <p class="menu-label">Catalog</p>
        <div class="menu-list">
          <a @click.prevent="navigateTo('/admin/products')" class="panel-block" :class="{ 'is-active': isActive('/admin/products') }">
            <span class="panel-icon"><i class="fas fa-box"></i></span> Products
          </a>
          <a @click.prevent="navigateTo('/admin/categories')" class="panel-block" :class="{ 'is-active': isActive('/admin/categories') }">
            <span class="panel-icon"><i class="fas fa-tags"></i></span> Categories
          </a>
        </div>

        <p class="menu-label">Sales</p>
        <div class="menu-list">
          <a @click.prevent="navigateTo('/admin/orders')" class="panel-block" :class="{ 'is-active': isActive('/admin/orders') }">
            <span class="panel-icon"><i class="fas fa-shopping-cart"></i></span> Orders
          </a>
          <a @click.prevent="navigateTo('/admin/reports')" class="panel-block" :class="{ 'is-active': isActive('/admin/reports') }">
            <span class="panel-icon"><i class="fas fa-chart-bar"></i></span> Reports
          </a>
        </div>
      </template>

      <!-- Communication - for both admins and moderators -->
      <p class="menu-label">Communication</p>
      <div class="menu-list">
        <a @click.prevent="navigateTo('/admin/reviews')" class="panel-block" :class="{ 'is-active': isActive('/admin/reviews') }">
          <span class="panel-icon"><i class="fas fa-star"></i></span> Reviews
        </a>
        <a @click.prevent="navigateTo('/admin/ratings')" class="panel-block" :class="{ 'is-active': isActive('/admin/ratings') }">
          <span class="panel-icon"><i class="fas fa-star-half-alt"></i></span> Ratings
        </a>
        <a @click.prevent="navigateTo('/admin/chats')" class="panel-block" :class="{ 'is-active': isActive('/admin/chats') }">
          <span class="panel-icon"><i class="fas fa-comments"></i></span> Chats
        </a>
      </div>

      <!-- System - only for admins -->
      <template v-if="isAdmin">
        <p class="menu-label">System</p>
        <div class="menu-list">
          <a @click.prevent="navigateTo('/admin/addresses')" class="panel-block" :class="{ 'is-active': isActive('/admin/addresses') }">
            <span class="panel-icon"><i class="fas fa-map-marker-alt"></i></span> Addresses
          </a>
        </div>
      </template>

      <!-- Configuration section - only for admins -->
      <template v-if="isAdmin">
        <p class="menu-label">Configuration</p>
        <div class="menu-list">
          <a @click.prevent="navigateTo('/admin/settings')" class="panel-block" :class="{ 'is-active': isActive('/admin/settings') }">
            <span class="panel-icon"><i class="fas fa-cog"></i></span> Settings
          </a>
          <a @click.prevent="navigateTo('/admin/security')" class="panel-block" :class="{ 'is-active': isActive('/admin/security') }">
            <span class="panel-icon"><i class="fas fa-shield-alt"></i></span> Security & Logs
          </a>
        </div>
      </template>

      <div class="panel-block logout-container">
        <button class="button is-danger is-fullwidth" @click="logout" :disabled="isNavigating">
          <span class="icon"><i class="fas fa-sign-out-alt"></i></span>
          <span>Logout</span>
        </button>
      </div>
    </nav>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';

const store = useStore();
const router = useRouter();
const route = useRoute();
const isMobile = ref(false);
const isNavigating = ref(false);
const lastNavigationTime = ref(0);
const navigationDebounceTime = 300; // ms

// Props
const props = defineProps({
  isVisible: {
    type: Boolean,
    default: true
  }
});

// Emits
const emit = defineEmits(['toggle-sidebar']);

// Computed properties
const isAdmin = computed(() => store.getters['auth/isAdmin']);
const isModerator = computed(() => store.getters['auth/isModerator']);
const isAdminOrModerator = computed(() => store.getters['auth/isAdminOrModerator']);

const toggleSidebar = () => {
  emit('toggle-sidebar');
};

// Check if a route is active
const isActive = (path) => {
  return route.path === path || route.path.startsWith(`${path}/`);
};

// Debounced navigation
const navigateTo = (path) => {
  const now = Date.now();

  // Prevent rapid navigation clicks
  if (isNavigating.value || (now - lastNavigationTime.value < navigationDebounceTime)) {
    return;
  }

  // Don't navigate if we're already on this page
  if (isActive(path)) {
    return;
  }

  isNavigating.value = true;
  lastNavigationTime.value = now;

  // Cancel any pending API requests before navigation
  const apiService = store.getters['loading/apiService'];
  if (apiService) {
    apiService.cancelRequestsForRoute(route.path);
  }

  // Map paths to route names for more reliable navigation
  let routeName = '';
  let params = {};

  // Map common paths to their route names
  if (path === '/admin/dashboard') routeName = 'AdminDashboard';
  else if (path === '/admin/users') routeName = 'AdminUsers';
  else if (path === '/admin/products') routeName = 'AdminProducts';
  else if (path === '/admin/categories') routeName = 'AdminCategories';
  else if (path === '/admin/orders') routeName = 'AdminOrders';
  else if (path === '/admin/seller-requests') routeName = 'AdminSellerRequests';
  else if (path === '/admin/companies') routeName = 'AdminCompanies';
  else if (path === '/admin/reviews') routeName = 'AdminReviews';
  else if (path === '/admin/ratings') routeName = 'AdminRatings';
  else if (path === '/admin/chats') routeName = 'AdminChats';
  else if (path === '/admin/addresses') routeName = 'AdminAddresses';
  else if (path === '/admin/settings') routeName = 'AdminSettings';
  else if (path === '/admin/reports') routeName = 'AdminReports';
  else if (path === '/admin/security') routeName = 'AdminSecurity';
  else routeName = ''; // Default empty for path-based navigation

  // Navigate to the new route
  if (routeName) {
    // Use named route navigation
    router.push({ name: routeName, params }).finally(() => {
      // Reset navigation state after a delay
      setTimeout(() => {
        isNavigating.value = false;
      }, navigationDebounceTime);
    });
  } else {
    // Fallback to path-based navigation
    router.push(path).finally(() => {
      // Reset navigation state after a delay
      setTimeout(() => {
        isNavigating.value = false;
      }, navigationDebounceTime);
    });
  }

  // Close sidebar on mobile after navigation
  if (isMobile.value) {
    toggleSidebar();
  }
};

const logout = async () => {
  if (isNavigating.value) return;

  isNavigating.value = true;
  try {
    await store.dispatch('auth/logout');
    router.push('/login');
  } finally {
    setTimeout(() => {
      isNavigating.value = false;
    }, navigationDebounceTime);
  }
};

// Check if mobile view
const checkMobile = () => {
  isMobile.value = window.innerWidth < 1024;
};

onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});
</script>

<style scoped>
.sidebar-container {
  height: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, var(--admin-gray-900) 0%, var(--admin-gray-800) 100%);
  padding: var(--admin-space-xl);
  position: relative;
  border-right: 1px solid var(--admin-border-dark);
  box-shadow: var(--admin-shadow-lg);
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-space-2xl);
  padding-bottom: var(--admin-space-lg);
  border-bottom: 1px solid var(--admin-gray-700);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
}

.logo {
  height: 32px;
  width: auto;
  filter: brightness(0) invert(1);
}

.sidebar {
  height: 100%;
}

.menu-label {
  color: var(--admin-gray-400);
  font-weight: var(--admin-font-bold);
  text-transform: uppercase;
  font-size: var(--admin-text-xs);
  letter-spacing: 0.1em;
  margin-top: var(--admin-space-xl);
  margin-bottom: var(--admin-space-md);
  padding-left: var(--admin-space-md);
}

.menu-label:first-of-type {
  margin-top: 0;
}

.panel-block {
  display: flex;
  align-items: center;
  padding: var(--admin-space-md) var(--admin-space-md);
  cursor: pointer;
  transition: all var(--admin-transition-fast);
  color: var(--admin-gray-300);
  border-radius: var(--admin-radius-lg);
  margin-bottom: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.panel-block::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: var(--admin-primary);
  transform: scaleY(0);
  transition: transform var(--admin-transition-fast);
}

.panel-block:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--admin-white);
  transform: translateX(4px);
}

.panel-block:hover::before {
  transform: scaleY(1);
}

.panel-block.is-active {
  background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-dark) 100%);
  color: var(--admin-white);
  font-weight: var(--admin-font-bold);
  box-shadow: var(--admin-shadow-md);
  transform: translateX(4px);
}

.panel-block.is-active::before {
  transform: scaleY(1);
}

.panel-icon {
  margin-right: var(--admin-space-md);
  font-size: var(--admin-text-base);
  width: 20px;
  text-align: center;
  flex-shrink: 0;
}

.logout-container {
  margin-top: var(--admin-space-2xl);
  border: none;
  padding-top: var(--admin-space-lg);
  border-top: 1px solid var(--admin-gray-700);
}

.sidebar-close {
  position: absolute;
  top: var(--admin-space-lg);
  right: var(--admin-space-lg);
  background: none;
  border: none;
  color: var(--admin-white);
  cursor: pointer;
  z-index: 10;
  padding: var(--admin-space-sm);
  border-radius: var(--admin-radius-md);
  transition: all var(--admin-transition-fast);
}

.sidebar-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Responsive Adjustments */
@media screen and (max-width: 1023px) {
  .sidebar-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    z-index: 30;
    box-shadow: var(--admin-shadow-2xl);
  }
}

@media screen and (max-width: 768px) {
  .sidebar-container {
    width: 100vw;
    padding: var(--admin-space-lg);
  }

  .logo {
    height: 28px;
  }

  .panel-block {
    padding: var(--admin-space-lg) var(--admin-space-md);
    font-size: var(--admin-text-base);
  }
}
</style>
