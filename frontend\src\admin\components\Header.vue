<template>
  <header class="admin-header">
    <nav class="navbar" role="navigation" aria-label="main navigation">
      <!-- Mobile Sidebar Toggle -->
      <button
        class="sidebar-toggle is-hidden-desktop"
        @click="toggleSidebar">
        <span class="icon">
          <i class="fas fa-bars"></i>
        </span>
      </button>

      <div class="navbar-brand">
        <h1 class="navbar-item title is-4">Klondike Admin</h1>
      </div>

      <div class="navbar-menu" :class="{ 'is-active': isMenuActive }">
        <div class="navbar-end">
          <!-- Notifications -->
          <div class="navbar-item has-dropdown" :class="{ 'is-active': isNotificationsActive }">
            <a class="navbar-link notification-link" @click="toggleNotifications">
              <span class="icon">
                <i class="fas fa-bell"></i>
                <span class="notification-badge" v-if="unreadNotifications > 0">{{ unreadNotifications }}</span>
              </span>
            </a>

            <div class="navbar-dropdown is-right notifications-dropdown">
              <div class="notifications-header">
                <h3 class="title is-6">Notifications</h3>
                <a @click="markAllAsRead" class="is-size-7">Mark all as read</a>
              </div>

              <div class="notifications-list" v-if="notifications.length > 0">
                <a
                  v-for="notification in notifications"
                  :key="notification.id"
                  class="navbar-item notification-item"
                  :class="{ 'is-unread': !notification.isRead }"
                  @click="handleNotificationClick(notification)">
                  <div class="notification-icon">
                    <span class="icon">
                      <i :class="getNotificationIcon(notification.type)"></i>
                    </span>
                  </div>
                  <div class="notification-content">
                    <p class="notification-text">{{ notification.message }}</p>
                    <p class="notification-time">{{ formatTime(notification.createdAt) }}</p>
                  </div>
                </a>
              </div>

              <div class="notifications-empty" v-else>
                <p>No notifications</p>
              </div>

              <div class="notifications-footer">
                <router-link to="/admin/notifications" class="navbar-item">
                  View all notifications
                </router-link>
              </div>
            </div>
          </div>

          <!-- User Menu -->
          <div class="navbar-item has-dropdown" :class="{ 'is-active': isUserMenuActive }">
            <a class="navbar-link" @click="toggleUserMenu">
              <span class="icon"><i class="fas fa-user"></i></span>
              <span>{{ username }}</span>
            </a>

            <div class="navbar-dropdown is-right">
              <router-link to="/admin/profile" class="navbar-item">
                <span class="icon"><i class="fas fa-user-circle"></i></span>
                <span>Profile</span>
              </router-link>
              <hr class="navbar-divider">
              <a class="navbar-item" @click="logout">
                <span class="icon"><i class="fas fa-sign-out-alt"></i></span>
                <span>Logout</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </nav>
  </header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { format, formatDistanceToNow } from 'date-fns';

// Store and router
const store = useStore();
const router = useRouter();

// User data
const username = computed(() => store.getters['auth/user']?.username || 'Admin');

// Mobile menu state
const isMenuActive = ref(false);
const isUserMenuActive = ref(false);
const isNotificationsActive = ref(false);

// Notifications
const notifications = ref([
  {
    id: 1,
    type: 'order',
    message: 'New order #12345 has been placed',
    isRead: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 30) // 30 minutes ago
  },
  {
    id: 2,
    type: 'seller',
    message: 'New seller application from John Doe',
    isRead: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2 hours ago
  },
  {
    id: 3,
    type: 'product',
    message: 'Product "iPhone 13" is low in stock (2 remaining)',
    isRead: true,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 5) // 5 hours ago
  }
]);

// Computed
const unreadNotifications = computed(() => {
  return notifications.value.filter(n => !n.isRead).length;
});

// Emits
const emit = defineEmits(['toggle-sidebar']);

// Methods
const toggleSidebar = () => {
  emit('toggle-sidebar');
};

const toggleUserMenu = () => {
  isUserMenuActive.value = !isUserMenuActive.value;
  if (isUserMenuActive.value) {
    isNotificationsActive.value = false;
  }
};

const toggleNotifications = () => {
  isNotificationsActive.value = !isNotificationsActive.value;
  if (isNotificationsActive.value) {
    isUserMenuActive.value = false;
  }
};

const logout = async () => {
  await store.dispatch('auth/logout');
  router.push('/login');
};

const markAllAsRead = () => {
  notifications.value.forEach(notification => {
    notification.isRead = true;
  });
};

const handleNotificationClick = (notification) => {
  notification.isRead = true;

  // Navigate based on notification type
  if (notification.type === 'order') {
    router.push('/admin/orders');
  } else if (notification.type === 'seller') {
    router.push('/admin/seller-requests');
  } else if (notification.type === 'product') {
    router.push('/admin/products');
  }

  isNotificationsActive.value = false;
};

const getNotificationIcon = (type) => {
  switch (type) {
    case 'order':
      return 'fas fa-shopping-cart';
    case 'seller':
      return 'fas fa-user-plus';
    case 'product':
      return 'fas fa-box';
    default:
      return 'fas fa-bell';
  }
};

const formatTime = (date) => {
  return formatDistanceToNow(new Date(date), { addSuffix: true });
};

// Close dropdowns when clicking outside
const handleClickOutside = (event) => {
  const userDropdown = document.querySelector('.navbar-item.has-dropdown:last-child');
  const notificationsDropdown = document.querySelector('.navbar-item.has-dropdown:first-child');

  if (userDropdown && !userDropdown.contains(event.target)) {
    isUserMenuActive.value = false;
  }

  if (notificationsDropdown && !notificationsDropdown.contains(event.target)) {
    isNotificationsActive.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.admin-header {
  padding: 0.5rem 1.5rem;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

.navbar {
  background-color: transparent;
}

.navbar-brand {
  margin-left: 3rem;
}

.navbar-brand .title {
  color: #333;
}

.navbar-item, .navbar-link {
  color: #333;
}

.navbar-dropdown {
  background-color: #fff;
  border-color: #e0e0e0;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.navbar-dropdown .navbar-item:hover {
  background-color: #f5f5f5;
  color: #ff7700;
}

/* Notifications */
.notification-link {
  position: relative;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #ff3860;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notifications-dropdown {
  width: 300px;
  padding: 0;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid #e0e0e0;
}

.notifications-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  padding: 0.75rem;
  border-bottom: 1px solid #f0f0f0;
}

.notification-item.is-unread {
  background-color: #f0f8ff;
}

.notification-icon {
  margin-right: 0.75rem;
}

.notification-content {
  flex: 1;
}

.notification-text {
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.notification-time {
  font-size: 0.75rem;
  color: #999;
}

.notifications-empty {
  padding: 1.5rem;
  text-align: center;
  color: #999;
}

.notifications-footer {
  border-top: 1px solid #e0e0e0;
  text-align: center;
}

.notifications-footer .navbar-item {
  justify-content: center;
  color: #ff7700;
}

/* Mobile Sidebar Toggle */
.sidebar-toggle {
  position: absolute;
  top: 0.75rem;
  left: 0.75rem;
  background-color: transparent;
  border: none;
  color: #333;
  font-size: 1.25rem;
  cursor: pointer;
  z-index: 10;
}

/* Responsive Adjustments */
@media screen and (max-width: 1023px) {
  .navbar-brand {
    margin-left: 3rem;
  }
}
</style>
