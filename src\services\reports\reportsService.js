import api from '../api.service'
import { mockData } from './mockData'
import { formatters } from './formatters'
import { ReportCache } from './cache'

/**
 * Unified Reports Service
 * Combines all report-related functionality into a single, optimized service
 */
class UnifiedReportsService {
  constructor() {
    this.baseUrl = '/api/admin/reports'
    this.cache = new ReportCache()
    this.isDevelopment = process.env.NODE_ENV === 'development'
    this.useMockData = this.isDevelopment || false // Can be toggled for testing
  }

  // ==================== CORE API METHODS ====================

  /**
   * Generic method to get any report type
   * @param {string} reportType - Type of report (financial, sales, products, users, orders)
   * @param {Object} filters - Report filters and parameters
   * @returns {Promise<Object>} Report data
   */
  async getReport(reportType, filters = {}) {
    const cacheKey = this.generateCacheKey(reportType, filters)
    
    // Check cache first
    const cachedData = this.cache.get(cacheKey)
    if (cachedData) {
      return cachedData
    }

    try {
      let data
      
      if (this.useMockData) {
        data = mockData.getReportData(reportType, filters)
      } else {
        const validatedFilters = this.validateFilters(filters)
        const response = await api.get(`${this.baseUrl}/${reportType}`, {
          params: validatedFilters
        })
        data = response.data
      }

      // Cache the result
      this.cache.set(cacheKey, data)
      return data

    } catch (error) {
      console.error(`Error fetching ${reportType} report:`, error)
      
      // Fallback to mock data on error
      if (!this.useMockData) {
        console.warn(`Falling back to mock data for ${reportType} report`)
        return mockData.getReportData(reportType, filters)
      }
      
      throw new Error(error.response?.data?.message || `Failed to fetch ${reportType} report`)
    }
  }

  /**
   * Get dashboard summary with all report types
   * @param {Object} filters - Global filters
   * @returns {Promise<Object>} Dashboard summary data
   */
  async getDashboardSummary(filters = {}) {
    const cacheKey = this.generateCacheKey('dashboard', filters)
    const cachedData = this.cache.get(cacheKey)
    
    if (cachedData) {
      return cachedData
    }

    try {
      let data

      if (this.useMockData) {
        data = mockData.getDashboardSummary(filters)
      } else {
        const response = await api.get(`${this.baseUrl}/dashboard-summary`, {
          params: this.validateFilters(filters)
        })
        data = response.data
      }

      this.cache.set(cacheKey, data)
      return data

    } catch (error) {
      console.error('Error fetching dashboard summary:', error)
      
      if (!this.useMockData) {
        return mockData.getDashboardSummary(filters)
      }
      
      throw new Error(error.response?.data?.message || 'Failed to fetch dashboard summary')
    }
  }

  /**
   * Export report in specified format
   * @param {string} reportType - Type of report
   * @param {string} format - Export format (csv, pdf, excel)
   * @param {Object} filters - Report filters
   * @returns {Promise<Blob>} File blob for download
   */
  async exportReport(reportType, format, filters = {}) {
    try {
      if (this.useMockData) {
        return mockData.generateExportFile(reportType, format, filters)
      }

      const response = await api.get(`${this.baseUrl}/${reportType}/export/${format}`, {
        params: this.validateFilters(filters),
        responseType: 'blob',
        headers: {
          'Accept': this.getAcceptHeader(format)
        }
      })

      return response.data

    } catch (error) {
      console.error(`Error exporting ${reportType} report as ${format}:`, error)
      
      if (!this.useMockData) {
        return mockData.generateExportFile(reportType, format, filters)
      }
      
      throw new Error(error.response?.data?.message || `Failed to export ${reportType} report`)
    }
  }

  // ==================== SPECIFIC REPORT METHODS ====================

  async getFinancialReport(filters = {}) {
    return this.getReport('financial', filters)
  }

  async getSalesReport(filters = {}) {
    return this.getReport('sales', filters)
  }

  async getProductsReport(filters = {}) {
    return this.getReport('products', filters)
  }

  async getUsersReport(filters = {}) {
    return this.getReport('users', filters)
  }

  async getOrdersReport(filters = {}) {
    return this.getReport('orders', filters)
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Validate and sanitize filters
   * @param {Object} filters - Raw filters
   * @returns {Object} Validated filters
   */
  validateFilters(filters) {
    const validatedFilters = {}
    const allowedKeys = [
      'startDate', 'endDate', 'category', 'status', 'limit', 'offset',
      'sortBy', 'sortOrder', 'reportType', 'format', 'groupBy'
    ]

    Object.entries(filters).forEach(([key, value]) => {
      if (allowedKeys.includes(key) && value !== null && value !== undefined && value !== '') {
        validatedFilters[key] = value
      }
    })

    // Validate date range
    if (validatedFilters.startDate && validatedFilters.endDate) {
      const validation = this.validateDateRange(
        new Date(validatedFilters.startDate),
        new Date(validatedFilters.endDate)
      )
      
      if (!validation.isValid) {
        throw new Error(`Invalid date range: ${validation.errors.join(', ')}`)
      }
    }

    return validatedFilters
  }

  /**
   * Generate cache key for request
   * @param {string} reportType - Report type
   * @param {Object} filters - Filters object
   * @returns {string} Cache key
   */
  generateCacheKey(reportType, filters) {
    const sortedFilters = Object.keys(filters)
      .sort()
      .reduce((result, key) => {
        result[key] = filters[key]
        return result
      }, {})
    
    return `${reportType}_${JSON.stringify(sortedFilters)}`
  }

  /**
   * Get appropriate Accept header for export format
   * @param {string} format - Export format
   * @returns {string} Accept header value
   */
  getAcceptHeader(format) {
    const headers = {
      csv: 'text/csv',
      pdf: 'application/pdf',
      excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    }
    return headers[format] || 'application/octet-stream'
  }

  /**
   * Download exported file
   * @param {Blob} blob - File blob
   * @param {string} filename - Base filename
   * @param {string} format - File format
   */
  downloadFile(blob, filename, format) {
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    
    const extension = formatters.getFileExtension(format)
    link.download = `${filename}.${extension}`
    
    document.body.appendChild(link)
    link.click()
    
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }

  /**
   * Generate filename for export
   * @param {string} reportType - Report type
   * @param {Object} filters - Report filters
   * @returns {string} Generated filename
   */
  generateFilename(reportType, filters = {}) {
    const { startDate, endDate } = filters
    const start = startDate ? new Date(startDate).toISOString().split('T')[0] : 'all'
    const end = endDate ? new Date(endDate).toISOString().split('T')[0] : 'time'
    const timestamp = new Date().toISOString().split('T')[0]
    
    return `${reportType}_report_${start}_to_${end}_${timestamp}`
  }

  /**
   * Validate date range
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Object} Validation result
   */
  validateDateRange(startDate, endDate) {
    const errors = []
    
    if (!startDate) errors.push('Start date is required')
    if (!endDate) errors.push('End date is required')
    if (startDate && endDate && startDate > endDate) {
      errors.push('Start date must be before end date')
    }
    if (startDate && startDate > new Date()) {
      errors.push('Start date cannot be in the future')
    }
    if (endDate && endDate > new Date()) {
      errors.push('End date cannot be in the future')
    }
    
    // Check if date range is too large (more than 1 year)
    if (startDate && endDate) {
      const diffTime = Math.abs(endDate - startDate)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays > 365) {
        errors.push('Date range cannot exceed 1 year')
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // ==================== CACHE MANAGEMENT ====================

  /**
   * Clear all cached data
   */
  clearCache() {
    this.cache.clear()
  }

  /**
   * Clear cache for specific report type
   * @param {string} reportType - Report type to clear
   */
  clearReportCache(reportType) {
    this.cache.clearByPattern(reportType)
  }

  // ==================== CONFIGURATION ====================

  /**
   * Toggle mock data usage
   * @param {boolean} useMock - Whether to use mock data
   */
  setMockDataMode(useMock) {
    this.useMockData = useMock
    this.clearCache() // Clear cache when switching modes
  }

  /**
   * Get current configuration
   * @returns {Object} Current configuration
   */
  getConfig() {
    return {
      baseUrl: this.baseUrl,
      useMockData: this.useMockData,
      isDevelopment: this.isDevelopment,
      cacheSize: this.cache.size()
    }
  }

  // ==================== HEALTH CHECK ====================

  /**
   * Check service health
   * @returns {Promise<Object>} Health status
   */
  async healthCheck() {
    try {
      if (this.useMockData) {
        return { status: 'healthy', mode: 'mock', timestamp: new Date().toISOString() }
      }

      const response = await api.get(`${this.baseUrl}/health`)
      return response.data

    } catch (error) {
      console.error('Health check failed:', error)
      return { 
        status: 'error', 
        message: error.message, 
        timestamp: new Date().toISOString() 
      }
    }
  }
}

// Create and export singleton instance
export const reportsService = new UnifiedReportsService()
export default reportsService

// Export formatters for direct use
export { formatters }
