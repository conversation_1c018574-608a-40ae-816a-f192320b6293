<template>
  <div class="admin-page-container">
    <div class="page-header">
      <div class="level-left">
        <div class="level-item">
          <h1 class="page-title">Products</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <button class="button is-primary" @click="showAddModal = true">
            <span class="icon"><i class="fas fa-plus"></i></span>
            <span>Add Product</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Search and Filter -->
    <div class="box">
      <div class="field has-addons">
        <div class="control is-expanded">
          <input class="input" type="text" placeholder="Search products..." v-model="searchQuery">
        </div>
        <div class="control">
          <button class="button is-info" @click="searchProducts">
            <span class="icon"><i class="fas fa-search"></i></span>
          </button>
        </div>
      </div>

      <div class="columns">
        <div class="column">
          <div class="field">
            <label class="label">Category</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="filters.category">
                  <option value="">All Categories</option>
                  <option v-for="category in categories" :key="category.id" :value="category.id">
                    {{ category.name }}
                  </option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <div class="column">
          <div class="field">
            <label class="label">Sort By</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="filters.sortBy">
                  <option value="name">Name</option>
                  <option value="price">Price</option>
                  <option value="createdAt">Date Added</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <div class="column">
          <div class="field">
            <label class="label">Order</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="filters.sortOrder">
                  <option value="asc">Ascending</option>
                  <option value="desc">Descending</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Products Table -->
    <div class="box">
      <div class="table-container">
        <table class="table is-fullwidth is-striped">
          <thead>
            <tr>
              <th>ID</th>
              <th>Name</th>
              <th>Category</th>
              <th>Price</th>
              <th>Stock</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="product in products" :key="product.id">
              <td>{{ product.id.substring(0, 8) }}</td>
              <td>{{ product.name }}</td>
              <td>{{ product.category }}</td>
              <td>{{ formatPrice(product.price) }}</td>
              <td>{{ product.stock }}</td>
              <td>
                <div class="buttons are-small">
                  <button class="button is-info" @click="editProduct(product)">
                    <span class="icon"><i class="fas fa-edit"></i></span>
                  </button>
                  <button class="button is-danger" @click="confirmDelete(product)">
                    <span class="icon"><i class="fas fa-trash"></i></span>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <nav class="pagination is-centered" role="navigation" aria-label="pagination">
        <a class="pagination-previous" :disabled="currentPage === 1" @click="changePage(currentPage - 1)">Previous</a>
        <a class="pagination-next" :disabled="currentPage === totalPages" @click="changePage(currentPage + 1)">Next</a>
        <ul class="pagination-list">
          <li v-for="page in paginationItems" :key="page.value">
            <a
              v-if="page.type === 'page'"
              class="pagination-link"
              :class="{ 'is-current': page.value === currentPage }"
              @click="changePage(page.value)"
            >
              {{ page.value }}
            </a>
            <span v-else class="pagination-ellipsis">&hellip;</span>
          </li>
        </ul>
      </nav>
    </div>

    <!-- Add/Edit Product Modal -->
    <div class="modal" :class="{ 'is-active': showAddModal || showEditModal }">
      <div class="modal-background" @click="closeModals"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">{{ showEditModal ? 'Edit Product' : 'Add Product' }}</p>
          <button class="delete" aria-label="close" @click="closeModals"></button>
        </header>
        <section class="modal-card-body">
          <div class="field">
            <label class="label">Name</label>
            <div class="control">
              <input class="input" type="text" placeholder="Product name" v-model="productForm.name">
            </div>
          </div>

          <div class="field">
            <label class="label">Slug</label>
            <div class="control">
              <input
                class="input"
                :class="{ 'is-danger': slugError }"
                type="text"
                placeholder="product-slug"
                v-model="productForm.slug"
                @input="validateSlug">
            </div>
            <p class="help" :class="{ 'is-danger': slugError }">
              {{ slugError ? slugError : 'URL-friendly version of the name. Leave blank to auto-generate.' }}
            </p>
          </div>

          <div class="field">
            <label class="label">Description</label>
            <div class="control">
              <textarea class="textarea" placeholder="Product description" v-model="productForm.description"></textarea>
            </div>
          </div>

          <div class="columns">
            <div class="column">
              <div class="field">
                <label class="label">Price</label>
                <div class="control">
                  <input class="input" type="number" step="0.01" placeholder="Price" v-model="productForm.price">
                </div>
              </div>
            </div>

            <div class="column">
              <div class="field">
                <label class="label">Stock</label>
                <div class="control">
                  <input class="input" type="number" placeholder="Stock quantity" v-model="productForm.stock">
                </div>
              </div>
            </div>
          </div>

          <div class="field">
            <label class="label">Category</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="productForm.categoryId">
                  <option v-for="category in categories" :key="category.id" :value="category.id">
                    {{ category.name }}
                  </option>
                </select>
              </div>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button class="button is-success" @click="saveProduct">Save</button>
          <button class="button" @click="closeModals">Cancel</button>
        </footer>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal" :class="{ 'is-active': showDeleteModal }">
      <div class="modal-background" @click="showDeleteModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Confirm Delete</p>
          <button class="delete" aria-label="close" @click="showDeleteModal = false"></button>
        </header>
        <section class="modal-card-body">
          Are you sure you want to delete the product "{{ productToDelete?.name }}"? This action cannot be undone.
        </section>
        <footer class="modal-card-foot">
          <button class="button is-danger" @click="deleteProduct">Delete</button>
          <button class="button" @click="showDeleteModal = false">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { slugify, isValidSlug } from '@/utils/slugify';

// Mock data - would be replaced with API calls
const products = ref([
  { id: '1234abcd5678efgh', name: 'Smartphone X', slug: 'smartphone-x', category: 'Electronics', price: 999.99, stock: 50 },
  { id: '2345bcde6789fghi', name: 'Laptop Pro', slug: 'laptop-pro', category: 'Electronics', price: 1499.99, stock: 25 },
  { id: '3456cdef7890ghij', name: 'Wireless Headphones', slug: 'wireless-headphones', category: 'Audio', price: 199.99, stock: 100 },
  { id: '4567defg8901hijk', name: 'Smart Watch', slug: 'smart-watch', category: 'Wearables', price: 299.99, stock: 75 },
  { id: '5678efgh9012ijkl', name: 'Bluetooth Speaker', slug: 'bluetooth-speaker', category: 'Audio', price: 129.99, stock: 60 },
  { id: '6789fghi0123jklm', name: 'Tablet Mini', slug: 'tablet-mini', category: 'Electronics', price: 399.99, stock: 40 },
  { id: '7890ghij1234klmn', name: 'Digital Camera', slug: 'digital-camera', category: 'Photography', price: 599.99, stock: 30 },
  { id: '8901hijk2345lmno', name: 'Gaming Console', slug: 'gaming-console', category: 'Gaming', price: 499.99, stock: 20 },
  { id: '9012ijkl3456mnop', name: 'Wireless Mouse', slug: 'wireless-mouse', category: 'Accessories', price: 49.99, stock: 150 },
  { id: '0123jklm4567nopq', name: 'External Hard Drive', slug: 'external-hard-drive', category: 'Storage', price: 129.99, stock: 80 }
]);

const categories = ref([
  { id: 1, name: 'Electronics' },
  { id: 2, name: 'Audio' },
  { id: 3, name: 'Wearables' },
  { id: 4, name: 'Photography' },
  { id: 5, name: 'Gaming' },
  { id: 6, name: 'Accessories' },
  { id: 7, name: 'Storage' }
]);

// Pagination
const currentPage = ref(1);
const itemsPerPage = ref(10);
const totalItems = ref(100);

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage.value));

const paginationItems = computed(() => {
  const items = [];
  const maxVisiblePages = 5;

  if (totalPages.value <= maxVisiblePages) {
    // Show all pages
    for (let i = 1; i <= totalPages.value; i++) {
      items.push({ type: 'page', value: i });
    }
  } else {
    // Always show first page
    items.push({ type: 'page', value: 1 });

    // Calculate start and end of visible pages
    let startPage = Math.max(2, currentPage.value - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages.value - 1, startPage + maxVisiblePages - 3);

    // Adjust if we're near the beginning
    if (startPage > 2) {
      items.push({ type: 'ellipsis' });
    }

    // Add visible pages
    for (let i = startPage; i <= endPage; i++) {
      items.push({ type: 'page', value: i });
    }

    // Adjust if we're near the end
    if (endPage < totalPages.value - 1) {
      items.push({ type: 'ellipsis' });
    }

    // Always show last page
    items.push({ type: 'page', value: totalPages.value });
  }

  return items;
});

// Search and filters
const searchQuery = ref('');
const filters = ref({
  category: '',
  sortBy: 'name',
  sortOrder: 'asc'
});

// Modals
const showAddModal = ref(false);
const showEditModal = ref(false);
const showDeleteModal = ref(false);
const productToDelete = ref(null);
const productForm = ref({
  id: null,
  name: '',
  slug: '',
  description: '',
  price: 0,
  stock: 0,
  categoryId: null
});

const slugError = ref('');

// Auto-generate slug when name changes
watch(() => productForm.value.name, (newName) => {
  // Only auto-generate if slug is empty or user hasn't manually edited it
  if (!productForm.value.slug) {
    productForm.value.slug = slugify(newName);
  }
});

// Validate slug
const validateSlug = () => {
  if (!productForm.value.slug) {
    slugError.value = '';
    return true;
  }

  if (!isValidSlug(productForm.value.slug)) {
    slugError.value = 'Slug can only contain lowercase letters, numbers, and hyphens.';
    return false;
  }

  slugError.value = '';
  return true;
};

// Methods
const formatPrice = (price) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(price);
};

const searchProducts = () => {
  // Here you would call your API with search parameters
  console.log('Searching for:', searchQuery.value, 'with filters:', filters.value);
};

const changePage = (page) => {
  currentPage.value = page;
  // Here you would fetch the data for the new page
};

const editProduct = (product) => {
  productForm.value = {
    id: product.id,
    name: product.name,
    slug: product.slug || slugify(product.name),
    description: 'Product description here...',
    price: product.price,
    stock: product.stock,
    categoryId: categories.value.find(c => c.name === product.category)?.id || null
  };
  showEditModal.value = true;
};

const confirmDelete = (product) => {
  productToDelete.value = product;
  showDeleteModal.value = true;
};

const deleteProduct = () => {
  // Here you would call your API to delete the product
  console.log('Deleting product:', productToDelete.value);

  // Mock implementation - remove from local array
  const index = products.value.findIndex(p => p.id === productToDelete.value.id);
  if (index !== -1) {
    products.value.splice(index, 1);
  }

  showDeleteModal.value = false;
  productToDelete.value = null;
};

const saveProduct = () => {
  // Validate form before saving
  if (!productForm.value.name) {
    alert('Name is required');
    return;
  }

  // If slug is empty, generate it from the name
  if (!productForm.value.slug) {
    productForm.value.slug = slugify(productForm.value.name);
  }

  // Validate slug
  if (!validateSlug()) {
    alert('Please fix the slug format issues before saving');
    return;
  }

  if (productForm.value.id) {
    // Update existing product
    console.log('Updating product:', productForm.value);

    // Mock implementation - update in local array
    const index = products.value.findIndex(p => p.id === productForm.value.id);
    if (index !== -1) {
      const category = categories.value.find(c => c.id === productForm.value.categoryId)?.name || '';
      products.value[index] = {
        ...products.value[index],
        name: productForm.value.name,
        slug: productForm.value.slug,
        price: productForm.value.price,
        stock: productForm.value.stock,
        category
      };
    }
  } else {
    // Create new product
    console.log('Creating product:', productForm.value);

    // Mock implementation - add to local array
    const category = categories.value.find(c => c.id === productForm.value.categoryId)?.name || '';
    products.value.unshift({
      id: Math.random().toString(36).substring(2, 10) + Math.random().toString(36).substring(2, 10),
      name: productForm.value.name,
      slug: productForm.value.slug,
      price: productForm.value.price,
      stock: productForm.value.stock,
      category
    });
  }

  closeModals();
};

const closeModals = () => {
  showAddModal.value = false;
  showEditModal.value = false;
  productForm.value = {
    id: null,
    name: '',
    slug: '',
    description: '',
    price: 0,
    stock: 0,
    categoryId: null
  };
  slugError.value = '';
};

onMounted(() => {
  // Here you would fetch initial data from your API
  // Example: fetchProducts();
});
</script>

<style scoped>
.level {
  margin-bottom: 1.5rem;
}

.pagination {
  margin-top: 1.5rem;
}
</style>
