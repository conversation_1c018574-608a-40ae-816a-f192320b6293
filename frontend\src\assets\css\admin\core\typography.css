/* ===== ADMIN TYPOGRAPHY SYSTEM ===== */
/* Based on Reports page typography patterns */

/* ===== BASE TYPOGRAPHY ===== */
.admin-typography {
  font-family: var(--admin-font-family);
  line-height: var(--admin-leading-normal);
  color: var(--admin-gray-900);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== HEADINGS ===== */
.admin-h1,
.admin-page-title {
  font-size: var(--admin-text-5xl);
  font-weight: var(--admin-font-bold);
  line-height: var(--admin-leading-tight);
  color: var(--admin-gray-900);
  margin: 0 0 var(--admin-space-lg) 0;
}

.admin-h2,
.admin-section-title {
  font-size: var(--admin-text-3xl);
  font-weight: var(--admin-font-semibold);
  line-height: var(--admin-leading-tight);
  color: var(--admin-gray-900);
  margin: 0 0 var(--admin-space-md) 0;
}

.admin-h3,
.admin-subsection-title {
  font-size: var(--admin-text-2xl);
  font-weight: var(--admin-font-semibold);
  line-height: var(--admin-leading-tight);
  color: var(--admin-gray-800);
  margin: 0 0 var(--admin-space-md) 0;
}

.admin-h4,
.admin-card-title {
  font-size: var(--admin-text-xl);
  font-weight: var(--admin-font-semibold);
  line-height: var(--admin-leading-tight);
  color: var(--admin-gray-800);
  margin: 0 0 var(--admin-space-sm) 0;
}

.admin-h5,
.admin-metric-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-medium);
  line-height: var(--admin-leading-tight);
  color: var(--admin-gray-700);
  margin: 0 0 var(--admin-space-sm) 0;
}

.admin-h6,
.admin-label {
  font-size: var(--admin-text-base);
  font-weight: var(--admin-font-medium);
  line-height: var(--admin-leading-normal);
  color: var(--admin-gray-700);
  margin: 0 0 var(--admin-space-xs) 0;
}

/* ===== BODY TEXT ===== */
.admin-text-base,
.admin-body {
  font-size: var(--admin-text-base);
  font-weight: var(--admin-font-normal);
  line-height: var(--admin-leading-normal);
  color: var(--admin-gray-700);
}

.admin-text-lg {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-normal);
  line-height: var(--admin-leading-normal);
  color: var(--admin-gray-700);
}

.admin-text-sm,
.admin-caption {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-normal);
  line-height: var(--admin-leading-normal);
  color: var(--admin-gray-600);
}

.admin-text-xs,
.admin-micro {
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-normal);
  line-height: var(--admin-leading-normal);
  color: var(--admin-gray-500);
}

/* ===== SPECIALIZED TEXT ===== */

/* Page Headers */
.admin-page-header-title {
  font-size: var(--admin-text-4xl);
  font-weight: var(--admin-font-bold);
  color: white;
  margin: 0 0 var(--admin-space-md) 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-lg);
}

.admin-page-header-description {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-normal);
  color: rgba(255, 255, 255, 0.9);
  line-height: var(--admin-leading-relaxed);
  margin: 0;
}

/* Metric Values */
.admin-metric-value {
  font-size: var(--admin-text-2xl);
  font-weight: var(--admin-font-bold);
  color: var(--admin-gray-900);
  line-height: var(--admin-leading-tight);
}

.admin-metric-value-large {
  font-size: var(--admin-text-3xl);
  font-weight: var(--admin-font-bold);
  color: var(--admin-gray-900);
  line-height: var(--admin-leading-tight);
}

.admin-metric-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  color: var(--admin-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-metric-change {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

/* Status Text */
.admin-text-success {
  color: var(--admin-success);
}

.admin-text-warning {
  color: var(--admin-warning);
}

.admin-text-danger {
  color: var(--admin-danger);
}

.admin-text-info {
  color: var(--admin-info);
}

.admin-text-muted {
  color: var(--admin-gray-500);
}

/* ===== FONT WEIGHTS ===== */
.admin-font-normal {
  font-weight: var(--admin-font-normal);
}

.admin-font-medium {
  font-weight: var(--admin-font-medium);
}

.admin-font-semibold {
  font-weight: var(--admin-font-semibold);
}

.admin-font-bold {
  font-weight: var(--admin-font-bold);
}

/* ===== TEXT UTILITIES ===== */
.admin-text-center {
  text-align: center;
}

.admin-text-left {
  text-align: left;
}

.admin-text-right {
  text-align: right;
}

.admin-text-uppercase {
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-text-capitalize {
  text-transform: capitalize;
}

.admin-text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.admin-text-wrap {
  word-wrap: break-word;
  word-break: break-word;
}

.admin-text-nowrap {
  white-space: nowrap;
}

/* ===== LINE HEIGHTS ===== */
.admin-leading-tight {
  line-height: var(--admin-leading-tight);
}

.admin-leading-normal {
  line-height: var(--admin-leading-normal);
}

.admin-leading-relaxed {
  line-height: var(--admin-leading-relaxed);
}

/* ===== LINKS ===== */
.admin-link {
  color: var(--admin-primary);
  text-decoration: none;
  transition: color var(--admin-transition-normal);
}

.admin-link:hover {
  color: var(--admin-primary-dark);
  text-decoration: underline;
}

.admin-link:focus {
  outline: 2px solid var(--admin-primary);
  outline-offset: 2px;
}

/* ===== LISTS ===== */
.admin-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.admin-list-item {
  padding: var(--admin-space-sm) 0;
  border-bottom: 1px solid var(--admin-gray-200);
}

.admin-list-item:last-child {
  border-bottom: none;
}

/* ===== CODE ===== */
.admin-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--admin-text-sm);
  background: var(--admin-gray-100);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  color: var(--admin-gray-800);
}
