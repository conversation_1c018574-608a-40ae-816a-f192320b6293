<template>
  <div class="modal" :class="{ 'is-active': isOpen }">
    <div class="modal-background" @click="$emit('close')"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">{{ user ? 'Edit User' : 'Add User' }}</p>
        <button class="delete" aria-label="close" @click="$emit('close')"></button>
      </header>
      <section class="modal-card-body">
        <form @submit.prevent="submitForm">
          <!-- Basic Info -->
          <div class="columns">
            <div class="column is-6">
              <div class="field">
                <label class="label">First Name</label>
                <div class="control">
                  <input 
                    class="input" 
                    type="text" 
                    placeholder="First name" 
                    v-model="form.firstName"
                    required>
                </div>
              </div>
            </div>
            <div class="column is-6">
              <div class="field">
                <label class="label">Last Name</label>
                <div class="control">
                  <input 
                    class="input" 
                    type="text" 
                    placeholder="Last name" 
                    v-model="form.lastName"
                    required>
                </div>
              </div>
            </div>
          </div>
          
          <div class="field">
            <label class="label">Email</label>
            <div class="control">
              <input 
                class="input" 
                type="email" 
                placeholder="Email address" 
                v-model="form.email"
                required>
            </div>
          </div>
          
          <div class="field">
            <label class="label">Username</label>
            <div class="control">
              <input 
                class="input" 
                type="text" 
                placeholder="Username" 
                v-model="form.username"
                required>
            </div>
          </div>
          
          <!-- Password (only for new users) -->
          <div class="field" v-if="!user">
            <label class="label">Password</label>
            <div class="control">
              <input 
                class="input" 
                type="password" 
                placeholder="Password" 
                v-model="form.password"
                required>
            </div>
          </div>
          
          <!-- Role and Status -->
          <div class="columns">
            <div class="column is-6">
              <div class="field">
                <label class="label">Role</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="form.role" required>
                      <option value="Admin">Admin</option>
                      <option value="Seller">Seller</option>
                      <option value="Customer">Customer</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="column is-6">
              <div class="field">
                <label class="label">Status</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="form.status" required>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="pending">Pending</option>
                      <option value="banned">Banned</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Phone and Address -->
          <div class="field">
            <label class="label">Phone</label>
            <div class="control">
              <input 
                class="input" 
                type="tel" 
                placeholder="Phone number" 
                v-model="form.phone">
            </div>
          </div>
          
          <div class="field">
            <label class="label">Address</label>
            <div class="control">
              <textarea 
                class="textarea" 
                placeholder="Address" 
                v-model="form.address"
                rows="2"></textarea>
            </div>
          </div>
        </form>
      </section>
      <footer class="modal-card-foot">
        <button class="button is-primary" @click="submitForm" :disabled="isSubmitting">
          <span v-if="isSubmitting">
            <span class="icon">
              <i class="fas fa-spinner fa-spin"></i>
            </span>
            <span>Saving...</span>
          </span>
          <span v-else>Save</span>
        </button>
        <button class="button" @click="$emit('close')">Cancel</button>
      </footer>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true
  },
  user: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['close', 'save']);

// Form state
const form = reactive({
  id: null,
  firstName: '',
  lastName: '',
  email: '',
  username: '',
  password: '',
  role: 'Customer',
  status: 'active',
  phone: '',
  address: ''
});

// Submission state
const isSubmitting = ref(false);

// Submit form
const submitForm = async () => {
  isSubmitting.value = true;
  
  try {
    // Create a clean form object
    const userData = { ...form };
    
    // Remove password if editing user
    if (props.user && !userData.password) {
      delete userData.password;
    }
    
    emit('save', userData);
  } catch (error) {
    console.error('Error submitting form:', error);
  } finally {
    isSubmitting.value = false;
  }
};

// Reset form
const resetForm = () => {
  form.id = null;
  form.firstName = '';
  form.lastName = '';
  form.email = '';
  form.username = '';
  form.password = '';
  form.role = 'Customer';
  form.status = 'active';
  form.phone = '';
  form.address = '';
};

// Watch for user changes to update form
watch(() => props.user, (newUser) => {
  if (newUser) {
    // Populate form with user data
    Object.keys(form).forEach(key => {
      if (key in newUser && key !== 'password') {
        form[key] = newUser[key];
      }
    });
  } else {
    resetForm();
  }
}, { immediate: true });

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    resetForm();
  }
});
</script>

<style scoped>
.modal-card {
  width: 80%;
  max-width: 600px;
}
</style>
