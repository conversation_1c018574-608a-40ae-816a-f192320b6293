// Order and Payment Status Constants
// These constants should match the backend enums exactly

// Order Status Enum (matching backend OrderStatus enum)
export const ORDER_STATUS = {
  PROCESSING: 0,  // OrderStatus.Processing = 0
  PENDING: 1,     // OrderStatus.Pending = 1
  SHIPPED: 2,     // OrderStatus.Shipped = 2
  DELIVERED: 3,   // OrderStatus.Delivered = 3
  CANCELLED: 4    // OrderStatus.Cancelled = 4
};

// Payment Status Enum (matching backend PaymentStatus enum)
export const PAYMENT_STATUS = {
  PENDING: 0,     // PaymentStatus.Pending = 0
  COMPLETED: 1,   // PaymentStatus.Completed = 1
  REFUNDED: 2,    // PaymentStatus.Refunded = 2
  FAILED: 3       // PaymentStatus.Failed = 3
};

// Currency Enum (matching backend Currency enum)
export const CURRENCY = {
  UAH: 0,         // Currency.UAH = 0
  USD: 1,         // Currency.USD = 1
  EUR: 2          // Currency.EUR = 2
};

// Order Status Options for UI components
export const ORDER_STATUS_OPTIONS = [
  { value: ORDER_STATUS.PROCESSING, label: 'Processing' },
  { value: ORDER_STATUS.PENDING, label: 'Pending' },
  { value: ORDER_STATUS.SHIPPED, label: 'Shipped' },
  { value: ORDER_STATUS.DELIVERED, label: 'Delivered' },
  { value: ORDER_STATUS.CANCELLED, label: 'Cancelled' }
];

// Payment Status Options for UI components
export const PAYMENT_STATUS_OPTIONS = [
  { value: PAYMENT_STATUS.PENDING, label: 'Pending' },
  { value: PAYMENT_STATUS.COMPLETED, label: 'Completed' },
  { value: PAYMENT_STATUS.REFUNDED, label: 'Refunded' },
  { value: PAYMENT_STATUS.FAILED, label: 'Failed' }
];

// Status Text Mappings
export const ORDER_STATUS_TEXT = {
  [ORDER_STATUS.PROCESSING]: 'Processing',
  [ORDER_STATUS.PENDING]: 'Pending',
  [ORDER_STATUS.SHIPPED]: 'Shipped',
  [ORDER_STATUS.DELIVERED]: 'Delivered',
  [ORDER_STATUS.CANCELLED]: 'Cancelled',
  // String fallbacks for backward compatibility
  'Processing': 'Processing',
  'Pending': 'Pending',
  'Shipped': 'Shipped',
  'Delivered': 'Delivered',
  'Cancelled': 'Cancelled'
};

export const PAYMENT_STATUS_TEXT = {
  [PAYMENT_STATUS.PENDING]: 'Pending',
  [PAYMENT_STATUS.COMPLETED]: 'Completed',
  [PAYMENT_STATUS.REFUNDED]: 'Refunded',
  [PAYMENT_STATUS.FAILED]: 'Failed',
  // String fallbacks for backward compatibility
  'Pending': 'Pending',
  'Completed': 'Completed',
  'Refunded': 'Refunded',
  'Failed': 'Failed'
};

// CSS Class Mappings for status display
export const ORDER_STATUS_CLASSES = {
  [ORDER_STATUS.PROCESSING]: 'is-info',
  [ORDER_STATUS.PENDING]: 'is-warning',
  [ORDER_STATUS.SHIPPED]: 'is-primary',
  [ORDER_STATUS.DELIVERED]: 'is-success',
  [ORDER_STATUS.CANCELLED]: 'is-danger',
  // String fallbacks
  'Processing': 'is-info',
  'Pending': 'is-warning',
  'Shipped': 'is-primary',
  'Delivered': 'is-success',
  'Cancelled': 'is-danger'
};

export const PAYMENT_STATUS_CLASSES = {
  [PAYMENT_STATUS.PENDING]: 'is-warning',
  [PAYMENT_STATUS.COMPLETED]: 'is-success',
  [PAYMENT_STATUS.REFUNDED]: 'is-info',
  [PAYMENT_STATUS.FAILED]: 'is-danger',
  // String fallbacks
  'Pending': 'is-warning',
  'Completed': 'is-success',
  'Refunded': 'is-info',
  'Failed': 'is-danger'
};

// Currency mappings
export const CURRENCY_TEXT = {
  [CURRENCY.UAH]: 'UAH',
  [CURRENCY.USD]: 'USD',
  [CURRENCY.EUR]: 'EUR',
  // String fallbacks
  'UAH': 'UAH',
  'USD': 'USD',
  'EUR': 'EUR'
};

export const CURRENCY_LOCALE = {
  [CURRENCY.UAH]: { locale: 'uk-UA', currency: 'UAH' },
  [CURRENCY.USD]: { locale: 'en-US', currency: 'USD' },
  [CURRENCY.EUR]: { locale: 'en-EU', currency: 'EUR' },
  // String fallbacks
  'UAH': { locale: 'uk-UA', currency: 'UAH' },
  'USD': { locale: 'en-US', currency: 'USD' },
  'EUR': { locale: 'en-EU', currency: 'EUR' }
};

// Backend API transformation maps
export const ORDER_STATUS_TO_API = {
  [ORDER_STATUS.PROCESSING]: 'Processing',
  [ORDER_STATUS.PENDING]: 'Pending',
  [ORDER_STATUS.SHIPPED]: 'Shipped',
  [ORDER_STATUS.DELIVERED]: 'Delivered',
  [ORDER_STATUS.CANCELLED]: 'Cancelled'
};

export const PAYMENT_STATUS_TO_API = {
  [PAYMENT_STATUS.PENDING]: 'Pending',
  [PAYMENT_STATUS.COMPLETED]: 'Completed',
  [PAYMENT_STATUS.REFUNDED]: 'Refunded',
  [PAYMENT_STATUS.FAILED]: 'Failed'
};

// Utility functions
export const getOrderStatusText = (status) => {
  return ORDER_STATUS_TEXT[status] || status || 'Unknown';
};

export const getPaymentStatusText = (status) => {
  return PAYMENT_STATUS_TEXT[status] || status || 'Unknown';
};

export const getOrderStatusClass = (status) => {
  return ORDER_STATUS_CLASSES[status] || 'is-light';
};

export const getPaymentStatusClass = (status) => {
  return PAYMENT_STATUS_CLASSES[status] || 'is-light';
};

export const getCurrencyText = (currency) => {
  return CURRENCY_TEXT[currency] || currency || 'UAH';
};

export const getCurrencyLocale = (currency) => {
  return CURRENCY_LOCALE[currency] || CURRENCY_LOCALE[CURRENCY.UAH];
};

export const transformOrderStatusForAPI = (status) => {
  return ORDER_STATUS_TO_API[status] || status;
};

export const transformPaymentStatusForAPI = (status) => {
  return PAYMENT_STATUS_TO_API[status] || status;
};

// Enhanced type conversion functions
export const parseOrderStatus = (status) => {
  // Handle null/undefined
  if (status === null || status === undefined) {
    return ORDER_STATUS.PENDING;
  }

  // Handle numeric values
  if (typeof status === 'number') {
    return Object.values(ORDER_STATUS).includes(status) ? status : ORDER_STATUS.PENDING;
  }

  // Handle string values
  if (typeof status === 'string') {
    const upperStatus = status.toUpperCase();
    switch (upperStatus) {
      case 'PROCESSING': return ORDER_STATUS.PROCESSING;
      case 'PENDING': return ORDER_STATUS.PENDING;
      case 'SHIPPED': return ORDER_STATUS.SHIPPED;
      case 'DELIVERED': return ORDER_STATUS.DELIVERED;
      case 'CANCELLED': return ORDER_STATUS.CANCELLED;
      default: return ORDER_STATUS.PENDING;
    }
  }

  return ORDER_STATUS.PENDING;
};

export const parsePaymentStatus = (status) => {
  // Handle null/undefined
  if (status === null || status === undefined) {
    return PAYMENT_STATUS.PENDING;
  }

  // Handle numeric values
  if (typeof status === 'number') {
    return Object.values(PAYMENT_STATUS).includes(status) ? status : PAYMENT_STATUS.PENDING;
  }

  // Handle string values
  if (typeof status === 'string') {
    const upperStatus = status.toUpperCase();
    switch (upperStatus) {
      case 'PENDING': return PAYMENT_STATUS.PENDING;
      case 'COMPLETED': return PAYMENT_STATUS.COMPLETED;
      case 'REFUNDED': return PAYMENT_STATUS.REFUNDED;
      case 'FAILED': return PAYMENT_STATUS.FAILED;
      default: return PAYMENT_STATUS.PENDING;
    }
  }

  return PAYMENT_STATUS.PENDING;
};

export const formatOrderStatus = (status) => {
  const parsedStatus = parseOrderStatus(status);
  return getOrderStatusText(parsedStatus);
};

export const formatPaymentStatus = (status) => {
  const parsedStatus = parsePaymentStatus(status);
  return getPaymentStatusText(parsedStatus);
};

// Validation functions
export const isValidOrderStatus = (status) => {
  return Object.values(ORDER_STATUS).includes(parseOrderStatus(status));
};

export const isValidPaymentStatus = (status) => {
  return Object.values(PAYMENT_STATUS).includes(parsePaymentStatus(status));
};

// Status transition validation
export const canTransitionOrderStatus = (fromStatus, toStatus) => {
  const from = parseOrderStatus(fromStatus);
  const to = parseOrderStatus(toStatus);

  // Define allowed transitions
  const allowedTransitions = {
    [ORDER_STATUS.PENDING]: [ORDER_STATUS.PROCESSING, ORDER_STATUS.CANCELLED],
    [ORDER_STATUS.PROCESSING]: [ORDER_STATUS.SHIPPED, ORDER_STATUS.CANCELLED],
    [ORDER_STATUS.SHIPPED]: [ORDER_STATUS.DELIVERED],
    [ORDER_STATUS.DELIVERED]: [], // Final state
    [ORDER_STATUS.CANCELLED]: [] // Final state
  };

  return allowedTransitions[from]?.includes(to) || false;
};

export const canTransitionPaymentStatus = (fromStatus, toStatus) => {
  const from = parsePaymentStatus(fromStatus);
  const to = parsePaymentStatus(toStatus);

  // Define allowed transitions
  const allowedTransitions = {
    [PAYMENT_STATUS.PENDING]: [PAYMENT_STATUS.COMPLETED, PAYMENT_STATUS.FAILED],
    [PAYMENT_STATUS.COMPLETED]: [PAYMENT_STATUS.REFUNDED],
    [PAYMENT_STATUS.REFUNDED]: [], // Final state
    [PAYMENT_STATUS.FAILED]: [PAYMENT_STATUS.PENDING] // Can retry
  };

  return allowedTransitions[from]?.includes(to) || false;
};
