﻿﻿using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.CartItem;

public class UpdateCartItemCommandHandler : IRequestHandler<UpdateCartItemCommand, bool>
{
    private readonly ICartItemRepository _cartItemRepository;
    private readonly ICartRepository _cartRepository;

    public UpdateCartItemCommandHandler(
        ICartItemRepository cartItemRepository,
        ICartRepository cartRepository)
    {
        _cartItemRepository = cartItemRepository;
        _cartRepository = cartRepository;
    }

    public async Task<bool> Handle(UpdateCartItemCommand request, CancellationToken cancellationToken)
    {
        var cartItem = await _cartItemRepository.GetByIdAsync(request.Id, cancellationToken);
        
        if (cartItem == null)
        {
            return false;
        }

        cartItem.Update(request.Quantity);
        await _cartItemRepository.UpdateAsync(cartItem, cancellationToken);
        
        // Оновлюємо дату оновлення кошика
        var cart = await _cartRepository.GetByIdAsync(cartItem.CartId, cancellationToken);
        if (cart != null)
        {
            cart.Update();
            await _cartRepository.UpdateAsync(cart, cancellationToken);
        }
        
        return true;
    }
}
