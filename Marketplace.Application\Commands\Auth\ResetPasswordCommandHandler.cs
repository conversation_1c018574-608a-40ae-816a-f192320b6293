﻿using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using Marketplace.Infrastructure.Services.Auth;
using MediatR;

namespace Marketplace.Application.Commands.Auth;

public class ResetPasswordCommandHandler : IRequestHandler<ResetPasswordCommand>
{
    private readonly IUserRepository _userRepository;
    private readonly IPasswordHasher _passwordHasher;

    public ResetPasswordCommandHandler(IUserRepository userRepository, IPasswordHasher passwordHasher)
    {
        _userRepository = userRepository;
        _passwordHasher = passwordHasher;
    }

    public async Task Handle(ResetPasswordCommand request, CancellationToken cancellationToken)
    {
        var user = await _userRepository.GetByIdAsync(request.Id, cancellationToken)
        ?? throw new KeyNotFoundException("Користувача не знайдено!");

        if (user.PasswordResetToken != request.Token)
            throw new InvalidOperationException("Неправильний токен оновлення паролю.");

        // Hash the new password
        string hashedPassword = _passwordHasher.HashPassword(request.NewPassword);
        user.Password = new Password(hashedPassword);
        user.PasswordResetToken = null;
        await _userRepository.UpdateAsync(user, cancellationToken);
    }
}