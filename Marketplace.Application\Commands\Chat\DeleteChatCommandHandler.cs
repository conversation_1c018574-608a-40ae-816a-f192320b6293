﻿using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.Chat;

public class DeleteChatCommandHandler : IRequestHandler<DeleteChatCommand, bool>
{
    private readonly IChatRepository _repository;

    public DeleteChatCommandHandler(IChatRepository repository)
    {
        _repository = repository;
    }

    public async Task<bool> Handle(DeleteChatCommand request, CancellationToken cancellationToken)
    {
        var item = await _repository.GetByIdAsync(request.Id, cancellationToken);
        if (item == null)
            return false;

        await _repository.DeleteAsync(item.Id, cancellationToken);
        return true;
    }
}


