using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Marketplace.Infrastructure.Persistence;
using Marketplace.Application.Queries.Reports;

namespace Marketplace.Application.Handlers.Reports
{
    /// <summary>
    /// Base class for all report handlers to avoid code duplication
    /// Provides common functionality for data access, error handling, and metric calculations
    /// </summary>
    public abstract class BaseReportHandler<TQuery, TResult> : IRequestHandler<TQuery, TResult>
        where TQuery : BaseReportQuery, IRequest<TResult>
        where TResult : class
    {
        protected readonly MarketplaceDbContext _context;
        protected readonly ILogger _logger;

        protected BaseReportHandler(MarketplaceDbContext context, ILogger logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<TResult> Handle(TQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing {ReportType} report request for period {StartDate} to {EndDate}",
                    typeof(TQuery).Name, request.StartDate, request.EndDate);

                var result = await GenerateReportAsync(request, cancellationToken);
                
                _logger.LogInformation("Successfully generated {ReportType} report", typeof(TQuery).Name);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating {ReportType} report", typeof(TQuery).Name);
                return CreateErrorResult(ex);
            }
        }

        /// <summary>
        /// Abstract method to be implemented by specific report handlers
        /// </summary>
        protected abstract Task<TResult> GenerateReportAsync(TQuery request, CancellationToken cancellationToken);

        /// <summary>
        /// Creates an error result when report generation fails
        /// </summary>
        protected abstract TResult CreateErrorResult(Exception ex);

        /// <summary>
        /// Converts DateTime to UTC for database queries
        /// </summary>
        protected DateTime ToUtc(DateTime dateTime)
        {
            return DateTime.SpecifyKind(dateTime, DateTimeKind.Utc);
        }

        /// <summary>
        /// Gets orders within the specified date range
        /// </summary>
        protected async Task<List<Domain.Entities.Order>> GetOrdersInRangeAsync(
            DateTime startDate, 
            DateTime endDate, 
            CancellationToken cancellationToken)
        {
            var startDateUtc = ToUtc(startDate);
            var endDateUtc = ToUtc(endDate);

            return await _context.Orders
                .Where(o => o.CreatedAt >= startDateUtc && o.CreatedAt <= endDateUtc)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Gets order items for the specified orders
        /// </summary>
        protected async Task<List<Domain.Entities.OrderItem>> GetOrderItemsAsync(
            List<Guid> orderIds, 
            CancellationToken cancellationToken)
        {
            return await _context.OrderItems
                .Include(oi => oi.Product)
                .Where(oi => orderIds.Contains(oi.OrderId))
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Creates a standard metric item
        /// </summary>
        protected MetricItem CreateMetric(string key, string label, object value, string type = "number", string icon = "fas fa-chart-line")
        {
            return new MetricItem
            {
                Key = key,
                Label = label,
                Value = value,
                Type = type,
                Icon = icon,
                PreviousValue = null,
                ChangePercentage = 0m
            };
        }

        /// <summary>
        /// Creates a standard table column
        /// </summary>
        protected TableColumn CreateColumn(string key, string label, string type = "text", bool sortable = true)
        {
            return new TableColumn
            {
                Key = key,
                Label = label,
                Type = type,
                Sortable = sortable
            };
        }

        /// <summary>
        /// Creates a standard report summary
        /// </summary>
        protected ReportSummary CreateSummary(string title, string description, Dictionary<string, object>? metadata = null)
        {
            return new ReportSummary
            {
                Title = title,
                Description = description,
                GeneratedAt = DateTime.UtcNow,
                Metadata = metadata ?? new Dictionary<string, object>()
            };
        }

        /// <summary>
        /// Calculates percentage change between two values
        /// </summary>
        protected decimal CalculatePercentageChange(decimal current, decimal previous)
        {
            if (previous == 0) return current > 0 ? 100 : 0;
            return Math.Round(((current - previous) / previous) * 100, 2);
        }

        /// <summary>
        /// Safely calculates average to avoid division by zero
        /// </summary>
        protected decimal SafeAverage(decimal total, int count)
        {
            return count > 0 ? Math.Round(total / count, 2) : 0;
        }

        /// <summary>
        /// Formats currency values consistently
        /// </summary>
        protected string FormatCurrency(decimal value)
        {
            return value.ToString("C2");
        }

        /// <summary>
        /// Formats percentage values consistently
        /// </summary>
        protected string FormatPercentage(decimal value)
        {
            return $"{value:F1}%";
        }

        /// <summary>
        /// Creates pagination info for tables
        /// </summary>
        protected (int skip, int take) GetPagination(TQuery request)
        {
            var skip = (request.Page - 1) * request.PageSize;
            var take = request.PageSize;
            return (skip, take);
        }

        /// <summary>
        /// Applies sorting to queryable based on request parameters
        /// </summary>
        protected IQueryable<T> ApplySorting<T>(IQueryable<T> query, string? sortBy, string? sortDirection)
        {
            if (string.IsNullOrEmpty(sortBy))
                return query;

            // This is a simplified version - in real implementation you'd use Expression trees
            // or a more sophisticated sorting mechanism
            return query;
        }
    }

    /// <summary>
    /// Specialized base class for report handlers that return ReportResult
    /// </summary>
    public abstract class BaseReportResultHandler<TQuery> : BaseReportHandler<TQuery, ReportResult>
        where TQuery : BaseReportQuery, IRequest<ReportResult>
    {
        protected BaseReportResultHandler(MarketplaceDbContext context, ILogger logger) 
            : base(context, logger)
        {
        }

        protected override ReportResult CreateErrorResult(Exception ex)
        {
            return new ReportResult
            {
                Summary = new ReportSummary
                {
                    Title = "Error",
                    Description = $"Failed to generate report: {ex.Message}",
                    GeneratedAt = DateTime.UtcNow
                }
            };
        }
    }
}
