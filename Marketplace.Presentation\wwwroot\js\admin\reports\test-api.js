// Test script for Reports API functionality
import unifiedReportsService from './services/unifiedReportsService.js'

class ReportsAPITester {
  constructor() {
    this.results = []
    this.startTime = Date.now()
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString()
    const result = { timestamp, message, type }
    this.results.push(result)
    
    const color = {
      'info': 'color: blue',
      'success': 'color: green', 
      'error': 'color: red',
      'warning': 'color: orange'
    }[type] || 'color: black'
    
    console.log(`%c[${timestamp}] ${message}`, color)
  }

  async testHealthCheck() {
    this.log('Testing health check...', 'info')
    try {
      const result = await unifiedReportsService.healthCheck()
      this.log(`Health check result: ${JSON.stringify(result)}`, 'success')
      return true
    } catch (error) {
      this.log(`Health check failed: ${error.message}`, 'error')
      return false
    }
  }

  async testReportFetch(reportType) {
    this.log(`Testing ${reportType} report fetch...`, 'info')
    try {
      const filters = {
        startDate: '2024-01-01',
        endDate: '2024-01-31',
        limit: 10
      }
      
      const result = await unifiedReportsService.getReport(reportType, filters)
      
      if (result && typeof result === 'object') {
        this.log(`${reportType} report fetched successfully`, 'success')
        this.log(`Report structure: ${Object.keys(result).join(', ')}`, 'info')
        
        // Validate expected structure
        if (result.type === reportType) {
          this.log(`Report type validation passed`, 'success')
        } else {
          this.log(`Report type validation failed: expected ${reportType}, got ${result.type}`, 'warning')
        }
        
        if (result.metrics && result.metrics.items) {
          this.log(`Metrics found: ${result.metrics.items.length} items`, 'success')
        }
        
        if (result.charts) {
          this.log(`Charts found: ${Object.keys(result.charts).join(', ')}`, 'success')
        }
        
        if (result.table && result.table.data) {
          this.log(`Table data found: ${result.table.data.length} rows`, 'success')
        }
        
        return true
      } else {
        this.log(`Invalid response format for ${reportType}`, 'error')
        return false
      }
    } catch (error) {
      this.log(`${reportType} report fetch failed: ${error.message}`, 'error')
      return false
    }
  }

  async testExportFunctionality(reportType, format) {
    this.log(`Testing ${reportType} export to ${format}...`, 'info')
    try {
      const filters = {
        startDate: '2024-01-01',
        endDate: '2024-01-31'
      }
      
      const result = await unifiedReportsService.exportReport(reportType, format, filters)
      
      if (result) {
        this.log(`${reportType} export to ${format} successful`, 'success')
        return true
      } else {
        this.log(`${reportType} export to ${format} returned empty result`, 'warning')
        return false
      }
    } catch (error) {
      this.log(`${reportType} export to ${format} failed: ${error.message}`, 'error')
      return false
    }
  }

  async testDashboardSummary() {
    this.log('Testing dashboard summary...', 'info')
    try {
      const filters = {
        startDate: '2024-01-01',
        endDate: '2024-01-31'
      }
      
      const result = await unifiedReportsService.getDashboardSummary(filters)
      
      if (result && typeof result === 'object') {
        this.log('Dashboard summary fetched successfully', 'success')
        
        const reportTypes = ['financial', 'sales', 'products', 'users', 'orders']
        reportTypes.forEach(type => {
          if (result[type]) {
            this.log(`${type} data available in dashboard`, 'success')
          } else {
            this.log(`${type} data missing in dashboard`, 'warning')
          }
        })
        
        return true
      } else {
        this.log('Invalid dashboard summary format', 'error')
        return false
      }
    } catch (error) {
      this.log(`Dashboard summary failed: ${error.message}`, 'error')
      return false
    }
  }

  async testCacheFunctionality() {
    this.log('Testing cache functionality...', 'info')
    try {
      // Clear cache first
      unifiedReportsService.clearCache()
      this.log('Cache cleared', 'info')
      
      // First request (should hit API)
      const start1 = Date.now()
      await unifiedReportsService.getFinancialReport()
      const time1 = Date.now() - start1
      this.log(`First request took ${time1}ms`, 'info')
      
      // Second request (should hit cache)
      const start2 = Date.now()
      await unifiedReportsService.getFinancialReport()
      const time2 = Date.now() - start2
      this.log(`Second request took ${time2}ms`, 'info')
      
      if (time2 < time1) {
        this.log('Cache functionality working correctly', 'success')
        return true
      } else {
        this.log('Cache may not be working as expected', 'warning')
        return false
      }
    } catch (error) {
      this.log(`Cache test failed: ${error.message}`, 'error')
      return false
    }
  }

  async runAllTests() {
    this.log('Starting comprehensive API tests...', 'info')
    this.log('='.repeat(50), 'info')
    
    const tests = [
      { name: 'Health Check', test: () => this.testHealthCheck() },
      { name: 'Financial Report', test: () => this.testReportFetch('financial') },
      { name: 'Sales Report', test: () => this.testReportFetch('sales') },
      { name: 'Products Report', test: () => this.testReportFetch('products') },
      { name: 'Users Report', test: () => this.testReportFetch('users') },
      { name: 'Orders Report', test: () => this.testReportFetch('orders') },
      { name: 'Dashboard Summary', test: () => this.testDashboardSummary() },
      { name: 'Cache Functionality', test: () => this.testCacheFunctionality() },
      { name: 'CSV Export', test: () => this.testExportFunctionality('sales', 'csv') },
      { name: 'PDF Export', test: () => this.testExportFunctionality('financial', 'pdf') }
    ]
    
    let passed = 0
    let failed = 0
    
    for (const { name, test } of tests) {
      this.log(`\n--- Running ${name} Test ---`, 'info')
      try {
        const result = await test()
        if (result) {
          passed++
          this.log(`✓ ${name} test passed`, 'success')
        } else {
          failed++
          this.log(`✗ ${name} test failed`, 'error')
        }
      } catch (error) {
        failed++
        this.log(`✗ ${name} test error: ${error.message}`, 'error')
      }
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    const totalTime = Date.now() - this.startTime
    this.log('\n' + '='.repeat(50), 'info')
    this.log(`Test Summary: ${passed} passed, ${failed} failed`, passed > failed ? 'success' : 'error')
    this.log(`Total execution time: ${totalTime}ms`, 'info')
    this.log('='.repeat(50), 'info')
    
    return { passed, failed, totalTime, results: this.results }
  }
}

// Export for use in browser console or other scripts
window.ReportsAPITester = ReportsAPITester

// Auto-run tests if this script is loaded directly
if (typeof window !== 'undefined' && window.location.pathname.includes('reports')) {
  console.log('Reports API Tester loaded. Run new ReportsAPITester().runAllTests() to start testing.')
}

export default ReportsAPITester
