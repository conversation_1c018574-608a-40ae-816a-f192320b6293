﻿﻿using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.CartItem;

public class DeleteCartItemCommandHandler : IRequestHandler<DeleteCartItemCommand, bool>
{
    private readonly ICartItemRepository _cartItemRepository;
    private readonly ICartRepository _cartRepository;

    public DeleteCartItemCommandHandler(
        ICartItemRepository cartItemRepository,
        ICartRepository cartRepository)
    {
        _cartItemRepository = cartItemRepository;
        _cartRepository = cartRepository;
    }

    public async Task<bool> Handle(DeleteCartItemCommand request, CancellationToken cancellationToken)
    {
        var cartItem = await _cartItemRepository.GetByIdAsync(request.Id, cancellationToken);
        
        if (cartItem == null)
        {
            return false;
        }

        // Зберігаємо CartId перед видаленням
        var cartId = cartItem.CartId;
        
        await _cartItemRepository.DeleteAsync(request.Id, cancellationToken);
        
        // Оновлюємо дату оновлення кошика
        var cart = await _cartRepository.GetByIdAsync(cartId, cancellationToken);
        if (cart != null)
        {
            cart.Update();
            await _cartRepository.UpdateAsync(cart, cancellationToken);
        }
        
        return true;
    }
}
