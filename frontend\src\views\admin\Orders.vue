<template>
  <div class="admin-page-container">
    <div class="page-header">
      <div class="level-left">
        <div class="level-item">
          <h1 class="page-title">Orders</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <button class="button is-primary" @click="exportOrders">
            <span class="icon"><i class="fas fa-file-export"></i></span>
            <span>Export</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Search and Filter -->
    <div class="box">
      <div class="field has-addons">
        <div class="control is-expanded">
          <input class="input" type="text" placeholder="Search orders..." v-model="searchQuery">
        </div>
        <div class="control">
          <button class="button is-info" @click="searchOrders">
            <span class="icon"><i class="fas fa-search"></i></span>
          </button>
        </div>
      </div>

      <div class="columns">
        <div class="column">
          <div class="field">
            <label class="label">Status</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="filters.status">
                  <option value="">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="processing">Processing</option>
                  <option value="shipped">Shipped</option>
                  <option value="delivered">Delivered</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <div class="column">
          <div class="field">
            <label class="label">Date Range</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="filters.dateRange">
                  <option value="">All Time</option>
                  <option value="today">Today</option>
                  <option value="yesterday">Yesterday</option>
                  <option value="last7days">Last 7 Days</option>
                  <option value="last30days">Last 30 Days</option>
                  <option value="thisMonth">This Month</option>
                  <option value="lastMonth">Last Month</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <div class="column">
          <div class="field">
            <label class="label">Sort By</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="filters.sortBy">
                  <option value="createdAt">Date</option>
                  <option value="total">Total</option>
                  <option value="status">Status</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Orders Table -->
    <div class="box">
      <div class="table-container">
        <table class="table is-fullwidth is-striped">
          <thead>
            <tr>
              <th>Order ID</th>
              <th>Customer</th>
              <th>Date</th>
              <th>Total</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="order in orders" :key="order.id">
              <td>{{ order.id }}</td>
              <td>{{ order.customer }}</td>
              <td>{{ formatDate(order.createdAt) }}</td>
              <td>{{ formatPrice(order.total) }}</td>
              <td>
                <span class="tag" :class="getStatusTagClass(order.status)">
                  {{ order.status }}
                </span>
              </td>
              <td>
                <div class="buttons are-small">
                  <button class="button is-info" @click="viewOrder(order)">
                    <span class="icon"><i class="fas fa-eye"></i></span>
                  </button>
                  <button class="button is-success" @click="updateStatus(order)">
                    <span class="icon"><i class="fas fa-edit"></i></span>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <nav class="pagination is-centered" role="navigation" aria-label="pagination">
        <a class="pagination-previous" :disabled="currentPage === 1" @click="changePage(currentPage - 1)">Previous</a>
        <a class="pagination-next" :disabled="currentPage === totalPages" @click="changePage(currentPage + 1)">Next</a>
        <ul class="pagination-list">
          <li v-for="page in paginationItems" :key="page.value">
            <a
              v-if="page.type === 'page'"
              class="pagination-link"
              :class="{ 'is-current': page.value === currentPage }"
              @click="changePage(page.value)"
            >
              {{ page.value }}
            </a>
            <span v-else class="pagination-ellipsis">&hellip;</span>
          </li>
        </ul>
      </nav>
    </div>

    <!-- View Order Modal -->
    <div class="modal" :class="{ 'is-active': showViewModal }">
      <div class="modal-background" @click="showViewModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Order Details - #{{ selectedOrder?.id }}</p>
          <button class="delete" aria-label="close" @click="showViewModal = false"></button>
        </header>
        <section class="modal-card-body" v-if="selectedOrder">
          <div class="columns">
            <div class="column">
              <h3 class="title is-5">Customer Information</h3>
              <p><strong>Name:</strong> {{ selectedOrder.customer }}</p>
              <p><strong>Email:</strong> {{ selectedOrder.email }}</p>
              <p><strong>Phone:</strong> {{ selectedOrder.phone }}</p>
            </div>
            <div class="column">
              <h3 class="title is-5">Shipping Address</h3>
              <p>{{ selectedOrder.shippingAddress.street }}</p>
              <p>{{ selectedOrder.shippingAddress.city }}, {{ selectedOrder.shippingAddress.region }} {{ selectedOrder.shippingAddress.postalCode }}</p>
            </div>
          </div>

          <hr>

          <h3 class="title is-5">Order Items</h3>
          <table class="table is-fullwidth">
            <thead>
              <tr>
                <th>Product</th>
                <th>Price</th>
                <th>Quantity</th>
                <th>Subtotal</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in selectedOrder.items" :key="index">
                <td>{{ item.product }}</td>
                <td>{{ formatPrice(item.price) }}</td>
                <td>{{ item.quantity }}</td>
                <td>{{ formatPrice(item.price * item.quantity) }}</td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <th colspan="3" class="has-text-right">Subtotal:</th>
                <td>{{ formatPrice(selectedOrder.subtotal) }}</td>
              </tr>
              <tr>
                <th colspan="3" class="has-text-right">Shipping:</th>
                <td>{{ formatPrice(selectedOrder.shipping) }}</td>
              </tr>
              <tr>
                <th colspan="3" class="has-text-right">Tax:</th>
                <td>{{ formatPrice(selectedOrder.tax) }}</td>
              </tr>
              <tr>
                <th colspan="3" class="has-text-right">Total:</th>
                <td>{{ formatPrice(selectedOrder.total) }}</td>
              </tr>
            </tfoot>
          </table>

          <hr>

          <div class="columns">
            <div class="column">
              <h3 class="title is-5">Payment Information</h3>
              <p><strong>Method:</strong> {{ selectedOrder.paymentMethod }}</p>
              <p><strong>Status:</strong> {{ selectedOrder.paymentStatus }}</p>
              <p><strong>Transaction ID:</strong> {{ selectedOrder.transactionId }}</p>
            </div>
            <div class="column">
              <h3 class="title is-5">Order Status</h3>
              <div class="field">
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="selectedOrder.status">
                      <option value="pending">Pending</option>
                      <option value="processing">Processing</option>
                      <option value="shipped">Shipped</option>
                      <option value="delivered">Delivered</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>
                </div>
              </div>
              <button class="button is-success is-fullwidth" @click="saveOrderStatus">
                Update Status
              </button>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button class="button" @click="showViewModal = false">Close</button>
        </footer>
      </div>
    </div>

    <!-- Update Status Modal -->
    <div class="modal" :class="{ 'is-active': showStatusModal }">
      <div class="modal-background" @click="showStatusModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Update Order Status - #{{ selectedOrder?.id }}</p>
          <button class="delete" aria-label="close" @click="showStatusModal = false"></button>
        </header>
        <section class="modal-card-body" v-if="selectedOrder">
          <div class="field">
            <label class="label">Status</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="selectedOrder.status">
                  <option value="pending">Pending</option>
                  <option value="processing">Processing</option>
                  <option value="shipped">Shipped</option>
                  <option value="delivered">Delivered</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
            </div>
          </div>

          <div class="field" v-if="selectedOrder.status === 'shipped'">
            <label class="label">Tracking Number</label>
            <div class="control">
              <input class="input" type="text" placeholder="Enter tracking number" v-model="selectedOrder.trackingNumber">
            </div>
          </div>

          <div class="field">
            <label class="label">Notes</label>
            <div class="control">
              <textarea class="textarea" placeholder="Add notes about this status change" v-model="statusNotes"></textarea>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button class="button is-success" @click="saveOrderStatus">Save Changes</button>
          <button class="button" @click="showStatusModal = false">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// Mock data - would be replaced with API calls
const orders = ref([
  {
    id: 'ORD-12345',
    customer: 'John Doe',
    email: '<EMAIL>',
    phone: '(*************',
    createdAt: new Date(2023, 10, 15),
    total: 129.99,
    subtotal: 119.99,
    shipping: 5.00,
    tax: 5.00,
    status: 'delivered',
    paymentMethod: 'Credit Card',
    paymentStatus: 'Paid',
    transactionId: 'TXN-98765',
    trackingNumber: 'TRK-54321',
    shippingAddress: {
      street: '123 Main St',
      city: 'Anytown',
      region: 'CA',
      postalCode: '12345'
    },
    items: [
      { product: 'Smartphone X', price: 119.99, quantity: 1 }
    ]
  },
  {
    id: 'ORD-12346',
    customer: 'Jane Smith',
    email: '<EMAIL>',
    phone: '(*************',
    createdAt: new Date(2023, 10, 14),
    total: 1549.98,
    subtotal: 1499.98,
    shipping: 10.00,
    tax: 40.00,
    status: 'shipped',
    paymentMethod: 'PayPal',
    paymentStatus: 'Paid',
    transactionId: 'TXN-87654',
    trackingNumber: 'TRK-65432',
    shippingAddress: {
      street: '456 Oak Ave',
      city: 'Somewhere',
      region: 'NY',
      postalCode: '67890'
    },
    items: [
      { product: 'Laptop Pro', price: 1499.99, quantity: 1 }
    ]
  },
  {
    id: 'ORD-12347',
    customer: 'Mike Johnson',
    email: '<EMAIL>',
    phone: '(*************',
    createdAt: new Date(2023, 10, 13),
    total: 229.97,
    subtotal: 209.97,
    shipping: 10.00,
    tax: 10.00,
    status: 'processing',
    paymentMethod: 'Credit Card',
    paymentStatus: 'Paid',
    transactionId: 'TXN-76543',
    trackingNumber: '',
    shippingAddress: {
      street: '789 Pine St',
      city: 'Elsewhere',
      region: 'TX',
      postalCode: '54321'
    },
    items: [
      { product: 'Wireless Headphones', price: 199.99, quantity: 1 },
      { product: 'Headphone Case', price: 9.99, quantity: 1 }
    ]
  },
  {
    id: 'ORD-12348',
    customer: 'Sarah Williams',
    email: '<EMAIL>',
    phone: '(*************',
    createdAt: new Date(2023, 10, 12),
    total: 329.98,
    subtotal: 299.98,
    shipping: 15.00,
    tax: 15.00,
    status: 'pending',
    paymentMethod: 'Bank Transfer',
    paymentStatus: 'Pending',
    transactionId: '',
    trackingNumber: '',
    shippingAddress: {
      street: '321 Maple Dr',
      city: 'Nowhere',
      region: 'FL',
      postalCode: '98765'
    },
    items: [
      { product: 'Smart Watch', price: 299.99, quantity: 1 }
    ]
  },
  {
    id: 'ORD-12349',
    customer: 'David Brown',
    email: '<EMAIL>',
    phone: '(*************',
    createdAt: new Date(2023, 10, 11),
    total: 159.98,
    subtotal: 139.98,
    shipping: 10.00,
    tax: 10.00,
    status: 'cancelled',
    paymentMethod: 'Credit Card',
    paymentStatus: 'Refunded',
    transactionId: 'TXN-65432',
    trackingNumber: '',
    shippingAddress: {
      street: '654 Cedar Ln',
      city: 'Someplace',
      region: 'WA',
      postalCode: '43210'
    },
    items: [
      { product: 'Bluetooth Speaker', price: 129.99, quantity: 1 },
      { product: 'Speaker Stand', price: 9.99, quantity: 1 }
    ]
  }
]);

// Pagination
const currentPage = ref(1);
const itemsPerPage = ref(10);
const totalItems = ref(50);

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage.value));

const paginationItems = computed(() => {
  const items = [];
  const maxVisiblePages = 5;

  if (totalPages.value <= maxVisiblePages) {
    // Show all pages
    for (let i = 1; i <= totalPages.value; i++) {
      items.push({ type: 'page', value: i });
    }
  } else {
    // Always show first page
    items.push({ type: 'page', value: 1 });

    // Calculate start and end of visible pages
    let startPage = Math.max(2, currentPage.value - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages.value - 1, startPage + maxVisiblePages - 3);

    // Adjust if we're near the beginning
    if (startPage > 2) {
      items.push({ type: 'ellipsis' });
    }

    // Add visible pages
    for (let i = startPage; i <= endPage; i++) {
      items.push({ type: 'page', value: i });
    }

    // Adjust if we're near the end
    if (endPage < totalPages.value - 1) {
      items.push({ type: 'ellipsis' });
    }

    // Always show last page
    items.push({ type: 'page', value: totalPages.value });
  }

  return items;
});

// Search and filters
const searchQuery = ref('');
const filters = ref({
  status: '',
  dateRange: '',
  sortBy: 'CreatedAt'
});

// Modals
const showViewModal = ref(false);
const showStatusModal = ref(false);
const selectedOrder = ref(null);
const statusNotes = ref('');

// Methods
const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

const exportOrders = () => {
  // In a real application, this would generate a CSV or Excel file
  console.log('Exporting orders...');
  alert('Orders exported successfully!');
};

const formatPrice = (price) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(price);
};

const getStatusTagClass = (status) => {
  switch (status) {
    case 'pending':
      return 'is-warning';
    case 'processing':
      return 'is-info';
    case 'shipped':
      return 'is-primary';
    case 'delivered':
      return 'is-success';
    case 'cancelled':
      return 'is-danger';
    default:
      return 'is-light';
  }
};

const searchOrders = () => {
  // Here you would call your API with search parameters
  console.log('Searching orders with:', searchQuery.value, filters.value);
};

const changePage = (page) => {
  currentPage.value = page;
  // Here you would fetch the data for the new page
  console.log('Changed to page:', page);
};

// Initialize data when component is mounted
onMounted(() => {
  // Here you would fetch initial data from your API
  console.log('Orders component mounted');
});

const viewOrder = (order) => {
  selectedOrder.value = { ...order };
  showViewModal.value = true;
};

const updateStatus = (order) => {
  selectedOrder.value = { ...order };
  statusNotes.value = '';
  showStatusModal.value = true;
};

const saveOrderStatus = () => {
  // Here you would call your API to update the order status
  console.log('Saving order status:', selectedOrder.value.status);
  console.log('Status notes:', statusNotes.value);

  // Update the order in the local array
  const index = orders.value.findIndex(o => o.id === selectedOrder.value.id);
  if (index !== -1) {
    orders.value[index].status = selectedOrder.value.status;
    if (selectedOrder.value.trackingNumber) {
      orders.value[index].trackingNumber = selectedOrder.value.trackingNumber;
    }
  }

  // Close modals
  showViewModal.value = false;
  showStatusModal.value = false;
};

const changePage = (page) => {
  currentPage.value = page;
  // Here you would fetch the data for the new page
};

const viewOrder = (order) => {
  selectedOrder.value = { ...order };
  showViewModal.value = true;
};

const updateStatus = (order) => {
  selectedOrder.value = { ...order };
  statusNotes.value = '';
  showStatusModal.value = true;
};

const saveOrderStatus = () => {
  // Here you would call your API to update the order status
  console.log('Updating order status:', selectedOrder.value.status);
  console.log('Status notes:', statusNotes.value);

  // Mock implementation - update in local array
  const index = orders.value.findIndex(o => o.id === selectedOrder.value.id);
  if (index !== -1) {
    orders.value[index].status = selectedOrder.value.status;
    if (selectedOrder.value.trackingNumber) {
      orders.value[index].trackingNumber = selectedOrder.value.trackingNumber;
    }
  }

  showViewModal.value = false;
  showStatusModal.value = false;
};

onMounted(() => {
  // Here you would fetch initial data from your API
  // Example: fetchOrders();
});
</script>

<style scoped>
.level {
  margin-bottom: 1.5rem;
}

.pagination {
  margin-top: 1.5rem;
}
</style>
