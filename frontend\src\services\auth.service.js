import apiService from './api';

const API_URL = '/api/auth/';

class AuthService {
  async login(email, password) {
    try {
      console.log('Attempting login with email:', email);

      const response = await apiService.post(API_URL + 'login', {
        email,
        password
      });

      // Log the entire response for debugging
      console.log('Full login response:', response);
      console.log('Response data:', response.data);

      // Check if we have a response with data
      if (!response || !response.data) {
        console.error('Empty response or no data');
        throw new Error('Empty response from server');
      }

      // Get the data from the response
      const responseData = response.data;

      // Check if the response follows the ApiResponse<T> format
      let token, user;

      if (responseData.success && responseData.data) {
        // ApiResponse<T> format where data contains { token, user }
        token = responseData.data.token;
        user = responseData.data.user;
        console.log('Found token and user in ApiResponse.data format');
      } else {
        // Direct format where response.data contains { token, user }
        token = responseData.token;
        user = responseData.user;
        console.log('Found token and user in direct format');
      }

      // Validate token
      if (!token) {
        console.error('No token in response:', responseData);
        throw new Error('No authentication token received');
      }

      // Validate user data
      if (!user) {
        console.error('No user data in response:', responseData);
        throw new Error('No user data received');
      }

      // Log user data for debugging
      console.log('User data from login:', user);
      console.log('User role:', user.role);

      // Store authentication data
      localStorage.setItem('user', JSON.stringify(user));
      localStorage.setItem('token', token);

      // Return the auth data
      return {
        token: token,
        user: user
      };
    } catch (error) {
      console.error('Login error:', error);
      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
      }

      // Clear any potentially corrupted auth data
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      throw error;
    }
  }

  async googleLogin(idToken) {
    try {
      console.log('Attempting Google login with token');

      // Send the ID token to the backend for verification
      const response = await apiService.post(API_URL + 'google-login', {
        idToken
      });

      // Log the entire response for debugging
      console.log('Full Google login response:', response);
      console.log('Google login response data:', response.data);

      // Check if we have a response with data
      if (!response || !response.data) {
        console.error('Empty response or no data from Google login');
        throw new Error('Empty response from server');
      }

      // Get the data from the response
      const responseData = response.data;

      // Check if the response follows the ApiResponse<T> format
      let token, user;

      if (responseData.success && responseData.data) {
        // ApiResponse<T> format where data contains { token, user }
        token = responseData.data.token;
        user = responseData.data.user;
        console.log('Found token and user in ApiResponse.data format for Google login');
      } else {
        // Direct format where response.data contains { token, user }
        token = responseData.token;
        user = responseData.user;
        console.log('Found token and user in direct format for Google login');
      }

      // Validate token
      if (!token) {
        console.error('No token in Google login response:', responseData);
        throw new Error('No authentication token received');
      }

      // Validate user data
      if (!user) {
        console.error('No user data in Google login response:', responseData);
        throw new Error('No user data received');
      }

      // Log user data for debugging
      console.log('User data from Google login:', user);
      console.log('User role from Google login:', user.role);

      // Store authentication data
      localStorage.setItem('user', JSON.stringify(user));
      localStorage.setItem('token', token);

      // Return the auth data
      return {
        token: token,
        user: user
      };
    } catch (error) {
      console.error('Google login error:', error);
      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
      }

      // Clear any potentially corrupted auth data
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      throw error;
    }
  }

  logout() {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
  }

  async register(user) {
    console.log('Registering user:', user);
    return apiService.post(API_URL + 'register', {
      username: user.username || user.email.split('@')[0], // Generate username from email if not provided
      email: user.email,
      password: user.password
      // Role is now set on the server side
    });
  }
}

export default new AuthService();



