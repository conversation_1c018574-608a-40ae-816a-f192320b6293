<template>
  <div class="report-table">
    <div class="table-header">
      <h3 class="table-title">{{ title }}</h3>
      <div class="table-actions">
        <div class="search-container" v-if="searchable">
          <i class="fas fa-search"></i>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search..."
            class="search-input"
          />
        </div>
        <button
          v-if="exportable"
          @click="exportTable"
          class="export-btn"
          :disabled="loading || !data.length"
        >
          <i class="fas fa-download"></i>
          Export
        </button>
      </div>
    </div>

    <div class="table-container">
      <div v-if="loading" class="table-loading">
        <i class="fas fa-spinner fa-spin"></i>
        <span>Loading data...</span>
      </div>

      <div v-else-if="!data.length" class="table-empty">
        <i class="fas fa-table"></i>
        <span>No data available</span>
      </div>

      <table v-else class="data-table">
        <thead>
          <tr>
            <th
              v-for="column in columns"
              :key="column.key"
              @click="sortBy(column.key)"
              :class="{ sortable: column.sortable !== false, active: sortColumn === column.key }"
            >
              {{ column.label }}
              <i
                v-if="column.sortable !== false"
                :class="getSortIcon(column.key)"
                class="sort-icon"
              ></i>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(row, index) in paginatedData"
            :key="index"
            @click="$emit('row-click', row)"
            :class="{ clickable: $listeners['row-click'] }"
          >
            <td
              v-for="column in columns"
              :key="column.key"
              :class="column.class"
            >
              <span v-if="column.type === 'currency'">
                {{ formatCurrency(row[column.key]) }}
              </span>
              <span v-else-if="column.type === 'percentage'">
                {{ formatPercentage(row[column.key]) }}
              </span>
              <span v-else-if="column.type === 'number'">
                {{ formatNumber(row[column.key]) }}
              </span>
              <span v-else-if="column.type === 'date'">
                {{ formatDate(row[column.key]) }}
              </span>
              <span v-else-if="column.type === 'status'">
                <span :class="['status-badge', getStatusClass(row[column.key])]">
                  {{ row[column.key] }}
                </span>
              </span>
              <span v-else>
                {{ row[column.key] }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div v-if="showPagination && totalPages > 1" class="table-pagination">
      <div class="pagination-info">
        Showing {{ startIndex + 1 }}-{{ endIndex }} of {{ filteredData.length }} entries
      </div>
      <div class="pagination-controls">
        <button
          @click="currentPage = 1"
          :disabled="currentPage === 1"
          class="pagination-btn"
        >
          <i class="fas fa-angle-double-left"></i>
        </button>
        <button
          @click="currentPage--"
          :disabled="currentPage === 1"
          class="pagination-btn"
        >
          <i class="fas fa-angle-left"></i>
        </button>
        
        <span class="pagination-pages">
          <button
            v-for="page in visiblePages"
            :key="page"
            @click="currentPage = page"
            :class="['pagination-btn', { active: currentPage === page }]"
          >
            {{ page }}
          </button>
        </span>
        
        <button
          @click="currentPage++"
          :disabled="currentPage === totalPages"
          class="pagination-btn"
        >
          <i class="fas fa-angle-right"></i>
        </button>
        <button
          @click="currentPage = totalPages"
          :disabled="currentPage === totalPages"
          class="pagination-btn"
        >
          <i class="fas fa-angle-double-right"></i>
        </button>
      </div>
      <div class="pagination-size">
        <select v-model="pageSize" @change="currentPage = 1" class="page-size-select">
          <option value="10">10 per page</option>
          <option value="25">25 per page</option>
          <option value="50">50 per page</option>
          <option value="100">100 per page</option>
        </select>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'ReportTable',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    columns: {
      type: Array,
      required: true
    },
    title: {
      type: String,
      default: 'Data Table'
    },
    loading: {
      type: Boolean,
      default: false
    },
    searchable: {
      type: Boolean,
      default: true
    },
    exportable: {
      type: Boolean,
      default: false
    },
    showPagination: {
      type: Boolean,
      default: true
    },
    defaultPageSize: {
      type: Number,
      default: 25
    }
  },
  emits: ['row-click', 'export'],
  setup(props, { emit }) {
    // Reactive state
    const searchQuery = ref('')
    const sortColumn = ref('')
    const sortDirection = ref('asc')
    const currentPage = ref(1)
    const pageSize = ref(props.defaultPageSize)

    // Computed properties
    const filteredData = computed(() => {
      if (!searchQuery.value) return props.data
      
      const query = searchQuery.value.toLowerCase()
      return props.data.filter(row => {
        return props.columns.some(column => {
          const value = row[column.key]
          return value && value.toString().toLowerCase().includes(query)
        })
      })
    })

    const sortedData = computed(() => {
      if (!sortColumn.value) return filteredData.value
      
      return [...filteredData.value].sort((a, b) => {
        const aVal = a[sortColumn.value]
        const bVal = b[sortColumn.value]
        
        let comparison = 0
        if (aVal > bVal) comparison = 1
        if (aVal < bVal) comparison = -1
        
        return sortDirection.value === 'desc' ? -comparison : comparison
      })
    })

    const totalPages = computed(() => Math.ceil(sortedData.value.length / pageSize.value))
    
    const startIndex = computed(() => (currentPage.value - 1) * pageSize.value)
    const endIndex = computed(() => Math.min(startIndex.value + pageSize.value, sortedData.value.length))
    
    const paginatedData = computed(() => {
      return sortedData.value.slice(startIndex.value, endIndex.value)
    })

    const visiblePages = computed(() => {
      const pages = []
      const start = Math.max(1, currentPage.value - 2)
      const end = Math.min(totalPages.value, currentPage.value + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    })

    // Methods
    const sortBy = (column) => {
      if (sortColumn.value === column) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
      } else {
        sortColumn.value = column
        sortDirection.value = 'asc'
      }
      currentPage.value = 1
    }

    const getSortIcon = (column) => {
      if (sortColumn.value !== column) return 'fas fa-sort'
      return sortDirection.value === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'
    }

    const formatCurrency = (value) => {
      if (typeof value !== 'number') return value
      return new Intl.NumberFormat('uk-UA', {
        style: 'currency',
        currency: 'UAH'
      }).format(value)
    }

    const formatPercentage = (value) => {
      if (typeof value !== 'number') return value
      return `${value.toFixed(2)}%`
    }

    const formatNumber = (value) => {
      if (typeof value !== 'number') return value
      return new Intl.NumberFormat('uk-UA').format(value)
    }

    const formatDate = (value) => {
      if (!value) return value
      return new Date(value).toLocaleDateString('uk-UA')
    }

    const getStatusClass = (status) => {
      const statusMap = {
        'active': 'success',
        'inactive': 'danger',
        'pending': 'warning',
        'completed': 'success',
        'cancelled': 'danger',
        'processing': 'info'
      }
      return statusMap[status?.toLowerCase()] || 'default'
    }

    const exportTable = () => {
      emit('export', {
        data: sortedData.value,
        columns: props.columns,
        title: props.title
      })
    }

    // Watch for data changes to reset pagination
    watch(() => props.data, () => {
      currentPage.value = 1
    })

    watch(searchQuery, () => {
      currentPage.value = 1
    })

    return {
      searchQuery,
      sortColumn,
      sortDirection,
      currentPage,
      pageSize,
      filteredData,
      paginatedData,
      totalPages,
      startIndex,
      endIndex,
      visiblePages,
      sortBy,
      getSortIcon,
      formatCurrency,
      formatPercentage,
      formatNumber,
      formatDate,
      getStatusClass,
      exportTable
    }
  }
}
</script>

<style scoped>
.report-table {
  width: 100%;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.table-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-container i {
  position: absolute;
  left: 0.75rem;
  color: #9ca3af;
  font-size: 0.875rem;
}

.search-input {
  padding: 0.5rem 0.75rem 0.5rem 2.25rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  width: 200px;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.export-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.2s;
}

.export-btn:hover:not(:disabled) {
  background: #059669;
}

.export-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.table-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.table-loading,
.table-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 3rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.table-loading i {
  color: #3b82f6;
}

.table-empty i {
  color: #9ca3af;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: #f9fafb;
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
  white-space: nowrap;
}

.data-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
}

.data-table th.sortable:hover {
  background: #f3f4f6;
}

.data-table th.active {
  background: #eff6ff;
  color: #1d4ed8;
}

.sort-icon {
  margin-left: 0.5rem;
  font-size: 0.75rem;
  opacity: 0.5;
}

.data-table th.active .sort-icon {
  opacity: 1;
}

.data-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f3f4f6;
  font-size: 0.875rem;
  color: #374151;
}

.data-table tr:hover {
  background: #f9fafb;
}

.data-table tr.clickable {
  cursor: pointer;
}

.data-table tr.clickable:hover {
  background: #f3f4f6;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.success {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.danger {
  background: #fee2e2;
  color: #991b1b;
}

.status-badge.warning {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.info {
  background: #dbeafe;
  color: #1e40af;
}

.status-badge.default {
  background: #f3f4f6;
  color: #374151;
}

.table-pagination {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-top: 1.5rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.pagination-info {
  font-size: 0.875rem;
  color: #6b7280;
  flex: 1;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.pagination-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-btn:first-child {
  border-radius: 6px 0 0 6px;
}

.pagination-btn:last-child {
  border-radius: 0 6px 6px 0;
}

.pagination-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.pagination-btn:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.pagination-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.pagination-pages {
  display: flex;
}

.pagination-pages .pagination-btn {
  border-radius: 0;
  border-left: none;
}

.pagination-pages .pagination-btn:first-child {
  border-left: 1px solid #d1d5db;
}

.pagination-size {
  flex-shrink: 0;
}

.page-size-select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 0.875rem;
  cursor: pointer;
}

@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    align-items: stretch;
  }

  .table-actions {
    justify-content: space-between;
  }

  .search-input {
    width: 150px;
  }

  .table-container {
    overflow-x: auto;
  }

  .data-table {
    min-width: 600px;
  }

  .table-pagination {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .pagination-info {
    text-align: center;
  }

  .pagination-controls {
    justify-content: center;
  }
}
</style>
