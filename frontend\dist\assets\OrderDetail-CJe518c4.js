import{_ as As,g as i,h as ks,f as Ns,i as Ss,c as d,a as s,b as k,w as U,r as xs,d as y,t as l,s as Cs,F as is,p as rs,k as N,z as b,C as w,n as S,A as us,m as ws,e as Os,o as n}from"./index-BKy0rL_2.js";import{o as c}from"./orders-HBUhG7wP.js";import{S as V}from"./StatusBadge-DZXrI7cG.js";/* empty css                                                                    */const Ps={class:"order-detail"},Is={class:"level"},Rs={class:"level-right"},Ds={class:"level-item"},Es={key:0,class:"has-text-centered py-6"},Ms={key:1,class:"notification is-danger"},Us={key:2,class:"notification is-warning"},Vs={key:3},Bs={class:"card mb-4"},$s={class:"card-content"},Fs={class:"columns"},Ts={class:"column is-8"},Ls={class:"order-title"},qs={class:"order-date"},zs={class:"column is-4 has-text-right"},Gs={class:"buttons is-right"},Hs={class:"columns"},Qs={class:"column is-4"},js={class:"card"},Js={class:"card-content"},Ks={class:"info-group"},Ws={class:"info-value"},Xs={class:"info-group"},Ys={class:"info-value"},Zs={class:"info-group"},st={class:"info-value"},tt={class:"info-group"},et={class:"info-value"},lt={class:"card mt-4"},at={class:"card-content"},ot={class:"info-group"},nt={class:"info-value"},dt={class:"info-group"},it={class:"info-value"},rt=["href"],ut={class:"info-group"},ct={class:"info-value"},vt={class:"info-group"},pt={class:"info-value"},ft={key:1},ht={class:"column is-8"},mt={class:"card"},_t={class:"card-content"},gt={class:"table-container"},yt={class:"table is-fullwidth"},bt={key:0},At={class:"product-info"},kt={class:"image is-48x48 mr-2"},Nt=["src","alt"],St={class:"product-name"},xt={class:"product-id"},Ct={class:"has-text-right"},wt={class:"has-text-right"},Ot={key:0},Pt={class:"has-text-right"},It={class:"has-text-right"},Rt={class:"has-text-right total-cell"},Dt={class:"columns mt-4"},Et={class:"column is-6"},Mt={class:"card"},Ut={class:"card-content"},Vt={class:"address"},Bt={key:0},$t={key:1},Ft={class:"column is-6"},Tt={class:"card"},Lt={class:"card-content"},qt={class:"address"},zt={key:0},Gt={key:1},Ht={class:"card mt-4"},Qt={class:"card-content"},jt={key:0,class:"has-text-centered py-4"},Jt={key:1,class:"has-text-centered py-4"},Kt={key:2,class:"notes-list"},Wt={class:"note-header"},Xt={class:"note-author"},Yt={class:"note-date"},Zt={class:"note-content"},se={class:"add-note mt-4"},te={class:"field"},ee={class:"control"},le={class:"field"},ae={class:"control"},oe=["disabled"],ne={class:"modal-card"},de={class:"modal-card-body"},ie={class:"content"},re={class:"field"},ue={class:"control"},ce={class:"select is-fullwidth"},ve={class:"field"},pe={class:"control"},fe={class:"select is-fullwidth"},he={class:"field"},me={class:"control"},_e={class:"modal-card-foot"},ge={class:"modal-card"},ye={class:"modal-card-body"},be={class:"content"},Ae={class:"field"},ke={class:"control has-icons-left"},Ne=["max"],Se={class:"help"},xe={class:"field"},Ce={class:"control"},we={class:"modal-card-foot"},Oe=["disabled"],Pe={__name:"OrderDetail",setup(Ie){const cs=Ns();Os();const O=i(!0),P=i(!1),h=i(null),e=i({}),m=i([]),_=i(""),I=i(!1),R=i(!1),v=i(""),p=i(""),A=i(""),D=i(!1),B=i(!1),f=i(0),g=i(""),E=i(!1),r=ks(()=>cs.params.id),vs=async()=>{O.value=!0,h.value=null;try{console.log("Fetching order with ID:",r.value);const o=await c.getOrderById(r.value);console.log("Received order data:",o),o===null?(e.value=null,h.value="Order not found."):(e.value=o,v.value=o.status||0,p.value=o.paymentStatus||0)}catch(o){console.error("Error fetching order:",o),h.value="Failed to load order data. Please try again.",e.value=null}finally{O.value=!1}},ps=async()=>{P.value=!0;try{const o=await c.getOrderNotes(r.value);m.value=o}catch(o){console.error("Error fetching order notes:",o)}finally{P.value=!1}},$=o=>o?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(o)):"",u=o=>new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH"}).format(o),fs=o=>{o.target.src="https://via.placeholder.com/48?text=No+Image"},hs=async()=>{if(_.trim()){I.value=!0;try{const o=await c.addOrderNote(r.value,_);o.note&&(m.value.unshift(o.note),_.value="")}catch(o){console.error("Error adding note:",o)}finally{I.value=!1}}},ms=()=>{v.value=e.value.status||0,p.value=e.value.paymentStatus||0,A.value="",R.value=!0},x=()=>{R.value=!1},_s=async()=>{D.value=!0;try{if(v.value!==e.value.status&&(await c.updateOrderStatus(r.value,v.value),e.value.status=v.value),p.value!==e.value.paymentStatus&&(await c.updatePaymentStatus(r.value,p.value),e.value.paymentStatus=p.value),A.value.trim()){const o=await c.addOrderNote(r.value,A.value);o.note&&m.value.unshift(o.note)}x()}catch(o){console.error("Error updating order status:",o)}finally{D.value=!1}},C=()=>{B.value=!1},gs=async()=>{if(!(!f.value||!g.value.trim())){E.value=!0;try{await c.refundOrder(r.value,f.value,g.value),e.value.status="refunded",e.value.paymentStatus="refunded";const o=await c.addOrderNote(r.value,`Refund processed: ${u(f.value)}. Reason: ${g.value}`);o.note&&m.value.unshift(o.note),C()}catch(o){console.error("Error processing refund:",o)}finally{E.value=!1}}},ys=()=>{window.print()};return Ss(()=>{vs(),ps()}),(o,t)=>{var F,T,L,q,z,G,H,Q,j,J,K,W,X,Y,Z,ss,ts,es,ls,as,os,ns,ds;const M=xs("router-link");return n(),d("div",Ps,[s("div",Is,[t[10]||(t[10]=s("div",{class:"level-left"},[s("div",{class:"level-item"},[s("h1",{class:"title"},"Order Details")])],-1)),s("div",Rs,[s("div",Ds,[k(M,{to:"/admin/orders",class:"button is-light"},{default:U(()=>t[9]||(t[9]=[s("span",{class:"icon"},[s("i",{class:"fas fa-arrow-left"})],-1),s("span",null,"Back to Orders",-1)])),_:1})])])]),O.value?(n(),d("div",Es,t[11]||(t[11]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),s("p",{class:"mt-2"},"Loading order details...",-1)]))):h.value?(n(),d("div",Ms,[s("button",{class:"delete",onClick:t[0]||(t[0]=a=>h.value=null)}),y(" "+l(h.value),1)])):!e.value||!e.value.id?(n(),d("div",Us,[t[13]||(t[13]=s("p",null,"Order not found.",-1)),k(M,{to:"/admin/orders",class:"button is-primary mt-4"},{default:U(()=>t[12]||(t[12]=[y(" Back to Orders ")])),_:1})])):(n(),d("div",Vs,[s("div",Bs,[s("div",$s,[s("div",Fs,[s("div",Ts,[s("h2",Ls,"Order #"+l(e.value.id),1),s("p",qs,l($(e.value.createdAt)),1)]),s("div",zs,[s("div",Gs,[s("button",{class:"button is-primary",onClick:t[1]||(t[1]=a=>o.$router.push(`/admin/orders/${r.value}/edit`))},t[14]||(t[14]=[s("span",{class:"icon"},[s("i",{class:"fas fa-edit"})],-1),s("span",null,"Edit Order",-1)])),s("button",{class:"button is-info",onClick:ms},t[15]||(t[15]=[s("span",{class:"icon"},[s("i",{class:"fas fa-cog"})],-1),s("span",null,"Update Status",-1)])),s("button",{class:"button is-info",onClick:ys},t[16]||(t[16]=[s("span",{class:"icon"},[s("i",{class:"fas fa-print"})],-1),s("span",null,"Print",-1)]))])])])])]),s("div",Hs,[s("div",Qs,[s("div",js,[t[21]||(t[21]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Order Status")],-1)),s("div",Js,[s("div",Ks,[t[17]||(t[17]=s("h3",{class:"info-label"},"Status",-1)),s("p",Ws,[k(V,{status:e.value.status||0,type:"order"},null,8,["status"])])]),s("div",Xs,[t[18]||(t[18]=s("h3",{class:"info-label"},"Payment Status",-1)),s("p",Ys,[k(V,{status:e.value.paymentStatus||0,type:"payment"},null,8,["status"])])]),s("div",Zs,[t[19]||(t[19]=s("h3",{class:"info-label"},"Payment Method",-1)),s("p",st,l(e.value.paymentMethod||"N/A"),1)]),s("div",tt,[t[20]||(t[20]=s("h3",{class:"info-label"},"Shipping Method",-1)),s("p",et,l(e.value.shippingMethod||"N/A"),1)])])]),s("div",lt,[t[27]||(t[27]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Customer Information")],-1)),s("div",at,[s("div",ot,[t[22]||(t[22]=s("h3",{class:"info-label"},"Customer",-1)),s("p",nt,l(e.value.customerName||e.value.userName||"N/A"),1)]),s("div",dt,[t[23]||(t[23]=s("h3",{class:"info-label"},"Email",-1)),s("p",it,[s("a",{href:`mailto:${e.value.customerEmail||e.value.email}`},l(e.value.customerEmail||e.value.email||"N/A"),9,rt)])]),s("div",ut,[t[24]||(t[24]=s("h3",{class:"info-label"},"Phone",-1)),s("p",ct,l(((F=e.value.shippingAddress)==null?void 0:F.phone)||"N/A"),1)]),s("div",vt,[t[26]||(t[26]=s("h3",{class:"info-label"},"Customer Account",-1)),s("p",pt,[e.value.userId?(n(),Cs(M,{key:0,to:`/admin/users/${e.value.userId}`},{default:U(()=>t[25]||(t[25]=[y(" View Customer Profile ")])),_:1},8,["to"])):(n(),d("span",ft,"Guest Checkout"))])])])])]),s("div",ht,[s("div",mt,[t[34]||(t[34]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Order Items")],-1)),s("div",_t,[s("div",gt,[s("table",yt,[t[33]||(t[33]=s("thead",null,[s("tr",null,[s("th",null,"Product"),s("th",null,"Price"),s("th",null,"Quantity"),s("th",{class:"has-text-right"},"Total")])],-1)),s("tbody",null,[!e.value.items||e.value.items.length===0?(n(),d("tr",bt,t[28]||(t[28]=[s("td",{colspan:"4",class:"has-text-centered has-text-grey"},[s("div",{class:"py-4"},[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-shopping-cart fa-2x"})]),s("p",{class:"mt-2"},"No items in this order")])],-1)]))):(n(!0),d(is,{key:1},rs(e.value.items,(a,bs)=>(n(),d("tr",{key:(a==null?void 0:a.id)||(a==null?void 0:a.productId)||bs},[s("td",null,[s("div",At,[s("figure",kt,[s("img",{src:a.image||a.productImage||"https://via.placeholder.com/48",alt:a.productName||"Product",onError:fs},null,40,Nt)]),s("div",null,[s("p",St,l(a.productName||"Unknown Product"),1),s("p",xt,"ID: "+l(a.productId||"N/A"),1)])])]),s("td",null,l(u(a.price||0)),1),s("td",null,l(a.quantity||0),1),s("td",Ct,l(u(a.total||a.price*a.quantity||0)),1)]))),128))]),s("tfoot",null,[s("tr",null,[t[29]||(t[29]=s("td",{colspan:"3",class:"has-text-right"},[s("strong",null,"Subtotal:")],-1)),s("td",wt,l(u(e.value.subtotal||e.value.totalPriceAmount||0)),1)]),e.value.discount?(n(),d("tr",Ot,[t[30]||(t[30]=s("td",{colspan:"3",class:"has-text-right"},[s("strong",null,"Discount:")],-1)),s("td",Pt,"-"+l(u(e.value.discount)),1)])):N("",!0),s("tr",null,[t[31]||(t[31]=s("td",{colspan:"3",class:"has-text-right"},[s("strong",null,"Shipping:")],-1)),s("td",It,l(u(e.value.shipping||0)),1)]),s("tr",null,[t[32]||(t[32]=s("td",{colspan:"3",class:"has-text-right"},[s("strong",null,"Total:")],-1)),s("td",Rt,l(u(e.value.total||e.value.totalPriceAmount||0)),1)])])])])])]),s("div",Dt,[s("div",Et,[s("div",Mt,[t[35]||(t[35]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Shipping Address")],-1)),s("div",Ut,[s("address",Vt,[s("p",null,l((T=e.value.shippingAddress)==null?void 0:T.firstName)+" "+l((L=e.value.shippingAddress)==null?void 0:L.lastName),1),s("p",null,l((q=e.value.shippingAddress)==null?void 0:q.address1),1),(z=e.value.shippingAddress)!=null&&z.address2?(n(),d("p",Bt,l((G=e.value.shippingAddress)==null?void 0:G.address2),1)):N("",!0),s("p",null,l((H=e.value.shippingAddress)==null?void 0:H.city)+", "+l((Q=e.value.shippingAddress)==null?void 0:Q.state)+" "+l((j=e.value.shippingAddress)==null?void 0:j.postalCode),1),s("p",null,l((J=e.value.shippingAddress)==null?void 0:J.country),1),(K=e.value.shippingAddress)!=null&&K.phone?(n(),d("p",$t,"Phone: "+l((W=e.value.shippingAddress)==null?void 0:W.phone),1)):N("",!0)])])])]),s("div",Ft,[s("div",Tt,[t[36]||(t[36]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Billing Address")],-1)),s("div",Lt,[s("address",qt,[s("p",null,l((X=e.value.billingAddress)==null?void 0:X.firstName)+" "+l((Y=e.value.billingAddress)==null?void 0:Y.lastName),1),s("p",null,l((Z=e.value.billingAddress)==null?void 0:Z.address1),1),(ss=e.value.billingAddress)!=null&&ss.address2?(n(),d("p",zt,l((ts=e.value.billingAddress)==null?void 0:ts.address2),1)):N("",!0),s("p",null,l((es=e.value.billingAddress)==null?void 0:es.city)+", "+l((ls=e.value.billingAddress)==null?void 0:ls.state)+" "+l((as=e.value.billingAddress)==null?void 0:as.postalCode),1),s("p",null,l((os=e.value.billingAddress)==null?void 0:os.country),1),(ns=e.value.billingAddress)!=null&&ns.phone?(n(),d("p",Gt,"Phone: "+l((ds=e.value.billingAddress)==null?void 0:ds.phone),1)):N("",!0)])])])])]),s("div",Ht,[t[40]||(t[40]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Order Notes")],-1)),s("div",Qt,[P.value?(n(),d("div",jt,t[37]||(t[37]=[s("span",{class:"icon"},[s("i",{class:"fas fa-spinner fa-pulse"})],-1),s("p",{class:"mt-2"},"Loading notes...",-1)]))):m.value.length?(n(),d("div",Kt,[(n(!0),d(is,null,rs(m.value,a=>(n(),d("div",{key:a.id,class:"note-item"},[s("div",Wt,[s("span",Xt,l(a.createdBy),1),s("span",Yt,l($(a.createdAt)),1)]),s("div",Zt,l(a.content),1)]))),128))])):(n(),d("div",Jt,t[38]||(t[38]=[s("p",null,"No notes for this order",-1)]))),s("div",se,[s("div",te,[t[39]||(t[39]=s("label",{class:"label"},"Add Note",-1)),s("div",ee,[b(s("textarea",{class:"textarea","onUpdate:modelValue":t[2]||(t[2]=a=>_.value=a),placeholder:"Add a note about this order"},"                    ",512),[[w,_.value]])])]),s("div",le,[s("div",ae,[s("button",{class:S(["button is-primary",{"is-loading":I.value}]),onClick:hs,disabled:!_.value.trim()}," Add Note ",10,oe)])])])])])])])])),s("div",{class:S(["modal",{"is-active":R.value}])},[s("div",{class:"modal-background",onClick:x}),s("div",ne,[s("header",{class:"modal-card-head"},[t[41]||(t[41]=s("p",{class:"modal-card-title"},"Update Order Status",-1)),s("button",{class:"delete","aria-label":"close",onClick:x})]),s("section",de,[s("div",ie,[s("p",null,[t[42]||(t[42]=s("strong",null,"Order ID:",-1)),y(" "+l(e.value.id),1)]),s("p",null,[t[43]||(t[43]=s("strong",null,"Current Status:",-1)),k(V,{status:e.value.status||0,type:"order"},null,8,["status"])]),s("div",re,[t[45]||(t[45]=s("label",{class:"label"},"New Status",-1)),s("div",ue,[s("div",ce,[b(s("select",{"onUpdate:modelValue":t[3]||(t[3]=a=>v.value=a)},t[44]||(t[44]=[s("option",{value:0},"Processing",-1),s("option",{value:1},"Pending",-1),s("option",{value:2},"Paid",-1),s("option",{value:3},"Shipped",-1),s("option",{value:4},"Delivered",-1),s("option",{value:5},"Cancelled",-1)]),512),[[us,v.value]])])])]),s("div",ve,[t[47]||(t[47]=s("label",{class:"label"},"Payment Status",-1)),s("div",pe,[s("div",fe,[b(s("select",{"onUpdate:modelValue":t[4]||(t[4]=a=>p.value=a)},t[46]||(t[46]=[s("option",{value:0},"Pending",-1),s("option",{value:1},"Completed",-1),s("option",{value:2},"Refunded",-1),s("option",{value:3},"Failed",-1)]),512),[[us,p.value]])])])]),s("div",he,[t[48]||(t[48]=s("label",{class:"label"},"Note (Optional)",-1)),s("div",me,[b(s("textarea",{class:"textarea","onUpdate:modelValue":t[5]||(t[5]=a=>A.value=a),placeholder:"Add a note about this status change"},"                ",512),[[w,A.value]])])])])]),s("footer",_e,[s("button",{class:S(["button is-primary",{"is-loading":D.value}]),onClick:_s}," Update Status ",2),s("button",{class:"button",onClick:x},"Cancel")])])],2),s("div",{class:S(["modal",{"is-active":B.value}])},[s("div",{class:"modal-background",onClick:C}),s("div",ge,[s("header",{class:"modal-card-head"},[t[49]||(t[49]=s("p",{class:"modal-card-title"},"Process Refund",-1)),s("button",{class:"delete","aria-label":"close",onClick:C})]),s("section",ye,[s("div",be,[s("p",null,[t[50]||(t[50]=s("strong",null,"Order ID:",-1)),y(" "+l(e.value.id),1)]),s("p",null,[t[51]||(t[51]=s("strong",null,"Order Total:",-1)),y(" "+l(u(e.value.total||e.value.totalPriceAmount||0)),1)]),s("div",Ae,[t[53]||(t[53]=s("label",{class:"label"},"Refund Amount",-1)),s("div",ke,[b(s("input",{class:"input",type:"number","onUpdate:modelValue":t[6]||(t[6]=a=>f.value=a),min:"0",max:e.value.total||e.value.totalPriceAmount||0,step:"0.01"},null,8,Ne),[[w,f.value]]),t[52]||(t[52]=s("span",{class:"icon is-small is-left"},[s("i",{class:"fas fa-hryvnia"})],-1))]),s("p",Se,[s("a",{href:"#",onClick:t[7]||(t[7]=ws(a=>f.value=e.value.total||e.value.totalPriceAmount||0,["prevent"]))},"Refund full amount")])]),s("div",xe,[t[54]||(t[54]=s("label",{class:"label"},"Reason for Refund",-1)),s("div",Ce,[b(s("textarea",{class:"textarea","onUpdate:modelValue":t[8]||(t[8]=a=>g.value=a),placeholder:"Enter reason for refund"},"                ",512),[[w,g.value]])])])])]),s("footer",we,[s("button",{class:S(["button is-danger",{"is-loading":E.value}]),onClick:gs,disabled:!f.value||!g.value.trim()}," Process Refund ",10,Oe),s("button",{class:"button",onClick:C},"Cancel")])])],2)])}}},Ue=As(Pe,[["__scopeId","data-v-bfadc422"]]);export{Ue as default};
