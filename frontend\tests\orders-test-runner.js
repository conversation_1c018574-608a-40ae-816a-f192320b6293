/**
 * Автоматизований тест-раннер для функціональності Orders
 * Запускається в консолі браузера для перевірки основних функцій
 */

class OrdersTestRunner {
  constructor() {
    this.results = [];
    this.startTime = Date.now();
    this.testCount = 0;
    this.passedCount = 0;
    this.failedCount = 0;
  }

  // Логування результатів тестів
  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logEntry = { timestamp, message, type };
    this.results.push(logEntry);
    
    const prefix = `[OrdersTest ${type.toUpperCase()}]`;
    switch (type) {
      case 'pass':
        console.log(`✅ ${prefix} ${message}`);
        break;
      case 'fail':
        console.error(`❌ ${prefix} ${message}`);
        break;
      case 'warn':
        console.warn(`⚠️ ${prefix} ${message}`);
        break;
      default:
        console.log(`ℹ️ ${prefix} ${message}`);
    }
  }

  // Виконання тесту з обробкою помилок
  async runTest(testName, testFunction) {
    this.testCount++;
    this.log(`Starting test: ${testName}`);
    
    try {
      await testFunction();
      this.passedCount++;
      this.log(`Test passed: ${testName}`, 'pass');
      return true;
    } catch (error) {
      this.failedCount++;
      this.log(`Test failed: ${testName} - ${error.message}`, 'fail');
      console.error('Test error details:', error);
      return false;
    }
  }

  // Перевірка доступності ordersService
  async testOrdersServiceAvailability() {
    if (!window.ordersService) {
      throw new Error('ordersService is not available on window object');
    }
    
    const requiredMethods = [
      'getOrders', 'forceRefresh', 'getCacheStats', 
      'getLogs', 'getDiagnostics', 'exportLogs'
    ];
    
    for (const method of requiredMethods) {
      if (typeof window.ordersService[method] !== 'function') {
        throw new Error(`Method ${method} is not available on ordersService`);
      }
    }
  }

  // Тест системи кешування
  async testCachingSystem() {
    // Очищуємо кеш
    window.ordersService.forceRefresh();
    
    // Отримуємо статистику кешу
    const initialStats = window.ordersService.getCacheStats();
    if (typeof initialStats !== 'object') {
      throw new Error('getCacheStats should return an object');
    }
    
    // Перевіряємо, що кеш очищений
    if (initialStats.size !== 0) {
      throw new Error('Cache should be empty after forceRefresh');
    }
  }

  // Тест логування
  async testLoggingSystem() {
    // Очищуємо логи
    window.ordersService.clearLogs();
    
    // Виконуємо операцію, яка повинна створити лог
    window.ordersService.forceRefresh();
    
    // Перевіряємо логи
    const logs = window.ordersService.getLogs();
    if (!Array.isArray(logs)) {
      throw new Error('getLogs should return an array');
    }
    
    // Перевіряємо діагностику
    const diagnostics = window.ordersService.getDiagnostics();
    if (typeof diagnostics !== 'object' || !diagnostics.sessionId) {
      throw new Error('getDiagnostics should return object with sessionId');
    }
  }

  // Тест констант статусів
  async testStatusConstants() {
    // Перевіряємо, що константи доступні
    if (!window.ORDER_STATUS_OPTIONS) {
      throw new Error('ORDER_STATUS_OPTIONS not available');
    }
    
    if (!window.PAYMENT_STATUS_OPTIONS) {
      throw new Error('PAYMENT_STATUS_OPTIONS not available');
    }
    
    // Перевіряємо структуру констант
    const orderStatuses = window.ORDER_STATUS_OPTIONS;
    if (!Array.isArray(orderStatuses) || orderStatuses.length === 0) {
      throw new Error('ORDER_STATUS_OPTIONS should be non-empty array');
    }
    
    // Перевіряємо, що кожен статус має value та label
    for (const status of orderStatuses) {
      if (typeof status.value === 'undefined' || !status.label) {
        throw new Error('Each status should have value and label');
      }
    }
  }

  // Тест eventBus
  async testEventBus() {
    if (!window.eventBus) {
      throw new Error('eventBus is not available on window object');
    }
    
    // Тестуємо підписку та емісію події
    let eventReceived = false;
    const unsubscribe = window.eventBus.on('test:event', () => {
      eventReceived = true;
    });
    
    window.eventBus.emit('test:event', { test: true });
    
    if (!eventReceived) {
      throw new Error('Event was not received');
    }
    
    // Відписуємося
    unsubscribe();
    
    // Перевіряємо діагностичну інформацію
    const debugInfo = window.eventBus.getDebugInfo();
    if (typeof debugInfo !== 'object') {
      throw new Error('getDebugInfo should return an object');
    }
  }

  // Тест доступності Vue компонентів
  async testVueComponentsAvailability() {
    // Перевіряємо, що Vue додаток запущений
    if (!window.Vue && !document.querySelector('#app').__vue__) {
      throw new Error('Vue application is not running');
    }
    
    // Перевіряємо наявність основних елементів Orders сторінки
    const ordersElements = [
      '.admin-orders',
      '.table-container',
      '.pagination-wrapper'
    ];
    
    // Якщо ми на сторінці Orders, перевіряємо елементи
    if (window.location.pathname.includes('/orders')) {
      for (const selector of ordersElements) {
        const element = document.querySelector(selector);
        if (!element) {
          this.log(`Element ${selector} not found (might be normal if not on Orders page)`, 'warn');
        }
      }
    }
  }

  // Тест продуктивності
  async testPerformance() {
    const startTime = performance.now();
    
    // Симулюємо операції
    window.ordersService.getCacheStats();
    window.ordersService.getDiagnostics();
    const logs = window.ordersService.getLogs();
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Перевіряємо, що операції виконуються швидко (менше 100мс)
    if (duration > 100) {
      throw new Error(`Performance test failed: operations took ${duration}ms (should be < 100ms)`);
    }
    
    this.log(`Performance test passed: ${duration.toFixed(2)}ms`);
  }

  // Запуск всіх тестів
  async runAllTests() {
    this.log('Starting Orders functionality tests...');
    this.log(`Test environment: ${navigator.userAgent}`);
    this.log(`Current URL: ${window.location.href}`);
    
    const tests = [
      ['Orders Service Availability', () => this.testOrdersServiceAvailability()],
      ['Caching System', () => this.testCachingSystem()],
      ['Logging System', () => this.testLoggingSystem()],
      ['Status Constants', () => this.testStatusConstants()],
      ['Event Bus', () => this.testEventBus()],
      ['Vue Components', () => this.testVueComponentsAvailability()],
      ['Performance', () => this.testPerformance()]
    ];
    
    for (const [testName, testFunction] of tests) {
      await this.runTest(testName, testFunction);
      // Невелика затримка між тестами
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    this.generateReport();
  }

  // Генерація звіту
  generateReport() {
    const duration = Date.now() - this.startTime;
    const successRate = ((this.passedCount / this.testCount) * 100).toFixed(1);
    
    this.log('='.repeat(50));
    this.log('TEST RESULTS SUMMARY');
    this.log('='.repeat(50));
    this.log(`Total tests: ${this.testCount}`);
    this.log(`Passed: ${this.passedCount}`, 'pass');
    this.log(`Failed: ${this.failedCount}`, this.failedCount > 0 ? 'fail' : 'info');
    this.log(`Success rate: ${successRate}%`);
    this.log(`Duration: ${duration}ms`);
    this.log('='.repeat(50));
    
    if (this.failedCount === 0) {
      this.log('🎉 All tests passed! Orders functionality is working correctly.', 'pass');
    } else {
      this.log('⚠️ Some tests failed. Please check the issues above.', 'fail');
    }
    
    // Зберігаємо результати в window для подальшого аналізу
    window.ordersTestResults = {
      summary: {
        total: this.testCount,
        passed: this.passedCount,
        failed: this.failedCount,
        successRate: parseFloat(successRate),
        duration
      },
      details: this.results
    };
    
    this.log('Test results saved to window.ordersTestResults');
  }

  // Експорт результатів тестів
  exportResults() {
    const data = {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      testResults: window.ordersTestResults
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `orders-test-results-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    this.log('Test results exported to file');
  }
}

// Глобальна функція для запуску тестів
window.runOrdersTests = async function() {
  const runner = new OrdersTestRunner();
  await runner.runAllTests();
  return runner;
};

// Глобальна функція для експорту результатів
window.exportOrdersTestResults = function() {
  if (window.ordersTestResults) {
    const runner = new OrdersTestRunner();
    runner.exportResults();
  } else {
    console.error('No test results available. Run tests first with runOrdersTests()');
  }
};

console.log('📋 Orders Test Runner loaded!');
console.log('🚀 Run tests with: runOrdersTests()');
console.log('📁 Export results with: exportOrdersTestResults()');
