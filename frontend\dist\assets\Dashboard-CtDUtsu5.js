import{_ as S,c as f,o as v,a as e,t as C,n as F,g as b,h as P,x as B,i as E,F as O,p as L,y as Y,z as G,A as K,r as H,b as w,w as A,q as R,j as X,k as W,d as j}from"./index-BKy0rL_2.js";import{C as V}from"./auto-Cz6uSJnr.js";import{S as ee}from"./StatusBadge-DZXrI7cG.js";/* empty css                                                                    */const ae={class:"admin-card admin-stat-card"},se={class:"level is-mobile"},te={class:"level-left"},oe={class:"level-item"},le={class:"stat-heading"},ne={class:"stat-value"},re={class:"level-right"},ie={class:"level-item"},de={class:"stat-icon"},ce={__name:"AdminStatCard",props:{title:{type:String,required:!0},value:{type:[String,Number],required:!0},icon:{type:String,required:!0},color:{type:String,default:"is-primary"}},setup(r){return(l,a)=>(v(),f("div",ae,[e("div",{class:F(["admin-card-content",r.color])},[e("div",se,[e("div",te,[e("div",oe,[e("div",null,[e("p",le,C(r.title),1),e("p",ne,C(r.value),1)])])]),e("div",re,[e("div",ie,[e("span",de,[e("i",{class:F(["fas","fa-"+r.icon,"fa-2x"])},null,2)])])])])],2)]))}},I=S(ce,[["__scopeId","data-v-469c4f1f"]]),ue={class:"card"},fe={class:"card-header"},ve={class:"card-header-icon"},pe={class:"tabs is-toggle is-small"},me={class:"card-content"},he={key:0,class:"has-text-centered py-6"},ge={key:1,class:"has-text-centered py-6"},ye={key:2,class:"chart-container"},_e={__name:"SalesChart",props:{data:{type:Array,required:!0},loading:{type:Boolean,default:!1}},emits:["period-changed"],setup(r,{emit:l}){const a=r,s=l,t=b(null),_=b(null),m=b("month"),p=b(!1),o=d=>{m.value!==d&&(m.value=d,p.value=!0,s("period-changed",d))},c=P(()=>({labels:a.data.map(d=>d.label),datasets:[{label:"Sales",data:a.data.map(d=>d.value),backgroundColor:"rgba(255, 119, 0, 0.2)",borderColor:"#ff7700",borderWidth:2,pointBackgroundColor:"#ff7700",pointBorderColor:"#ffffff",pointBorderWidth:2,pointRadius:4,pointHoverRadius:6,tension:.3,fill:!0}]})),g=()=>{_.value&&_.value.destroy();const d=t.value.getContext("2d");_.value=new V(d,{type:"line",data:c.value,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.7)",titleFont:{size:14,weight:"bold"},bodyFont:{size:13},padding:12,callbacks:{label:function(h){let y=h.dataset.label||"";return y&&(y+=": "),h.parsed.y!==null&&(y+=new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH"}).format(h.parsed.y)),y}}}},scales:{y:{beginAtZero:!0,ticks:{color:"#e0e0e0",font:{weight:"bold",size:12},callback:function(h){return new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH",notation:"compact",compactDisplay:"short"}).format(h)}},grid:{color:"rgba(255, 255, 255, 0.1)"}},x:{ticks:{color:"#e0e0e0",font:{weight:"bold",size:12}},grid:{display:!1}}}}})};return B(()=>a.data,()=>{p.value=!1,setTimeout(()=>{a.data&&a.data.length>0&&g()},0)},{deep:!0}),E(()=>{a.data&&a.data.length>0&&g()}),(d,h)=>(v(),f("div",ue,[e("div",fe,[h[3]||(h[3]=e("p",{class:"card-header-title sales-title"},"Sales Overview",-1)),e("div",ve,[e("div",pe,[e("ul",null,[e("li",{class:F({"is-active":m.value==="week"})},[e("a",{onClick:h[0]||(h[0]=y=>o("week"))},"Week")],2),e("li",{class:F({"is-active":m.value==="month"})},[e("a",{onClick:h[1]||(h[1]=y=>o("month"))},"Month")],2),e("li",{class:F({"is-active":m.value==="year"})},[e("a",{onClick:h[2]||(h[2]=y=>o("year"))},"Year")],2)])])])]),e("div",me,[p.value?(v(),f("div",he,h[4]||(h[4]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading chart data...",-1)]))):!r.data||r.data.length===0?(v(),f("div",ge,h[5]||(h[5]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-chart-line fa-2x"})],-1),e("p",{class:"mt-2"},"No sales data available for this period",-1)]))):(v(),f("div",ye,[e("canvas",{ref_key:"chartCanvas",ref:t,height:"300"},null,512)]))])]))}},be=S(_e,[["__scopeId","data-v-dd806dfe"]]),$e={class:"card"},ke={class:"card-content"},we={key:0,class:"has-text-centered py-6"},Ce={key:1,class:"has-text-centered py-6"},xe={key:2},De={class:"chart-container"},Pe={class:"status-legend"},Ae={class:"legend-label"},Se={class:"legend-value"},Ne={__name:"OrdersByStatusChart",props:{data:{type:Array,required:!0},loading:{type:Boolean,default:!1}},setup(r){const l=r,a=b(null),s=b(null),t=b(!1),_=["#ff7700","#3298dc","#48c774","#ffdd57","#ff3860","#9c27b0","#00d1b2","#f39c12","#8e44ad","#3498db"],m=()=>{if(!a.value)return;const o=a.value.getContext("2d");s.value&&s.value.destroy();const c=l.data.map(d=>d.status),g=l.data.map(d=>d.count);s.value=new V(o,{type:"doughnut",data:{labels:c,datasets:[{data:g,backgroundColor:_.slice(0,l.data.length),borderColor:"#ffffff",borderWidth:2,hoverOffset:10}]},options:{responsive:!0,maintainAspectRatio:!1,cutout:"70%",plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleFont:{size:14,weight:"bold"},bodyFont:{size:13,weight:"bold"},titleColor:"#ffffff",bodyColor:"#ffffff",padding:12,cornerRadius:6,boxPadding:6,callbacks:{label:function(d){const h=d.label||"",y=d.raw||0,u=d.dataset.data.reduce((N,D)=>N+D,0),x=Math.round(y/u*100);return`${h}: ${y} (${x}%)`}}}}}})},p=()=>{s.value&&(s.value.data.labels=l.data.map(o=>o.status),s.value.data.datasets[0].data=l.data.map(o=>o.count),s.value.update())};return B(()=>l.data,()=>{t.value=!1,setTimeout(()=>{l.data&&l.data.length>0&&(s.value?p():m())},0)},{deep:!0}),E(()=>{l.data&&l.data.length>0&&m()}),(o,c)=>(v(),f("div",$e,[c[2]||(c[2]=e("div",{class:"card-header"},[e("p",{class:"card-header-title status-title"},"Orders by Status")],-1)),e("div",ke,[t.value?(v(),f("div",we,c[0]||(c[0]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading chart data...",-1)]))):!r.data||r.data.length===0?(v(),f("div",Ce,c[1]||(c[1]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-chart-pie fa-2x"})],-1),e("p",{class:"mt-2"},"No order data available",-1)]))):(v(),f("div",xe,[e("div",De,[e("canvas",{ref_key:"chartCanvas",ref:a},null,512)]),e("div",Pe,[(v(!0),f(O,null,L(r.data,(g,d)=>(v(),f("div",{key:g.status,class:"legend-item"},[e("span",{class:"legend-color",style:Y({backgroundColor:_[d%_.length]})},null,4),e("span",Ae,C(g.status),1),e("span",Se,C(g.count),1)]))),128))])]))])]))}},Te=S(Ne,[["__scopeId","data-v-a8f7f040"]]),qe={class:"card"},Fe={class:"card-header"},Re={class:"card-header-icon"},Ie={class:"field"},Me={class:"control"},Ee={class:"select is-small"},Ue={class:"card-content"},Be={class:"level"},Oe={class:"level-left"},Le={class:"level-item"},He={class:"heading"},Ve={class:"title is-4"},ze={class:"level-right"},We={class:"level-item"},je={class:"title is-5"},Je={key:0,class:"has-text-centered py-6"},Ze={key:1,class:"has-text-centered py-6"},Qe={key:2,class:"chart-container"},Ye={__name:"SiteProfitChart",props:{data:{type:Array,required:!0},totalProfit:{type:Number,default:0},loading:{type:Boolean,default:!1}},emits:["period-changed"],setup(r,{emit:l}){const a=r,s=l,t=b(null),_=b("month"),m=b(!1);let p=null;const o=P(()=>{if(!a.data||a.data.length===0)return 0;const y=a.data.length;return a.data.reduce((x,N)=>x+(N.value||0),0)/y}),c=P(()=>!a.data||a.data.length===0?0:a.data.reduce((y,u)=>y+(u.value||0),0)),g=y=>{const u=typeof y=="string"?Number(y):y;return isNaN(u)?"UAH 0.00":new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH",minimumFractionDigits:2,maximumFractionDigits:2}).format(u)},d=()=>{s("period-changed",_.value)},h=()=>{if(!t.value||!a.data||a.data.length===0)return;p&&p.destroy();const y=t.value.getContext("2d");p=new V(y,{type:"line",data:{labels:a.data.map(u=>u.label||u.date),datasets:[{label:"Site Profit (UAH)",data:a.data.map(u=>u.value||0),borderColor:"#3273dc",backgroundColor:"rgba(50, 115, 220, 0.1)",borderWidth:3,fill:!0,tension:.4,pointBackgroundColor:"#3273dc",pointBorderColor:"#ffffff",pointBorderWidth:2,pointRadius:5,pointHoverRadius:8}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#ffffff",bodyColor:"#ffffff",borderColor:"#3273dc",borderWidth:1,callbacks:{label:function(u){return`Profit: ${g(u.parsed.y)}`}}}},scales:{y:{beginAtZero:!0,ticks:{color:"#4a4a4a",font:{weight:"bold",size:12},callback:function(u){return g(u)}},grid:{color:"rgba(0, 0, 0, 0.1)"}},x:{ticks:{color:"#4a4a4a",font:{weight:"bold",size:12}},grid:{display:!1}}}}})};return B(()=>a.data,()=>{m.value=!1,setTimeout(()=>{a.data&&a.data.length>0&&h()},0)},{deep:!0}),E(()=>{a.data&&a.data.length>0&&h()}),(y,u)=>(v(),f("div",qe,[e("div",Fe,[u[2]||(u[2]=e("p",{class:"card-header-title"},[e("span",{class:"icon"},[e("i",{class:"fas fa-chart-line"})]),e("span",null,"Site Profit (15% Commission)")],-1)),e("div",Re,[e("div",Ie,[e("div",Me,[e("div",Ee,[G(e("select",{"onUpdate:modelValue":u[0]||(u[0]=x=>_.value=x),onChange:d},u[1]||(u[1]=[e("option",{value:"week"},"Last 7 days",-1),e("option",{value:"month"},"Last 30 days",-1),e("option",{value:"quarter"},"Last 3 months",-1),e("option",{value:"year"},"Last 12 months",-1)]),544),[[K,_.value]])])])])])]),e("div",Ue,[e("div",Be,[e("div",Oe,[e("div",Le,[e("div",null,[e("p",He,"Total Profit ("+C(_.value==="week"?"Last 7 days":_.value==="month"?"Last 30 days":_.value==="quarter"?"Last 3 months":"Last 12 months")+")",1),e("p",Ve,C(g(c.value)),1)])])]),e("div",ze,[e("div",We,[e("div",null,[u[3]||(u[3]=e("p",{class:"heading"},"Average Daily",-1)),e("p",je,C(g(o.value)),1)])])])]),m.value?(v(),f("div",Je,u[4]||(u[4]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading profit data...",-1)]))):!r.data||r.data.length===0?(v(),f("div",Ze,u[5]||(u[5]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-chart-line fa-2x"})],-1),e("p",{class:"mt-2"},"No profit data available for this period",-1)]))):(v(),f("div",Qe,[e("canvas",{ref_key:"chartCanvas",ref:t,height:"300"},null,512)]))])]))}},Ge=S(Ye,[["__scopeId","data-v-140d9799"]]),Ke={class:"card"},Xe={class:"card-header"},ea={class:"card-header-icon"},aa={class:"card-content"},sa={key:0,class:"has-text-centered py-4"},ta={key:1,class:"has-text-centered py-4"},oa={key:2,class:"table-container"},la={class:"table is-fullwidth is-hoverable"},na={class:"has-text-right"},ra={__name:"RecentOrders",props:{orders:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},setup(r){const l=s=>new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH"}).format(s),a=s=>{if(!s)return"";const t=new Date(s),m=new Date-t,p=Math.round(m/1e3),o=Math.round(p/60),c=Math.round(o/60),g=Math.round(c/24);return p<60?"Just now":o<60?`${o} minute${o!==1?"s":""} ago`:c<24?`${c} hour${c!==1?"s":""} ago`:g<7?`${g} day${g!==1?"s":""} ago`:new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)};return(s,t)=>{const _=H("router-link");return v(),f("div",Ke,[e("div",Xe,[t[1]||(t[1]=e("p",{class:"card-header-title orders-title"},"Recent Orders",-1)),e("div",ea,[w(_,{to:"/admin/orders",class:"button is-small is-primary"},{default:A(()=>t[0]||(t[0]=[e("span",null,"View All",-1),e("span",{class:"icon is-small"},[e("i",{class:"fas fa-arrow-right"})],-1)])),_:1})])]),e("div",aa,[r.loading?(v(),f("div",sa,t[2]||(t[2]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading orders...",-1)]))):!r.orders||r.orders.length===0?(v(),f("div",ta,t[3]||(t[3]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-shopping-cart fa-2x"})],-1),e("p",{class:"mt-2"},"No recent orders found",-1)]))):(v(),f("div",oa,[e("table",la,[t[5]||(t[5]=e("thead",null,[e("tr",null,[e("th",null,"Order ID"),e("th",null,"Customer"),e("th",null,"Total"),e("th",null,"Status"),e("th",null,"Date"),e("th")])],-1)),e("tbody",null,[(v(!0),f(O,null,L(r.orders,m=>(v(),f("tr",{key:m.id},[e("td",null,C(m.id),1),e("td",null,C(m.customerName),1),e("td",null,C(l(m.total)),1),e("td",null,[w(ee,{status:m.status,type:"order"},null,8,["status"])]),e("td",null,C(a(m.createdAt)),1),e("td",na,[w(_,{to:`/admin/orders/${m.id}`,class:"button is-small"},{default:A(()=>t[4]||(t[4]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-eye"})],-1)])),_:2},1032,["to"])])]))),128))])])]))])])}}},ia=S(ra,[["__scopeId","data-v-47ee18bc"]]),da={class:"card"},ca={class:"card-header"},ua={class:"card-header-icon"},fa={class:"card-content"},va={key:0,class:"has-text-centered py-4"},pa={key:1,class:"has-text-centered py-4"},ma={key:2},ha={class:"seller-request-header"},ga={class:"seller-info"},ya={class:"seller-name"},_a={class:"store-name"},ba={class:"request-date"},$a={class:"seller-request-actions"},ka=["onClick"],wa=["onClick"],Ca={__name:"PendingSellerRequests",props:{requests:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["approve","reject"],setup(r,{emit:l}){const a=l,s=m=>{if(!m)return"";const p=new Date(m),c=new Date-p,g=Math.round(c/1e3),d=Math.round(g/60),h=Math.round(d/60),y=Math.round(h/24);return g<60?"Just now":d<60?`${d} minute${d!==1?"s":""} ago`:h<24?`${h} hour${h!==1?"s":""} ago`:y<7?`${y} day${y!==1?"s":""} ago`:new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(p)},t=m=>{a("approve",m)},_=m=>{a("reject",m)};return(m,p)=>{const o=H("router-link");return v(),f("div",da,[e("div",ca,[p[1]||(p[1]=e("p",{class:"card-header-title requests-title"},"Pending Seller Requests",-1)),e("div",ua,[w(o,{to:"/admin/seller-requests",class:"button is-small is-primary"},{default:A(()=>p[0]||(p[0]=[e("span",null,"View All",-1),e("span",{class:"icon is-small"},[e("i",{class:"fas fa-arrow-right"})],-1)])),_:1})])]),e("div",fa,[r.loading?(v(),f("div",va,p[2]||(p[2]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading requests...",-1)]))):!r.requests||r.requests.length===0?(v(),f("div",pa,p[3]||(p[3]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-store fa-2x"})],-1),e("p",{class:"mt-2"},"No pending seller requests",-1)]))):(v(),f("div",ma,[(v(!0),f(O,null,L(r.requests,c=>(v(),f("div",{class:"seller-request",key:c.id},[e("div",ha,[e("div",ga,[e("h4",ya,C(c.userName),1),e("p",_a,C(c.companyName),1)]),e("div",ba,C(s(c.createdAt)),1)]),e("div",$a,[w(o,{to:`/admin/seller-requests/${c.id}`,class:"button is-small"},{default:A(()=>p[4]||(p[4]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-eye"})],-1),e("span",null,"View",-1)])),_:2},1032,["to"]),e("button",{class:"button is-small is-success",onClick:g=>t(c.id)},p[5]||(p[5]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-check"})],-1),e("span",null,"Approve",-1)]),8,ka),e("button",{class:"button is-small is-danger",onClick:g=>_(c.id)},p[6]||(p[6]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-times"})],-1),e("span",null,"Reject",-1)]),8,wa)])]))),128))]))])])}}},xa=S(Ca,[["__scopeId","data-v-42ba6d1e"]]),U={async getDashboardData(){var r,l;try{const a=await R.get("/api/admin/dashboard",{timeout:15e3});if(console.log("Dashboard data response:",a),console.log("Full API response:",{status:a.status,statusText:a.statusText,headers:a.headers,data:a.data}),!a.data)throw new Error("Empty response from server");return a.data.data&&a.data.data.stats&&(a.data.data.stats.revenue=Number(a.data.data.stats.revenue)),a.data.data?a.data.data:(a.data.success!==void 0,a.data)}catch(a){console.error("Error fetching dashboard data:",a);const s=((l=(r=a.response)==null?void 0:r.data)==null?void 0:l.message)||"Failed to load dashboard data from the server";throw new Error(s)}},async getSalesData(r="month"){var l,a;try{const s=await R.get(`/api/admin/dashboard/sales?period=${r}`,{timeout:15e3});if(console.log("Sales data response:",s),!s.data||!s.data.data)throw new Error("Invalid response format from server");return s.data.data}catch(s){console.error("Error fetching sales data:",s);const t=((a=(l=s.response)==null?void 0:l.data)==null?void 0:a.message)||`Failed to load sales data for period: ${r}`;throw new Error(t)}},async getOrdersByStatus(){var r,l;try{const a=await R.get("/api/admin/dashboard/orders-by-status",{timeout:15e3});if(console.log("Orders by status response:",a),!a.data||!a.data.data)throw new Error("Invalid response format from server");return a.data.data}catch(a){console.error("Error fetching orders by status:",a);const s=((l=(r=a.response)==null?void 0:r.data)==null?void 0:l.message)||"Failed to load orders by status data";throw new Error(s)}},async getRecentOrders(r=5){var l,a;try{const s=await R.get(`/api/admin/dashboard/recent-orders?limit=${r}`,{timeout:15e3});if(console.log("Recent orders response:",s),!s.data||!s.data.data)throw new Error("Invalid response format from server");return s.data.data}catch(s){console.error("Error fetching recent orders:",s);const t=((a=(l=s.response)==null?void 0:l.data)==null?void 0:a.message)||"Failed to load recent orders";throw new Error(t)}},async getPendingSellerRequests(r=5){var l,a;try{const s=await R.get(`/api/admin/dashboard/pending-seller-requests?limit=${r}`,{timeout:15e3});if(console.log("Pending seller requests response:",s),!s.data||!s.data.data)throw new Error("Invalid response format from server");return s.data.data}catch(s){console.error("Error fetching pending seller requests:",s);const t=((a=(l=s.response)==null?void 0:l.data)==null?void 0:a.message)||"Failed to load pending seller requests";throw new Error(t)}}},Da={class:"admin-dashboard"},Pa={class:"admin-page-header"},Aa={class:"level"},Sa={class:"level-right"},Na={class:"level-item"},Ta=["disabled"],qa={class:"icon"},Fa={key:0,class:"notification is-danger"},Ra={key:1,class:"has-text-centered"},Ia={key:0,class:"mt-4"},Ma={key:2},Ea={key:3},Ua={class:"columns is-multiline"},Ba={class:"column is-one-fifth"},Oa={class:"column is-one-fifth"},La={class:"column is-one-fifth"},Ha={class:"column is-one-fifth"},Va={class:"column is-one-fifth"},za={class:"columns"},Wa={class:"column is-8"},ja={class:"column is-4"},Ja={class:"columns"},Za={class:"column is-12"},Qa={class:"columns"},Ya={class:"column is-12"},Ga={class:"card quick-actions-card"},Ka={class:"card-content"},Xa={class:"buttons is-centered"},es={class:"columns"},as={class:"column is-6"},ss={class:"column is-6"},ts={__name:"Dashboard",setup(r){const l=b(!0),a=b(null),s=b(0),t=b(null),_=P(()=>!l.value&&o.value.products===0&&o.value.users===0&&o.value.orders===0&&o.value.revenue===0&&c.value.length===0&&g.value.length===0),m=P(()=>{const $=typeof o.value.revenue=="string"?Number(o.value.revenue):o.value.revenue;return isNaN($)?0:$*.15}),p=P(()=>!d.value||d.value.length===0?0:d.value.reduce(($,n)=>$+(n.value||0),0)),o=b({products:0,users:0,orders:0,revenue:0}),c=b([]),g=b([]),d=b([]),h=b("month"),y=b("month"),u=b([]),x=b([]),N=$=>{const n=typeof $=="string"?Number($):$;return isNaN(n)?(console.error("Invalid currency value:",$),"UAH 0.00"):new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH",currencyDisplay:"code"}).format(n).replace("UAH","UAH")};P(()=>x.value.length>0),P(()=>x.value.length);let D=null;const M=async()=>{D&&clearTimeout(D),t.value&&(clearInterval(t.value),t.value=null),l.value=!0,a.value=null,s.value=0,t.value=setInterval(()=>{s.value++},1e3),D=setTimeout(async()=>{var $,n;try{console.log("Fetching dashboard data...");const i=await U.getDashboardData();if(console.log("Dashboard data received:",i),console.log("Data structure check:",{hasData:!!i,dataType:typeof i,keys:i?Object.keys(i):"none"}),!i)throw new Error("No data received from server");if(i.stats){if(o.value={products:i.stats.products||0,users:i.stats.users||0,orders:i.stats.orders||0,revenue:Number(i.stats.revenue)||0},console.log("Revenue value:",o.value.revenue,typeof o.value.revenue),(typeof o.value.revenue!="number"||isNaN(o.value.revenue))&&(console.warn("Revenue is not a valid number, converting:",i.stats.revenue),o.value.revenue=0,typeof i.stats.revenue=="string")){const k=parseFloat(i.stats.revenue.replace(/[^\d.-]/g,""));isNaN(k)||(o.value.revenue=k,console.log("Parsed revenue:",k))}c.value=i.salesData||[],g.value=i.ordersByStatus||[],u.value=i.recentOrders||[],x.value=i.sellerRequests||[],d.value=(i.salesData||[]).map(k=>({...k,value:k.value*.15}))}else console.log("Trying alternative data structure"),o.value={products:i.products||0,users:i.users||0,orders:i.orders||0,revenue:Number(i.revenue)||0},console.log("Revenue value (alt):",o.value.revenue,typeof o.value.revenue),c.value=[],g.value=[],u.value=[],x.value=[],d.value=[];_.value&&console.warn("Dashboard data is empty")}catch(i){!(($=i.message)!=null&&$.includes("canceled"))&&!((n=i.message)!=null&&n.includes("aborted"))&&(console.error("Error fetching dashboard data:",i),a.value=`Failed to load dashboard data: ${i.message}. Please try again later.`)}finally{t.value&&(clearInterval(t.value),t.value=null),l.value=!1,D=null}},100)},J=()=>{D&&(clearTimeout(D),D=null),t.value&&(clearInterval(t.value),t.value=null),l.value=!1,s.value=0,setTimeout(()=>{M()},500)};let T=null;const Z=async $=>{T&&clearTimeout(T),h.value=$,T=setTimeout(async()=>{var n,i;try{const k=await U.getSalesData($);c.value=k}catch(k){!((n=k.message)!=null&&n.includes("canceled"))&&!((i=k.message)!=null&&i.includes("aborted"))&&(console.error("Error fetching sales data:",k),a.value=k.message||`Failed to load sales data for period: ${$}`)}finally{T=null}},100)};let q=null;const Q=async $=>{q&&clearTimeout(q),y.value=$,q=setTimeout(async()=>{var n,i;try{const k=await U.getSalesData($);d.value=k.map(z=>({...z,value:z.value*.15}))}catch(k){!((n=k.message)!=null&&n.includes("canceled"))&&!((i=k.message)!=null&&i.includes("aborted"))&&(console.error("Error fetching profit data:",k),a.value=k.message||`Failed to load profit data for period: ${$}`)}finally{q=null}},100)};return E(()=>{M()}),X(()=>{D&&clearTimeout(D),T&&clearTimeout(T),q&&clearTimeout(q),t.value&&(clearInterval(t.value),t.value=null)}),($,n)=>{const i=H("router-link");return v(),f("div",Da,[e("div",Pa,[e("div",Aa,[n[2]||(n[2]=e("div",{class:"level-left"},[e("div",{class:"level-item"},[e("h1",{class:"admin-page-title"},"Dashboard")])],-1)),e("div",Sa,[e("div",Na,[e("button",{class:"admin-button admin-button-primary",onClick:M,disabled:l.value},[e("span",qa,[e("i",{class:F(["fas fa-sync-alt",{"fa-spin":l.value}])},null,2)]),n[1]||(n[1]=e("span",null,"Refresh",-1))],8,Ta)])])])]),a.value?(v(),f("div",Fa,[e("button",{class:"delete",onClick:n[0]||(n[0]=k=>a.value=null)}),j(" "+C(a.value),1)])):W("",!0),l.value&&!o.value.products?(v(),f("div",Ra,[n[4]||(n[4]=e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-3x"})],-1)),n[5]||(n[5]=e("p",{class:"mt-3"},"Loading dashboard data...",-1)),n[6]||(n[6]=e("p",{class:"mt-2 has-text-grey"},"This may take a moment to fetch data from the database",-1)),s.value>10?(v(),f("div",Ia,[e("button",{class:"button is-warning",onClick:J},n[3]||(n[3]=[e("span",{class:"icon"},[e("i",{class:"fas fa-exclamation-triangle"})],-1),e("span",null,"Taking too long? Click to retry",-1)]))])):W("",!0)])):_.value?(v(),f("div",Ma,[e("div",{class:"notification is-warning"},[n[8]||(n[8]=e("p",null,[e("strong",null,"No data available."),j(" The database may be empty or there was an error retrieving the data.")],-1)),e("button",{class:"button is-small is-warning mt-2",onClick:M},n[7]||(n[7]=[e("span",{class:"icon"},[e("i",{class:"fas fa-sync-alt"})],-1),e("span",null,"Try Again",-1)]))])])):(v(),f("div",Ea,[e("div",Ua,[e("div",Ba,[w(I,{title:"Products",value:o.value.products,icon:"box",color:"is-info"},null,8,["value"])]),e("div",Oa,[w(I,{title:"Users",value:o.value.users,icon:"users",color:"is-success"},null,8,["value"])]),e("div",La,[w(I,{title:"Orders",value:o.value.orders,icon:"shopping-cart",color:"is-warning"},null,8,["value"])]),e("div",Ha,[w(I,{title:"Revenue",value:N(o.value.revenue),icon:"hryvnia",color:"is-primary"},null,8,["value"])]),e("div",Va,[w(I,{title:"Site Profit",value:N(m.value),icon:"chart-line",color:"is-link"},null,8,["value"])])]),e("div",za,[e("div",Wa,[w(be,{data:c.value,onPeriodChanged:Z},null,8,["data"])]),e("div",ja,[w(Te,{data:g.value},null,8,["data"])])]),e("div",Ja,[e("div",Za,[w(Ge,{data:d.value,"total-profit":p.value,onPeriodChanged:Q},null,8,["data","total-profit"])])]),e("div",Qa,[e("div",Ya,[e("div",Ga,[n[13]||(n[13]=e("div",{class:"card-header"},[e("p",{class:"card-header-title"},"Quick Actions")],-1)),e("div",Ka,[e("div",Xa,[w(i,{to:"/admin/products/create",class:"button is-info"},{default:A(()=>n[9]||(n[9]=[e("span",{class:"icon"},[e("i",{class:"fas fa-plus"})],-1),e("span",null,"Add Product",-1)])),_:1}),w(i,{to:"/admin/categories/create",class:"button is-success"},{default:A(()=>n[10]||(n[10]=[e("span",{class:"icon"},[e("i",{class:"fas fa-folder-plus"})],-1),e("span",null,"Add Category",-1)])),_:1}),w(i,{to:"/admin/users",class:"button is-warning"},{default:A(()=>n[11]||(n[11]=[e("span",{class:"icon"},[e("i",{class:"fas fa-user-cog"})],-1),e("span",null,"Manage Users",-1)])),_:1}),w(i,{to:"/admin/orders",class:"button is-primary"},{default:A(()=>n[12]||(n[12]=[e("span",{class:"icon"},[e("i",{class:"fas fa-shipping-fast"})],-1),e("span",null,"Process Orders",-1)])),_:1})])])])])]),e("div",es,[e("div",as,[w(ia,{orders:u.value},null,8,["orders"])]),e("div",ss,[w(xa,{requests:x.value},null,8,["requests"])])])]))])}}},is=S(ts,[["__scopeId","data-v-b105bac8"]]);export{is as default};
