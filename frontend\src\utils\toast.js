/**
 * Simple toast notification utility
 * This can be replaced with a more robust toast library if needed
 */

// Default toast options
const defaultOptions = {
  duration: 3000,
  position: 'top-right',
  type: 'info'
};

/**
 * Show a toast notification
 * @param {string} message - The message to display
 * @param {string} type - The type of toast (success, error, warning, info)
 * @param {object} options - Additional options
 */
export function showToast(message, type = 'info', options = {}) {
  // Merge options with defaults
  const settings = { ...defaultOptions, ...options, type };
  
  // Create toast element
  const toast = document.createElement('div');
  toast.className = `toast toast-${settings.type}`;
  toast.textContent = message;
  
  // Set position
  toast.style.position = 'fixed';
  
  switch (settings.position) {
    case 'top-left':
      toast.style.top = '20px';
      toast.style.left = '20px';
      break;
    case 'top-center':
      toast.style.top = '20px';
      toast.style.left = '50%';
      toast.style.transform = 'translateX(-50%)';
      break;
    case 'top-right':
      toast.style.top = '20px';
      toast.style.right = '20px';
      break;
    case 'bottom-left':
      toast.style.bottom = '20px';
      toast.style.left = '20px';
      break;
    case 'bottom-center':
      toast.style.bottom = '20px';
      toast.style.left = '50%';
      toast.style.transform = 'translateX(-50%)';
      break;
    case 'bottom-right':
      toast.style.bottom = '20px';
      toast.style.right = '20px';
      break;
    default:
      toast.style.top = '20px';
      toast.style.right = '20px';
  }
  
  // Set styles
  toast.style.zIndex = '9999';
  toast.style.padding = '12px 20px';
  toast.style.borderRadius = '4px';
  toast.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  toast.style.marginBottom = '10px';
  toast.style.minWidth = '250px';
  toast.style.maxWidth = '350px';
  toast.style.fontSize = '14px';
  toast.style.fontWeight = '500';
  toast.style.transition = 'all 0.3s ease';
  
  // Set color based on type
  switch (settings.type) {
    case 'success':
      toast.style.backgroundColor = '#48c774';
      toast.style.color = '#fff';
      break;
    case 'error':
      toast.style.backgroundColor = '#f14668';
      toast.style.color = '#fff';
      break;
    case 'warning':
      toast.style.backgroundColor = '#ffdd57';
      toast.style.color = '#333';
      break;
    case 'info':
    default:
      toast.style.backgroundColor = '#3298dc';
      toast.style.color = '#fff';
  }
  
  // Add to DOM
  document.body.appendChild(toast);
  
  // Animate in
  setTimeout(() => {
    toast.style.opacity = '1';
  }, 10);
  
  // Remove after duration
  setTimeout(() => {
    toast.style.opacity = '0';
    setTimeout(() => {
      document.body.removeChild(toast);
    }, 300);
  }, settings.duration);
}

export default {
  showToast
};
