/* ===== ADMIN DASHBOARD STYLES ===== */
/* Based on Reports page design patterns */

/* ===== DASHBOARD LAYOUT ===== */
.admin-dashboard {
  padding: var(--admin-space-2xl);
  background-color: var(--admin-bg-primary);
  min-height: 100vh;
}

.admin-dashboard .admin-page-header {
  background: var(--admin-gradient-primary);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-3xl);
  margin-bottom: var(--admin-space-2xl);
  color: white;
  box-shadow: var(--admin-shadow-lg);
}

.admin-dashboard .admin-page-title {
  font-size: var(--admin-text-4xl);
  font-weight: var(--admin-font-bold);
  color: white;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-lg);
}

/* ===== STATS CARDS GRID ===== */
.admin-dashboard .columns.is-multiline {
  margin-bottom: var(--admin-space-2xl);
}

.admin-dashboard .column.is-one-fifth {
  padding: var(--admin-space-md);
}

/* ===== STAT CARD STYLING ===== */
.admin-stat-card {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  transition: all var(--admin-transition-normal);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.admin-stat-card:hover {
  box-shadow: var(--admin-shadow-lg);
  transform: translateY(-2px);
}

.admin-stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--admin-space-lg);
}

.admin-stat-card-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--admin-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.admin-stat-card-icon.is-info {
  background: var(--admin-info-bg);
  color: var(--admin-info);
}

.admin-stat-card-icon.is-success {
  background: var(--admin-success-bg);
  color: var(--admin-success);
}

.admin-stat-card-icon.is-warning {
  background: var(--admin-warning-bg);
  color: var(--admin-warning);
}

.admin-stat-card-icon.is-primary {
  background: var(--admin-primary-bg);
  color: var(--admin-primary);
}

.admin-stat-card-icon.is-link {
  background: var(--admin-secondary-bg);
  color: var(--admin-secondary);
}

.admin-stat-card-content {
  flex: 1;
}

.admin-stat-card-title {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
  margin-bottom: var(--admin-space-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: var(--admin-font-medium);
}

.admin-stat-card-value {
  font-size: var(--admin-text-3xl);
  font-weight: var(--admin-font-bold);
  color: var(--admin-gray-900);
  line-height: var(--admin-leading-tight);
}

/* ===== CHARTS SECTION ===== */
.admin-dashboard .columns {
  margin-bottom: var(--admin-space-2xl);
}

.admin-chart-container {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  height: 100%;
}

.admin-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-space-xl);
  padding-bottom: var(--admin-space-lg);
  border-bottom: 1px solid var(--admin-border-color);
}

.admin-chart-title {
  font-size: var(--admin-text-xl);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0;
}

.admin-chart-controls {
  display: flex;
  gap: var(--admin-space-sm);
}

.admin-chart-body {
  position: relative;
  height: 300px;
}

/* ===== QUICK ACTIONS CARD ===== */
.quick-actions-card {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  overflow: hidden;
}

.quick-actions-card .card-header {
  background: var(--admin-gray-50);
  border-bottom: 1px solid var(--admin-border-color);
  padding: var(--admin-space-lg) var(--admin-space-2xl);
}

.quick-actions-card .card-header-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
}

.quick-actions-card .card-content {
  padding: var(--admin-space-2xl);
}

.quick-actions-card .buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admin-space-lg);
  justify-content: center;
}

.quick-actions-card .button {
  padding: var(--admin-space-lg) var(--admin-space-2xl);
  border-radius: var(--admin-radius-lg);
  font-weight: var(--admin-font-medium);
  transition: all var(--admin-transition-normal);
  border: 1px solid transparent;
}

.quick-actions-card .button.is-info {
  background: var(--admin-info);
  color: white;
  border-color: var(--admin-info);
}

.quick-actions-card .button.is-info:hover {
  background: var(--admin-info-dark);
  border-color: var(--admin-info-dark);
  transform: translateY(-1px);
}

.quick-actions-card .button.is-success {
  background: var(--admin-success);
  color: white;
  border-color: var(--admin-success);
}

.quick-actions-card .button.is-success:hover {
  background: var(--admin-success-dark);
  border-color: var(--admin-success-dark);
  transform: translateY(-1px);
}

.quick-actions-card .button.is-warning {
  background: var(--admin-warning);
  color: white;
  border-color: var(--admin-warning);
}

.quick-actions-card .button.is-warning:hover {
  background: var(--admin-warning-dark);
  border-color: var(--admin-warning-dark);
  transform: translateY(-1px);
}

.quick-actions-card .button.is-primary {
  background: var(--admin-primary);
  color: white;
  border-color: var(--admin-primary);
}

.quick-actions-card .button.is-primary:hover {
  background: var(--admin-primary-dark);
  border-color: var(--admin-primary-dark);
  transform: translateY(-1px);
}

/* ===== RECENT ACTIVITY SECTION ===== */
.admin-recent-activity {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  height: 100%;
}

.admin-recent-activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-space-xl);
  padding-bottom: var(--admin-space-lg);
  border-bottom: 1px solid var(--admin-border-color);
}

.admin-recent-activity-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0;
}

/* ===== LOADING STATES ===== */
.admin-dashboard .admin-loading {
  text-align: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.admin-dashboard .admin-loading i {
  font-size: 3rem;
  color: var(--admin-primary);
  margin-bottom: var(--admin-space-lg);
  animation: spin 1s linear infinite;
}

.admin-dashboard .admin-loading-text {
  font-size: var(--admin-text-lg);
  margin-bottom: var(--admin-space-sm);
}

.admin-dashboard .admin-loading-subtext {
  color: var(--admin-gray-400);
  font-size: var(--admin-text-sm);
}

/* ===== ERROR STATES ===== */
.admin-dashboard .admin-error {
  background: var(--admin-danger-bg);
  border: 1px solid var(--admin-danger-light);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-space-lg);
  color: var(--admin-danger-dark);
  margin-bottom: var(--admin-space-xl);
}

.admin-dashboard .admin-warning {
  background: var(--admin-warning-bg);
  border: 1px solid var(--admin-warning-light);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-space-lg);
  color: var(--admin-warning-dark);
  margin-bottom: var(--admin-space-xl);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .admin-dashboard .column.is-one-fifth {
    flex: none;
    width: 50%;
  }
}

@media (max-width: 768px) {
  .admin-dashboard {
    padding: var(--admin-space-lg);
  }
  
  .admin-dashboard .admin-page-header {
    padding: var(--admin-space-2xl);
  }
  
  .admin-dashboard .admin-page-title {
    font-size: var(--admin-text-2xl);
  }
  
  .admin-dashboard .column.is-one-fifth {
    width: 100%;
  }
  
  .admin-chart-body {
    height: 250px;
  }
  
  .quick-actions-card .buttons {
    flex-direction: column;
    align-items: stretch;
  }
  
  .quick-actions-card .button {
    width: 100%;
    justify-content: center;
  }
}
