<template>
  <div class="modal" :class="{ 'is-active': isOpen }">
    <div class="modal-background" @click="cancel"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">{{ title }}</p>
        <button class="delete" aria-label="close" @click="cancel"></button>
      </header>
      <section class="modal-card-body">
        <p>{{ message }}</p>
      </section>
      <footer class="modal-card-foot">
        <button
          class="button"
          :class="cancelButtonClass"
          @click="cancel">
          {{ cancelText }}
        </button>
        <button
          class="button"
          :class="confirmButtonClass"
          @click="confirm">
          {{ confirmText }}
        </button>
      </footer>
    </div>
  </div>
</template>

<script setup>
defineProps({
  isOpen: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: 'Confirm Action'
  },
  message: {
    type: String,
    default: 'Are you sure you want to perform this action?'
  },
  confirmText: {
    type: String,
    default: 'Confirm'
  },
  cancelText: {
    type: String,
    default: 'Cancel'
  },
  confirmButtonClass: {
    type: String,
    default: 'is-danger'
  },
  cancelButtonClass: {
    type: String,
    default: 'is-light'
  }
});

const emit = defineEmits(['confirm', 'cancel']);

const confirm = () => {
  emit('confirm');
};

const cancel = () => {
  emit('cancel');
};
</script>

<style scoped>
.modal-card-head {
  background-color: #f5f5f5;
}

.modal-card-title {
  font-weight: 600;
}

.modal-card-foot {
  justify-content: flex-end;
}

.button.is-danger {
  background-color: #ff3860;
}

.button.is-danger:hover {
  background-color: #ff1443;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

.button.is-warning {
  background-color: #ffdd57;
}

.button.is-warning:hover {
  background-color: #ffd83d;
}

.button.is-success {
  background-color: #23d160;
}

.button.is-success:hover {
  background-color: #20bc56;
}
</style>
