// categories.js - Vuex store module for managing categories data
import { categoriesService } from '@/admin/services/categories';

export default {
  namespaced: true,

  state: {
    categories: [],
    categoryTree: [],
    categoryMap: {}, // For quick lookups by ID
    loading: false,
    error: null,
    lastFetched: null,
    cacheTimeout: 5 * 60 * 1000, // 5 minutes in milliseconds
  },

  getters: {
    allCategories: state => state.categories,

    categoryById: state => id => state.categoryMap[id] || null,

    categoryTree: state => state.categoryTree,

    rootCategories: state => state.categories.filter(c => !c.parentId),

    isLoading: state => state.loading,

    hasError: state => !!state.error,

    errorMessage: state => state.error,

    isCacheValid: state => {
      if (!state.lastFetched) return false;
      const now = new Date().getTime();
      return (now - state.lastFetched) < state.cacheTimeout;
    }
  },

  mutations: {
    SET_CATEGORIES(state, categories) {
      state.categories = categories;

      // Update category map for quick lookups
      state.categoryMap = {};
      categories.forEach(category => {
        state.categoryMap[category.id] = category;
      });

      state.lastFetched = new Date().getTime();
    },

    SET_CATEGORY_TREE(state, tree) {
      state.categoryTree = tree;
    },

    ADD_CATEGORY(state, category) {
      state.categories.push(category);
      state.categoryMap[category.id] = category;
    },

    UPDATE_CATEGORY(state, updatedCategory) {
      const index = state.categories.findIndex(c => c.id === updatedCategory.id);
      if (index !== -1) {
        state.categories.splice(index, 1, updatedCategory);
        state.categoryMap[updatedCategory.id] = updatedCategory;
      }
    },

    REMOVE_CATEGORY(state, categoryId) {
      state.categories = state.categories.filter(c => c.id !== categoryId);
      delete state.categoryMap[categoryId];
    },

    SET_LOADING(state, isLoading) {
      state.loading = isLoading;
    },

    SET_ERROR(state, error) {
      state.error = error;
    },

    CLEAR_ERROR(state) {
      state.error = null;
    },

    INVALIDATE_CACHE(state) {
      state.lastFetched = null;
    }
  },

  actions: {
    async fetchCategories({ commit, state, getters }, params = {}) {
      // If cache is valid and no specific filters are applied, use cached data
      const hasFilters = Object.keys(params).length > 0;
      if (!hasFilters && getters.isCacheValid && state.categories.length > 0) {
        return { categories: state.categories };
      }

      commit('SET_LOADING', true);
      commit('CLEAR_ERROR');

      try {
        const response = await categoriesService.getCategories(params);
        if (response && response.categories) {
          commit('SET_CATEGORIES', response.categories);
        }
        return response;
      } catch (error) {
        const errorMessage = error.message || 'Failed to fetch categories';
        commit('SET_ERROR', errorMessage);

        // Preserve the original error structure for better error handling
        const enhancedError = new Error(errorMessage);
        enhancedError.originalError = error.originalError || error;
        throw enhancedError;
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async fetchCategoryTree({ commit, state, getters }) {
      // Use cached tree if available and valid
      if (getters.isCacheValid && state.categoryTree.length > 0) {
        return state.categoryTree;
      }

      commit('SET_LOADING', true);
      commit('CLEAR_ERROR');

      try {
        const tree = await categoriesService.getCategoryTree();
        commit('SET_CATEGORY_TREE', tree);
        return tree;
      } catch (error) {
        const errorMessage = error.message || 'Failed to fetch category tree';
        commit('SET_ERROR', errorMessage);

        // Preserve the original error structure for better error handling
        const enhancedError = new Error(errorMessage);
        enhancedError.originalError = error.originalError || error;
        throw enhancedError;
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async fetchCategoryById({ commit, getters }, id) {
      // Check if we already have this category in the store
      const existingCategory = getters.categoryById(id);
      if (existingCategory) {
        return existingCategory;
      }

      commit('SET_LOADING', true);
      commit('CLEAR_ERROR');

      try {
        const category = await categoriesService.getCategoryById(id);
        commit('UPDATE_CATEGORY', category);
        return category;
      } catch (error) {
        commit('SET_ERROR', error.message || `Failed to fetch category ${id}`);
        throw error;
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async createCategory({ commit }, categoryData) {
      commit('SET_LOADING', true);
      commit('CLEAR_ERROR');

      try {
        const response = await categoriesService.createCategory(categoryData);
        if (response.success && response.category) {
          commit('ADD_CATEGORY', response.category);
          commit('INVALIDATE_CACHE'); // Invalidate tree cache
        }
        return response;
      } catch (error) {
        commit('SET_ERROR', error.message || 'Failed to create category');
        throw error;
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async updateCategory({ commit }, { id, categoryData }) {
      commit('SET_LOADING', true);
      commit('CLEAR_ERROR');

      try {
        const response = await categoriesService.updateCategory(id, categoryData);
        if (response.success && response.category) {
          commit('UPDATE_CATEGORY', response.category);
          commit('INVALIDATE_CACHE'); // Invalidate tree cache
        }
        return response;
      } catch (error) {
        commit('SET_ERROR', error.message || `Failed to update category ${id}`);
        throw error;
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async deleteCategory({ commit }, id) {
      commit('SET_LOADING', true);
      commit('CLEAR_ERROR');

      try {
        const response = await categoriesService.deleteCategory(id);
        if (response.success) {
          commit('REMOVE_CATEGORY', id);
          commit('INVALIDATE_CACHE'); // Invalidate tree cache
        }
        return response;
      } catch (error) {
        commit('SET_ERROR', error.message || `Failed to delete category ${id}`);
        throw error;
      } finally {
        commit('SET_LOADING', false);
      }
    }
  }
};
