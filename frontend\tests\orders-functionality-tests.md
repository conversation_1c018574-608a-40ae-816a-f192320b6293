# Тестові сценарії для функціональності Orders

## Підготовка до тестування

### Перед початком тестування:
1. Перек<PERSON>найтеся, що backend сервер запущений
2. Переконайтеся, що frontend додаток запущений (`npm run dev`)
3. Увійдіть в адмін-панель з правами адміністратора
4. Відкрийте Developer Tools (F12) для моніторингу консолі

## 1. Тестування системи кешування

### Сценарій 1.1: Базове кешування
**Мета:** Перевірити, що дані кешуються та використовуються правильно

**Кроки:**
1. Перейдіть на сторінку Orders (`/admin/orders`)
2. Дочекайтеся завантаження даних
3. Перейдіть на іншу сторінку (наприклад, Products)
4. Поверніться на сторінку Orders
5. Перевірте в консолі повідомлення про використання кешу

**Очікуваний результат:**
- При поверненні на сторінку Orders дані завантажуються з кешу
- В консолі з'являється повідомлення "📦 Cache hit for: orders_v1_..."

### Сценарій 1.2: Інвалідація кешу
**Мета:** Перевірити, що кеш очищується після змін

**Кроки:**
1. Завантажте список замовлень
2. Відредагуйте будь-яке замовлення
3. Збережіть зміни
4. Поверніться до списку замовлень

**Очікуваний результат:**
- Після збереження змін кеш автоматично очищується
- Список замовлень оновлюється з сервера

### Сценарій 1.3: Примусове оновлення
**Мета:** Перевірити функцію forceRefresh

**Кроки:**
1. Відкрийте консоль браузера
2. Виконайте: `window.ordersService?.forceRefresh()`
3. Перевірте, що кеш очищено

**Очікуваний результат:**
- В консолі з'являється повідомлення про очищення кешу
- Наступний запит завантажує дані з сервера

## 2. Тестування стандартизації статусів

### Сценарій 2.1: Відображення статусів
**Мета:** Перевірити правильне відображення статусів замовлень

**Кроки:**
1. Перейдіть на сторінку Orders
2. Перевірте, що всі статуси відображаються правильно
3. Перевірте кольорове кодування статусів

**Очікуваний результат:**
- Processing - синій (is-info)
- Pending - жовтий (is-warning)
- Shipped - фіолетовий (is-primary)
- Delivered - зелений (is-success)
- Cancelled - червоний (is-danger)

### Сценарій 2.2: Зміна статусу замовлення
**Мета:** Перевірити функціональність зміни статусу

**Кроки:**
1. Знайдіть замовлення зі статусом "Pending"
2. Змініть статус на "Processing"
3. Збережіть зміни
4. Перевірте, що статус оновився

**Очікуваний результат:**
- Статус успішно змінюється
- Відображається правильний колір та текст
- В консолі логується операція зміни статусу

### Сценарій 2.3: Валідація переходів статусів
**Мета:** Перевірити валідацію допустимих переходів статусів

**Кроки:**
1. Спробуйте змінити статус "Delivered" на "Pending"
2. Перевірте, що з'являється помилка валідації

**Очікуваний результат:**
- Система блокує недопустимі переходи статусів
- Показується повідомлення про помилку

## 3. Тестування компонентів відображення

### Сценарій 3.1: Завантаження списку замовлень
**Мета:** Перевірити правильне завантаження та відображення

**Кроки:**
1. Перейдіть на `/admin/orders`
2. Дочекайтеся завантаження
3. Перевірте відображення індикатора завантаження
4. Перевірте, що дані відображаються правильно

**Очікуваний результат:**
- Показується індикатор завантаження
- Дані завантажуються та відображаються
- Пагінація працює правильно

### Сценарій 3.2: Обробка помилок
**Мета:** Перевірити відображення помилок

**Кроки:**
1. Вимкніть backend сервер
2. Спробуйте завантажити Orders
3. Перевірте відображення помилки
4. Натисніть кнопку "Retry"

**Очікуваний результат:**
- Показується повідомлення про помилку
- Кнопка "Retry" дозволяє повторити запит
- Помилка логується в консоль

### Сценарій 3.3: Фільтрація та пошук
**Мета:** Перевірити функціональність фільтрів

**Кроки:**
1. Використайте пошук по email клієнта
2. Застосуйте фільтр по статусу замовлення
3. Застосуйте фільтр по статусу оплати
4. Скиньте всі фільтри

**Очікуваний результат:**
- Фільтри працюють правильно
- Результати оновлюються в реальному часі
- Скидання фільтрів повертає всі дані

## 4. Тестування системи подій

### Сценарій 4.1: Події оновлення замовлень
**Мета:** Перевірити роботу eventBus

**Кроки:**
1. Відкрийте дві вкладки з Orders
2. В одній вкладці змініть статус замовлення
3. Перевірте, що друга вкладка оновилася

**Очікуваний результат:**
- Події правильно передаються між компонентами
- Дані синхронізуються між вкладками

### Сценарій 4.2: Логування подій
**Мета:** Перевірити детальне логування

**Кроки:**
1. Відкрийте консоль браузера
2. Виконайте різні операції з замовленнями
3. Перевірте логи в консолі

**Очікуваний результат:**
- Всі операції логуються з деталями
- Логи містять timestamp, operation, та дані

## 5. Тестування API-запитів

### Сценарій 5.1: Механізм повторних спроб
**Мета:** Перевірити retry логіку

**Кроки:**
1. Симулюйте нестабільне з'єднання
2. Спробуйте оновити статус замовлення
3. Перевірте повторні спроби в консолі

**Очікуваний результат:**
- Система автоматично повторює невдалі запити
- Логується інформація про спроби

### Сценарій 5.2: Обробка помилок API
**Мета:** Перевірити обробку різних типів помилок

**Кроки:**
1. Спробуйте операції з вимкненим backend
2. Спробуйте операції з неправильними даними
3. Перевірте повідомлення про помилки

**Очікуваний результат:**
- Показуються зрозумілі повідомлення про помилки
- Помилки правильно логуються

## 6. Тестування продуктивності

### Сценарій 6.1: Великі списки
**Мета:** Перевірити продуктивність з великою кількістю замовлень

**Кроки:**
1. Завантажте сторінку з великою кількістю замовлень
2. Прокрутіть список
3. Застосуйте фільтри
4. Перевірте швидкість відгуку

**Очікуваний результат:**
- Сторінка завантажується швидко
- Скролінг плавний
- Фільтри працюють без затримок

### Сценарій 6.2: Оптимізація рендерингу
**Мета:** Перевірити v-memo оптимізацію

**Кроки:**
1. Відкрийте Vue DevTools
2. Перевірте кількість re-renders при зміні даних
3. Порівняйте з очікуваними значеннями

**Очікуваний результат:**
- Мінімальна кількість перерендерів
- Оптимальне використання пам'яті

## 7. Тестування логування

### Сценарій 7.1: Експорт логів
**Мета:** Перевірити функціональність експорту логів

**Кроки:**
1. Виконайте кілька операцій з замовленнями
2. В консолі виконайте: `window.ordersService?.exportLogs()`
3. Перевірте завантажений файл

**Очікуваний результат:**
- Файл логів завантажується
- Містить всі операції з деталями
- Формат JSON правильний

### Сценарій 7.2: Діагностична інформація
**Мета:** Перевірити діагностичні функції

**Кроки:**
1. В консолі виконайте: `window.ordersService?.getDiagnostics()`
2. Перевірте отриману інформацію

**Очікуваний результат:**
- Показується статистика сесії
- Інформація про кеш
- Кількість логів по рівнях

## Критерії успішного проходження тестів

✅ **Всі тести пройдені успішно, якщо:**
- Система кешування працює правильно
- Статуси відображаються та змінюються коректно
- Компоненти завантажуються без помилок
- Події передаються між компонентами
- API запити виконуються з retry логікою
- Продуктивність відповідає очікуванням
- Логування працює повністю

❌ **Тести провалені, якщо:**
- Є помилки компіляції або runtime помилки
- Дані не завантажуються або відображаються неправильно
- Кеш не працює або не очищається
- Статуси відображаються неправильно
- Події не передаються
- Продуктивність неприйнятна
