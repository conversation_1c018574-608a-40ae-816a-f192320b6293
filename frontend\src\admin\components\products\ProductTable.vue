<template>
  <div class="admin-product-table">
    <div class="table-container">
      <table class="table">
        <thead>
          <tr>
            <th style="width: 100px;">Image</th>
            <th>Name</th>
            <th>Category</th>
            <th>Price</th>
            <th>Stock</th>
            <th>Status</th>
            <th style="width: 140px;">Actions</th>
          </tr>
        </thead>
        <tbody v-if="!loading && products.length > 0">
          <tr v-for="product in products" :key="product.id">
            <td>
              <figure class="image is-64x64">
                <img
                  :src="getProductImageUrl(product)"
                  :alt="product.name"
                  class="product-thumbnail"
                  @error="handleImageError" />
              </figure>
            </td>
            <td>
              <div>
                <strong>{{ product.name }}</strong>
                <br>
                <small class="has-text-grey">{{ product.slug }}</small>
              </div>
            </td>
            <td>{{ product.categoryName || 'Unknown' }}</td>
            <td>{{ formatCurrency(product.priceAmount || product.price || product.priceValue) }}</td>
            <td>
              <span class="tag" :class="getStockClass(product.stock || product.stockQuantity || 0)">
                {{ product.stock || product.stockQuantity || 0 }}
              </span>
            </td>
            <td>
              <status-badge :status="product.status" type="product" />
            </td>
            <td style="width: 140px;">
              <div class="admin-product-actions">
                <button
                  class="button is-info"
                  @click="$emit('view', product)"
                  title="View Product">
                  <i class="fas fa-eye"></i>
                </button>
                <button
                  class="button is-primary"
                  @click="$emit('edit', product)"
                  title="Edit Product">
                  <i class="fas fa-edit"></i>
                </button>
                <button
                  class="button is-danger"
                  @click="$emit('delete', product)"
                  title="Delete Product">
                  <span class="icon is-small"><i class="fas fa-trash"></i></span>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else-if="loading">
          <tr>
            <td colspan="7" class="has-text-centered">
              <div class="loader-wrapper">
                <div class="loader is-loading"></div>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else>
          <tr>
            <td colspan="7" class="has-text-centered">
              No products found.
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import StatusBadge from '@/admin/components/common/StatusBadge.vue';

const props = defineProps({
  products: {
    type: Array,
    required: true
  },
  categories: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

defineEmits(['view', 'edit', 'delete']);

const formatCurrency = (value) => {
  if (!value || isNaN(value)) return '₴0.00';

  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH',
    minimumFractionDigits: 2
  }).format(value);
};

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    // Handle backend date format: "2025-06-02 20:35:40.231835+03"
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('uk-UA', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  } catch (e) {
    console.error('Error formatting date:', e, dateString);
    return dateString;
  }
};

// Function to get category name by ID
const getCategoryName = (categoryId) => {
  if (!categoryId || !props.categories || props.categories.length === 0) {
    return 'Unknown';
  }

  // Recursive function to search in nested categories
  const findCategory = (categories, id) => {
    for (const category of categories) {
      if (category.id === id) {
        return category.name;
      }
      if (category.children && category.children.length > 0) {
        const found = findCategory(category.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  return findCategory(props.categories, categoryId) || 'Unknown';
};

// Function to get display category name with logging
// Removed - using direct categoryName in template

// Function to get stock level class
const getStockClass = (stock) => {
  const stockValue = parseInt(stock) || 0;

  if (stockValue <= 0) {
    return 'is-danger'; // Out of stock
  } else if (stockValue <= 10) {
    return 'is-warning'; // Low stock
  } else {
    return 'is-success'; // In stock
  }
};

// Function to get product image URL with fallback
const getProductImageUrl = (product) => {
  // Стандартизована логіка отримання зображення продукту
  // Пріоритет: imageUrl -> mainImageUrl -> image -> placeholder
  let imageUrl = null;

  // Спочатку перевіряємо основні поля зображень
  if (product.imageUrl && product.imageUrl.trim() !== '') {
    imageUrl = product.imageUrl;
  } else if (product.mainImageUrl && product.mainImageUrl.trim() !== '') {
    imageUrl = product.mainImageUrl;
  } else if (product.image && product.image.trim() !== '') {
    imageUrl = product.image;
  }

  // Якщо знайшли зображення, повертаємо його
  if (imageUrl) {
    return imageUrl;
  }

  // Повертаємо оптимізований placeholder
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjhGOUZBIiByeD0iOCIvPgo8cGF0aCBkPSJNNDAgNTZDNDkuOTQxMSA1NiA1OCA0Ny45NDExIDU4IDM4QzU4IDI4LjA1ODkgNDkuOTQxMSAyMCA0MCAyMEMzMC4wNTg5IDIwIDIyIDI4LjA1ODkgMjIgMzhDMjIgNDcuOTQxMSAzMC4wNTg5IDU2IDQwIDU2WiIgc3Ryb2tlPSIjRDFEMUQxIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPHBhdGggZD0iTTMwIDMySDMyVjM0SDMwVjMyWiIgZmlsbD0iI0QxRDFEMSIvPgo8cGF0aCBkPSJNNDggMzJINTBWMzRINDhWMzJaIiBmaWxsPSIjRDFEMUQxIi8+CjxwYXRoIGQ9Ik0zMiA0NEMzMiA0Ni4yMDkxIDMzLjc5MDkgNDggMzYgNDhDMzguMjA5MSA0OCA0MCA0Ni4yMDkxIDQwIDQ0SDMyWiIgZmlsbD0iI0QxRDFEMSIvPgo8L3N2Zz4K';
};

// Function to handle image loading errors
const handleImageError = (event) => {
  // Запобігаємо повторним помилкам завантаження
  if (event.target.dataset.errorHandled) return;

  event.target.dataset.errorHandled = 'true';
  event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjhGOUZBIiByeD0iOCIvPgo8cGF0aCBkPSJNNDAgNTZDNDkuOTQxMSA1NiA1OCA0Ny45NDExIDU4IDM4QzU4IDI4LjA1ODkgNDkuOTQxMSAyMCA0MCAyMEMzMC4wNTg5IDIwIDIyIDI4LjA1ODkgMjIgMzhDMjIgNDcuOTQxMSAzMC4wNTg5IDU2IDQwIDU2WiIgc3Ryb2tlPSIjRDFEMUQxIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPHBhdGggZD0iTTMwIDMySDMyVjM0SDMwVjMyWiIgZmlsbD0iI0QxRDFEMSIvPgo8cGF0aCBkPSJNNDggMzJINTBWMzRINDhWMzJaIiBmaWxsPSIjRDFEMUQxIi8+CjxwYXRoIGQ9Ik0zMiA0NEMzMiA0Ni4yMDkxIDMzLjc5MDkgNDggMzYgNDhDMzguMjA5MSA0OCA0MCA0Ni4yMDkxIDQwIDQ0SDMyWiIgZmlsbD0iI0QxRDFEMSIvPgo8L3N2Zz4K';

  // Додаємо клас для стилізації помилкових зображень
  event.target.classList.add('image-error');
};

// Functions available in template automatically
</script>

<style scoped>
.product-thumbnail {
  object-fit: cover;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.product-thumbnail:hover {
  border-color: #3273dc;
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.product-thumbnail.image-error {
  opacity: 0.6;
  filter: grayscale(100%);
}

.image.is-64x64 {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loader-wrapper {
  padding: 2rem;
  display: flex;
  justify-content: center;
}

.loader {
  height: 80px;
  width: 80px;
}

/* Improved table styling */
.table td {
  vertical-align: middle;
}

.table td:first-child {
  text-align: center;
}

/* Better button spacing */
.buttons.are-small.has-addons .button {
  margin-right: 0;
}

/* Product name styling */
.table td strong {
  font-weight: 600;
}

.table td small {
  font-size: 0.75rem;
  opacity: 0.8;
}
</style>
