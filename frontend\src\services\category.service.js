import apiClient from './api.service';

class CategoryService {
  // Get all categories with optional filtering and pagination

  async getAll(params = {})
  {
    try 
    {
      return await apiClient.get('/categories/all', { params });
    } 
    catch (error) 
    {
      console.error('Error fetching categories:', error);

      // If the endpoint doesn't exist, try the admin endpoint
      if (error.response && error.response.status === 404) 
      {
        console.warn('Categories endpoint not found, trying admin endpoint');
        return await apiClient.get('/admin/categories', { params });
      }

      throw error;
    }
  }

  async getAllRootCategories(params = {}) 
  {
    try 
    {
      return await apiClient.get('/categories/root', { params });
    } 
    catch (error) 
    {
      console.error('Error fetching categories:', error);

      // If the endpoint doesn't exist, try the admin endpoint
      if (error.response && error.response.status === 404) 
      {
        console.warn('Categories endpoint not found, trying admin endpoint');
        return await apiClient.get('/admin/categories', { params });
      }

      throw error;
    }
  }

  async getSubCategories(slug, params = {}) 
  {
    try 
    {
      return await apiClient.get(`/categories/${slug}/subcategories`, { params });
    } 
    catch (error) 
    {
      console.error('Error fetching subcategories:', error);

      // If the endpoint doesn't exist, try the admin endpoint
      if (error.response && error.response.status === 404) 
      {
        console.warn('subcategories endpoint not found, trying admin endpoint');
        return await apiClient.get('/admin/categories', { params });
      }

      throw error;
    }
  }

  async getProducts(slug, params = {}) 
  {
    try 
    {
      return await apiClient.get(`/categories/${slug}/products`, { params });
    } 
    catch (error) 
    {
      console.error('Error fetching categories:', error);

      // If the endpoint doesn't exist, try the admin endpoint
      if (error.response && error.response.status === 404) 
      {
        console.warn('Categories endpoint not found, trying admin endpoint');
        return await apiClient.get(`/admin/categories/${slug}/products`, { params });
      }

      throw error;
    }
  }

  // Get category by ID
  async getById(id) 
  {
    try 
    {
      return await apiClient.get(`/categories/${id}`);
    } 
    catch (error) 
    {
      console.error(`Error fetching category ${id}:`, error);

      // If the endpoint doesn't exist, try the admin endpoint
      if (error.response && error.response.status === 404) 
      {
        console.warn('Category endpoint not found, trying admin endpoint');
        return await apiClient.get(`/admin/categories/${id}`);
      }

      throw error;
    }
  }

  async getBySlug(slug) 
  {
    try 
    {
      return await apiClient.get(`/categories/slug/${slug}`);
    } 
    catch (error) 
    {
      console.error(`Error fetching category ${slug}:`, error);
      throw error;
    }
  }

  // Create new category
  async create(categoryData) {
    try {
      return await apiClient.post('/categories', categoryData);
    } catch (error) {
      console.error('Error creating category:', error);

      // If the endpoint doesn't exist, try the admin endpoint
      if (error.response && error.response.status === 404) {
        console.warn('Categories endpoint not found, trying admin endpoint');
        return await apiClient.post('/admin/categories', categoryData);
      }

      throw error;
    }
  }

  // Update existing category
  async update(id, categoryData) {
    try {
      return await apiClient.put(`/categories/${id}`, categoryData);
    } catch (error) {
      console.error(`Error updating category ${id}:`, error);

      // If the endpoint doesn't exist, try the admin endpoint
      if (error.response && error.response.status === 404) {
        console.warn('Category endpoint not found, trying admin endpoint');
        return await apiClient.put(`/admin/categories/${id}`, categoryData);
      }

      throw error;
    }
  }

  // Delete category
  async delete(id) {
    try {
      return await apiClient.delete(`/categories/${id}`);
    } catch (error) {
      console.error(`Error deleting category ${id}:`, error);

      // If the endpoint doesn't exist, try the admin endpoint
      if (error.response && error.response.status === 404) {
        console.warn('Category endpoint not found, trying admin endpoint');
        return await apiClient.delete(`/admin/categories/${id}`);
      }

      throw error;
    }
  }

  // Get category statistics
  async getStats() {
    try {
      return await apiClient.get('/categories/stats');
    } catch (error) {
      console.error('Error fetching category stats:', error);

      // If the endpoint doesn't exist, try the admin endpoint
      if (error.response && error.response.status === 404) {
        console.warn('Category stats endpoint not found, trying admin endpoint');
        return await apiClient.get('/admin/categories/stats');
      }

      throw error;
    }
  }
}

export default new CategoryService();
