﻿﻿using AutoMapper;
using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.CartItem;

public class StoreCartItemCommandHandler : IRequestHandler<StoreCartItemCommand, Guid>
{
    private readonly ICartItemRepository _cartItemRepository;
    private readonly ICartRepository _cartRepository;
    private readonly IMapper _mapper;

    public StoreCartItemCommandHandler(
        ICartItemRepository cartItemRepository,
        ICartRepository cartRepository,
        IMapper mapper)
    {
        _cartItemRepository = cartItemRepository;
        _cartRepository = cartRepository;
        _mapper = mapper;
    }

    public async Task<Guid> Handle(StoreCartItemCommand request, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи існує вже такий товар у кошику
        var existingItem = await _cartItemRepository.GetByCartIdAndProductIdAsync(
            request.CartId, 
            request.ProductId, 
            cancellationToken);

        if (existingItem != null)
        {
            // Якщо товар вже є в кошику, оновлюємо кількість
            existingItem.Update(existingItem.Quantity + request.Quantity);
            await _cartItemRepository.UpdateAsync(existingItem, cancellationToken);
            
            // Оновлюємо дату оновлення кошика
            var cart = await _cartRepository.GetByIdAsync(request.CartId, cancellationToken);
            if (cart != null)
            {
                cart.Update();
                await _cartRepository.UpdateAsync(cart, cancellationToken);
            }
            
            return existingItem.Id;
        }
        else
        {
            // Якщо товару немає в кошику, додаємо новий
            var cartItem = _mapper.Map<Domain.Entities.CartItem>(request);
            await _cartItemRepository.AddAsync(cartItem, cancellationToken);
            
            // Оновлюємо дату оновлення кошика
            var cart = await _cartRepository.GetByIdAsync(request.CartId, cancellationToken);
            if (cart != null)
            {
                cart.Update();
                await _cartRepository.UpdateAsync(cart, cancellationToken);
            }
            
            return cartItem.Id;
        }
    }
}
