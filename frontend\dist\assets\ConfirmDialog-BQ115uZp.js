import{_ as i,c as r,o as d,a as t,t as a,n as c}from"./index-BKy0rL_2.js";const m={class:"modal-card"},u={class:"modal-card-head"},f={class:"modal-card-title"},C={class:"modal-card-body"},g={class:"modal-card-foot"},y={__name:"ConfirmDialog",props:{isOpen:{type:Boolean,required:!0},title:{type:String,default:"Confirm Action"},message:{type:String,default:"Are you sure you want to perform this action?"},confirmText:{type:String,default:"Confirm"},cancelText:{type:String,default:"Cancel"},confirmButtonClass:{type:String,default:"is-danger"},cancelButtonClass:{type:String,default:"is-light"}},emits:["confirm","cancel"],setup(e,{emit:s}){const n=s,l=()=>{n("confirm")},o=()=>{n("cancel")};return(b,h)=>(d(),r("div",{class:c(["modal",{"is-active":e.isOpen}])},[t("div",{class:"modal-background",onClick:o}),t("div",m,[t("header",u,[t("p",f,a(e.title),1),t("button",{class:"delete","aria-label":"close",onClick:o})]),t("section",C,[t("p",null,a(e.message),1)]),t("footer",g,[t("button",{class:c(["button",e.cancelButtonClass]),onClick:o},a(e.cancelText),3),t("button",{class:c(["button",e.confirmButtonClass]),onClick:l},a(e.confirmText),3)])])],2))}},_=i(y,[["__scopeId","data-v-8d2b7a1b"]]);export{_ as C};
