<template>
  <div class="card">
    <div class="card-header">
      <p class="card-header-title status-title">Orders by Status</p>
    </div>
    <div class="card-content">
      <div v-if="loading" class="has-text-centered py-6">
        <span class="icon is-large">
          <i class="fas fa-spinner fa-pulse fa-2x"></i>
        </span>
        <p class="mt-2">Loading chart data...</p>
      </div>
      <div v-else-if="!data || data.length === 0" class="has-text-centered py-6">
        <span class="icon is-large">
          <i class="fas fa-chart-pie fa-2x"></i>
        </span>
        <p class="mt-2">No order data available</p>
      </div>
      <div v-else>
        <div class="chart-container">
          <canvas ref="chartCanvas"></canvas>
        </div>
        <div class="status-legend">
          <div
            v-for="(item, index) in data"
            :key="item.status"
            class="legend-item">
            <span
              class="legend-color"
              :style="{ backgroundColor: chartColors[index % chartColors.length] }">
            </span>
            <span class="legend-label">{{ item.status }}</span>
            <span class="legend-value">{{ item.count }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import Chart from 'chart.js/auto';

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const chartCanvas = ref(null);
const chart = ref(null);
const loading = ref(false);

// Chart colors
const chartColors = [
  '#ff7700', // Primary (Orange)
  '#3298dc', // Info (Blue)
  '#48c774', // Success (Green)
  '#ffdd57', // Warning (Yellow)
  '#ff3860', // Danger (Red)
  '#9c27b0', // Purple
  '#00d1b2', // Turquoise
  '#f39c12', // Orange
  '#8e44ad', // Violet
  '#3498db'  // Light Blue
];

// Initialize chart
const createChart = () => {
  if (!chartCanvas.value) return;

  const ctx = chartCanvas.value.getContext('2d');

  // Destroy existing chart if it exists
  if (chart.value) {
    chart.value.destroy();
  }

  // Extract labels and data from props
  const labels = props.data.map(item => item.status);
  const data = props.data.map(item => item.count);

  // Create chart
  chart.value = new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: labels,
      datasets: [{
        data: data,
        backgroundColor: chartColors.slice(0, props.data.length),
        borderColor: '#ffffff',
        borderWidth: 2,
        hoverOffset: 10
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      cutout: '70%',
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleFont: {
            size: 14,
            weight: 'bold'
          },
          bodyFont: {
            size: 13,
            weight: 'bold'
          },
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          padding: 12,
          cornerRadius: 6,
          boxPadding: 6,
          callbacks: {
            label: function(context) {
              const label = context.label || '';
              const value = context.raw || 0;
              const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
              const percentage = Math.round((value / total) * 100);
              return `${label}: ${value} (${percentage}%)`;
            }
          }
        }
      }
    }
  });
};

const updateChart = () => {
  if (!chart.value) return;

  // Update chart data
  chart.value.data.labels = props.data.map(item => item.status);
  chart.value.data.datasets[0].data = props.data.map(item => item.count);
  chart.value.update();
};

// Watch for data changes
watch(() => props.data, () => {
  loading.value = false;

  // Wait for next tick to ensure DOM is updated
  setTimeout(() => {
    if (props.data && props.data.length > 0) {
      if (chart.value) {
        updateChart();
      } else {
        createChart();
      }
    }
  }, 0);
}, { deep: true });

onMounted(() => {
  if (props.data && props.data.length > 0) {
    createChart();
  }
});
</script>

<style scoped>
.card {
  height: 100%;
  transition: box-shadow 0.3s;
}

.card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-header-title {
  font-weight: 600;
}

.chart-container {
  height: 200px;
  position: relative;
  margin-bottom: 1rem;
}

.status-legend {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
}

.legend-color {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.legend-label {
  flex: 1;
  font-weight: 500;
}

.legend-value {
  font-weight: 600;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.mt-2 {
  margin-top: 0.5rem;
}
</style>
