<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Data Processing</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 3px; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 3px; }
        .data-preview { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>Test API Data Processing</h1>
    
    <div class="test-section">
        <h2>Financial Report Test</h2>
        <button onclick="testFinancialReport()">Test Financial Report</button>
        <div id="financial-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Sales Report Test</h2>
        <button onclick="testSalesReport()">Test Sales Report</button>
        <div id="sales-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Export Test</h2>
        <button onclick="testExport()">Test Export</button>
        <div id="export-result"></div>
    </div>

    <script type="module">
        // Import the reports service
        import reportsService from './frontend/src/services/reports.service.js';
        
        window.reportsService = reportsService; // Make available globally for buttons
        
        function showResult(containerId, success, message, data = null) {
            const container = document.getElementById(containerId);
            container.innerHTML = `
                <div class="${success ? 'success' : 'error'}">
                    ${message}
                </div>
                ${data ? `<div class="data-preview">${JSON.stringify(data, null, 2)}</div>` : ''}
            `;
        }
        
        window.testFinancialReport = async function() {
            try {
                const params = {
                    startDate: new Date('2025-05-27'),
                    endDate: new Date('2025-06-26')
                };
                
                const data = await reportsService.getFinancialReport(params);
                
                // Test formatting of metrics
                if (data.metrics && data.metrics.items) {
                    const formattedMetrics = data.metrics.items.map(metric => ({
                        ...metric,
                        formattedValue: reportsService.formatMetricValue(metric.value, metric.type)
                    }));
                    
                    showResult('financial-result', true, 
                        `✅ Financial report loaded successfully! Found ${data.metrics.items.length} metrics.`,
                        { 
                            originalMetrics: data.metrics.items.slice(0, 3),
                            formattedMetrics: formattedMetrics.slice(0, 3)
                        }
                    );
                } else {
                    showResult('financial-result', false, '❌ Invalid data structure - missing metrics');
                }
            } catch (error) {
                showResult('financial-result', false, `❌ Error: ${error.message}`);
            }
        };
        
        window.testSalesReport = async function() {
            try {
                const params = {
                    startDate: new Date('2025-05-27'),
                    endDate: new Date('2025-06-26')
                };
                
                const data = await reportsService.getSalesReport(params);
                
                if (data.metrics && data.metrics.items) {
                    const formattedMetrics = data.metrics.items.map(metric => ({
                        ...metric,
                        formattedValue: reportsService.formatMetricValue(metric.value, metric.type)
                    }));
                    
                    showResult('sales-result', true, 
                        `✅ Sales report loaded successfully! Found ${data.metrics.items.length} metrics.`,
                        { 
                            originalMetrics: data.metrics.items.slice(0, 3),
                            formattedMetrics: formattedMetrics.slice(0, 3)
                        }
                    );
                } else {
                    showResult('sales-result', false, '❌ Invalid data structure - missing metrics');
                }
            } catch (error) {
                showResult('sales-result', false, `❌ Error: ${error.message}`);
            }
        };
        
        window.testExport = async function() {
            try {
                const params = {
                    startDate: new Date('2025-05-27'),
                    endDate: new Date('2025-06-26')
                };
                
                // Test filename generation
                const filename = reportsService.generateFilename('financial', 'excel');
                const extension = reportsService.getFileExtension('excel');
                
                showResult('export-result', true, 
                    `✅ Export methods working!`,
                    { 
                        generatedFilename: filename,
                        fileExtension: extension,
                        formattedCurrency: reportsService.formatCurrency(1234.56),
                        formattedNumber: reportsService.formatNumber(1234567),
                        formattedPercentage: reportsService.formatPercentage(25.5),
                        formattedDate: reportsService.formatDate(new Date()),
                        formattedFileSize: reportsService.formatFileSize(1048576)
                    }
                );
            } catch (error) {
                showResult('export-result', false, `❌ Error: ${error.message}`);
            }
        };
        
        console.log('API Data Test page loaded. Reports service available:', !!reportsService);
    </script>
</body>
</html>
