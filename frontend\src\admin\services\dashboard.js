import api from '@/services/api';

export const dashboardService = {
  async getDashboardData() {
    try {
      // Add timeout to prevent hanging requests
      const response = await api.get('/api/admin/dashboard', { timeout: 15000 });

      // Log the response for debugging
      console.log('Dashboard data response:', response);

      // Log the full response for debugging
      console.log('Full API response:', {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data
      });

      // Check if the response has the expected structure
      if (!response.data) {
        throw new Error('Empty response from server');
      }

      // Ensure revenue is a number (not a string)
      if (response.data.data && response.data.data.stats) {
        response.data.data.stats.revenue = Number(response.data.data.stats.revenue);
      }

      // Handle different response structures
      if (response.data.data) {
        // Standard ApiResponse<T> structure
        return response.data.data;
      } else if (response.data.success !== undefined) {
        // ApiResponse structure but data is directly in response.data
        return response.data;
      } else {
        // Direct data structure
        return response.data;
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);

      // Throw a more descriptive error instead of returning mock data
      const errorMessage = error.response?.data?.message || 'Failed to load dashboard data from the server';
      throw new Error(errorMessage);
    }
  },

  async getSalesData(period = 'month') {
    try {
      // Add timeout to prevent hanging requests
      const response = await api.get(`/api/admin/dashboard/sales?period=${period}`, { timeout: 15000 });

      // Log the response for debugging
      console.log('Sales data response:', response);

      // Check if the response has the expected structure
      if (!response.data || !response.data.data) {
        throw new Error('Invalid response format from server');
      }

      return response.data.data;
    } catch (error) {
      console.error('Error fetching sales data:', error);

      // Throw a more descriptive error instead of returning mock data
      const errorMessage = error.response?.data?.message || `Failed to load sales data for period: ${period}`;
      throw new Error(errorMessage);
    }
  },

  async getOrdersByStatus() {
    try {
      // Add timeout to prevent hanging requests
      const response = await api.get('/api/admin/dashboard/orders-by-status', { timeout: 15000 });

      // Log the response for debugging
      console.log('Orders by status response:', response);

      // Check if the response has the expected structure
      if (!response.data || !response.data.data) {
        throw new Error('Invalid response format from server');
      }

      return response.data.data;
    } catch (error) {
      console.error('Error fetching orders by status:', error);

      // Throw a more descriptive error instead of returning mock data
      const errorMessage = error.response?.data?.message || 'Failed to load orders by status data';
      throw new Error(errorMessage);
    }
  },

  async getRecentOrders(limit = 5) {
    try {
      // Add timeout to prevent hanging requests
      const response = await api.get(`/api/admin/dashboard/recent-orders?limit=${limit}`, { timeout: 15000 });

      // Log the response for debugging
      console.log('Recent orders response:', response);

      // Check if the response has the expected structure
      if (!response.data || !response.data.data) {
        throw new Error('Invalid response format from server');
      }

      return response.data.data;
    } catch (error) {
      console.error('Error fetching recent orders:', error);

      // Throw a more descriptive error instead of returning mock data
      const errorMessage = error.response?.data?.message || 'Failed to load recent orders';
      throw new Error(errorMessage);
    }
  },

  async getPendingSellerRequests(limit = 5) {
    try {
      // Add timeout to prevent hanging requests
      const response = await api.get(`/api/admin/dashboard/pending-seller-requests?limit=${limit}`, { timeout: 15000 });

      // Log the response for debugging
      console.log('Pending seller requests response:', response);

      // Check if the response has the expected structure
      if (!response.data || !response.data.data) {
        throw new Error('Invalid response format from server');
      }

      return response.data.data;
    } catch (error) {
      console.error('Error fetching pending seller requests:', error);

      // Throw a more descriptive error instead of returning mock data
      const errorMessage = error.response?.data?.message || 'Failed to load pending seller requests';
      throw new Error(errorMessage);
    }
  }
};
