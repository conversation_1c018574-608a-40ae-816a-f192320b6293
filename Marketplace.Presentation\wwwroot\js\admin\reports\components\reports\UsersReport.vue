<template>
  <div class="users-report">
    <!-- Header -->
    <div class="report-header">
      <div class="header-content">
        <h1 class="report-title">
          <i class="fas fa-users"></i>
          Users Report
        </h1>
        <p class="report-description">
          Comprehensive analysis of user activity, registration trends, and engagement metrics
        </p>
      </div>
      
      <!-- Export Actions -->
      <div class="header-actions">
        <ExportButtons 
          :report-type="'users'"
          :filters="filters"
          :disabled="isLoading"
        />
      </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
      <ReportFilters 
        :show-report-type="false"
        :report-type="'users'"
        @filters-changed="handleFiltersChanged"
      />
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading users data...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <div class="error-content">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Failed to Load Users Data</h3>
        <p>{{ error }}</p>
        <button @click="refreshData" class="retry-btn">
          <i class="fas fa-redo"></i>
          Try Again
        </button>
      </div>
    </div>

    <!-- Report Content -->
    <div v-else-if="hasData" class="report-content">
      <!-- Key Metrics -->
      <div class="metrics-section">
        <h3 class="section-title">
          <i class="fas fa-chart-line"></i>
          User Engagement Metrics
        </h3>
        
        <div class="metrics-grid">
          <MetricCard
            v-for="metric in userMetrics"
            :key="metric.key"
            :metric="metric"
            :loading="isLoading"
          />
        </div>
      </div>

      <!-- User Registration Trend -->
      <div class="chart-section">
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">User Registration Trend</h3>
            <div class="chart-controls">
              <select v-model="chartPeriod" @change="updateChart" class="chart-select">
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>
          </div>
          
          <div class="chart-content">
            <canvas ref="registrationChartCanvas" id="registration-chart"></canvas>
          </div>
        </div>
      </div>

      <!-- User Types Distribution -->
      <div class="distribution-section">
        <div class="distribution-container">
          <div class="distribution-header">
            <h3 class="distribution-title">Users by Type</h3>
          </div>
          
          <div class="distribution-content">
            <div class="chart-wrapper">
              <canvas ref="userTypesChartCanvas" id="user-types-chart"></canvas>
            </div>
            
            <div class="distribution-legend">
              <div
                v-for="(item, index) in userTypesData"
                :key="item.type"
                class="legend-item"
              >
                <div
                  class="legend-color"
                  :style="{ backgroundColor: getUserTypeColor(index) }"
                ></div>
                <div class="legend-info">
                  <div class="legend-label">{{ item.type }}</div>
                  <div class="legend-value">
                    {{ formatNumber(item.count) }} users ({{ item.percentage.toFixed(1) }}%)
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Activity Heatmap -->
      <div class="heatmap-section">
        <div class="heatmap-container">
          <div class="heatmap-header">
            <h3 class="heatmap-title">User Activity Heatmap</h3>
            <div class="heatmap-legend">
              <span class="legend-label">Less</span>
              <div class="heatmap-scale">
                <div class="scale-item" v-for="i in 5" :key="i" :class="`level-${i}`"></div>
              </div>
              <span class="legend-label">More</span>
            </div>
          </div>
          
          <div class="heatmap-content">
            <div class="heatmap-grid">
              <div class="heatmap-days">
                <div class="day-label" v-for="day in weekDays" :key="day">{{ day }}</div>
              </div>
              <div class="heatmap-hours">
                <div
                  v-for="hour in 24"
                  :key="hour"
                  class="hour-column"
                >
                  <div class="hour-label">{{ hour - 1 }}:00</div>
                  <div
                    v-for="day in 7"
                    :key="day"
                    class="activity-cell"
                    :class="getActivityLevel(day - 1, hour - 1)"
                    :title="getActivityTooltip(day - 1, hour - 1)"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- User Insights -->
      <div class="insights-section">
        <h3 class="section-title">
          <i class="fas fa-lightbulb"></i>
          User Insights
        </h3>
        
        <div class="insights-grid">
          <div
            v-for="insight in userInsights"
            :key="insight.id"
            class="insight-card"
            :class="insight.type"
          >
            <div class="insight-icon">
              <i :class="insight.icon"></i>
            </div>
            <div class="insight-content">
              <h4 class="insight-title">{{ insight.title }}</h4>
              <p class="insight-description">{{ insight.description }}</p>
              <div v-if="insight.action" class="insight-action">
                <button @click="handleInsightAction(insight)" class="action-btn">
                  {{ insight.action.label }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Top Users Table -->
      <div class="table-section">
        <ReportTable
          :data="tableData"
          :columns="tableColumns"
          :loading="isLoading"
          title="Top Active Users"
          :exportable="true"
          @row-click="viewUserDetails"
        />
      </div>

      <!-- Recent Registrations -->
      <div class="recent-section">
        <div class="recent-container">
          <div class="recent-header">
            <h3 class="recent-title">
              <i class="fas fa-user-plus"></i>
              Recent Registrations
            </h3>
            <span class="recent-count">{{ recentRegistrations.length }} new users today</span>
          </div>
          <div class="recent-content">
            <div
              v-for="user in recentRegistrations.slice(0, 10)"
              :key="user.id"
              class="recent-user"
            >
              <div class="user-avatar">
                <img v-if="user.avatar" :src="user.avatar" :alt="user.name" />
                <i v-else class="fas fa-user"></i>
              </div>
              <div class="user-info">
                <div class="user-name">{{ user.name }}</div>
                <div class="user-email">{{ user.email }}</div>
                <div class="user-time">{{ formatTimeAgo(user.registeredAt) }}</div>
              </div>
              <div class="user-type">
                <span :class="['type-badge', user.type]">{{ user.type }}</span>
              </div>
            </div>
            <div v-if="recentRegistrations.length > 10" class="more-users">
              +{{ recentRegistrations.length - 10 }} more users registered today
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="empty-state">
      <div class="empty-content">
        <i class="fas fa-users"></i>
        <h3>No Users Data Available</h3>
        <p>Try adjusting your filters or date range to see users data.</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { reportsService, formatters } from '../../../../../src/services/reports'
import { Chart } from 'chart.js'
import ReportFilters from '../shared/ReportFilters.vue'
import MetricCard from '../shared/MetricCard.vue'
import ExportButtons from '../shared/ExportButtons.vue'
import ReportTable from '../shared/ReportTable.vue'

export default {
  name: 'UsersReport',
  components: {
    ReportFilters,
    MetricCard,
    ExportButtons,
    ReportTable
  },
  setup() {
    // Reactive references
    const registrationChartCanvas = ref(null)
    const userTypesChartCanvas = ref(null)
    const chartPeriod = ref('monthly')
    const registrationChart = ref(null)
    const userTypesChart = ref(null)

    // Static data
    const weekDays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']

    // Local state
    const isLoading = ref(false)
    const error = ref(null)
    const reportData = ref(null)
    const filters = ref({
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0],
      reportType: 'users'
    })

    // Computed properties
    const hasData = computed(() => reportData.value && reportData.value.type === 'users')

    const userMetrics = computed(() => {
      const data = reportData.value
      if (!data?.metrics?.items) return []

      return data.metrics.items.map(item => ({
        key: item.key,
        label: item.label,
        value: item.value,
        type: item.type || 'number',
        icon: item.icon || 'fas fa-users',
        trend: item.trend || 'neutral',
        changePercentage: item.changePercentage || 0,
        previousValue: item.previousValue
      }))
    })

    const userTypesData = computed(() => {
      const data = reportData.value
      if (!data?.charts?.userTypes?.data) return []

      return data.charts.userTypes.data.map(item => ({
        type: item.label,
        count: item.value,
        percentage: item.percentage || 0
      }))
    })

    const userInsights = computed(() => {
      const data = reportData.value
      return data?.insights || []
    })

    const tableData = computed(() => {
      const data = reportData.value
      return data?.table?.data || []
    })

    const tableColumns = computed(() => {
      const data = reportData.value
      return data?.table?.columns || [
        { key: 'name', label: 'User Name', sortable: true },
        { key: 'email', label: 'Email', sortable: true },
        { key: 'type', label: 'Type', type: 'status', sortable: true },
        { key: 'registeredAt', label: 'Registered', type: 'date', sortable: true },
        { key: 'lastActive', label: 'Last Active', type: 'date', sortable: true },
        { key: 'orders', label: 'Orders', type: 'number', sortable: true },
        { key: 'totalSpent', label: 'Total Spent', type: 'currency', sortable: true }
      ]
    })

    const recentRegistrations = computed(() => {
      const data = reportData.value
      return data?.recentRegistrations || []
    })

    const activityHeatmapData = computed(() => {
      const data = reportData.value
      return data?.activityHeatmap || []
    })

    // Methods
    const fetchUsersData = async (newFilters = null) => {
      try {
        isLoading.value = true
        error.value = null

        const currentFilters = newFilters || filters.value
        reportData.value = await reportsService.getUsersReport(currentFilters)

        if (newFilters) {
          filters.value = { ...filters.value, ...newFilters }
        }
      } catch (err) {
        error.value = err.message
        console.error('Error fetching users data:', err)
      } finally {
        isLoading.value = false
      }
    }

    const handleFiltersChanged = async (newFilters) => {
      await fetchUsersData(newFilters)
      await nextTick()
      updateCharts()
    }

    const refreshData = async () => {
      await fetchUsersData()
      await nextTick()
      updateCharts()
    }

    const updateChart = async () => {
      await nextTick()
      updateCharts()
    }

    const updateCharts = () => {
      updateRegistrationChart()
      updateUserTypesChart()
    }

    const updateRegistrationChart = () => {
      if (!registrationChartCanvas.value) return

      const data = reportData.value
      if (!data?.charts?.registrations) return

      const ctx = registrationChartCanvas.value.getContext('2d')

      if (registrationChart.value) {
        registrationChart.value.destroy()
      }

      registrationChart.value = new Chart(ctx, {
        type: 'line',
        data: {
          labels: data.charts.registrations.labels || [],
          datasets: [{
            label: 'New Registrations',
            data: data.charts.registrations.data || [],
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return formatNumber(value)
                }
              }
            }
          }
        }
      })
    }

    const updateUserTypesChart = () => {
      if (!userTypesChartCanvas.value || !userTypesData.value.length) return

      const ctx = userTypesChartCanvas.value.getContext('2d')

      if (userTypesChart.value) {
        userTypesChart.value.destroy()
      }

      userTypesChart.value = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: userTypesData.value.map(item => item.type),
          datasets: [{
            data: userTypesData.value.map(item => item.count),
            backgroundColor: userTypesData.value.map((_, index) => getUserTypeColor(index))
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          }
        }
      })
    }

    const getUserTypeColor = (index) => {
      const colors = [
        '#3b82f6', '#10b981', '#f59e0b', '#ef4444',
        '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
      ]
      return colors[index % colors.length]
    }

    const getActivityLevel = (day, hour) => {
      const activity = activityHeatmapData.value.find(item =>
        item.day === day && item.hour === hour
      )
      if (!activity) return 'level-0'

      const level = Math.min(5, Math.max(1, Math.ceil(activity.count / 10)))
      return `level-${level}`
    }

    const getActivityTooltip = (day, hour) => {
      const activity = activityHeatmapData.value.find(item =>
        item.day === day && item.hour === hour
      )
      const dayName = weekDays[day]
      const timeStr = `${hour}:00`
      const count = activity?.count || 0
      return `${dayName} ${timeStr}: ${count} active users`
    }

    const formatNumber = (value) => {
      if (typeof value !== 'number') return value
      return new Intl.NumberFormat('uk-UA').format(value)
    }

    const formatTimeAgo = (date) => {
      const now = new Date()
      const past = new Date(date)
      const diffMs = now - past
      const diffMins = Math.floor(diffMs / 60000)
      const diffHours = Math.floor(diffMins / 60)
      const diffDays = Math.floor(diffHours / 24)

      if (diffMins < 60) return `${diffMins}m ago`
      if (diffHours < 24) return `${diffHours}h ago`
      return `${diffDays}d ago`
    }

    const handleInsightAction = (insight) => {
      if (insight.action?.callback) {
        insight.action.callback()
      }
    }

    const viewUserDetails = (user) => {
      // Navigate to user details or show modal
      console.log('View user details:', user)
    }

    // Lifecycle
    onMounted(async () => {
      await fetchUsersData()
      await nextTick()
      updateCharts()
    })

    // Watch for data changes
    watch(() => reportData.value, async () => {
      await nextTick()
      updateCharts()
    }, { deep: true })

    return {
      registrationChartCanvas,
      userTypesChartCanvas,
      chartPeriod,
      weekDays,
      isLoading,
      error,
      hasData,
      filters,
      userMetrics,
      userTypesData,
      userInsights,
      tableData,
      tableColumns,
      recentRegistrations,
      fetchUsersData,
      handleFiltersChanged,
      refreshData,
      updateChart,
      getUserTypeColor,
      getActivityLevel,
      getActivityTooltip,
      formatNumber,
      formatTimeAgo,
      handleInsightAction,
      viewUserDetails
    }
  }
}
</script>

<style scoped>
.users-report {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 0;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.report-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.report-title i {
  color: #10b981;
}

.report-description {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  flex-shrink: 0;
}

.filters-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-container,
.error-container,
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-spinner,
.error-content,
.empty-content {
  text-align: center;
  color: #6b7280;
}

.loading-spinner i {
  font-size: 2rem;
  color: #3b82f6;
  margin-bottom: 1rem;
}

.error-content i,
.empty-content i {
  font-size: 3rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.empty-content i {
  color: #9ca3af;
}

.retry-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 1rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #2563eb;
}

.report-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.metrics-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.chart-section,
.distribution-section,
.heatmap-section,
.insights-section,
.table-section,
.recent-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-container,
.distribution-container,
.heatmap-container,
.recent-container {
  width: 100%;
}

.chart-header,
.distribution-header,
.heatmap-header,
.recent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.chart-title,
.distribution-title,
.heatmap-title,
.recent-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.recent-count {
  font-size: 0.875rem;
  color: #10b981;
  font-weight: 500;
}

.chart-controls {
  display: flex;
  gap: 1rem;
}

.chart-select {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 0.875rem;
  cursor: pointer;
}

.chart-content {
  position: relative;
  height: 400px;
}

.distribution-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  align-items: center;
}

.chart-wrapper {
  position: relative;
  height: 300px;
}

.distribution-legend {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.legend-info {
  flex: 1;
}

.legend-label {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.legend-value {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Heatmap Styles */
.heatmap-legend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.heatmap-scale {
  display: flex;
  gap: 2px;
}

.scale-item {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.scale-item.level-1 { background: #ebedf0; }
.scale-item.level-2 { background: #c6e48b; }
.scale-item.level-3 { background: #7bc96f; }
.scale-item.level-4 { background: #239a3b; }
.scale-item.level-5 { background: #196127; }

.heatmap-content {
  overflow-x: auto;
}

.heatmap-grid {
  display: flex;
  gap: 1rem;
  min-width: 800px;
}

.heatmap-days {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding-top: 20px;
}

.day-label {
  height: 12px;
  font-size: 0.75rem;
  color: #6b7280;
  display: flex;
  align-items: center;
  width: 30px;
}

.heatmap-hours {
  display: flex;
  gap: 2px;
  flex: 1;
}

.hour-column {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.hour-label {
  height: 20px;
  font-size: 0.75rem;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

.activity-cell {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  background: #ebedf0;
  cursor: pointer;
  transition: all 0.2s;
}

.activity-cell:hover {
  transform: scale(1.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.activity-cell.level-0 { background: #ebedf0; }
.activity-cell.level-1 { background: #c6e48b; }
.activity-cell.level-2 { background: #7bc96f; }
.activity-cell.level-3 { background: #239a3b; }
.activity-cell.level-4 { background: #196127; }
.activity-cell.level-5 { background: #0d2818; }

/* Insights Styles */
.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.insight-card {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #10b981;
  background: #f0fdf4;
}

.insight-card.warning {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.insight-card.success {
  border-left-color: #10b981;
  background: #f0fdf4;
}

.insight-card.danger {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.insight-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.insight-card.warning .insight-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.insight-card.success .insight-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.insight-card.danger .insight-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.insight-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.insight-action {
  margin-top: 1rem;
}

.action-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background: #059669;
}

/* Recent Users Styles */
.recent-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recent-user {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #f3f4f6;
  border-radius: 8px;
  transition: all 0.2s;
}

.recent-user:hover {
  background: #f9fafb;
  border-color: #e5e7eb;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-avatar i {
  color: #9ca3af;
  font-size: 1.25rem;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.user-email {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.user-time {
  font-size: 0.75rem;
  color: #9ca3af;
}

.user-type {
  flex-shrink: 0;
}

.type-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.type-badge.buyer {
  background: #dbeafe;
  color: #1e40af;
}

.type-badge.seller {
  background: #d1fae5;
  color: #065f46;
}

.type-badge.admin {
  background: #fef3c7;
  color: #92400e;
}

.more-users {
  text-align: center;
  font-size: 0.875rem;
  color: #6b7280;
  font-style: italic;
  padding: 1rem;
  border: 1px dashed #d1d5db;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .report-header {
    flex-direction: column;
    gap: 1rem;
  }

  .distribution-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .chart-wrapper {
    height: 250px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }

  .heatmap-grid {
    min-width: 600px;
  }

  .recent-user {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .user-info {
    width: 100%;
  }
}
</style>
