﻿using AutoMapper;
using Google.Apis.Auth;
using Marketplace.Application.Commands.Auth;
using Marketplace.Application.Responses;
using Marketplace.Application.Services.Auth;
using Marketplace.Domain.Entities;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Configuration;

public class GoogleLoginCommandHandler : IRequestHandler<GoogleLoginCommand, AuthResponse>
{
    private readonly IUserRepository _userRepository;
    private readonly IJwtTokenService _jwtTokenService;
    private readonly string _googleClientId;
    private readonly IMapper _mapper;

    public GoogleLoginCommandHandler(IUserRepository userRepository, IJwtTokenService jwtTokenService, IConfiguration configuration, IMapper mapper)
    {
        _userRepository = userRepository;
        _jwtTokenService = jwtTokenService;
        _googleClientId = configuration["Authentication:Google:ClientId"]
            ?? throw new ArgumentNullException("Authentication:Google:ClientId is not configured.");
        _mapper = mapper;
    }

    public async Task<AuthResponse> Handle(GoogleLoginCommand request, CancellationToken cancellationToken)
    {
        var payload = await GoogleJsonWebSignature.ValidateAsync(request.idToken, new GoogleJsonWebSignature.ValidationSettings
        {
            Audience = new[] { _googleClientId }
        });

        var user = await _userRepository.GetByEmailAsync(payload.Email, cancellationToken);

        if (user == null)
        {
            user = new User
            {
                Id = Guid.NewGuid(),
                Email = new Email(payload.Email),
                Username = payload.Name,
                Password = null,
                Role = Role.Buyer,
                EmailConfirmedAt = DateTime.UtcNow
            };
            await _userRepository.AddAsync(user, cancellationToken);
        }

        user.LastSeenAt = DateTime.UtcNow;
        await _userRepository.UpdateAsync(user, cancellationToken);

        var token = await _jwtTokenService.GenerateTokenAsync(user, cancellationToken);
        return new AuthResponse(token, _mapper.Map<UserResponse>(user));
    }
}