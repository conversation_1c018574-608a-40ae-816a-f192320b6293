<template>
  <div class="card">
    <div class="card-header">
      <p class="card-header-title sales-title">Sales Overview</p>
      <div class="card-header-icon">
        <div class="tabs is-toggle is-small">
          <ul>
            <li :class="{ 'is-active': selectedPeriod === 'week' }">
              <a @click="changePeriod('week')">Week</a>
            </li>
            <li :class="{ 'is-active': selectedPeriod === 'month' }">
              <a @click="changePeriod('month')">Month</a>
            </li>
            <li :class="{ 'is-active': selectedPeriod === 'year' }">
              <a @click="changePeriod('year')">Year</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="card-content">
      <div v-if="loading" class="has-text-centered py-6">
        <span class="icon is-large">
          <i class="fas fa-spinner fa-pulse fa-2x"></i>
        </span>
        <p class="mt-2">Loading chart data...</p>
      </div>
      <div v-else-if="!data || data.length === 0" class="has-text-centered py-6">
        <span class="icon is-large">
          <i class="fas fa-chart-line fa-2x"></i>
        </span>
        <p class="mt-2">No sales data available for this period</p>
      </div>
      <div v-else class="chart-container">
        <canvas ref="chartCanvas" height="300"></canvas>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import Chart from 'chart.js/auto';

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['period-changed']);

const chartCanvas = ref(null);
const chart = ref(null);
const selectedPeriod = ref('month');
const loading = ref(false);

// Change period handler
const changePeriod = (period) => {
  if (selectedPeriod.value === period) return;

  selectedPeriod.value = period;
  loading.value = true;
  emit('period-changed', period);
};

// Computed properties for chart data
const chartData = computed(() => {
  return {
    labels: props.data.map(item => item.label),
    datasets: [
      {
        label: 'Sales',
        data: props.data.map(item => item.value),
        backgroundColor: 'rgba(255, 119, 0, 0.2)',
        borderColor: '#ff7700',
        borderWidth: 2,
        pointBackgroundColor: '#ff7700',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6,
        tension: 0.3,
        fill: true
      }
    ]
  };
});

// Initialize chart
const initChart = () => {
  if (chart.value) {
    chart.value.destroy();
  }

  const ctx = chartCanvas.value.getContext('2d');

  chart.value = new Chart(ctx, {
    type: 'line',
    data: chartData.value,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          titleFont: {
            size: 14,
            weight: 'bold'
          },
          bodyFont: {
            size: 13
          },
          padding: 12,
          callbacks: {
            label: function(context) {
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              if (context.parsed.y !== null) {
                label += new Intl.NumberFormat('uk-UA', {
                  style: 'currency',
                  currency: 'UAH'
                }).format(context.parsed.y);
              }
              return label;
            }
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            color: '#e0e0e0',
            font: {
              weight: 'bold',
              size: 12
            },
            callback: function(value) {
              return new Intl.NumberFormat('uk-UA', {
                style: 'currency',
                currency: 'UAH',
                notation: 'compact',
                compactDisplay: 'short'
              }).format(value);
            }
          },
          grid: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        },
        x: {
          ticks: {
            color: '#e0e0e0',
            font: {
              weight: 'bold',
              size: 12
            }
          },
          grid: {
            display: false
          }
        }
      }
    }
  });
};

// Update chart when data changes
watch(() => props.data, () => {
  loading.value = false;

  // Wait for next tick to ensure DOM is updated
  setTimeout(() => {
    if (props.data && props.data.length > 0) {
      initChart();
    }
  }, 0);
}, { deep: true });

onMounted(() => {
  if (props.data && props.data.length > 0) {
    initChart();
  }
});
</script>

<style scoped>
.card {
  height: 100%;
  transition: box-shadow 0.3s;
}

.card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-header-title {
  font-weight: 600;
}

.chart-container {
  height: 300px;
  position: relative;
}

.tabs.is-toggle li.is-active a {
  background-color: #ff7700;
  border-color: #ff7700;
}

.tabs.is-toggle a {
  border-width: 1px;
  border-style: solid;
  border-color: #dbdbdb;
  border-radius: 4px;
  padding: 0.25em 0.75em;
}

.tabs.is-toggle ul {
  border-bottom: none;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.mt-2 {
  margin-top: 0.5rem;
}
</style>
