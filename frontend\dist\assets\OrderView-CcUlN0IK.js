import{_ as P,c as m,o as c,a as s,d as f,n as x,t as d,g as b,h as w,f as A,i as k,b as S,w as N,r as M,s as I,k as T,D as O,e as U}from"./index-BKy0rL_2.js";import{O as E,a as F}from"./OrderStatusUpdate-BfyDb0cm.js";import{g as B,a as R,b as V,c as L,o as D}from"./orders-HBUhG7wP.js";const q={class:"order-details"},j={class:"columns"},z={class:"column is-8"},H={class:"card"},G={class:"card-content"},J={class:"columns"},K={class:"column is-6"},Q={class:"field"},W={class:"control"},X=["value"],Y={class:"field"},Z={class:"control"},ss=["value"],es={class:"field"},ts={class:"control"},as=["value"],ls={class:"column is-6"},os={class:"field"},is={class:"control"},ns=["value"],ds={class:"field"},rs={class:"control"},cs=["value"],us={class:"field"},vs={class:"control"},ms=["value"],ps={class:"card mt-4"},_s={class:"card-content"},fs={class:"columns"},hs={class:"column is-6"},ys={class:"field"},gs={class:"control"},bs=["value"],Ss={class:"field"},Os={class:"control"},xs=["value"],Cs={class:"column is-6"},$s={class:"field"},Ds={class:"control"},Ns=["value"],Ps={class:"field"},ws={class:"control"},As=["value"],ks={class:"card mt-4"},Ms={class:"card-content"},Is={class:"field"},Ts={class:"control"},Us=["value"],Es={class:"column is-4"},Fs={class:"card"},Bs={class:"card-content"},Rs={class:"field"},Vs={class:"control"},Ls={class:"field"},qs={class:"control"},js={class:"field"},zs={class:"control"},Hs=["value"],Gs={class:"card mt-4"},Js={class:"card-content"},Ks={class:"field"},Qs={class:"control"},Ws=["value"],Xs={class:"field"},Ys={class:"control"},Zs=["value"],se={class:"field"},ee={class:"control"},te=["value"],ae={__name:"OrderDetails",props:{order:{type:Object,required:!0,default:()=>({})}},setup(a){const C=o=>o?new Date(o).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",$=o=>o?new Date(o).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"}):"N/A",t=o=>!o&&o!==0?"N/A":new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(o),p=o=>({0:"Processing",1:"Pending",2:"Shipped",3:"Delivered",4:"Cancelled",Processing:"Processing",Pending:"Pending",Shipped:"Shipped",Delivered:"Delivered",Cancelled:"Cancelled"})[o]||o||"Unknown",r=o=>({0:"is-info",1:"is-warning",2:"is-primary",3:"is-success",4:"is-danger",Processing:"is-info",Pending:"is-warning",Shipped:"is-primary",Delivered:"is-success",Cancelled:"is-danger"})[o]||"is-light",n=o=>({0:"Pending",1:"Completed",2:"Refunded",3:"Failed",Pending:"Pending",Completed:"Completed",Refunded:"Refunded",Failed:"Failed"})[o]||o||"Unknown",u=o=>({0:"is-warning",1:"is-success",2:"is-info",3:"is-danger",Pending:"is-warning",Completed:"is-success",Refunded:"is-info",Failed:"is-danger"})[o]||"is-light";return(o,e)=>{var h,_,y,g;return c(),m("div",q,[s("div",j,[s("div",z,[s("div",H,[e[6]||(e[6]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},[s("span",{class:"icon"},[s("i",{class:"fas fa-receipt"})]),f(" Order Information ")])],-1)),s("div",G,[s("div",J,[s("div",K,[s("div",Q,[e[0]||(e[0]=s("label",{class:"label"},"Order ID",-1)),s("div",W,[s("input",{class:"input",type:"text",value:a.order.id,readonly:""},null,8,X)])]),s("div",Y,[e[1]||(e[1]=s("label",{class:"label"},"Customer Name",-1)),s("div",Z,[s("input",{class:"input",type:"text",value:a.order.customerName,readonly:""},null,8,ss)])]),s("div",es,[e[2]||(e[2]=s("label",{class:"label"},"Customer Email",-1)),s("div",ts,[s("input",{class:"input",type:"email",value:a.order.customerEmail,readonly:""},null,8,as)])])]),s("div",ls,[s("div",os,[e[3]||(e[3]=s("label",{class:"label"},"Order Date",-1)),s("div",is,[s("input",{class:"input",type:"text",value:C(a.order.createdAt),readonly:""},null,8,ns)])]),s("div",ds,[e[4]||(e[4]=s("label",{class:"label"},"Order Time",-1)),s("div",rs,[s("input",{class:"input",type:"text",value:$(a.order.createdAt),readonly:""},null,8,cs)])]),s("div",us,[e[5]||(e[5]=s("label",{class:"label"},"Total Amount",-1)),s("div",vs,[s("input",{class:"input",type:"text",value:t(a.order.totalPriceAmount||a.order.total),readonly:""},null,8,ms)])])])])])]),s("div",ps,[e[11]||(e[11]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},[s("span",{class:"icon"},[s("i",{class:"fas fa-shipping-fast"})]),f(" Shipping Address ")])],-1)),s("div",_s,[s("div",fs,[s("div",hs,[s("div",ys,[e[7]||(e[7]=s("label",{class:"label"},"Address Line",-1)),s("div",gs,[s("input",{class:"input",type:"text",value:((h=a.order.shippingAddress)==null?void 0:h.address1)||((_=a.order.shippingAddress)==null?void 0:_.address)||"N/A",readonly:""},null,8,bs)])]),s("div",Ss,[e[8]||(e[8]=s("label",{class:"label"},"City",-1)),s("div",Os,[s("input",{class:"input",type:"text",value:((y=a.order.shippingAddress)==null?void 0:y.city)||"N/A",readonly:""},null,8,xs)])])]),s("div",Cs,[s("div",$s,[e[9]||(e[9]=s("label",{class:"label"},"Country",-1)),s("div",Ds,[s("input",{class:"input",type:"text",value:((g=a.order.shippingAddress)==null?void 0:g.country)||"N/A",readonly:""},null,8,Ns)])]),s("div",Ps,[e[10]||(e[10]=s("label",{class:"label"},"Shipping Method",-1)),s("div",ws,[s("input",{class:"input",type:"text",value:a.order.shippingMethodName||a.order.shippingMethod||"N/A",readonly:""},null,8,As)])])])])])]),s("div",ks,[e[12]||(e[12]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},[s("span",{class:"icon"},[s("i",{class:"fas fa-sticky-note"})]),f(" Notes ")])],-1)),s("div",Ms,[s("div",Is,[s("div",Ts,[s("textarea",{class:"textarea",value:a.order.notes||"No notes",readonly:"",rows:"3",placeholder:"No notes available for this order"},null,8,Us)])])])])]),s("div",Es,[s("div",Fs,[e[16]||(e[16]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},[s("span",{class:"icon"},[s("i",{class:"fas fa-info-circle"})]),f(" Status Information ")])],-1)),s("div",Bs,[s("div",Rs,[e[13]||(e[13]=s("label",{class:"label"},"Order Status",-1)),s("div",Vs,[s("span",{class:x(["tag is-medium",r(a.order.status)])},d(p(a.order.status)),3)])]),s("div",Ls,[e[14]||(e[14]=s("label",{class:"label"},"Payment Status",-1)),s("div",qs,[s("span",{class:x(["tag is-medium",u(a.order.paymentStatus)])},d(n(a.order.paymentStatus)),3)])]),s("div",js,[e[15]||(e[15]=s("label",{class:"label"},"Payment Method",-1)),s("div",zs,[s("input",{class:"input",type:"text",value:a.order.paymentMethodText||a.order.paymentMethod||"N/A",readonly:""},null,8,Hs)])])])]),s("div",Gs,[e[20]||(e[20]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},[s("span",{class:"icon"},[s("i",{class:"fas fa-user"})]),f(" Customer Information ")])],-1)),s("div",Js,[s("div",Ks,[e[17]||(e[17]=s("label",{class:"label"},"Customer ID",-1)),s("div",Qs,[s("input",{class:"input",type:"text",value:a.order.customerId,readonly:""},null,8,Ws)])]),s("div",Xs,[e[18]||(e[18]=s("label",{class:"label"},"Name",-1)),s("div",Ys,[s("input",{class:"input",type:"text",value:a.order.customerName,readonly:""},null,8,Zs)])]),s("div",se,[e[19]||(e[19]=s("label",{class:"label"},"Email",-1)),s("div",ee,[s("input",{class:"input",type:"email",value:a.order.customerEmail,readonly:""},null,8,te)])])])])])])])}}},le=P(ae,[["__scopeId","data-v-c399b4d5"]]),oe={class:"admin-order-view"},ie={class:"level"},ne={class:"level-left"},de={class:"level-item"},re={class:"breadcrumb","aria-label":"breadcrumbs"},ce={class:"level-right"},ue={class:"level-item"},ve={class:"buttons"},me={key:0,class:"has-text-centered py-6"},pe={key:1,class:"notification is-danger"},_e={key:2,class:"notification is-warning"},fe={key:3},he={class:"card mb-4"},ye={class:"card-content"},ge={class:"level"},be={class:"level-left"},Se={class:"level-item"},Oe={class:"title is-4"},xe={class:"subtitle is-6"},Ce={class:"level-right"},$e={class:"level-item"},De={class:"tags"},Ne={class:"mt-4"},Pe={class:"mt-4"},we={__name:"OrderView",setup(a){const C=A(),$=U(),t=b(null),p=b(!1),r=b(!1),n=b(null),u=w(()=>C.params.id),o=async()=>{if(!u.value){n.value="Order ID is required";return}p.value=!0,n.value=null;try{console.log("Fetching order details for ID:",u.value);const i=await D.getOrderById(u.value);i?(t.value=i,console.log("Order details loaded:",i)):n.value="Order not found"}catch(i){console.error("Error fetching order details:",i),n.value=i.message||"Failed to load order details"}finally{p.value=!1}},e=async(i,l)=>{r.value=!0;try{await D.updateOrderStatus(i,l),t.value&&(t.value.status=l),console.log("Order status updated successfully")}catch(v){console.error("Error updating order status:",v),n.value="Failed to update order status"}finally{r.value=!1}},h=async(i,l)=>{r.value=!0;try{await D.updatePaymentStatus(i,l),t.value&&(t.value.paymentStatus=l),console.log("Payment status updated successfully")}catch(v){console.error("Error updating payment status:",v),n.value="Failed to update payment status"}finally{r.value=!1}},_=()=>{$.push("/admin/orders")},y=i=>i?new Date(i).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",g=i=>i?new Date(i).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"}):"N/A";return k(()=>{o()}),(i,l)=>{const v=M("router-link");return c(),m("div",oe,[s("div",ie,[s("div",ne,[s("div",de,[s("nav",re,[s("ul",null,[s("li",null,[S(v,{to:"/admin/orders"},{default:N(()=>l[0]||(l[0]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-list"})],-1),s("span",null,"Orders",-1)])),_:1})]),l[1]||(l[1]=s("li",{class:"is-active"},[s("a",{href:"#","aria-current":"page"},[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-eye"})]),s("span",null,"View Order")])],-1))])])])]),s("div",ce,[s("div",ue,[s("div",ve,[t.value?(c(),I(v,{key:0,to:`/admin/orders/${u.value}/edit`,class:"button is-primary"},{default:N(()=>l[2]||(l[2]=[s("span",{class:"icon"},[s("i",{class:"fas fa-edit"})],-1),s("span",null,"Edit Order",-1)])),_:1},8,["to"])):T("",!0),s("button",{class:"button is-light",onClick:_},l[3]||(l[3]=[s("span",{class:"icon"},[s("i",{class:"fas fa-arrow-left"})],-1),s("span",null,"Back to Orders",-1)]))])])])]),p.value?(c(),m("div",me,l[4]||(l[4]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),s("p",{class:"mt-2"},"Loading order details...",-1)]))):n.value?(c(),m("div",pe,[s("p",null,d(n.value),1),s("button",{class:"button is-light mt-2",onClick:o},l[5]||(l[5]=[s("span",{class:"icon"},[s("i",{class:"fas fa-redo"})],-1),s("span",null,"Retry",-1)]))])):t.value?(c(),m("div",fe,[s("div",he,[s("div",ye,[s("div",ge,[s("div",be,[s("div",Se,[s("div",null,[s("h1",Oe," Order #"+d(t.value.id),1),s("p",xe," Placed on "+d(y(t.value.createdAt))+" at "+d(g(t.value.createdAt)),1)])])]),s("div",Ce,[s("div",$e,[s("div",De,[s("span",{class:x(["tag is-medium",O(B)(t.value.status)])},d(O(R)(t.value.status)),3),s("span",{class:x(["tag is-medium",O(V)(t.value.paymentStatus)])},d(O(L)(t.value.paymentStatus)),3)])])])])])]),S(le,{order:t.value},null,8,["order"]),s("div",Ne,[S(E,{items:t.value.items||[],tax:0,shipping:t.value.shipping||0,discount:t.value.discount||0,editable:!1},null,8,["items","shipping","discount"])]),s("div",Pe,[S(F,{"order-id":t.value.id,"current-order-status":t.value.status,"current-payment-status":t.value.paymentStatus,"customer-name":t.value.customerName,"status-history":t.value.statusHistory||[],loading:r.value,onUpdateOrderStatus:e,onUpdatePaymentStatus:h},null,8,["order-id","current-order-status","current-payment-status","customer-name","status-history","loading"])])])):(c(),m("div",_e,[l[7]||(l[7]=s("p",null,"Order not found.",-1)),s("button",{class:"button is-light mt-2",onClick:_},l[6]||(l[6]=[s("span",{class:"icon"},[s("i",{class:"fas fa-arrow-left"})],-1),s("span",null,"Back to Orders",-1)]))]))])}}},Ie=P(we,[["__scopeId","data-v-ea668707"]]);export{Ie as default};
