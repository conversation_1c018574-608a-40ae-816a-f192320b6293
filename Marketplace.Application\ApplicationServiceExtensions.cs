﻿using FluentValidation;
using Marketplace.Application.Behaviors;
using Marketplace.Application.Services;
using Marketplace.Application.Services.Auth;
using Marketplace.Application.Services.UserRole;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace Marketplace.Application;
public static class ApplicationServiceExtensions
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        services.AddMediatR(cfg =>
            cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));

        services.AddScoped<IEmailService, EmailService>();
        services.AddScoped<IJwtTokenService, JwtTokenService>();
        services.AddScoped<ITokenService, TokenService>();
        services.AddScoped<IUserRoleService, UserRoleService>();
        services.AddScoped<ICategoryHierarchyService, CategoryHierarchyService>();

        // Reports services
        services.AddScoped<IReportService, ReportService>();
        services.AddScoped<IExportService, ExportService>();

        // Реєстрація AutoMapper
        services.AddAutoMapper(Assembly.GetExecutingAssembly());

        // Реєстрація валідації (FluentValidation)
        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

        // Реєстрація поведінки валідації для MediatR (опціонально)
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));

        return services;
    }
}

