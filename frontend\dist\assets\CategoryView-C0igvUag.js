import{_ as Z,B as se,g as p,h as j,x as K,c as r,o as d,a as e,t as c,m as W,H as te,z as $,C as x,k as b,n as _,F as X,p as Y,i as ae,f as le,b as D,d as V,w as S,r as oe,A as ie,I,e as ne}from"./index-BKy0rL_2.js";import{C as re}from"./ConfirmDialog-BQ115uZp.js";const de={class:"modal-card"},ce={class:"modal-card-head"},ue={class:"modal-card-title"},ve={class:"modal-card-body"},pe={class:"field"},me={class:"control"},ge={class:"field"},fe={class:"control"},he={class:"field"},ye={class:"control"},_e={class:"field"},be={class:"control"},Ce={class:"dropdown-trigger"},$e={class:"field has-addons"},ke={class:"control is-expanded"},we={class:"control"},xe={class:"icon"},De={class:"dropdown-menu",role:"menu"},Pe={class:"dropdown-content"},Se=["onMousedown"],Ie={class:"category-item"},Ue={class:"category-name"},Ee={class:"category-slug has-text-grey is-size-7"},Me={key:0,class:"help"},Te={class:"field"},Ae={key:0,class:"mb-3"},Ve={class:"image is-128x128"},Fe=["src","alt"],Oe={class:"tabs is-small"},Be={key:1,class:"field"},Le={class:"file has-name is-fullwidth"},Ne={class:"file-label"},Re={key:0,class:"file-name"},ze={key:2,class:"field"},qe={class:"control"},je={class:"field"},Ge={class:"control"},He={class:"field"},Je={class:"control"},Qe={class:"help"},We={class:"field"},Ke={class:"control"},Xe={class:"help"},Ye={class:"modal-card-foot"},Ze=["disabled"],es={key:0},ss={key:1},ts={__name:"CategoryFormModal",props:{isOpen:{type:Boolean,required:!0},category:{type:Object,default:null},categories:{type:Array,default:()=>[]}},emits:["close","save"],setup(F,{emit:O}){const k=F,U=O,a=se({id:null,name:"",slug:"",description:"",parentId:"",imageUrl:"",displayOrder:0,metaTitle:"",metaDescription:""}),v=p("file"),i=p(null),h=p(null),m=p(!1),g=p(""),y=p(null),w=p(!1),C=j(()=>k.categories.filter(n=>n.id!==a.id)),P=j(()=>{const n=C.value;if(!g.value.trim())return n;const t=g.value.toLowerCase().trim();return n.filter(o=>o.name.toLowerCase().includes(t)||o.slug.toLowerCase().includes(t))}),G=()=>{var n;a.name&&(!a.slug||a.slug===E(((n=k.category)==null?void 0:n.name)||""))&&(a.slug=E(a.name))},E=n=>n.toString().toLowerCase().trim().replace(/\s+/g,"-").replace(/&/g,"-and-").replace(/[^\w\-]+/g,"").replace(/\-\-+/g,"-").replace(/^-+/,"").replace(/-+$/,""),M=n=>{const t=n.target.files[0];if(!t)return;if(!t.type.startsWith("image/")){alert("Please select an image file");return}if(t.size>5*1024*1024){alert("File size must be less than 5MB");return}i.value=t;const o=new FileReader;o.onload=R=>{a.imageUrl=R.target.result},o.readAsDataURL(t)},T=()=>{a.imageUrl="",i.value=null,h.value&&(h.value.value="")},B=n=>{n?(y.value=n,a.parentId=n.id,g.value=n.name):(y.value=null,a.parentId="",g.value=""),m.value=!1},L=()=>{m.value=!0},H=()=>{m.value?m.value=!1:(y.value&&g.value===y.value.name&&(g.value=""),L())},J=()=>{m.value||(m.value=!0)},Q=()=>{setTimeout(()=>{m.value=!1,y.value?g.value=y.value.name:g.value=""},200)},N=async()=>{w.value=!0;try{const n={...a};n.displayOrder=parseInt(n.displayOrder),n.parentId===""&&(n.parentId=null),U("save",n)}catch(n){console.error("Error submitting form:",n)}finally{w.value=!1}},A=()=>{a.id=null,a.name="",a.slug="",a.description="",a.parentId="",a.imageUrl="",a.displayOrder=0,a.metaTitle="",a.metaDescription="",i.value=null,v.value="file",h.value&&(h.value.value=""),y.value=null,g.value="",m.value=!1};return K(()=>k.category,n=>{if(n){if(Object.keys(a).forEach(t=>{t in n&&(a[t]=n[t])}),n.parentId&&k.categories){const t=k.categories.find(o=>o.id===n.parentId);t&&(y.value=t,g.value=t.name)}}else A()},{immediate:!0}),K(()=>k.isOpen,n=>{n||A()}),(n,t)=>(d(),r("div",{class:_(["modal",{"is-active":F.isOpen}])},[e("div",{class:"modal-background",onClick:t[0]||(t[0]=o=>n.$emit("close"))}),e("div",de,[e("header",ce,[e("p",ue,c(F.category?"Edit Category":"Add Category"),1),e("button",{class:"delete","aria-label":"close",onClick:t[1]||(t[1]=o=>n.$emit("close"))})]),e("section",ve,[e("form",{onSubmit:W(N,["prevent"])},[e("div",pe,[t[14]||(t[14]=e("label",{class:"label"},"Name",-1)),e("div",me,[$(e("input",{class:"input",type:"text",placeholder:"Category name","onUpdate:modelValue":t[2]||(t[2]=o=>a.name=o),onInput:G,required:""},null,544),[[x,a.name]])])]),e("div",ge,[t[15]||(t[15]=e("label",{class:"label"},"Slug",-1)),e("div",fe,[$(e("input",{class:"input",type:"text",placeholder:"category-slug","onUpdate:modelValue":t[3]||(t[3]=o=>a.slug=o),required:""},null,512),[[x,a.slug]])]),t[16]||(t[16]=e("p",{class:"help"},"URL-friendly version of the name. Auto-generated but can be edited.",-1))]),e("div",he,[t[17]||(t[17]=e("label",{class:"label"},"Description",-1)),e("div",ye,[$(e("textarea",{class:"textarea",placeholder:"Category description","onUpdate:modelValue":t[4]||(t[4]=o=>a.description=o),rows:"3"},null,512),[[x,a.description]])])]),e("div",_e,[t[19]||(t[19]=e("label",{class:"label"},"Parent Category",-1)),e("div",be,[e("div",{class:_(["dropdown",{"is-active":m.value}])},[e("div",Ce,[e("div",$e,[e("div",ke,[$(e("input",{class:"input",type:"text",placeholder:"Search for parent category (leave empty for top level)...","onUpdate:modelValue":t[5]||(t[5]=o=>g.value=o),onInput:J,onFocus:L,onBlur:Q},null,544),[[x,g.value]])]),e("div",we,[e("button",{class:"button",type:"button",onClick:H},[e("span",xe,[e("i",{class:_(["fas fa-chevron-down",{"fa-rotate-180":m.value}])},null,2)])])])])]),e("div",De,[e("div",Pe,[e("a",{class:_(["dropdown-item",{"is-active":!a.parentId}]),onMousedown:t[6]||(t[6]=W(o=>B(null),["prevent"]))},t[18]||(t[18]=[e("div",{class:"category-item"},[e("div",{class:"category-name"},"None (Top Level)"),e("div",{class:"category-slug has-text-grey is-size-7"},"Root category")],-1)]),34),(d(!0),r(X,null,Y(P.value,o=>(d(),r("a",{key:o.id,class:_(["dropdown-item",{"is-active":a.parentId===o.id}]),onMousedown:W(R=>B(o),["prevent"])},[e("div",Ie,[e("div",Ue,c(o.name),1),e("div",Ee,c(o.slug),1)])],42,Se))),128))])])],2)]),y.value?(d(),r("p",Me," Selected: "+c(y.value.name),1)):b("",!0)]),e("div",Te,[t[24]||(t[24]=e("label",{class:"label"},"Category Image",-1)),a.imageUrl?(d(),r("div",Ae,[e("figure",Ve,[e("img",{src:a.imageUrl,alt:a.name,class:"is-rounded"},null,8,Fe)]),e("button",{type:"button",class:"button is-small is-danger mt-2",onClick:T},t[20]||(t[20]=[e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1),e("span",null,"Remove Image",-1)]))])):b("",!0),e("div",Oe,[e("ul",null,[e("li",{class:_({"is-active":v.value==="file"})},[e("a",{onClick:t[7]||(t[7]=o=>v.value="file")},"Upload File")],2),e("li",{class:_({"is-active":v.value==="url"})},[e("a",{onClick:t[8]||(t[8]=o=>v.value="url")},"Image URL")],2)])]),v.value==="file"?(d(),r("div",Be,[e("div",Le,[e("label",Ne,[e("input",{class:"file-input",type:"file",accept:"image/*",onChange:M,ref_key:"fileInput",ref:h},null,544),t[21]||(t[21]=e("span",{class:"file-cta"},[e("span",{class:"icon"},[e("i",{class:"fas fa-upload"})]),e("span",{class:"file-label"},"Choose image...")],-1)),i.value?(d(),r("span",Re,c(i.value.name),1)):b("",!0)])]),t[22]||(t[22]=e("p",{class:"help"},"Supported formats: JPG, PNG, GIF. Max size: 5MB",-1))])):b("",!0),v.value==="url"?(d(),r("div",ze,[e("div",qe,[$(e("input",{class:"input",type:"url",placeholder:"https://example.com/image.jpg","onUpdate:modelValue":t[9]||(t[9]=o=>a.imageUrl=o)},null,512),[[x,a.imageUrl]])]),t[23]||(t[23]=e("p",{class:"help"},"Enter a direct link to an image",-1))])):b("",!0)]),e("div",je,[t[25]||(t[25]=e("label",{class:"label"},"Display Order",-1)),e("div",Ge,[$(e("input",{class:"input",type:"number",min:"0",placeholder:"0","onUpdate:modelValue":t[10]||(t[10]=o=>a.displayOrder=o)},null,512),[[x,a.displayOrder,void 0,{number:!0}]])]),t[26]||(t[26]=e("p",{class:"help"},"Categories with lower numbers will be displayed first.",-1))]),t[29]||(t[29]=te('<div class="field" data-v-8809d8ca><label class="label" data-v-8809d8ca><span class="icon-text" data-v-8809d8ca><span class="icon" data-v-8809d8ca><i class="fas fa-search" data-v-8809d8ca></i></span><span data-v-8809d8ca>SEO Settings</span></span></label></div>',1)),e("div",He,[t[27]||(t[27]=e("label",{class:"label"},"Meta Title",-1)),e("div",Je,[$(e("input",{class:"input",type:"text",placeholder:"SEO title for search engines","onUpdate:modelValue":t[11]||(t[11]=o=>a.metaTitle=o),maxlength:"60"},null,512),[[x,a.metaTitle]])]),e("p",Qe,[e("span",{class:_({"has-text-danger":a.metaTitle&&a.metaTitle.length>60})},c(a.metaTitle?a.metaTitle.length:0)+"/60 characters ",3)])]),e("div",We,[t[28]||(t[28]=e("label",{class:"label"},"Meta Description",-1)),e("div",Ke,[$(e("textarea",{class:"textarea",placeholder:"SEO description for search engines","onUpdate:modelValue":t[12]||(t[12]=o=>a.metaDescription=o),maxlength:"160",rows:"3"},null,512),[[x,a.metaDescription]])]),e("p",Xe,[e("span",{class:_({"has-text-danger":a.metaDescription&&a.metaDescription.length>160})},c(a.metaDescription?a.metaDescription.length:0)+"/160 characters ",3)])])],32)]),e("footer",Ye,[e("button",{class:"button is-primary",onClick:N,disabled:w.value},[w.value?(d(),r("span",es,t[30]||(t[30]=[e("span",{class:"icon"},[e("i",{class:"fas fa-spinner fa-spin"})],-1),e("span",null,"Saving...",-1)]))):(d(),r("span",ss,"Save"))],8,Ze),e("button",{class:"button",onClick:t[13]||(t[13]=o=>n.$emit("close"))},"Cancel")])])],2))}},as=Z(ts,[["__scopeId","data-v-8809d8ca"]]),ls={class:"category-view"},os={key:0,class:"has-text-centered py-6"},is={key:1,class:"notification is-danger"},ns={key:2,class:"has-text-centered py-6"},rs={key:3},ds={class:"level mb-5"},cs={class:"level-left"},us={class:"level-item"},vs={class:"breadcrumb","aria-label":"breadcrumbs"},ps={class:"is-active"},ms={href:"#","aria-current":"page"},gs={class:"title is-4"},fs={class:"columns is-multiline mb-5"},hs={class:"column is-8"},ys={class:"card"},_s={class:"card-content"},bs={class:"columns"},Cs={key:0,class:"column is-3"},$s={class:"image is-128x128"},ks=["src","alt"],ws={class:"column"},xs={class:"field"},Ds={class:"content"},Ps={class:"field"},Ss={class:"content"},Is={key:0,class:"field"},Us={class:"content"},Es={class:"column is-4"},Ms={class:"card"},Ts={class:"card-content"},As={class:"field"},Vs={class:"title is-4 has-text-primary"},Fs={key:0,class:"field"},Os={class:"content"},Bs={key:0,class:"card mb-5"},Ls={class:"card-content"},Ns={class:"columns"},Rs={key:0,class:"column is-6"},zs={class:"field"},qs={class:"content"},js={key:1,class:"column is-6"},Gs={class:"field"},Hs={class:"content"},Js={class:"card"},Qs={class:"card-header"},Ws={class:"card-header-title"},Ks={class:"card-header-icon"},Xs={class:"icon"},Ys={class:"card-content"},Zs={key:0,class:"has-text-centered py-4"},et={key:1,class:"has-text-centered py-4"},st={key:2},tt={class:"level mb-4"},at={class:"level-left"},lt={class:"level-item"},ot={class:"subtitle is-6"},it={class:"level-right"},nt={class:"level-item"},rt=["disabled"],dt={class:"table-container"},ct={class:"table is-fullwidth is-striped is-hoverable"},ut={class:"media"},vt={key:0,class:"media-left"},pt={class:"image is-48x48"},mt=["src","alt"],gt={class:"media-content"},ft={class:"title is-6 has-text-dark"},ht={class:"subtitle is-7 has-text-grey-dark"},yt={class:"tag is-primary is-medium"},_t={class:"buttons are-small"},bt={class:"modal-card"},Ct={class:"modal-card-body"},$t={class:"field"},kt={class:"control"},wt={class:"select is-fullwidth"},xt=["value"],Dt={class:"help"},Pt={class:"modal-card-foot"},St=["disabled"],It={__name:"CategoryView",setup(F){const O=le(),k=ne(),U=p(!1),a=p(!1),v=p(""),i=p(null),h=p([]),m=p([]),g=p(!1),y=p(!1),w=p(!1),C=p(""),P=p(!1),G=j(()=>{var s;if(!((s=i.value)!=null&&s.parentId)||!m.value.length)return"";const l=m.value.find(f=>f.id===i.value.parentId);return(l==null?void 0:l.name)||"Unknown"}),E=j(()=>m.value.filter(l=>{var s;return l.id!==((s=i.value)==null?void 0:s.id)})),M=async l=>{U.value=!0,v.value="";try{const s=await I.getById(l);i.value=s,await Promise.all([T(s.id),B()])}catch(s){console.error("Error fetching category:",s),s.name!=="CanceledError"&&s.code!=="ERR_CANCELED"&&(v.value="Failed to load category. Please try again.")}finally{U.value=!1}},T=async l=>{a.value=!0;try{const s=await I.getCategoryProducts(l,{pageSize:50});h.value=s.data||[]}catch(s){console.error("Error fetching products:",s),h.value=[]}finally{a.value=!1}},B=async()=>{try{const l=await I.getAll({pageSize:1e3});m.value=l.data||[]}catch(l){console.error("Error fetching all categories:",l),m.value=[]}},L=()=>{var l;(l=i.value)!=null&&l.id&&T(i.value.id)},H=l=>l&&l.priceAmount&&l.priceCurrency?`${l.priceAmount} ${l.priceCurrency}`:l&&l.price?`${l.price} UAH`:"N/A",J=l=>l===0?"is-danger":l<10?"is-warning":"is-success",Q=l=>{if(!l||typeof l!="string")return"is-light";switch(l.toLowerCase()){case"active":case"approved":return"is-success";case"inactive":case"rejected":return"is-danger";case"draft":case"pending":return"is-warning";default:return"is-light"}},N=()=>{g.value=!0},A=()=>{g.value=!1},n=async l=>{try{await I.updateCategory(i.value.id,l),A(),await M(i.value.id)}catch(s){console.error("Error saving category:",s),v.value="Failed to save category. Please try again."}},t=()=>{y.value=!0},o=()=>{y.value=!1},R=async()=>{try{await I.delete(i.value.id),o(),k.push("/admin/categories")}catch(l){console.error("Error deleting category:",l),o(),l.response&&l.response.data&&l.response.data.message?v.value=l.response.data.message:v.value="Failed to delete category. Please try again."}},z=()=>{w.value=!1,C.value=""},ee=async()=>{var l,s;if(!(!C.value||!((l=i.value)!=null&&l.id))){P.value=!0;try{const f=await I.bulkUpdateProductsCategory(i.value.id,C.value);z();const q=((s=E.value.find(u=>u.id===C.value))==null?void 0:s.name)||"selected category";alert(`Successfully moved ${f.updatedCount} products to ${q}`),await T(i.value.id)}catch(f){console.error("Error updating products category:",f),f.response&&f.response.data&&f.response.data.message?v.value=f.response.data.message:v.value="Failed to move products. Please try again."}finally{P.value=!1}}};return K(()=>O.params.id,l=>{l&&M(l)},{immediate:!0}),ae(()=>{const l=O.params.id;l&&M(l)}),(l,s)=>{var q;const f=oe("router-link");return d(),r("div",ls,[U.value?(d(),r("div",os,s[3]||(s[3]=[e("div",{class:"loader-wrapper"},[e("div",{class:"loader is-loading"}),e("p",{class:"mt-3"},"Loading category...")],-1)]))):v.value?(d(),r("div",is,[e("button",{class:"delete",onClick:s[0]||(s[0]=u=>v.value="")}),V(" "+c(v.value),1)])):i.value?(d(),r("div",rs,[e("div",ds,[e("div",cs,[e("div",us,[e("div",null,[e("nav",vs,[e("ul",null,[e("li",null,[D(f,{to:"/admin"},{default:S(()=>s[8]||(s[8]=[V("Dashboard")])),_:1})]),e("li",null,[D(f,{to:"/admin/categories"},{default:S(()=>s[9]||(s[9]=[V("Categories")])),_:1})]),e("li",ps,[e("a",ms,c(i.value.name),1)])])]),e("h1",gs,c(i.value.name),1),s[10]||(s[10]=e("p",{class:"subtitle is-6"},"Category Details",-1))])])]),e("div",{class:"level-right"},[e("div",{class:"level-item"},[e("div",{class:"buttons"},[e("button",{class:"button is-primary",onClick:N},s[11]||(s[11]=[e("span",{class:"icon"},[e("i",{class:"fas fa-edit"})],-1),e("span",null,"Edit Category",-1)])),e("button",{class:"button is-danger",onClick:t},s[12]||(s[12]=[e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1),e("span",null,"Delete",-1)]))])])])]),e("div",fs,[e("div",hs,[e("div",ys,[s[16]||(s[16]=e("div",{class:"card-header"},[e("p",{class:"card-header-title"},[e("span",{class:"icon"},[e("i",{class:"fas fa-info-circle"})]),e("span",null,"Basic Information")])],-1)),e("div",_s,[e("div",bs,[i.value.image?(d(),r("div",Cs,[e("figure",$s,[e("img",{src:i.value.image,alt:i.value.name,class:"is-rounded"},null,8,ks)])])):b("",!0),e("div",ws,[e("div",xs,[s[13]||(s[13]=e("label",{class:"label"},"Name",-1)),e("p",Ds,c(i.value.name),1)]),e("div",Ps,[s[14]||(s[14]=e("label",{class:"label"},"Slug",-1)),e("p",Ss,[e("code",null,c(i.value.slug),1)])]),i.value.description?(d(),r("div",Is,[s[15]||(s[15]=e("label",{class:"label"},"Description",-1)),e("p",Us,c(i.value.description),1)])):b("",!0)])])])])]),e("div",Es,[e("div",Ms,[s[19]||(s[19]=e("div",{class:"card-header"},[e("p",{class:"card-header-title"},[e("span",{class:"icon"},[e("i",{class:"fas fa-chart-bar"})]),e("span",null,"Statistics")])],-1)),e("div",Ts,[e("div",As,[s[17]||(s[17]=e("label",{class:"label"},"Products",-1)),e("p",Vs,c(h.value.length),1)]),i.value.parentId?(d(),r("div",Fs,[s[18]||(s[18]=e("label",{class:"label"},"Parent Category",-1)),e("p",Os,[D(f,{to:`/admin/categories/${i.value.parentId}`,class:"has-text-link"},{default:S(()=>[V(c(G.value),1)]),_:1},8,["to"])])])):b("",!0)])])])]),i.value.metaTitle||i.value.metaDescription?(d(),r("div",Bs,[s[22]||(s[22]=e("div",{class:"card-header"},[e("p",{class:"card-header-title"},[e("span",{class:"icon"},[e("i",{class:"fas fa-search"})]),e("span",null,"SEO Information")])],-1)),e("div",Ls,[e("div",Ns,[i.value.metaTitle?(d(),r("div",Rs,[e("div",zs,[s[20]||(s[20]=e("label",{class:"label"},"Meta Title",-1)),e("p",qs,c(i.value.metaTitle),1)])])):b("",!0),i.value.metaDescription?(d(),r("div",js,[e("div",Gs,[s[21]||(s[21]=e("label",{class:"label"},"Meta Description",-1)),e("p",Hs,c(i.value.metaDescription),1)])])):b("",!0)])])])):b("",!0),e("div",Js,[e("div",Qs,[e("p",Ws,[s[23]||(s[23]=e("span",{class:"icon"},[e("i",{class:"fas fa-box"})],-1)),e("span",null,"Products ("+c(h.value.length)+")",1)]),e("div",Ks,[e("button",{class:"button is-small is-primary",onClick:L},[e("span",Xs,[e("i",{class:_(["fas fa-sync-alt",{"fa-spin":a.value}])},null,2)]),s[24]||(s[24]=e("span",null,"Refresh",-1))])])]),e("div",Ys,[a.value?(d(),r("div",Zs,s[25]||(s[25]=[e("span",{class:"icon"},[e("i",{class:"fas fa-spinner fa-spin"})],-1),V(" Loading products... ")]))):h.value.length===0?(d(),r("div",et,s[26]||(s[26]=[e("span",{class:"icon is-large has-text-grey-light"},[e("i",{class:"fas fa-box-open fa-2x"})],-1),e("p",{class:"title is-6 has-text-grey"},"No products in this category",-1),e("p",{class:"subtitle is-7 has-text-grey"},"Products will appear here when added to this category",-1)]))):(d(),r("div",st,[e("div",tt,[e("div",at,[e("div",lt,[e("p",ot,c(h.value.length)+" products in this category",1)])]),e("div",it,[e("div",nt,[e("button",{class:"button is-warning",onClick:s[1]||(s[1]=u=>w.value=!0),disabled:h.value.length===0},s[27]||(s[27]=[e("span",{class:"icon"},[e("i",{class:"fas fa-exchange-alt"})],-1),e("span",null,"Move All Products",-1)]),8,rt)])])]),e("div",dt,[e("table",ct,[s[30]||(s[30]=e("thead",null,[e("tr",{class:"has-background-light"},[e("th",{class:"has-text-weight-bold has-text-dark"},"Product"),e("th",{class:"has-text-weight-bold has-text-dark"},"Price"),e("th",{class:"has-text-weight-bold has-text-dark"},"Stock"),e("th",{class:"has-text-weight-bold has-text-dark"},"Status"),e("th",{class:"has-text-weight-bold has-text-dark"},"Actions")])],-1)),e("tbody",null,[(d(!0),r(X,null,Y(h.value,u=>(d(),r("tr",{key:u.id},[e("td",null,[e("div",ut,[u.image?(d(),r("div",vt,[e("figure",pt,[e("img",{src:u.image,alt:u.name,class:"is-rounded"},null,8,mt)])])):b("",!0),e("div",gt,[e("p",ft,c(u.name),1),e("p",ht,c(u.sku),1)])])]),e("td",null,[e("span",yt,c(H(u)),1)]),e("td",null,[e("span",{class:_(["tag is-medium",J(u.stock)])},c(u.stock),3)]),e("td",null,[e("span",{class:_(["tag is-medium",Q(u.status)])},c(u.status),3)]),e("td",null,[e("div",_t,[D(f,{to:`/admin/products/${u.id}/view`,class:"button is-info",title:"View Product"},{default:S(()=>s[28]||(s[28]=[e("span",{class:"icon"},[e("i",{class:"fas fa-eye"})],-1),e("span",null,"View",-1)])),_:2},1032,["to"]),D(f,{to:`/admin/products/${u.id}/edit`,class:"button is-warning",title:"Edit Product"},{default:S(()=>s[29]||(s[29]=[e("span",{class:"icon"},[e("i",{class:"fas fa-edit"})],-1),e("span",null,"Edit",-1)])),_:2},1032,["to"])])])]))),128))])])])]))])])])):(d(),r("div",ns,[s[5]||(s[5]=e("span",{class:"icon is-large has-text-grey-light"},[e("i",{class:"fas fa-folder-open fa-3x"})],-1)),s[6]||(s[6]=e("p",{class:"title is-5 has-text-grey"},"Category not found",-1)),s[7]||(s[7]=e("p",{class:"subtitle is-6 has-text-grey"},"The requested category does not exist",-1)),D(f,{to:"/admin/categories",class:"button is-primary"},{default:S(()=>s[4]||(s[4]=[e("span",{class:"icon"},[e("i",{class:"fas fa-arrow-left"})],-1),e("span",null,"Back to Categories",-1)])),_:1})])),D(as,{"is-open":g.value,category:i.value,categories:m.value,onClose:A,onSave:n},null,8,["is-open","category","categories"]),D(re,{"is-open":y.value,title:"Delete Category",message:`Are you sure you want to delete '${(q=i.value)==null?void 0:q.name}'? This action cannot be undone.`,"confirm-text":"Delete","confirm-button-class":"is-danger",onConfirm:R,onCancel:o},null,8,["is-open","message"]),e("div",{class:_(["modal",{"is-active":w.value}])},[e("div",{class:"modal-background",onClick:z}),e("div",bt,[e("header",{class:"modal-card-head"},[s[31]||(s[31]=e("p",{class:"modal-card-title"},"Move All Products to Another Category",-1)),e("button",{class:"delete",onClick:z})]),e("section",Ct,[e("div",$t,[s[33]||(s[33]=e("label",{class:"label"},"Select Target Category",-1)),e("div",kt,[e("div",wt,[$(e("select",{"onUpdate:modelValue":s[2]||(s[2]=u=>C.value=u)},[s[32]||(s[32]=e("option",{value:""},"Choose a category...",-1)),(d(!0),r(X,null,Y(E.value,u=>(d(),r("option",{key:u.id,value:u.id},c(u.name),9,xt))),128))],512),[[ie,C.value]])])]),e("p",Dt,"All "+c(h.value.length)+" products will be moved to the selected category.",1)])]),e("footer",Pt,[e("button",{class:_(["button is-warning",{"is-loading":P.value}]),onClick:ee,disabled:!C.value||P.value}," Move Products ",10,St),e("button",{class:"button",onClick:z},"Cancel")])])],2)])}}},Mt=Z(It,[["__scopeId","data-v-0c5d5885"]]);export{Mt as default};
