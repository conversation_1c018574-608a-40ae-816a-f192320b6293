using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Marketplace.Infrastructure.Persistence;
using Marketplace.Application.Queries.Reports;

namespace Marketplace.Application.Handlers.Reports;

public class GetSalesReportQueryHandler : BaseReportResultHandler<GetSalesReportQuery>
{
    public GetSalesReportQueryHandler(MarketplaceDbContext context, ILogger<GetSalesReportQueryHandler> logger)
        : base(context, logger)
    {
    }

    protected override async Task<ReportResult> GenerateReportAsync(GetSalesReportQuery request, CancellationToken cancellationToken)
    {
        // Get orders and order items data using base class methods
        var orders = await GetOrdersInRangeAsync(request.StartDate, request.EndDate, cancellationToken);
        var orderIds = orders.Select(o => o.Id).ToList();
        var orderItems = await GetOrderItemsAsync(orderIds, cancellationToken);

        // Calculate metrics
        var totalSales = orderItems.Sum(oi => oi.Quantity);
        var totalRevenue = orders.Sum(o => o.TotalPrice.Amount);
        var totalOrders = orders.Count;
        var averageOrderValue = SafeAverage(totalRevenue, totalOrders);

            // Get top selling products
            var topProducts = orderItems
                .GroupBy(oi => new { oi.Product.Id, oi.Product.Name })
                .Select(g => new
                {
                    ProductId = g.Key.Id,
                    ProductName = g.Key.Name,
                    TotalSold = g.Sum(oi => oi.Quantity),
                    Revenue = g.Sum(oi => oi.Price.Amount * oi.Quantity)
                })
                .OrderByDescending(p => p.TotalSold)
                .Take(10)
                .ToList();

        // Create metrics using base class helper methods
        var metrics = new ReportMetrics
        {
            Items = new List<MetricItem>
            {
                CreateMetric("total_sales", "Total Sales", totalSales, "number", "fas fa-shopping-cart"),
                CreateMetric("total_revenue", "Total Revenue", totalRevenue, "currency", "fas fa-dollar-sign"),
                CreateMetric("total_orders", "Total Orders", totalOrders, "number", "fas fa-receipt"),
                CreateMetric("average_order_value", "Average Order Value", averageOrderValue, "currency", "fas fa-chart-line")
            }
        };

            // Create charts
            var charts = new ReportCharts
            {
                Primary = new ChartData
                {
                    Title = "Top Selling Products",
                    Type = "bar",
                    Data = new ChartDataset
                    {
                        Labels = topProducts.Select(p => p.ProductName).ToList(),
                        Datasets = new List<ChartSeries>
                        {
                            new ChartSeries
                            {
                                Label = "Units Sold",
                                Data = topProducts.Select(p => (decimal)p.TotalSold).ToList(),
                                BackgroundColor = "#3B82F6",
                                BorderColor = "#1E40AF"
                            }
                        }
                    }
                }
            };

            // Create table data
            var table = new ReportTable
            {
                Title = "Product Sales Details",
                Columns = new List<TableColumn>
                {
                    new TableColumn { Key = "product_name", Label = "Product", Type = "text" },
                    new TableColumn { Key = "units_sold", Label = "Units Sold", Type = "number" },
                    new TableColumn { Key = "revenue", Label = "Revenue", Type = "currency" }
                },
                Data = topProducts.Select(p => new Dictionary<string, object>
                {
                    ["product_name"] = p.ProductName,
                    ["units_sold"] = p.TotalSold,
                    ["revenue"] = p.Revenue
                }).ToList(),
                TotalCount = topProducts.Count,
                Page = request.Page,
                PageSize = request.PageSize
            };

            // Create insights
            var insights = new List<ReportInsight>();
            if (topProducts.Any())
            {
                var bestProduct = topProducts.First();
                insights.Add(new ReportInsight
                {
                    Title = "Best Selling Product",
                    Description = $"{bestProduct.ProductName} sold {bestProduct.TotalSold} units",
                    Type = "positive",
                    Icon = "fas fa-trophy",
                    Priority = 5
                });
            }

        // Create summary using base class helper method
        var summary = CreateSummary(
            "Sales Report Summary",
            $"Sales analysis from {request.StartDate:yyyy-MM-dd} to {request.EndDate:yyyy-MM-dd}",
            new Dictionary<string, object>
            {
                ["total_products"] = topProducts.Count,
                ["date_range"] = $"{request.StartDate:yyyy-MM-dd} to {request.EndDate:yyyy-MM-dd}"
            });

        return new ReportResult
        {
            Metrics = metrics,
            Charts = charts,
            Table = table,
            Insights = insights,
            Summary = summary
        };
    }
}
