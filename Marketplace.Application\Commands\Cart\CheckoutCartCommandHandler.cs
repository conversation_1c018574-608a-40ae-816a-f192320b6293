﻿﻿using Marketplace.Domain.Entities;
using Marketplace.Domain.Exceptions;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using MediatR;

namespace Marketplace.Application.Commands.Cart;

public class CheckoutCartCommandHandler : IRequestHandler<CheckoutCartCommand, Guid>
{
    private readonly ICartRepository _cartRepository;
    private readonly ICartItemRepository _cartItemRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly IOrderItemRepository _orderItemRepository;
    private readonly IProductRepository _productRepository;

    public CheckoutCartCommandHandler(
        ICartRepository cartRepository,
        ICartItemRepository cartItemRepository,
        IOrderRepository orderRepository,
        IOrderItemRepository orderItemRepository,
        IProductRepository productRepository)
    {
        _cartRepository = cartRepository;
        _cartItemRepository = cartItemRepository;
        _orderRepository = orderRepository;
        _orderItemRepository = orderItemRepository;
        _productRepository = productRepository;
    }

    public async Task<Guid> Handle(CheckoutCartCommand request, CancellationToken cancellationToken)
    {
        // Отримуємо кошик користувача
        var cart = await _cartRepository.GetByUserIdAsync(request.UserId, cancellationToken);

        if (cart == null)
        {
            throw new DomainException("Кошик не знайдено.");
        }

        // Отримуємо елементи кошика
        var cartItems = await _cartItemRepository.GetByCartIdAsync(cart.Id, cancellationToken);

        if (!cartItems.Any())
        {
            throw new DomainException("Кошик порожній.");
        }

        // Обчислюємо загальну вартість
        decimal totalAmount = 0;
        Currency? currency = null;

        foreach (var item in cartItems)
        {
            var product = await _productRepository.GetByIdAsync(item.ProductId, cancellationToken);

            if (product == null)
            {
                throw new DomainException($"Товар з ID {item.ProductId} не знайдено.");
            }

            if (product.Stock < item.Quantity)
            {
                throw new DomainException($"Недостатня кількість товару '{product.Name}' на складі.");
            }

            if (currency == null)
            {
                currency = product.Price.Currency;
            }
            else if (currency != product.Price.Currency)
            {
                throw new DomainException("Всі товари повинні мати однакову валюту.");
            }

            totalAmount += product.Price.Amount * item.Quantity;
        }

        if (currency == null)
        {
            throw new DomainException("Не вдалося визначити валюту.");
        }

        // Створюємо замовлення
        var order = new Domain.Entities.Order(
            request.UserId,
            new Money(totalAmount, currency.Value),
            request.ShippingAddressId,
            request.ShippingMethodId
        );

        await _orderRepository.AddAsync(order, cancellationToken);

        // Створюємо елементи замовлення
        foreach (var item in cartItems)
        {
            var product = await _productRepository.GetByIdAsync(item.ProductId, cancellationToken);

            // Створюємо елемент замовлення
            var orderItem = new Domain.Entities.OrderItem(
                order.Id,
                item.ProductId,
                item.Quantity,
                product.Price
            );

            await _orderItemRepository.AddAsync(orderItem, cancellationToken);

            // Оновлюємо кількість товару на складі
            product.UpdateStock(product.Stock - item.Quantity);
            await _productRepository.UpdateAsync(product, cancellationToken);
        }

        // Очищаємо кошик
        await _cartItemRepository.DeleteByCartIdAsync(cart.Id, cancellationToken);

        return order.Id;
    }
}
