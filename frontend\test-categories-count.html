<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Categories Count</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .category-hierarchy { margin-left: 20px; }
        .parent { font-weight: bold; color: #2c3e50; }
        .child { color: #3498db; margin-left: 20px; }
        .grandchild { color: #e74c3c; margin-left: 40px; }
    </style>
</head>
<body>
    <h1>Test Categories Count - Should be 139</h1>
    
    <div class="test-section">
        <h2>Categories Count Test</h2>
        <button onclick="testCategoriesCount()">Test Categories Count</button>
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5296';
        
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            resultsDiv.innerHTML += `<div class="${className}">${message}</div>`;
        }
        
        function logTable(data) {
            const resultsDiv = document.getElementById('results');
            let tableHtml = '<table><thead><tr><th>Level</th><th>Count</th><th>Examples</th></tr></thead><tbody>';
            
            data.forEach(row => {
                tableHtml += `<tr>
                    <td>${row.level}</td>
                    <td>${row.count}</td>
                    <td>${row.examples}</td>
                </tr>`;
            });
            
            tableHtml += '</tbody></table>';
            resultsDiv.innerHTML += tableHtml;
        }
        
        async function testCategoriesCount() {
            document.getElementById('results').innerHTML = '';
            
            log('🚀 Starting categories count test...', 'info');
            
            try {
                log('🧪 Testing categories endpoint...', 'info');
                
                const response = await axios.get(`${API_BASE}/api/categories/all?pageSize=200`);
                
                if (!response.data) {
                    log('❌ No data in response', 'error');
                    return;
                }
                
                const total = response.data.total || 0;
                const categories = response.data.data || [];
                
                log(`📊 Total categories in database: ${total}`, total === 139 ? 'success' : 'warning');
                log(`📦 Categories returned: ${categories.length}`, categories.length === 139 ? 'success' : 'warning');
                
                if (total !== 139) {
                    log(`⚠️ Expected 139 categories, but database has ${total}`, 'warning');
                }
                
                if (categories.length !== total) {
                    log(`⚠️ API returned ${categories.length} categories but total is ${total}`, 'warning');
                }
                
                // Analyze category hierarchy
                const parentCategories = categories.filter(cat => !cat.parentId);
                const childCategories = categories.filter(cat => cat.parentId && !categories.some(c => c.parentId === cat.id));
                const grandchildCategories = categories.filter(cat => {
                    const parent = categories.find(c => c.id === cat.parentId);
                    return parent && parent.parentId;
                });
                
                log('📋 Category hierarchy analysis:', 'info');
                
                const hierarchyData = [
                    {
                        level: 'Parent Categories (Root)',
                        count: parentCategories.length,
                        examples: parentCategories.slice(0, 3).map(c => c.name).join(', ')
                    },
                    {
                        level: 'Child Categories (Subcategories)',
                        count: childCategories.length,
                        examples: childCategories.slice(0, 3).map(c => c.name).join(', ')
                    },
                    {
                        level: 'Grandchild Categories (Sub-subcategories)',
                        count: grandchildCategories.length,
                        examples: grandchildCategories.slice(0, 3).map(c => c.name).join(', ')
                    }
                ];
                
                logTable(hierarchyData);
                
                const totalCalculated = parentCategories.length + childCategories.length + grandchildCategories.length;
                
                if (totalCalculated === total) {
                    log(`✅ Hierarchy calculation matches total: ${totalCalculated} = ${total}`, 'success');
                } else {
                    log(`❌ Hierarchy calculation doesn't match: ${totalCalculated} ≠ ${total}`, 'error');
                }
                
                // Test specific category examples
                log('🔍 Testing specific categories:', 'info');
                
                const smartphonesCategory = categories.find(c => c.name.includes('Смартфони'));
                const electronicsCategory = categories.find(c => c.name.includes('електроніка'));
                const oledCategory = categories.find(c => c.name.includes('OLED'));
                const ipadCategory = categories.find(c => c.name.includes('iPad'));
                
                if (smartphonesCategory) {
                    log(`✅ Found "Смартфони" category: ${smartphonesCategory.name}`, 'success');
                } else {
                    log(`❌ "Смартфони" category not found`, 'error');
                }
                
                if (electronicsCategory) {
                    log(`✅ Found electronics category: ${electronicsCategory.name}`, 'success');
                } else {
                    log(`❌ Electronics category not found`, 'error');
                }
                
                if (oledCategory) {
                    log(`✅ Found OLED category: ${oledCategory.name}`, 'success');
                } else {
                    log(`❌ OLED category not found`, 'error');
                }
                
                if (ipadCategory) {
                    log(`✅ Found iPad category: ${ipadCategory.name}`, 'success');
                } else {
                    log(`❌ iPad category not found`, 'error');
                }
                
                // Final result
                if (total === 139 && categories.length === 139) {
                    log('🎯 SUCCESS: All 139 categories are available!', 'success');
                } else {
                    log(`❌ ISSUE: Expected 139 categories, got ${categories.length}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Error testing categories: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
            
            log('🏁 Test completed!', 'info');
        }
        
        // Auto-run test on page load
        window.onload = () => {
            testCategoriesCount();
        };
    </script>
</body>
</html>
