<template>
  <div class="user-list">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Users</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <button
            class="button is-primary"
            @click="openCreateModal">
            <span class="icon">
              <i class="fas fa-plus"></i>
            </span>
            <span>Add User</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
      <div class="card-content">
        <div class="columns is-multiline">
          <div class="column is-5">
            <div class="field">
              <label class="label">Search</label>
              <div class="control has-icons-left">
                <input
                  class="input"
                  type="text"
                  placeholder="Name, email, username..."
                  v-model="filters.search">
                <span class="icon is-small is-left">
                  <i class="fas fa-search"></i>
                </span>
              </div>
            </div>
          </div>
          <div class="column is-5">
            <div class="field">
              <label class="label">Role</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="filters.role">
                    <option value="">All Roles</option>
                    <option :value="ROLE_KEYS.ADMIN">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.ADMIN] }}</option>
                    <option :value="ROLE_KEYS.MODERATOR">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.MODERATOR] }}</option>
                    <option :value="ROLE_KEYS.SELLER">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.SELLER] }}</option>
                    <option :value="ROLE_KEYS.SELLER_OWNER">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.SELLER_OWNER] }}</option>
                    <option :value="ROLE_KEYS.BUYER">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.BUYER] }}</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="column is-2">
            <div class="field" style="margin-top: 1.9rem;">
              <div class="buttons is-right">
                <button
                  class="button is-light"
                  @click="resetFilters"
                  :class="{ 'is-loading': loading }">
                  Reset Filters
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Users Table -->
    <div class="card">
      <div class="card-content">
        <!-- Status bar with filter info and counts -->
        <div class="level mb-4" v-if="!loading || users.length > 0">
          <div class="level-left">
            <div class="level-item">
              <p>
                <strong>{{ totalItems }}</strong> users found
                <span v-if="filters.search || filters.role">
                  with filters:
                  <span v-if="filters.search" class="tag is-info is-light mr-1">
                    Search: {{ filters.search }}
                  </span>
                  <span v-if="filters.role" class="tag is-info is-light">
                    Role: {{ formatRole(filters.role) }}
                  </span>
                </span>
              </p>
            </div>
          </div>
        </div>

        <!-- Показуємо індикатор завантаження тільки при першому завантаженні -->
        <div v-if="loading && isFirstLoad" class="has-text-centered py-6">
          <span class="icon is-large">
            <i class="fas fa-spinner fa-pulse fa-2x"></i>
          </span>
          <p class="mt-2">Loading users...</p>
        </div>

        <!-- Показуємо повідомлення про відсутність користувачів -->
        <div v-else-if="!loading && !users.length" class="has-text-centered py-6">
          <span class="icon is-large">
            <i class="fas fa-users fa-2x"></i>
          </span>
          <p class="mt-2">No users found</p>
          <p class="mt-2">Try adjusting your filters or add a new user</p>
          <div class="mt-4">
            <button
              class="button is-primary"
              @click="openCreateModal">
              <span class="icon">
                <i class="fas fa-plus"></i>
              </span>
              <span>Add User</span>
            </button>
          </div>
        </div>

        <!-- Показуємо таблицю користувачів, навіть під час оновлення даних -->
        <div v-else>
          <div class="table-container" :class="{ 'is-loading': loading && !isFirstLoad }">
            <table class="table is-fullwidth is-hoverable">
              <thead>
                <tr>
                  <th>Username</th>
                  <th>Email</th>
                  <th>Role</th>
                  <th>Registered</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="user in users" :key="user.id">
                  <td>{{ user.username }}</td>
                  <td>{{ user.email }}</td>
                  <td>
                    <span class="tag" :class="getRoleClass(user.role)">
                      {{ getRoleDisplayName(user.role) }}
                    </span>
                  </td>
                  <td>{{ formatDate(user.createdAt || user.emailConfirmedAt) }}</td>
                  <td>
                    <div class="buttons are-small">
                      <router-link
                        :to="`/admin/users/${user.id}`"
                        class="button is-info"
                        title="View">
                        <span class="icon is-small">
                          <i class="fas fa-eye"></i>
                        </span>
                      </router-link>
                      <router-link
                        v-if="canEditUser(user)"
                        :to="`/admin/users/${user.id}/edit`"
                        class="button is-primary"
                        title="Edit">
                        <span class="icon is-small">
                          <i class="fas fa-edit"></i>
                        </span>
                      </router-link>

                      <button
                        v-if="canDeleteUser(user)"
                        class="button is-danger"
                        @click="confirmDelete(user)"
                        title="Delete">
                        <span class="icon is-small">
                          <i class="fas fa-trash"></i>
                        </span>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Індикатор завантаження, який накладається на таблицю -->
            <div v-if="loading && !isFirstLoad" class="table-loading-overlay">
              <div class="loading-spinner">
                <i class="fas fa-spinner fa-pulse"></i>
              </div>
            </div>
          </div>

          <!-- Pagination -->
          <pagination
            :current-page="currentPage"
            :total-pages="totalPages"
            @page-changed="handlePageChange" />
        </div>
      </div>
    </div>

    <!-- User Form Modal -->
    <div class="modal" :class="{ 'is-active': showUserModal }">
      <div class="modal-background" @click="closeUserModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">{{ isEditMode ? 'Edit User' : 'Add User' }}</p>
          <button class="delete" aria-label="close" @click="closeUserModal"></button>
        </header>
        <section class="modal-card-body">


          <div class="field">
            <label class="label">Username*</label>
            <div class="control">
              <input
                class="input"
                type="text"
                v-model="userForm.username"
                required
                placeholder="Enter username">
            </div>
          </div>

          <div class="field">
            <label class="label">Email*</label>
            <div class="control">
              <input
                class="input"
                type="email"
                v-model="userForm.email"
                required
                placeholder="Enter email">
            </div>
          </div>

          <div class="field" v-if="!isEditMode">
            <label class="label">Password*</label>
            <div class="control">
              <input
                class="input"
                type="password"
                v-model="userForm.password"
                required
                placeholder="Enter password">
            </div>
          </div>

          <div class="field">
            <label class="label">Role*</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="userForm.role" required>
                  <option v-if="isAdmin" :value="ROLE_KEYS.ADMIN">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.ADMIN] }}</option>
                  <option :value="ROLE_KEYS.MODERATOR">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.MODERATOR] }}</option>
                  <option :value="ROLE_KEYS.SELLER">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.SELLER] }}</option>
                  <option :value="ROLE_KEYS.SELLER_OWNER">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.SELLER_OWNER] }}</option>
                  <option :value="ROLE_KEYS.BUYER">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.BUYER] }}</option>
                </select>
              </div>
            </div>
          </div>


        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-primary"
            @click="saveUser"
            :class="{ 'is-loading': saving }"
            :disabled="!isFormValid">
            {{ isEditMode ? 'Update User' : 'Create User' }}
          </button>
          <button class="button" @click="closeUserModal">Cancel</button>
        </footer>
      </div>
    </div>



    <!-- Delete Confirmation Modal -->
    <confirm-dialog
      :is-open="showDeleteModal"
      title="Delete User"
      :message="`Are you sure you want to delete ${userToDelete?.username}? This action cannot be undone.`"
      confirm-text="Delete"
      cancel-text="Cancel"
      @confirm="deleteUser"
      @cancel="cancelDelete" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useStore } from 'vuex';
import { usersService } from '@/admin/services/users';
import Pagination from '@/admin/components/common/Pagination.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
import {
  getRoleKey,
  getRoleDisplayName,
  getRoleClass,
  compareRoles,
  ROLE_KEYS,
  ROLE_DISPLAY_NAMES
} from '@/admin/services/roles';

// Store
const store = useStore();

// State
const users = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const itemsPerPage = ref(10);

// User form state
const showUserModal = ref(false);
const isEditMode = ref(false);
const saving = ref(false);
const userForm = reactive({
  username: '',
  email: '',
  password: '',
  role: ROLE_KEYS.BUYER
});



// Delete state
const showDeleteModal = ref(false);
const userToDelete = ref(null);

// Filters
const filters = reactive({
  search: '',
  role: ''
});

// Computed properties
const isAdmin = computed(() => store.getters['auth/isAdmin']);
const isModerator = computed(() => store.getters['auth/isModerator']);

const isFormValid = computed(() => {
  if (isEditMode.value) {
    return userForm.username && userForm.email && userForm.role;
  } else {
    return userForm.username && userForm.email && userForm.password && userForm.role;
  }
});

// Функція для перевірки, чи може користувач видалити іншого користувача
const canDeleteUser = (user) => {
  // Адміністратори можуть видаляти всіх
  if (isAdmin.value) return true;

  // Модератори не можуть видаляти адміністраторів
  if (isModerator.value && user.role === 'admin') return false;

  // Модератори можуть видаляти всіх інших
  if (isModerator.value) return true;

  return false;
};

// Функція для перевірки, чи може користувач редагувати іншого користувача
const canEditUser = (user) => {
  // Адміністратори можуть редагувати всіх
  if (isAdmin.value) return true;

  // Модератори не можуть редагувати адміністраторів
  if (isModerator.value && user.role === 'admin') return false;

  // Модератори можуть редагувати всіх інших
  if (isModerator.value) return true;

  return false;
};

// Змінна для відстеження першого завантаження
const isFirstLoad = ref(true);

// Fetch users
const fetchUsers = async (page = 1) => {
  // Встановлюємо loading тільки якщо це перше завантаження або змінилася сторінка
  if (isFirstLoad.value || currentPage.value !== page) {
    loading.value = true;
  }

  currentPage.value = page;

  try {
    // Convert filters to backend format
    const apiParams = {
      page: currentPage.value,
      pageSize: itemsPerPage.value
      // Не використовуємо сортування за роллю на бекенді, оскільки це викликає помилку
      // Сортування буде виконано на клієнті
    };

    // Не додаємо параметр пошуку до API-запиту, оскільки він викликає помилку
    // Пошук буде виконано на клієнті після отримання даних
    // Зберігаємо пошуковий запит для подальшого використання
    const searchQuery = filters.search && filters.search.trim() !== '' ?
                       filters.search.trim().toLowerCase() : '';

    // Add role parameter if present
    if (filters.role && filters.role.trim() !== '') {
      // Перетворюємо роль у правильний формат для API
      const roleMap = {
        'admin': 4,
        'seller': 1,
        'buyer': 0,
        'moderator': 3,
        'sellerowner': 2
      };

      const roleLower = filters.role.toLowerCase();
      if (roleMap.hasOwnProperty(roleLower)) {
        apiParams.role = roleMap[roleLower];
      }

      console.log('Adding role parameter:', filters.role, 'converted to:', apiParams.role);
    }

    console.log('API parameters:', apiParams);

    const response = await usersService.getUsers(apiParams);
    console.log('Response from API:', response);

    if (response.users) {
      // Обробляємо кожного користувача перед відображенням
      let processedUsers = response.users.map(user => {
        // Отримуємо рядкове представлення ролі
        const roleKey = getRoleKey(user.role);

        console.log(`Processing user ${user.username}: original role = ${user.role}, converted role = ${roleKey}`);

        // Нормалізуємо роль для всіх користувачів
        return {
          ...user,
          // Зберігаємо оригінальну роль для відправки на сервер
          originalRole: user.role,
          // Перетворюємо роль на рядок для відображення
          role: roleKey
        };
      });

      // Сортуємо користувачів за ролями: admin > moderator > seller > buyer
      console.log('Before sorting:', processedUsers.map(u => `${u.username} (${u.role})`));

      processedUsers.sort((a, b) => {
        console.log(`Sorting: ${a.username} (${a.role}) vs ${b.username} (${b.role})`);

        // Використовуємо функцію compareRoles для сортування
        const result = compareRoles(a.role, b.role);

        console.log(`Sort result: ${result}`);
        return result;
      });

      console.log('After sorting:', processedUsers.map(u => `${u.username} (${u.role})`));

      // Фільтруємо користувачів за пошуковим запитом на стороні клієнта
      if (searchQuery) {
        console.log('Filtering users by search query:', searchQuery);

        // Зберігаємо оригінальну кількість користувачів для логування
        const originalCount = processedUsers.length;

        processedUsers = processedUsers.filter(user => {
          // Шукаємо в імені користувача
          const usernameMatch = user.username &&
                               user.username.toLowerCase().includes(searchQuery);

          // Шукаємо в email
          const emailMatch = user.email &&
                            user.email.toLowerCase().includes(searchQuery);

          const match = usernameMatch || emailMatch;

          if (match) {
            console.log(`User ${user.username} matches search query`);
          }

          return match;
        });

        console.log(`Users after search filtering: ${processedUsers.length} (filtered from ${originalCount})`);
      }

      users.value = processedUsers;

      console.log('Final processed users:', users.value.length);
      console.log('Users by role:', users.value.reduce((acc, user) => {
        const role = typeof user.role === 'string' ? user.role.toLowerCase() : 'unknown';
        acc[role] = (acc[role] || 0) + 1;
        return acc;
      }, {}));
    } else {
      users.value = [];
    }

    // Оновлюємо інформацію про пагінацію
    if (response.pagination) {
      // Якщо є пошуковий запит, показуємо кількість знайдених користувачів
      if (searchQuery) {
        totalItems.value = users.value.length;
        // Розраховуємо кількість сторінок на основі кількості знайдених користувачів
        totalPages.value = Math.ceil(totalItems.value / itemsPerPage.value) || 1;
      } else {
        // Інакше використовуємо дані з сервера
        totalItems.value = response.pagination.total;
        totalPages.value = response.pagination.totalPages;
      }
      currentPage.value = response.pagination.page;
    }
  } catch (error) {
    console.error('Error fetching users:', error);

    // Log detailed error information for debugging
    if (error.response) {
      console.error('Server response error:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Request setup error:', error.message);
    }

    users.value = [];
    totalPages.value = 1;
    totalItems.value = 0;

    // Show user-friendly error notification
    alert('Failed to fetch users. Please check your connection and try again.');
  } finally {
    loading.value = false;

    // Після першого успішного завантаження встановлюємо isFirstLoad в false
    if (isFirstLoad.value) {
      isFirstLoad.value = false;
    }
  }
};

// Reset filters
const resetFilters = () => {
  filters.search = '';
  filters.role = '';
};

// Watchers for real-time filtering
// Debounce search to avoid too many requests
let searchTimeout = null;
watch(() => filters.search, (newValue) => {
  console.log('Search changed:', newValue);

  // Clear previous timeout
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }

  // Set new timeout (300ms debounce)
  searchTimeout = setTimeout(() => {
    fetchUsers(1);
  }, 300);
});

// Immediate filter for role changes
watch(() => filters.role, () => {
  console.log('Role changed:', filters.role);
  fetchUsers(1);
});

// Handle page change
const handlePageChange = (page) => {
  fetchUsers(page);
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(dateString));
};

// Виводимо порядок ролей для відлагодження
console.log('Role order for sorting:', ROLE_KEYS, ROLE_DISPLAY_NAMES);

// Функція для форматування ролі для відображення
const formatRole = (role) => {
  return getRoleDisplayName(role);
};

// Open create modal
const openCreateModal = () => {
  isEditMode.value = false;

  // Reset form
  userForm.username = '';
  userForm.email = '';
  userForm.password = '';
  userForm.role = 'Buyer';

  showUserModal.value = true;
};

// Open edit modal
const openEditModal = (user) => {
  isEditMode.value = true;

  // Fill form with user data
  userForm.id = user.id;
  userForm.username = user.username;
  userForm.email = user.email;
  userForm.password = '';

  // Використовуємо оригінальну роль, якщо вона є, інакше використовуємо поточну роль
  userForm.role = user.originalRole !== undefined ? user.originalRole : user.role;

  showUserModal.value = true;
};

// Close user modal
const closeUserModal = () => {
  showUserModal.value = false;
};

// Save user
const saveUser = async () => {
  if (!isFormValid.value) return;

  saving.value = true;

  try {
    if (isEditMode.value) {
      // Підготуємо дані для оновлення, видаливши порожні поля
      const userData = {
        username: userForm.username,
        email: userForm.email,
        role: userForm.role
      };

      // Додаємо пароль тільки якщо він був введений
      if (userForm.password) {
        userData.password = userForm.password;
      }

      console.log('Updating user with data:', userData);

      // Update existing user
      await usersService.updateUser(userForm.id, userData);

      // Оновлюємо користувача в списку
      const index = users.value.findIndex(u => u.id === userForm.id);
      if (index !== -1) {
        // Зберігаємо оригінальну роль для подальшого використання
        const originalRole = userData.role;

        // Перетворюємо роль на рядок для відображення
        const displayRole = getRoleKey(userData.role);

        // Оновлюємо користувача в списку
        users.value[index] = {
          ...users.value[index],
          ...userData,
          originalRole,
          role: displayRole
        };

        // Пересортовуємо список після оновлення ролі
        console.log('Before resorting after update:', users.value.map(u => `${u.username} (${u.role})`));

        users.value.sort((a, b) => {
          console.log(`Resorting after update: ${a.username} (${a.role}) vs ${b.username} (${b.role})`);

          // Використовуємо функцію compareRoles для сортування
          const result = compareRoles(a.role, b.role);

          console.log(`Sort result: ${result}`);
          return result;
        });

        console.log('After resorting after update:', users.value.map(u => `${u.username} (${u.role})`));
      }
    } else {
      // Підготуємо дані для створення нового користувача
      const userData = {
        username: userForm.username,
        email: userForm.email,
        password: userForm.password,
        role: userForm.role
      };

      console.log('Creating user with data:', userData);

      // Create new user
      const newUser = await usersService.createUser(userData);

      // Додаємо нового користувача до списку, якщо ми на першій сторінці
      if (currentPage.value === 1) {
        // Зберігаємо оригінальну роль для подальшого використання
        const originalRole = newUser.role;

        // Перетворюємо роль на рядок для відображення
        const displayRole = getRoleKey(newUser.role);

        // Додаємо користувача до списку
        const processedUser = {
          ...newUser,
          originalRole,
          role: displayRole
        };

        // Додаємо користувача на початок списку
        users.value.unshift(processedUser);

        // Пересортовуємо список після додавання нового користувача
        console.log('Before resorting after add:', users.value.map(u => `${u.username} (${u.role})`));

        users.value.sort((a, b) => {
          console.log(`Resorting after add: ${a.username} (${a.role}) vs ${b.username} (${b.role})`);

          // Використовуємо функцію compareRoles для сортування
          const result = compareRoles(a.role, b.role);

          console.log(`Sort result: ${result}`);
          return result;
        });

        console.log('After resorting after add:', users.value.map(u => `${u.username} (${u.role})`));
      } else {
        // Оновлюємо весь список, якщо ми не на першій сторінці
        fetchUsers(1);
      }
    }

    // Close modal
    closeUserModal();
  } catch (error) {
    console.error('Error saving user:', error);
    alert('Failed to save user. Please check the console for details.');
  } finally {
    saving.value = false;
  }
};



// Confirm delete
const confirmDelete = (user) => {
  userToDelete.value = user;
  showDeleteModal.value = true;
};

// Cancel delete
const cancelDelete = () => {
  showDeleteModal.value = false;
  userToDelete.value = null;
};

// Delete user
const deleteUser = async () => {
  if (!userToDelete.value) return;

  try {
    await usersService.deleteUser(userToDelete.value.id);

    // Remove from list
    users.value = users.value.filter(u => u.id !== userToDelete.value.id);

    showDeleteModal.value = false;
    userToDelete.value = null;
  } catch (error) {
    console.error('Error deleting user:', error);
  }
};

// Lifecycle hooks
onMounted(() => {
  fetchUsers();
});
</script>

<style scoped>
.user-list {
  padding: 1.5rem;
}

.title {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.table {
  width: 100%;
  table-layout: fixed;
}

.table th {
  font-weight: 600;
  color: #363636;
  background-color: #f9f9f5;
}

.table td {
  vertical-align: middle;
}

/* Встановлюємо ширину для кожної колонки */
.table th:nth-child(1), .table td:nth-child(1) { /* Username */
  width: 20%;
}

.table th:nth-child(2), .table td:nth-child(2) { /* Email */
  width: 30%;
}

.table th:nth-child(3), .table td:nth-child(3) { /* Role */
  width: 15%;
  text-align: center;
}

.table th:nth-child(4), .table td:nth-child(4) { /* Registered */
  width: 15%;
  text-align: center;
}

.table th:nth-child(5), .table td:nth-child(5) { /* Actions */
  width: 20%;
  text-align: center;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

/* Покращення відображення кнопок дій */
.buttons.are-small {
  justify-content: center;
}

/* Покращення відображення тегів ролей */
.tag {
  min-width: 80px;
  justify-content: center;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

/* Стилі для індикатора завантаження */
.table-container.is-loading {
  position: relative;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.table-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.5);
  z-index: 10;
}

.loading-spinner {
  font-size: 2rem;
  color: #ff7700;
}
</style>
