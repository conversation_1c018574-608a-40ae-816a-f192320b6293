<template>
  <div class="admin-page">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <h1 class="admin-page-title">
          <i class="fas fa-users admin-page-icon"></i>
          Users Management
        </h1>
        <p class="admin-page-subtitle">Manage user accounts, roles, and permissions</p>
      </div>
      <div class="admin-page-actions">
        <button
          class="admin-btn admin-btn-primary"
          @click="openCreateModal">
          <i class="fas fa-plus"></i>
          Add User
        </button>
      </div>
    </div>

    <!-- Search and Filters -->
    <search-and-filters
      v-model:search="filters.search"
      v-model:filters="activeFilters"
      :filter-options="filterOptions"
      :loading="loading"
      @clear-filters="clearFilters"
      @apply-filters="applyFilters">

      <!-- Custom filter slots -->
      <template #custom-filters>
        <div class="admin-filter-group">
          <label class="admin-filter-label">Role</label>
          <select v-model="filters.role" class="admin-filter-select">
            <option value="">All Roles</option>
            <option :value="ROLE_KEYS.ADMIN">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.ADMIN] }}</option>
            <option :value="ROLE_KEYS.MODERATOR">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.MODERATOR] }}</option>
            <option :value="ROLE_KEYS.SELLER">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.SELLER] }}</option>
            <option :value="ROLE_KEYS.SELLER_OWNER">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.SELLER_OWNER] }}</option>
            <option :value="ROLE_KEYS.BUYER">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.BUYER] }}</option>
          </select>
        </div>
      </template>
    </search-and-filters>

    <!-- Loading State -->
    <div v-if="loading && isFirstLoad" class="admin-loading-state">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading users...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="admin-error-state">
      <div class="admin-error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <h3 class="admin-error-title">Error Loading Users</h3>
      <p class="admin-error-message">{{ error }}</p>
      <button @click="loadUsers" class="admin-btn admin-btn-primary">
        <i class="fas fa-refresh"></i>
        Try Again
      </button>
    </div>

    <!-- Empty State -->
    <div v-else-if="!users.length" class="admin-empty-state">
      <div class="admin-empty-icon">
        <i class="fas fa-users"></i>
      </div>
      <h3 class="admin-empty-title">No Users Found</h3>
      <p class="admin-empty-message">
        {{ filters.search || filters.role ? 'No users match your current filters.' : 'No users have been created yet.' }}
      </p>
      <button v-if="!filters.search && !filters.role" @click="openCreateModal" class="admin-btn admin-btn-primary">
        <i class="fas fa-plus"></i>
        Add First User
      </button>
    </div>

    <!-- Users Table -->
    <div v-else class="admin-card">
      <div class="admin-card-header">
        <h3 class="admin-card-title">
          <i class="fas fa-table"></i>
          Users List
        </h3>
        <div class="admin-card-actions">
          <span class="admin-results-count">{{ totalItems }} users found</span>
        </div>
      </div>
      <div class="admin-card-content">

        <div class="admin-table-container">
          <table class="admin-table">
            <thead class="admin-table-header">
              <tr>
                <th class="admin-table-th">Username</th>
                <th class="admin-table-th">Email</th>
                <th class="admin-table-th">Role</th>
                <th class="admin-table-th">Registered</th>
                <th class="admin-table-th admin-table-th-actions">Actions</th>
              </tr>
            </thead>
            <tbody class="admin-table-body">
              <tr v-for="user in users" :key="user.id" class="admin-table-row">
                <td class="admin-table-td">
                  <div class="admin-user-info">
                    <div class="admin-user-avatar">
                      <i class="fas fa-user"></i>
                    </div>
                    <div class="admin-user-details">
                      <div class="admin-user-name">{{ user.username }}</div>
                      <div class="admin-user-id">ID: {{ user.id.substring(0, 8) }}...</div>
                    </div>
                  </div>
                </td>
                <td class="admin-table-td">
                  <div class="admin-email">{{ user.email }}</div>
                </td>
                <td class="admin-table-td">
                  <span class="admin-badge" :class="getRoleClass(user.role)">
                    {{ getRoleDisplayName(user.role) }}
                  </span>
                </td>
                <td class="admin-table-td">
                  <div class="admin-date">{{ formatDate(user.createdAt || user.emailConfirmedAt) }}</div>
                </td>
                <td class="admin-table-td admin-table-td-actions">
                  <div class="admin-table-actions">
                    <router-link
                      :to="`/admin/users/${user.id}`"
                      class="admin-btn admin-btn-sm admin-btn-secondary"
                      title="View Details">
                      <i class="fas fa-eye"></i>
                    </router-link>
                    <router-link
                      v-if="canEditUser(user)"
                      :to="`/admin/users/${user.id}/edit`"
                      class="admin-btn admin-btn-sm admin-btn-primary"
                      title="Edit User">
                      <i class="fas fa-edit"></i>
                    </router-link>

                    <button
                      v-if="canDeleteUser(user)"
                      class="admin-btn admin-btn-sm admin-btn-danger"
                      @click="confirmDelete(user)"
                      title="Delete User">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Pagination -->
      <pagination
        :current-page="currentPage"
        :total-pages="totalPages"
        @page-changed="handlePageChange" />
    </div>

    <!-- User Form Modal -->
    <div class="admin-modal" :class="{ 'admin-modal-active': showUserModal }">
      <div class="admin-modal-backdrop" @click="closeUserModal"></div>
      <div class="admin-modal-content">
        <div class="admin-modal-header">
          <h3 class="admin-modal-title">
            <i class="fas fa-user-plus"></i>
            {{ isEditMode ? 'Edit User' : 'Add User' }}
          </h3>
          <button class="admin-modal-close" @click="closeUserModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="admin-modal-body">


          <div class="admin-form-grid">
            <div class="admin-form-field">
              <label class="admin-form-label">Username*</label>
              <input
                class="admin-form-input"
                type="text"
                v-model="userForm.username"
                required
                placeholder="Enter username">
            </div>

            <div class="admin-form-field">
              <label class="admin-form-label">Email*</label>
              <input
                class="admin-form-input"
                type="email"
                v-model="userForm.email"
                required
                placeholder="Enter email">
            </div>

            <div class="admin-form-field" v-if="!isEditMode">
              <label class="admin-form-label">Password*</label>
              <input
                class="admin-form-input"
                type="password"
                v-model="userForm.password"
                required
                placeholder="Enter password">
            </div>

            <div class="admin-form-field">
              <label class="admin-form-label">Role*</label>
              <select class="admin-form-select" v-model="userForm.role" required>
                <option v-if="isAdmin" :value="ROLE_KEYS.ADMIN">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.ADMIN] }}</option>
                <option :value="ROLE_KEYS.MODERATOR">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.MODERATOR] }}</option>
                <option :value="ROLE_KEYS.SELLER">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.SELLER] }}</option>
                <option :value="ROLE_KEYS.SELLER_OWNER">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.SELLER_OWNER] }}</option>
                <option :value="ROLE_KEYS.BUYER">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.BUYER] }}</option>
              </select>
            </div>
          </div>
        </div>
        <div class="admin-modal-footer">
          <button
            class="admin-btn admin-btn-secondary"
            @click="closeUserModal">
            <i class="fas fa-times"></i>
            Cancel
          </button>
          <button
            class="admin-btn admin-btn-primary"
            @click="saveUser"
            :disabled="!isFormValid || saving">
            <i class="fas fa-save" :class="{ 'fa-spinner fa-pulse': saving }"></i>
            {{ isEditMode ? 'Update User' : 'Create User' }}
          </button>
        </div>
      </div>
    </div>



    <!-- Delete Confirmation Modal -->
    <confirm-dialog
      :is-open="showDeleteModal"
      title="Delete User"
      :message="`Are you sure you want to delete ${userToDelete?.username}? This action cannot be undone.`"
      confirm-text="Delete"
      cancel-text="Cancel"
      @confirm="deleteUser"
      @cancel="cancelDelete" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useStore } from 'vuex';
import { usersService } from '@/admin/services/users';
import SearchAndFilters from '@/admin/components/common/SearchAndFilters.vue';
import Pagination from '@/admin/components/common/Pagination.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
import {
  getRoleKey,
  getRoleDisplayName,
  getRoleClass,
  compareRoles,
  ROLE_KEYS,
  ROLE_DISPLAY_NAMES
} from '@/admin/services/roles';

// Store
const store = useStore();

// State
const users = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const itemsPerPage = ref(10);

// User form state
const showUserModal = ref(false);
const isEditMode = ref(false);
const saving = ref(false);
const userForm = reactive({
  username: '',
  email: '',
  password: '',
  role: ROLE_KEYS.BUYER
});



// Delete state
const showDeleteModal = ref(false);
const userToDelete = ref(null);

// Filters
const filters = reactive({
  search: '',
  role: ''
});

// Filter options for SearchAndFilters component
const filterOptions = computed(() => [
  {
    key: 'role',
    label: 'Role',
    type: 'select',
    options: [
      { value: '', label: 'All Roles' },
      { value: ROLE_KEYS.ADMIN, label: ROLE_DISPLAY_NAMES[ROLE_KEYS.ADMIN] },
      { value: ROLE_KEYS.MODERATOR, label: ROLE_DISPLAY_NAMES[ROLE_KEYS.MODERATOR] },
      { value: ROLE_KEYS.SELLER, label: ROLE_DISPLAY_NAMES[ROLE_KEYS.SELLER] },
      { value: ROLE_KEYS.SELLER_OWNER, label: ROLE_DISPLAY_NAMES[ROLE_KEYS.SELLER_OWNER] },
      { value: ROLE_KEYS.BUYER, label: ROLE_DISPLAY_NAMES[ROLE_KEYS.BUYER] }
    ]
  }
]);

// Active filters for display
const activeFilters = computed(() => {
  const active = [];
  if (filters.search) {
    active.push({ key: 'search', label: 'Search', value: filters.search });
  }
  if (filters.role) {
    active.push({ key: 'role', label: 'Role', value: formatRole(filters.role) });
  }
  return active;
});

// Error state
const error = ref(null);
const isFirstLoad = ref(true);

// Computed properties
const isAdmin = computed(() => store.getters['auth/isAdmin']);
const isModerator = computed(() => store.getters['auth/isModerator']);

const isFormValid = computed(() => {
  if (isEditMode.value) {
    return userForm.username && userForm.email && userForm.role;
  } else {
    return userForm.username && userForm.email && userForm.password && userForm.role;
  }
});

// Функція для перевірки, чи може користувач видалити іншого користувача
const canDeleteUser = (user) => {
  // Адміністратори можуть видаляти всіх
  if (isAdmin.value) return true;

  // Модератори не можуть видаляти адміністраторів
  if (isModerator.value && user.role === 'admin') return false;

  // Модератори можуть видаляти всіх інших
  if (isModerator.value) return true;

  return false;
};

// Функція для перевірки, чи може користувач редагувати іншого користувача
const canEditUser = (user) => {
  // Адміністратори можуть редагувати всіх
  if (isAdmin.value) return true;

  // Модератори не можуть редагувати адміністраторів
  if (isModerator.value && user.role === 'admin') return false;

  // Модератори можуть редагувати всіх інших
  if (isModerator.value) return true;

  return false;
};

// Filter methods
const clearFilters = () => {
  filters.search = '';
  filters.role = '';
  currentPage.value = 1;
  loadUsers();
};

const applyFilters = () => {
  currentPage.value = 1;
  loadUsers();
};

// Format role for display
const formatRole = (role) => {
  return ROLE_DISPLAY_NAMES[role] || role;
};

// Load users with error handling
const loadUsers = async (page = 1) => {
  if (isFirstLoad.value || currentPage.value !== page) {
    loading.value = true;
  }

  error.value = null;
  currentPage.value = page;

  try {
    const apiParams = {
      page: currentPage.value,
      pageSize: itemsPerPage.value
    };

    // Add search parameter
    if (filters.search && filters.search.trim()) {
      apiParams.search = filters.search.trim();
    }

    // Add role parameter
    if (filters.role && filters.role.trim()) {
      const roleMap = {
        'admin': 4,
        'seller': 1,
        'buyer': 0,
        'moderator': 3,
        'sellerowner': 2
      };

      const roleLower = filters.role.toLowerCase();
      if (roleMap.hasOwnProperty(roleLower)) {
        apiParams.role = roleMap[roleLower];
      }
    }

    const response = await usersService.getUsers(apiParams);

    if (response.users) {
      // Process users data
      const processedUsers = response.users.map(user => ({
        ...user,
        originalRole: user.role,
        role: getRoleKey(user.role)
      }));

      // Sort users by role priority
      processedUsers.sort((a, b) => compareRoles(a.role, b.role));

      users.value = processedUsers;

    } else {
      users.value = [];
    }

    // Update pagination info
    if (response.pagination) {
      totalItems.value = response.pagination.total;
      totalPages.value = response.pagination.totalPages;
      currentPage.value = response.pagination.page;
    }
  } catch (err) {
    console.error('Error loading users:', err);
    error.value = err.response?.data?.message || err.message || 'Failed to load users';
    users.value = [];
    totalPages.value = 1;
    totalItems.value = 0;
  } finally {
    loading.value = false;
    isFirstLoad.value = false;
  }
};

// Handle page change
const handlePageChange = (page) => {
  loadUsers(page);
};

// Watchers for real-time filtering
let searchTimeout = null;
watch(() => filters.search, () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }
  searchTimeout = setTimeout(() => {
    currentPage.value = 1;
    loadUsers();
  }, 300);
});

watch(() => filters.role, () => {
  currentPage.value = 1;
  loadUsers();
});

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(dateString));
};

// Виводимо порядок ролей для відлагодження
console.log('Role order for sorting:', ROLE_KEYS, ROLE_DISPLAY_NAMES);

// Open create modal
const openCreateModal = () => {
  isEditMode.value = false;

  // Reset form
  userForm.username = '';
  userForm.email = '';
  userForm.password = '';
  userForm.role = 'Buyer';

  showUserModal.value = true;
};

// Open edit modal
const openEditModal = (user) => {
  isEditMode.value = true;

  // Fill form with user data
  userForm.id = user.id;
  userForm.username = user.username;
  userForm.email = user.email;
  userForm.password = '';

  // Використовуємо оригінальну роль, якщо вона є, інакше використовуємо поточну роль
  userForm.role = user.originalRole !== undefined ? user.originalRole : user.role;

  showUserModal.value = true;
};

// Close user modal
const closeUserModal = () => {
  showUserModal.value = false;
};

// Save user
const saveUser = async () => {
  if (!isFormValid.value) return;

  saving.value = true;

  try {
    if (isEditMode.value) {
      // Підготуємо дані для оновлення, видаливши порожні поля
      const userData = {
        username: userForm.username,
        email: userForm.email,
        role: userForm.role
      };

      // Додаємо пароль тільки якщо він був введений
      if (userForm.password) {
        userData.password = userForm.password;
      }

      console.log('Updating user with data:', userData);

      // Update existing user
      await usersService.updateUser(userForm.id, userData);

      // Оновлюємо користувача в списку
      const index = users.value.findIndex(u => u.id === userForm.id);
      if (index !== -1) {
        // Зберігаємо оригінальну роль для подальшого використання
        const originalRole = userData.role;

        // Перетворюємо роль на рядок для відображення
        const displayRole = getRoleKey(userData.role);

        // Оновлюємо користувача в списку
        users.value[index] = {
          ...users.value[index],
          ...userData,
          originalRole,
          role: displayRole
        };

        // Пересортовуємо список після оновлення ролі
        console.log('Before resorting after update:', users.value.map(u => `${u.username} (${u.role})`));

        users.value.sort((a, b) => {
          console.log(`Resorting after update: ${a.username} (${a.role}) vs ${b.username} (${b.role})`);

          // Використовуємо функцію compareRoles для сортування
          const result = compareRoles(a.role, b.role);

          console.log(`Sort result: ${result}`);
          return result;
        });

        console.log('After resorting after update:', users.value.map(u => `${u.username} (${u.role})`));
      }
    } else {
      // Підготуємо дані для створення нового користувача
      const userData = {
        username: userForm.username,
        email: userForm.email,
        password: userForm.password,
        role: userForm.role
      };

      console.log('Creating user with data:', userData);

      // Create new user
      const newUser = await usersService.createUser(userData);

      // Додаємо нового користувача до списку, якщо ми на першій сторінці
      if (currentPage.value === 1) {
        // Зберігаємо оригінальну роль для подальшого використання
        const originalRole = newUser.role;

        // Перетворюємо роль на рядок для відображення
        const displayRole = getRoleKey(newUser.role);

        // Додаємо користувача до списку
        const processedUser = {
          ...newUser,
          originalRole,
          role: displayRole
        };

        // Додаємо користувача на початок списку
        users.value.unshift(processedUser);

        // Пересортовуємо список після додавання нового користувача
        console.log('Before resorting after add:', users.value.map(u => `${u.username} (${u.role})`));

        users.value.sort((a, b) => {
          console.log(`Resorting after add: ${a.username} (${a.role}) vs ${b.username} (${b.role})`);

          // Використовуємо функцію compareRoles для сортування
          const result = compareRoles(a.role, b.role);

          console.log(`Sort result: ${result}`);
          return result;
        });

        console.log('After resorting after add:', users.value.map(u => `${u.username} (${u.role})`));
      } else {
        // Оновлюємо весь список, якщо ми не на першій сторінці
        fetchUsers(1);
      }
    }

    // Close modal
    closeUserModal();
  } catch (error) {
    console.error('Error saving user:', error);
    alert('Failed to save user. Please check the console for details.');
  } finally {
    saving.value = false;
  }
};



// Confirm delete
const confirmDelete = (user) => {
  userToDelete.value = user;
  showDeleteModal.value = true;
};

// Cancel delete
const cancelDelete = () => {
  showDeleteModal.value = false;
  userToDelete.value = null;
};

// Delete user
const deleteUser = async () => {
  if (!userToDelete.value) return;

  try {
    await usersService.deleteUser(userToDelete.value.id);

    // Remove from list
    users.value = users.value.filter(u => u.id !== userToDelete.value.id);

    showDeleteModal.value = false;
    userToDelete.value = null;
  } catch (error) {
    console.error('Error deleting user:', error);
  }
};

// Lifecycle hooks
onMounted(() => {
  loadUsers();
});
</script>

<style scoped>
/* User info styling */
.admin-user-info {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-sm);
}

.admin-user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--admin-primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  flex-shrink: 0;
}

.admin-user-details {
  min-width: 0;
}

.admin-user-name {
  font-weight: 600;
  color: var(--admin-text-primary);
  margin-bottom: 2px;
}

.admin-user-id {
  font-size: 12px;
  color: var(--admin-text-secondary);
}

.admin-email {
  color: var(--admin-text-primary);
  word-break: break-word;
}

.admin-date {
  color: var(--admin-text-secondary);
  font-size: 14px;
}

/* Badge styling for roles */
.admin-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: var(--admin-border-radius-sm);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-badge.is-admin {
  background: #dc3545;
  color: white;
}

.admin-badge.is-moderator {
  background: #6f42c1;
  color: white;
}

.admin-badge.is-seller {
  background: #28a745;
  color: white;
}

.admin-badge.is-sellerowner {
  background: #17a2b8;
  color: white;
}

.admin-badge.is-buyer {
  background: #6c757d;
  color: white;
}
</style>
