// loading.js - Vuex store module for managing global loading state
import apiService from '@/services/api';

export default {
  namespaced: true,

  state: {
    isLoading: false,
    loadingMessage: '',
    pendingRequests: 0,
    routeChanging: false
  },

  getters: {
    isLoading: state => state.isLoading || state.routeChanging,
    loadingMessage: state => state.loadingMessage,
    hasPendingRequests: state => state.pendingRequests > 0,
    apiService: () => apiService
  },

  mutations: {
    SET_LOADING(state, isLoading) {
      state.isLoading = isLoading;
    },

    SET_LOADING_MESSAGE(state, message) {
      state.loadingMessage = message;
    },

    INCREMENT_PENDING_REQUESTS(state) {
      state.pendingRequests++;
    },

    DECREMENT_PENDING_REQUESTS(state) {
      state.pendingRequests = Math.max(0, state.pendingRequests - 1);
    },

    RESET_PENDING_REQUESTS(state) {
      state.pendingRequests = 0;
    },

    SET_ROUTE_CHANGING(state, isChanging) {
      state.routeChanging = isChanging;
    }
  },

  actions: {
    startLoading({ commit }, message = '') {
      commit('SET_LOADING', true);
      commit('SET_LOADING_MESSAGE', message);
    },

    stopLoading({ commit }) {
      commit('SET_LOADING', false);
      commit('SET_LOADING_MESSAGE', '');
    },

    startRequest({ commit, state }) {
      commit('INCREMENT_PENDING_REQUESTS');
      if (state.pendingRequests === 1) {
        commit('SET_LOADING', true);
      }
    },

    finishRequest({ commit, state }) {
      commit('DECREMENT_PENDING_REQUESTS');
      if (state.pendingRequests === 0) {
        commit('SET_LOADING', false);
      }
    },

    resetRequests({ commit }) {
      commit('RESET_PENDING_REQUESTS');
      commit('SET_LOADING', false);
    },

    startRouteChange({ commit }, message = 'Loading page...') {
      commit('SET_ROUTE_CHANGING', true);
      commit('SET_LOADING_MESSAGE', message);
    },

    finishRouteChange({ commit }) {
      commit('SET_ROUTE_CHANGING', false);
      commit('SET_LOADING_MESSAGE', '');
    }
  }
};
