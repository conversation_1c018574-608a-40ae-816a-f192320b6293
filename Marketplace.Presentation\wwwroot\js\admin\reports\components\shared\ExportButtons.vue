<template>
  <div class="export-buttons">
    <div class="export-group">
      <button
        v-for="format in exportFormats"
        :key="format.type"
        @click="handleExport(format.type)"
        :disabled="disabled || isExporting"
        :class="['export-btn', format.type]"
        :title="`Export as ${format.label}`"
      >
        <i :class="format.icon"></i>
        <span>{{ format.label }}</span>
        <div v-if="isExporting && currentExportType === format.type" class="export-progress">
          <div class="progress-bar" :style="{ width: `${exportProgress}%` }"></div>
        </div>
      </button>
    </div>
    
    <!-- Export Options Modal -->
    <div v-if="showExportModal" class="export-modal-overlay" @click="closeExportModal">
      <div class="export-modal" @click.stop>
        <div class="modal-header">
          <h3>Export Options</h3>
          <button @click="closeExportModal" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-content">
          <div class="export-format-info">
            <div class="format-icon">
              <i :class="selectedFormat.icon"></i>
            </div>
            <div class="format-details">
              <h4>{{ selectedFormat.label }} Export</h4>
              <p>{{ selectedFormat.description }}</p>
            </div>
          </div>
          
          <div class="export-options">
            <div class="option-group">
              <label class="option-label">
                <input 
                  v-model="exportOptions.includeCharts"
                  type="checkbox"
                  class="option-checkbox"
                />
                Include Charts and Visualizations
              </label>
              <p class="option-description">
                Add charts and graphs to the exported file (PDF and Excel only)
              </p>
            </div>
            
            <div class="option-group">
              <label class="option-label">
                <input 
                  v-model="exportOptions.includeMetrics"
                  type="checkbox"
                  class="option-checkbox"
                />
                Include Key Metrics Summary
              </label>
              <p class="option-description">
                Add a summary of key performance indicators
              </p>
            </div>
            
            <div class="option-group">
              <label class="option-label">
                <input 
                  v-model="exportOptions.includeInsights"
                  type="checkbox"
                  class="option-checkbox"
                />
                Include Insights and Recommendations
              </label>
              <p class="option-description">
                Add AI-generated insights and recommendations
              </p>
            </div>
            
            <div v-if="selectedFormat.type === 'pdf'" class="option-group">
              <label class="option-label">Page Orientation</label>
              <select v-model="exportOptions.orientation" class="option-select">
                <option value="portrait">Portrait</option>
                <option value="landscape">Landscape</option>
              </select>
            </div>
            
            <div v-if="selectedFormat.type === 'csv'" class="option-group">
              <label class="option-label">Field Separator</label>
              <select v-model="exportOptions.separator" class="option-select">
                <option value=",">Comma (,)</option>
                <option value=";">Semicolon (;)</option>
                <option value="\t">Tab</option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button @click="closeExportModal" class="cancel-btn">
            Cancel
          </button>
          <button @click="confirmExport" :disabled="isExporting" class="confirm-btn">
            <i v-if="isExporting" class="fas fa-spinner fa-spin"></i>
            <i v-else :class="selectedFormat.icon"></i>
            {{ isExporting ? 'Exporting...' : `Export ${selectedFormat.label}` }}
          </button>
        </div>
      </div>
    </div>
    
    <!-- Export Progress Toast -->
    <div v-if="showProgressToast" class="progress-toast">
      <div class="toast-content">
        <div class="toast-icon">
          <i v-if="exportStatus === 'success'" class="fas fa-check-circle text-green-500"></i>
          <i v-else-if="exportStatus === 'error'" class="fas fa-exclamation-circle text-red-500"></i>
          <i v-else class="fas fa-spinner fa-spin text-blue-500"></i>
        </div>
        <div class="toast-message">
          <div class="toast-title">{{ toastTitle }}</div>
          <div class="toast-description">{{ toastDescription }}</div>
        </div>
        <button @click="hideProgressToast" class="toast-close">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div v-if="exportStatus === 'progress'" class="toast-progress">
        <div class="progress-bar" :style="{ width: `${exportProgress}%` }"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { reportsService, formatters } from '../../../../../../src/services/reports'

export default {
  name: 'ExportButtons',
  props: {
    reportType: {
      type: String,
      required: true
    },
    filters: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    // Local state
    const showExportModal = ref(false)
    const selectedFormat = ref({})
    const currentExportType = ref('')
    const showProgressToast = ref(false)
    const exportStatus = ref('progress') // 'progress', 'success', 'error'
    const isExporting = ref(false)
    const exportProgress = ref(0)

    const exportOptions = ref({
      includeCharts: true,
      includeMetrics: true,
      includeInsights: true,
      orientation: 'landscape',
      separator: ','
    })

    const exportFormats = ref([
      {
        type: 'excel',
        label: 'Excel',
        icon: 'fas fa-file-excel',
        description: 'Export data in Microsoft Excel format with charts and formatting',
        color: '#10b981'
      },
      {
        type: 'pdf',
        label: 'PDF',
        icon: 'fas fa-file-pdf',
        description: 'Export as a formatted PDF document with charts and insights',
        color: '#ef4444'
      },
      {
        type: 'csv',
        label: 'CSV',
        icon: 'fas fa-file-csv',
        description: 'Export raw data in comma-separated values format',
        color: '#3b82f6'
      }
    ])

    const toastTitle = computed(() => {
      switch (exportStatus.value) {
        case 'progress':
          return 'Exporting Report...'
        case 'success':
          return 'Export Completed'
        case 'error':
          return 'Export Failed'
        default:
          return ''
      }
    })

    const toastDescription = computed(() => {
      switch (exportStatus.value) {
        case 'progress':
          return `Generating ${selectedFormat.value.label} file...`
        case 'success':
          return 'Your report has been downloaded successfully'
        case 'error':
          return 'There was an error exporting your report'
        default:
          return ''
      }
    })

    // Methods
    const handleExport = (formatType) => {
      const format = exportFormats.value.find(f => f.type === formatType)
      selectedFormat.value = format
      currentExportType.value = formatType
      
      // For simple exports, proceed directly
      if (formatType === 'csv') {
        confirmExport()
      } else {
        // Show options modal for Excel and PDF
        showExportModal.value = true
      }
    }

    const confirmExport = async () => {
      try {
        showExportModal.value = false
        showProgressToast.value = true
        exportStatus.value = 'progress'
        isExporting.value = true
        exportProgress.value = 0

        const exportFilters = {
          ...props.filters,
          ...exportOptions.value
        }

        // Simulate progress updates
        const progressInterval = setInterval(() => {
          if (exportProgress.value < 90) {
            exportProgress.value += Math.random() * 20
          }
        }, 200)

        const blob = await reportsService.exportReport(props.reportType, selectedFormat.value.type, exportFilters)

        // Complete progress
        exportProgress.value = 100
        clearInterval(progressInterval)

        // Download the file
        const filename = formatters.generateFilename(props.reportType, selectedFormat.value.type, exportFilters)
        reportsService.downloadFile(blob, filename.replace(/\.[^/.]+$/, ""), selectedFormat.value.type)

        exportStatus.value = 'success'
        setTimeout(() => {
          hideProgressToast()
        }, 3000)

      } catch (error) {
        console.error('Export failed:', error)
        exportStatus.value = 'error'
        setTimeout(() => {
          hideProgressToast()
        }, 5000)
      } finally {
        isExporting.value = false
        exportProgress.value = 0
      }
    }

    const closeExportModal = () => {
      showExportModal.value = false
      selectedFormat.value = {}
      currentExportType.value = ''
    }

    const hideProgressToast = () => {
      showProgressToast.value = false
      exportStatus.value = 'progress'
    }

    return {
      // State
      showExportModal,
      selectedFormat,
      currentExportType,
      exportOptions,
      showProgressToast,
      exportStatus,
      
      // Computed
      isExporting,
      exportProgress,
      exportFormats,
      toastTitle,
      toastDescription,
      
      // Methods
      handleExport,
      confirmExport,
      closeExportModal,
      hideProgressToast
    }
  }
}
</script>

<style scoped>
.export-buttons {
  position: relative;
}

.export-group {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.export-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: 2px solid transparent;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  min-width: 120px;
  justify-content: center;
}

.export-btn.excel {
  background: #10b981;
  color: white;
}

.export-btn.excel:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
}

.export-btn.pdf {
  background: #ef4444;
  color: white;
}

.export-btn.pdf:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
}

.export-btn.csv {
  background: #3b82f6;
  color: white;
}

.export-btn.csv:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.export-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.export-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  transition: width 0.3s ease;
}

.export-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.export-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-btn {
  padding: 0.5rem;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-content {
  padding: 1.5rem;
}

.export-format-info {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.format-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #3b82f6;
}

.format-details h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.format-details p {
  color: #6b7280;
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.4;
}

.export-options {
  space-y: 1.5rem;
}

.option-group {
  margin-bottom: 1.5rem;
}

.option-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  margin-bottom: 0.5rem;
}

.option-checkbox {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

.option-description {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
  line-height: 1.4;
}

.option-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.cancel-btn {
  padding: 0.75rem 1.5rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.confirm-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-btn:hover:not(:disabled) {
  background: #2563eb;
}

.confirm-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.progress-toast {
  position: fixed;
  top: 1rem;
  right: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  min-width: 300px;
  z-index: 1001;
  overflow: hidden;
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
}

.toast-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.toast-message {
  flex: 1;
}

.toast-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.toast-description {
  font-size: 0.875rem;
  color: #6b7280;
}

.toast-close {
  padding: 0.25rem;
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.toast-close:hover {
  background: #f3f4f6;
  color: #6b7280;
}

.toast-progress {
  height: 3px;
  background: #f3f4f6;
}

.toast-progress .progress-bar {
  background: #3b82f6;
}

@media (max-width: 640px) {
  .export-group {
    flex-direction: column;
  }
  
  .export-btn {
    width: 100%;
  }
  
  .export-modal {
    width: 95%;
    margin: 1rem;
  }
  
  .progress-toast {
    left: 1rem;
    right: 1rem;
    min-width: auto;
  }
}
</style>
