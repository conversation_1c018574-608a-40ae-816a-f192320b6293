﻿using AutoMapper;
using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.Auth;

public class ConfirmEmailCommandHandler : IRequestHandler<ConfirmEmailCommand>
{
    private readonly IUserRepository _userRepository;
    private readonly IMapper _mapper;

    public ConfirmEmailCommandHandler(IUserRepository userRepository, IMapper mapper)
    {
        _userRepository = userRepository;
        _mapper = mapper;
    }

    public async Task Handle(ConfirmEmailCommand request, CancellationToken cancellationToken)
    {
        var user = await _userRepository.GetByIdAsync(request.Id, cancellationToken)
        ?? throw new KeyNotFoundException("Користувача не знайдено!");

        if (user.EmailConfirmationToken != request.Token)
            throw new InvalidOperationException("Недійсний токен підтвердження електронної пошти.");

        user.EmailConfirmationToken = null;
        user.EmailConfirmedAt = DateTime.UtcNow;
        user.EmailConfirmed = true;
        await _userRepository.UpdateAsync(user, cancellationToken);
    }
}