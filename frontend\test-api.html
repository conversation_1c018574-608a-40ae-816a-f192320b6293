<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>API Test Page</h1>
    
    <div class="section">
        <h2>Products API Test</h2>
        <button onclick="testProducts()">Test Products API</button>
        <div id="products-result"></div>
    </div>
    
    <div class="section">
        <h2>Categories API Test</h2>
        <button onclick="testCategories()">Test Categories API</button>
        <div id="categories-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5296/api';
        
        async function makeRequest(url, options = {}) {
            try {
                const token = localStorage.getItem('token');
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    ...(token && { 'Authorization': `Bearer ${token}` })
                };
                
                const response = await fetch(url, {
                    ...options,
                    headers: { ...headers, ...options.headers }
                });
                
                const data = await response.json();
                return { success: response.ok, status: response.status, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        async function testProducts() {
            const resultDiv = document.getElementById('products-result');
            resultDiv.innerHTML = '<p>Testing...</p>';
            
            const result = await makeRequest(`${API_BASE}/admin/products?page=1&pageSize=5`);
            
            if (result.success) {
                resultDiv.innerHTML = `
                    <div class="success">✓ Products API Success</div>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="error">✗ Products API Failed (${result.status})</div>
                    <pre>${JSON.stringify(result.error || result.data, null, 2)}</pre>
                `;
            }
        }
        
        async function testCategories() {
            const resultDiv = document.getElementById('categories-result');
            resultDiv.innerHTML = '<p>Testing...</p>';
            
            const result = await makeRequest(`${API_BASE}/admin/categories?page=1&pageSize=10`);
            
            if (result.success) {
                resultDiv.innerHTML = `
                    <div class="success">✓ Categories API Success</div>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="error">✗ Categories API Failed (${result.status})</div>
                    <pre>${JSON.stringify(result.error || result.data, null, 2)}</pre>
                `;
            }
        }
        
        // Auto-test on load
        window.onload = function() {
            console.log('Testing APIs...');
            testProducts();
            testCategories();
        };
    </script>
</body>
</html>
