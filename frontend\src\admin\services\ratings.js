import api from '@/services/api';

export const ratingsService = {
  async getRatings(params = {}) {
    try {
      const response = await api.get('/api/admin/ratings', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching ratings:', error);
      throw new Error(error.response?.data?.message || 'Failed to load ratings');
    }
  },

  async getRatingById(id) {
    try {
      const response = await api.get(`/api/admin/ratings/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching rating:', error);
      throw new Error(error.response?.data?.message || 'Failed to load rating details');
    }
  },

  async updateRating(id, data) {
    try {
      const response = await api.put(`/api/admin/ratings/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating rating:', error);
      throw new Error(error.response?.data?.message || 'Failed to update rating');
    }
  },

  async deleteRating(id) {
    try {
      const response = await api.delete(`/api/admin/ratings/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting rating:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete rating');
    }
  },

  async bulkDeleteRatings(ids) {
    try {
      const response = await api.post('/api/admin/ratings/bulk-delete', { ids });
      return response.data;
    } catch (error) {
      console.error('Error bulk deleting ratings:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete ratings');
    }
  },

  async getRatingStats() {
    try {
      const response = await api.get('/api/admin/ratings/stats');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching rating stats:', error);
      throw new Error(error.response?.data?.message || 'Failed to load rating statistics');
    }
  }
};
