/* ===== ADMIN CARDS SYSTEM ===== */
/* Based on Reports page card patterns */

/* ===== BASE CARD ===== */
.admin-card {
  background: var(--admin-card-bg);
  border: 1px solid var(--admin-card-border);
  border-radius: var(--admin-card-radius);
  box-shadow: var(--admin-card-shadow);
  transition: all var(--admin-transition-normal);
  overflow: hidden;
}

.admin-card:hover {
  box-shadow: var(--admin-shadow-lg);
  transform: translateY(-1px);
}

.admin-card-header {
  padding: var(--admin-space-lg) var(--admin-card-padding);
  border-bottom: 1px solid var(--admin-border-color);
  background: var(--admin-gray-50);
}

.admin-card-body {
  padding: var(--admin-card-padding);
}

.admin-card-footer {
  padding: var(--admin-space-lg) var(--admin-card-padding);
  border-top: 1px solid var(--admin-border-color);
  background: var(--admin-gray-50);
}

/* ===== METRIC CARDS ===== */
.admin-metric-card {
  background: var(--admin-metric-card-bg);
  border: 1px solid var(--admin-metric-card-border);
  border-radius: var(--admin-metric-card-radius);
  padding: var(--admin-metric-card-padding);
  transition: all var(--admin-transition-normal);
  position: relative;
  overflow: hidden;
}

.admin-metric-card:hover {
  box-shadow: var(--admin-shadow-lg);
  transform: translateY(-1px);
}

.admin-metric-card.positive {
  border-left: 4px solid var(--admin-success);
  background: var(--admin-gradient-success);
}

.admin-metric-card.negative {
  border-left: 4px solid var(--admin-danger);
  background: var(--admin-gradient-danger);
}

.admin-metric-card.warning {
  border-left: 4px solid var(--admin-warning);
  background: var(--admin-gradient-warning);
}

.admin-metric-card.neutral {
  border-left: 4px solid var(--admin-gray-400);
}

.admin-metric-card-content {
  display: flex;
  align-items: center;
  gap: var(--admin-space-lg);
}

.admin-metric-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--admin-radius-lg);
  background: var(--admin-gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--admin-gray-600);
  flex-shrink: 0;
}

.admin-metric-card.positive .admin-metric-icon {
  background: rgba(16, 185, 129, 0.1);
  color: var(--admin-success);
}

.admin-metric-card.negative .admin-metric-icon {
  background: rgba(239, 68, 68, 0.1);
  color: var(--admin-danger);
}

.admin-metric-card.warning .admin-metric-icon {
  background: rgba(245, 158, 11, 0.1);
  color: var(--admin-warning);
}

.admin-metric-details {
  flex: 1;
}

.admin-metric-value {
  font-size: var(--admin-text-2xl);
  font-weight: var(--admin-font-bold);
  color: var(--admin-gray-900);
  line-height: var(--admin-leading-tight);
  margin-bottom: var(--admin-space-xs);
}

.admin-metric-label {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
  margin-bottom: var(--admin-space-xs);
}

.admin-metric-change {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-metric-change.positive {
  color: var(--admin-success);
}

.admin-metric-change.negative {
  color: var(--admin-danger);
}

.admin-metric-change.neutral {
  color: var(--admin-gray-500);
}

/* ===== INSIGHT CARDS ===== */
.admin-insight-card {
  display: flex;
  align-items: flex-start;
  gap: var(--admin-space-lg);
  padding: var(--admin-space-xl);
  border-radius: var(--admin-radius-lg);
  border-left: 4px solid var(--admin-border-color);
  background: var(--admin-gray-50);
  transition: all var(--admin-transition-normal);
}

.admin-insight-card:hover {
  box-shadow: var(--admin-shadow-md);
}

.admin-insight-card.positive {
  background: var(--admin-gradient-success);
  border-left-color: var(--admin-success);
}

.admin-insight-card.negative {
  background: var(--admin-gradient-danger);
  border-left-color: var(--admin-danger);
}

.admin-insight-card.warning {
  background: var(--admin-gradient-warning);
  border-left-color: var(--admin-warning);
}

.admin-insight-card.info {
  background: var(--admin-info-bg);
  border-left-color: var(--admin-info);
}

.admin-insight-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--admin-radius-lg);
  background: rgba(59, 130, 246, 0.1);
  color: var(--admin-info);
}

.admin-insight-card.positive .admin-insight-icon {
  background: rgba(16, 185, 129, 0.1);
  color: var(--admin-success);
}

.admin-insight-card.negative .admin-insight-icon {
  background: rgba(239, 68, 68, 0.1);
  color: var(--admin-danger);
}

.admin-insight-card.warning .admin-insight-icon {
  background: rgba(245, 158, 11, 0.1);
  color: var(--admin-warning);
}

.admin-insight-content {
  flex: 1;
}

.admin-insight-title {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin-bottom: var(--admin-space-xs);
}

.admin-insight-description {
  color: var(--admin-gray-700);
  font-size: var(--admin-text-sm);
  line-height: var(--admin-leading-relaxed);
}

/* ===== STAT CARDS ===== */
.admin-stat-card {
  background: var(--admin-white);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-space-xl);
  text-align: center;
  transition: all var(--admin-transition-normal);
}

.admin-stat-card:hover {
  box-shadow: var(--admin-shadow-lg);
  transform: translateY(-2px);
}

.admin-stat-value {
  font-size: var(--admin-text-3xl);
  font-weight: var(--admin-font-bold);
  color: var(--admin-gray-900);
  margin-bottom: var(--admin-space-sm);
}

.admin-stat-label {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* ===== CHART CARDS ===== */
.admin-chart-card {
  background: var(--admin-white);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-radius-xl);
  overflow: hidden;
  box-shadow: var(--admin-shadow-md);
}

.admin-chart-header {
  padding: var(--admin-space-lg) var(--admin-space-xl);
  border-bottom: 1px solid var(--admin-border-color);
  background: var(--admin-gray-50);
}

.admin-chart-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0;
}

.admin-chart-body {
  padding: var(--admin-space-xl);
}

/* ===== CARD UTILITIES ===== */
.admin-card-compact {
  padding: var(--admin-space-lg);
}

.admin-card-spacious {
  padding: var(--admin-space-4xl);
}

.admin-card-borderless {
  border: none;
  box-shadow: none;
}

.admin-card-elevated {
  box-shadow: var(--admin-shadow-xl);
}

.admin-card-flat {
  box-shadow: none;
  border: 1px solid var(--admin-border-color);
}
