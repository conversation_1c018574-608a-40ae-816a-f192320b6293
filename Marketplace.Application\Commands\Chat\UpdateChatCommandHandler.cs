﻿using AutoMapper;
using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.Chat;

public class UpdateChatCommandHandler : IRequestHandler<UpdateChatCommand, Unit>
{
    private readonly IChatRepository _repository;
    private readonly IMapper _mapper;

    public UpdateChatCommandHandler(IChatRepository repository, IMapper mapper)
    {
        _repository = repository;
        _mapper = mapper;
    }

    public async Task<Unit> Handle(UpdateChatCommand command, CancellationToken cancellationToken)
    {
        var item = await _repository.GetByIdAsync(command.Id, cancellationToken);
        if (item == null)
            throw new InvalidOperationException($"Chat with ID {command.Id} not found.");

        // Викликаємо метод Update із доменної моделі
        //item.Update(
        //    name: command.Name,
        //    slug: command.Slug != null ? new Slug(command.Slug) : null,
        //    description: command.Description,
        //    image: command.Image != null ? new Url(command.Image) : null,
        //    parentId: command.ParentId,
        //    metaTitle: command.MetaTitle,
        //    metaDescription: command.MetaDescription,
        //    metaImage: command.MetaImage != null ? new Url(command.MetaImage) : null
        //);

        await _repository.UpdateAsync(item, cancellationToken);
        return Unit.Value;
    }
}

