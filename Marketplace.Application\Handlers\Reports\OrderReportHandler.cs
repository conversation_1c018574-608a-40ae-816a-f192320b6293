using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Marketplace.Application.Queries.Reports;
using Marketplace.Infrastructure.Persistence;

namespace Marketplace.Application.Handlers.Reports;

public class GetOrdersReportQueryHandler : BaseReportResultHandler<GetOrdersReportQuery>
{
    public GetOrdersReportQueryHandler(MarketplaceDbContext context, ILogger<GetOrdersReportQueryHandler> logger)
        : base(context, logger)
    {
    }

    protected override async Task<ReportResult> GenerateReportAsync(GetOrdersReportQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Get orders data
            var startDateUtc = DateTime.SpecifyKind(request.StartDate, DateTimeKind.Utc);
            var endDateUtc = DateTime.SpecifyKind(request.EndDate, DateTimeKind.Utc);

            var orders = await _context.Orders
                .Include(o => o.Customer)
                .Where(o => o.CreatedAt >= startDateUtc && o.CreatedAt <= endDateUtc)
                .ToListAsync(cancellationToken);

            var orderItems = await _context.OrderItems
                .Include(oi => oi.Order)
                .Where(oi => oi.Order.CreatedAt >= startDateUtc && oi.Order.CreatedAt <= endDateUtc)
                .ToListAsync(cancellationToken);

            // Calculate metrics
            var totalOrders = orders.Count;
            var completedOrders = orders.Count(o => o.Status.ToString() == "Delivered");
            var pendingOrders = orders.Count(o => o.Status.ToString() == "Pending");
            var cancelledOrders = orders.Count(o => o.Status.ToString() == "Cancelled");
            var averageOrderValue = orders.Any() ? orders.Average(o => o.TotalPrice.Amount) : 0;

            // Create metrics
            var metrics = new ReportMetrics
            {
                Items = new List<MetricItem>
                {
                    new MetricItem
                    {
                        Key = "totalOrders",
                        Label = "Total Orders",
                        Value = totalOrders,
                        Type = "number",
                        Icon = "fas fa-shopping-cart",
                        PreviousValue = null,
                        ChangePercentage = 0m
                    },
                    new MetricItem
                    {
                        Key = "completedOrders",
                        Label = "Completed Orders",
                        Value = completedOrders,
                        Type = "number",
                        Icon = "fas fa-check-circle",
                        PreviousValue = null,
                        ChangePercentage = 0m
                    },
                    new MetricItem
                    {
                        Key = "pendingOrders",
                        Label = "Pending Orders",
                        Value = pendingOrders,
                        Type = "number",
                        Icon = "fas fa-clock",
                        PreviousValue = null,
                        ChangePercentage = 0m
                    },
                    new MetricItem
                    {
                        Key = "averageOrderValue",
                        Label = "Average Order Value",
                        Value = averageOrderValue,
                        Type = "currency",
                        Icon = "fas fa-calculator",
                        PreviousValue = null,
                        ChangePercentage = 0m
                    }
                }
            };

            // Generate table data
            var table = new ReportTable
            {
                Title = "Order Details",
                Columns = new List<TableColumn>
                {
                    new TableColumn { Key = "id", Label = "Order ID", Type = "text" },
                    new TableColumn { Key = "customer", Label = "Customer", Type = "text" },
                    new TableColumn { Key = "date", Label = "Date", Type = "date" },
                    new TableColumn { Key = "items", Label = "Items", Type = "number" },
                    new TableColumn { Key = "total", Label = "Total", Type = "currency" },
                    new TableColumn { Key = "status", Label = "Status", Type = "status" }
                },
                Data = orders.Select(o => new Dictionary<string, object>
                {
                    ["id"] = o.Id,
                    ["customer"] = o.Customer?.Username ?? "Unknown",
                    ["date"] = o.CreatedAt,
                    ["items"] = orderItems.Count(oi => oi.OrderId == o.Id),
                    ["total"] = o.TotalPrice.Amount,
                    ["status"] = o.Status.ToString()
                }).ToList(),
                TotalCount = orders.Count,
                Page = request.Page,
                PageSize = request.PageSize
            };

            var summary = new ReportSummary
            {
                Title = "Order Report Summary",
                Description = $"Order analysis from {request.StartDate:yyyy-MM-dd} to {request.EndDate:yyyy-MM-dd}",
                GeneratedAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["totalOrders"] = totalOrders,
                    ["completedOrders"] = completedOrders,
                    ["pendingOrders"] = pendingOrders,
                    ["cancelledOrders"] = cancelledOrders,
                    ["averageOrderValue"] = averageOrderValue,
                    ["period"] = new { start = request.StartDate, end = request.EndDate }
                }
            };

            return new ReportResult
            {
                Metrics = metrics,
                Table = table,
                Summary = summary
            };
        }
        catch (Exception ex)
        {
            return new ReportResult
            {
                Summary = new ReportSummary
                {
                    Title = "Error",
                    Description = $"Failed to generate order report: {ex.Message}",
                    GeneratedAt = DateTime.UtcNow
                }
            };
        }
    }
}
