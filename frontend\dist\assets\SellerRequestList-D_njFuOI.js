import{_ as re,g as d,B as ce,i as de,c as u,a as e,b as _,z as D,C as X,D as Y,A as ue,F as ve,p as pe,t as v,k as I,w as Z,r as me,n as k,d as fe,o as p}from"./index-BKy0rL_2.js";import{s as N}from"./seller-requests-aoHB3xeQ.js";import{S as ge}from"./StatusBadge-DZXrI7cG.js";import{P as _e}from"./Pagination-DcbqxmDq.js";import{C as he}from"./ConfirmDialog-BQ115uZp.js";/* empty css                                                                    */const ye={class:"seller-request-list"},be={class:"level"},Re={class:"level-right"},ke={class:"level-item"},Ce={class:"field has-addons"},Pe={class:"control"},xe={class:"box"},Ae={class:"columns"},Se={class:"column is-4"},we={class:"field"},je={class:"control"},De={class:"select is-fullwidth"},Ie={class:"card"},Ne={class:"card-content"},Fe={key:0,class:"has-text-centered py-6"},Ee={key:1,class:"has-text-centered py-6"},Ve={key:2},Te={class:"columns is-multiline"},Ue={class:"card seller-request-card"},ze={class:"card-content"},Me={class:"media"},qe={class:"media-left"},Be={class:"image is-64x64"},Le=["src","alt"],$e={class:"media-content"},Oe={class:"title is-4"},Qe={class:"subtitle is-6"},Ge=["href"],He={class:"subtitle is-6"},Je={class:"ml-2"},Ke={class:"content"},We={class:"field"},Xe={class:"field"},Ye={class:"field"},Ze={class:"field"},es={key:0,class:"field"},ss={class:"field mt-4"},ts={key:1,class:"field is-grouped mt-4"},as={class:"control"},ls=["onClick"],os={class:"control"},ns=["onClick"],is={key:2,class:"field mt-4"},rs={key:3,class:"field mt-4"},cs={class:"has-text-danger"},ds={key:0,class:"mt-2"},us={class:"modal-card"},vs={class:"modal-card-body"},ps={class:"field mt-4"},ms={class:"control"},fs={class:"modal-card-foot"},gs={__name:"SellerRequestList",setup(_s){const ee=(t,s)=>{let r;return function(...c){const l=()=>{clearTimeout(r),t(...c)};clearTimeout(r),r=setTimeout(l,s)}},o=d([]),C=d(!1),P=d(""),g=d(1),x=d(1),F=d(0),E=d(6),h=d(!1),y=d(!1),i=d(null),f=d(""),A=d(!1),S=ce({status:""}),w=async(t=1)=>{var s;C.value=!0,g.value=t;try{const r={page:g.value,pageSize:E.value,filter:P.value,status:S.status};console.log("🔍 Fetching seller requests with params:",r);const m=await N.getSellerRequests(r);console.log("📥 Raw API response:",m);let c=[],l={};if(m&&m.success&&m.data){const n=m.data;console.log("📊 Data structure:",n),console.log("📊 Data keys:",Object.keys(n)),n.data&&Array.isArray(n.data)?(c=n.data,l=n,console.log("📋 Found data in data.data:",c.length,"items")):Array.isArray(n)?(c=n,console.log("📋 Found data directly in data:",c.length,"items")):console.warn("📋 Unexpected data structure:",n)}console.log("📋 Final requests data:",c),console.log("📊 Final pagination data:",l),o.value=c.map(n=>({...n,processing:!1})),x.value=l.lastPage||l.totalPages||Math.ceil((l.total||c.length)/(l.pageSize||E.value)),F.value=l.total||l.totalItems||c.length,g.value=l.currentPage||1,console.log("📊 Pagination details:",{lastPage:l.lastPage,totalPages:l.totalPages,total:l.total,totalItems:l.totalItems,currentPage:l.currentPage,pageSize:l.pageSize}),console.log("✅ Final state:",{requestsCount:o.value.length,totalPages:x.value,totalItems:F.value,currentPage:g.value})}catch(r){console.error("❌ Error fetching seller requests:",r),console.error("❌ Error details:",((s=r.response)==null?void 0:s.data)||r.message)}finally{C.value=!1}},j=()=>{g.value=1,w()},V=ee(j,300),se=t=>{w(t)},T=t=>t?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(new Date(t)):"",b=t=>{if(typeof t=="string")return t.toLowerCase();switch(t){case 0:return"pending";case 1:return"approved";case 2:return"rejected";default:return"pending"}},te=t=>{t.target.src="https://via.placeholder.com/64?text=No+Image"},ae=t=>{i.value=t,h.value=!0},le=t=>{i.value=t,f.value="",y.value=!0},R=()=>{h.value=!1,y.value=!1,i.value=null,f.value=""},oe=async()=>{if(!i.value)return;const t=o.value.findIndex(s=>s.id===i.value.id);t!==-1&&(o.value[t].processing=!0);try{await N.approveSellerRequest(i.value.id),t!==-1&&(o.value[t].status=1,o.value[t].updatedAt=new Date,o.value[t].processing=!1),h.value=!1,i.value=null}catch(s){console.error("Error approving seller request:",s),t!==-1&&(o.value[t].processing=!1)}},ne=async()=>{if(!i.value)return;A.value=!0;const t=o.value.findIndex(s=>s.id===i.value.id);t!==-1&&(o.value[t].processing=!0);try{await N.rejectSellerRequest(i.value.id,f.value),t!==-1&&(o.value[t].status=2,o.value[t].updatedAt=new Date,o.value[t].rejectionReason=f.value,o.value[t].processing=!1),y.value=!1,i.value=null,f.value=""}catch(s){console.error("Error rejecting seller request:",s),t!==-1&&(o.value[t].processing=!1)}finally{A.value=!1}};return de(()=>{w()}),(t,s)=>{var m,c,l,n;const r=me("router-link");return p(),u("div",ye,[e("div",be,[s[5]||(s[5]=e("div",{class:"level-left"},[e("div",{class:"level-item"},[e("h1",{class:"title"},"Seller Requests")])],-1)),e("div",Re,[e("div",ke,[e("div",Ce,[e("div",Pe,[D(e("input",{class:"input",type:"text",placeholder:"Search by username, email, or company info...","onUpdate:modelValue":s[0]||(s[0]=a=>P.value=a),onInput:s[1]||(s[1]=(...a)=>Y(V)&&Y(V)(...a))},null,544),[[X,P.value]])]),e("div",{class:"control"},[e("button",{class:"button is-info",onClick:j},s[4]||(s[4]=[e("span",{class:"icon"},[e("i",{class:"fas fa-search"})],-1)]))])])])])]),e("div",xe,[e("div",Ae,[e("div",Se,[e("div",we,[s[7]||(s[7]=e("label",{class:"label"},"Status",-1)),e("div",je,[e("div",De,[D(e("select",{"onUpdate:modelValue":s[2]||(s[2]=a=>S.status=a),onChange:j},s[6]||(s[6]=[e("option",{value:""},"All Statuses",-1),e("option",{value:"pending"},"Pending",-1),e("option",{value:"approved"},"Approved",-1),e("option",{value:"rejected"},"Rejected",-1)]),544),[[ue,S.status]])])])])])])]),e("div",Ie,[e("div",Ne,[C.value&&!o.value.length?(p(),u("div",Fe,s[8]||(s[8]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading seller requests...",-1)]))):o.value.length?(p(),u("div",Ve,[e("div",Te,[(p(!0),u(ve,null,pe(o.value,a=>{var U,z,M,q,B,L,$,O,Q,G,H,J,K,W;return p(),u("div",{key:a.id,class:"column is-12-tablet is-6-desktop"},[e("div",Ue,[e("div",ze,[e("div",Me,[e("div",qe,[e("figure",Be,[e("img",{src:((U=a.user)==null?void 0:U.avatar)||"https://via.placeholder.com/64",alt:(((z=a.user)==null?void 0:z.firstName)||"")+" "+(((M=a.user)==null?void 0:M.lastName)||""),onError:te},null,40,Le)])]),e("div",$e,[e("p",Oe,v(((q=a.user)==null?void 0:q.username)||"Unknown User"),1),e("p",Qe,[e("a",{href:`mailto:${((L=(B=a.user)==null?void 0:B.email)==null?void 0:L.value)||(($=a.user)==null?void 0:$.email)}`},v(((Q=(O=a.user)==null?void 0:O.email)==null?void 0:Q.value)||((G=a.user)==null?void 0:G.email)),9,Ge)]),e("p",He,[_(ge,{status:b(a.status),type:"default"},null,8,["status"]),e("span",Je,v(T(a.createdAt)),1)])])]),e("div",Ke,[e("div",We,[s[10]||(s[10]=e("label",{class:"label"},"Company Name",-1)),e("p",null,v(((H=a.companyRequestData)==null?void 0:H.name)||"N/A"),1)]),e("div",Xe,[s[11]||(s[11]=e("label",{class:"label"},"Company Description",-1)),e("p",null,v(((J=a.companyRequestData)==null?void 0:J.description)||"N/A"),1)]),e("div",Ye,[s[12]||(s[12]=e("label",{class:"label"},"Contact Email",-1)),e("p",null,v(((K=a.companyRequestData)==null?void 0:K.contactEmail)||"N/A"),1)]),e("div",Ze,[s[13]||(s[13]=e("label",{class:"label"},"Contact Phone",-1)),e("p",null,v(((W=a.companyRequestData)==null?void 0:W.contactPhone)||"N/A"),1)]),a.additionalInformation?(p(),u("div",es,[s[14]||(s[14]=e("label",{class:"label"},"Additional Information",-1)),e("p",null,v(a.additionalInformation),1)])):I("",!0),e("div",ss,[_(r,{to:{name:"AdminSellerRequestDetail",params:{id:a.id}},class:"button is-info is-small"},{default:Z(()=>s[15]||(s[15]=[e("span",{class:"icon"},[e("i",{class:"fas fa-eye"})],-1),e("span",null,"View Details",-1)])),_:2},1032,["to"])]),b(a.status)==="pending"?(p(),u("div",ts,[e("div",as,[e("button",{class:k(["button is-success",{"is-loading":a.processing}]),onClick:ie=>ae(a)},s[16]||(s[16]=[e("span",{class:"icon"},[e("i",{class:"fas fa-check"})],-1),e("span",null,"Approve",-1)]),10,ls)]),e("div",os,[e("button",{class:k(["button is-danger",{"is-loading":a.processing}]),onClick:ie=>le(a)},s[17]||(s[17]=[e("span",{class:"icon"},[e("i",{class:"fas fa-times"})],-1),e("span",null,"Reject",-1)]),10,ns)])])):b(a.status)==="approved"?(p(),u("div",is,[_(r,{to:`/admin/users/${a.userId}`,class:"button is-info"},{default:Z(()=>s[18]||(s[18]=[e("span",{class:"icon"},[e("i",{class:"fas fa-user"})],-1),e("span",null,"View Seller Profile",-1)])),_:2},1032,["to"])])):b(a.status)==="rejected"?(p(),u("div",rs,[e("p",cs,[s[19]||(s[19]=e("span",{class:"icon"},[e("i",{class:"fas fa-info-circle"})],-1)),e("span",null,"Rejected on "+v(T(a.updatedAt)),1)]),a.rejectionReason?(p(),u("p",ds,[s[20]||(s[20]=e("strong",null,"Reason:",-1)),fe(" "+v(a.rejectionReason),1)])):I("",!0)])):I("",!0)])])])])}),128))]),_(_e,{"current-page":g.value,"total-pages":x.value,onPageChanged:se},null,8,["current-page","total-pages"])])):(p(),u("div",Ee,s[9]||(s[9]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-store fa-2x"})],-1),e("p",{class:"mt-2"},"No seller requests found",-1),e("p",{class:"mt-2"},"There are currently no seller requests to review",-1)])))])]),_(he,{"is-open":h.value,title:"Approve Seller Request",message:`Are you sure you want to approve ${(c=(m=i.value)==null?void 0:m.user)==null?void 0:c.username}'s seller request?`,"confirm-text":"Approve","cancel-text":"Cancel","confirm-button-class":"is-success",onConfirm:oe,onCancel:R},null,8,["is-open","message"]),e("div",{class:k(["modal",{"is-active":y.value}])},[e("div",{class:"modal-background",onClick:R}),e("div",us,[e("header",{class:"modal-card-head"},[s[21]||(s[21]=e("p",{class:"modal-card-title"},"Reject Seller Request",-1)),e("button",{class:"delete","aria-label":"close",onClick:R})]),e("section",vs,[e("p",null,"Are you sure you want to reject "+v((n=(l=i.value)==null?void 0:l.user)==null?void 0:n.username)+"'s seller request?",1),e("div",ps,[s[22]||(s[22]=e("label",{class:"label"},"Reason for Rejection (Optional)",-1)),e("div",ms,[D(e("textarea",{class:"textarea","onUpdate:modelValue":s[3]||(s[3]=a=>f.value=a),placeholder:"Provide a reason for rejection"},"              ",512),[[X,f.value]])])])]),e("footer",fs,[e("button",{class:k(["button is-danger",{"is-loading":A.value}]),onClick:ne}," Reject ",2),e("button",{class:"button is-light",onClick:R},"Cancel")])])],2)])}}},Ps=re(gs,[["__scopeId","data-v-8ff91d6f"]]);export{Ps as default};
