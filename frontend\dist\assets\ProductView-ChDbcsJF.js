import{_ as H,g as p,h as g,f as Q,i as V,c as o,k as d,a as s,d as G,t as n,n as v,H as f,F as k,p as w,e as W,q as Y,o as i}from"./index-BKy0rL_2.js";import{p as P}from"./products-A8mTMjnr.js";import{c as F}from"./companies-DFFZKRwv.js";const J={class:"product-view"},$={key:0,class:"has-text-centered py-6"},q={key:1,class:"notification is-danger"},K={key:2,class:"product-details"},X={class:"hero is-light is-bold mb-6"},ss={class:"hero-body py-4"},as={class:"level"},ts={class:"level-left"},es={class:"level-item"},ls={class:"title is-2 has-text-dark"},os={class:"subtitle is-5 has-text-grey"},is={class:"icon-text"},ns={class:"level-right"},cs={class:"level-item"},ds={class:"icon"},rs={class:"columns is-multiline"},us={class:"column is-12"},vs={class:"card"},ps={class:"card-content"},gs={class:"columns is-multiline"},fs={class:"column is-4"},ms={class:"field"},hs={class:"content"},_s={class:"has-background-light px-2 py-1"},ys={class:"column is-4"},xs={class:"field"},bs={class:"content"},Is={class:"icon-text"},ks={class:"column is-4"},ws={class:"field"},As={class:"content"},Ds={class:"icon-text"},Ns={class:"column is-12"},Cs={class:"field"},Ms={class:"content"},Ps={class:"has-text-dark"},js={class:"column is-6"},Ts={class:"card"},Es={class:"card-content"},Ss={class:"columns"},Us={class:"column is-6"},zs={class:"field"},Ls={class:"content"},Zs={class:"tag is-large is-success"},Rs={class:"column is-6"},Bs={class:"field"},Os={class:"content"},Hs={class:"column is-6"},Qs={class:"card"},Vs={class:"card-content"},Gs={class:"field"},Ws={class:"content"},Ys={class:"icon"},Fs={key:0,class:"field"},Js={class:"content"},$s={class:"icon-text"},qs={key:0,class:"column is-12"},Ks={class:"card"},Xs={class:"card-content"},sa={class:"table-container"},aa={class:"table is-fullwidth is-striped is-hoverable"},ta={key:0,class:"tags"},ea={key:1,class:"tag is-light"},la={class:"column is-12"},oa={class:"card"},ia={class:"card-header"},na={class:"card-header-title"},ca={class:"icon-text"},da={key:0,class:"tag is-light ml-2"},ra={class:"card-content"},ua={key:0,class:"columns is-multiline"},va={class:"card"},pa={class:"card-image"},ga={class:"image is-square"},fa=["src","alt"],ma={class:"card-content p-2"},ha={class:"level is-mobile"},_a={class:"level-left"},ya={class:"level-item"},xa={class:"icon is-small"},ba={key:1,class:"has-text-centered py-6"},Ia={key:1,class:"column is-6"},ka={class:"card"},wa={class:"card-content"},Aa={key:0,class:"field"},Da={class:"content"},Na={class:"has-text-dark"},Ca={key:1,class:"field"},Ma={class:"content"},Pa={class:"has-text-dark"},ja={key:2,class:"field"},Ta={class:"content has-text-centered"},Ea={class:"image is-128x128 is-inline-block"},Sa=["src","alt"],Ua={class:"column is-6"},za={class:"card"},La={class:"card-content"},Za={class:"field"},Ra={class:"content"},Ba={class:"icon-text"},Oa={key:0,class:"field"},Ha={class:"content"},Qa={class:"icon-text"},Va={__name:"ProductView",props:{productId:{type:String,required:!1}},setup(j){const T=j,E=Q(),A=W(),e=p(null),_=p(!0),h=p(null),u=p([]),m=p([]),r=p([]),y=g(()=>T.productId||E.params.id),S=g(()=>{var a;if(!((a=e.value)!=null&&a.companyId)||!u.value.length)return"Unknown Company";const t=u.value.find(l=>l.id===e.value.companyId);return(t==null?void 0:t.name)||"Unknown Company"}),U=g(()=>{var t,a;if((t=e.value)!=null&&t.categoryName){const l=e.value.categoryName.split(" > ");return l[l.length-1].trim()}if((a=e.value)!=null&&a.categoryId&&m.value.length>0){const l=m.value.find(c=>c.id===e.value.categoryId);return(l==null?void 0:l.name)||"Unknown Category"}return"Unknown Category"}),x=g(()=>{var t;return!((t=e.value)!=null&&t.companyId)||!u.value.length?null:u.value.find(a=>a.id===e.value.companyId)}),b=g(()=>{var t;if(!((t=e.value)!=null&&t.attributes))return{};try{return typeof e.value.attributes=="string"?JSON.parse(e.value.attributes):e.value.attributes}catch(a){return console.error("Error parsing attributes:",a),{}}});g(()=>r.value.find(t=>t.isMain)||r.value[0]||null);const D=t=>{if(!t)return"is-light";switch(String(t).toLowerCase()){case"approved":case"1":return"is-success";case"pending":case"0":return"is-warning";case"rejected":case"2":return"is-danger";default:return"is-light"}},N=t=>{if(!t)return"Unknown";switch(String(t).toLowerCase()){case"approved":case"1":return"Approved";case"pending":case"0":return"Pending";case"rejected":case"2":return"Rejected";default:return"Unknown"}},I=t=>{if(console.log("🕐 Formatting date:",{dateString:t,type:typeof t}),!t)return console.warn("⚠️ Empty date string provided"),"N/A";try{let a;if(typeof t=="string"){let c=t.trim();c.includes(" ")&&!c.includes("T")&&(c=c.replace(" ","T")),/[+-]\d{2}$/.test(c)&&(c+=":00"),console.log("🔄 Normalized date string:",c),a=new Date(c)}else t instanceof Date?a=t:a=new Date(t);if(isNaN(a.getTime()))return console.warn("❌ Invalid date after parsing:",{original:t,parsed:a}),"Invalid Date";const l=new Intl.DateTimeFormat("uk-UA",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZone:"Europe/Kiev"}).format(a);return console.log("✅ Date formatted successfully:",{original:t,formatted:l}),l}catch(a){return console.error("❌ Error formatting date:",a,t),String(t)||"N/A"}},C=t=>{t.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjgiIGhlaWdodD0iMTI4IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk2IDgwLjU2ODUgOTYgNjRDOTYgNDcuNDMxNSA4MC41Njg1IDMyIDY0IDMyQzQ3LjQzMTUgMzIgMzIgNDcuNDMxNSAzMiA2NEMzMiA4MC41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEMUQxRDEiIHN0cm9rZS13aWR0aD0iNCIgZmlsbD0ibm9uZSIvPgo8L3N2Zz4K"},z=()=>{x.value&&A.push(`/admin/companies/${x.value.id}`)},L=()=>{A.push("/admin/products")},Z=async()=>{var t,a,l;try{_.value=!0,h.value=null,console.log("🔄 Loading product with ID:",y.value);const c=await P.getProductById(y.value);e.value=c.data||c,console.log("📦 Product loaded:",e.value),console.log("📅 Product dates:",{createdAt:(t=e.value)==null?void 0:t.createdAt,updatedAt:(a=e.value)==null?void 0:a.updatedAt,approvedAt:(l=e.value)==null?void 0:l.approvedAt}),await R()}catch(c){console.error("❌ Error loading product:",c),h.value=c.message||"Failed to load product"}finally{_.value=!1}},R=async()=>{try{const t=await Y.get(`/api/admin/products/${y.value}/with-images`);t.data&&t.data.success&&t.data.data&&(r.value=t.data.data.images||[],console.log("Product images loaded:",r.value.length))}catch(t){console.error("Error loading product images:",t),r.value=[]}},B=async()=>{try{console.log("🏢 Loading companies for view...");const t=await F.getCompanies({pageSize:200});console.log("📦 Raw companies response:",t);let a=[];t&&t.data&&Array.isArray(t.data)?a=t.data:t&&t.companies&&Array.isArray(t.companies)?a=t.companies:Array.isArray(t)?a=t:(console.warn("⚠️ Unexpected companies response structure:",t),a=[]),u.value=a,console.log("✅ Companies loaded for view:",u.value.length)}catch(t){console.error("❌ Error loading companies for view:",t),u.value=[]}},O=async()=>{try{console.log("🏷️ Loading categories for view...");const t=await P.getCategories({pageSize:1e3});m.value=Array.isArray(t)?t:[],console.log("✅ Categories loaded for view:",m.value.length)}catch(t){console.error("❌ Error loading categories for view:",t),m.value=[]}};return V(async()=>{await Promise.all([Z(),B(),O()])}),(t,a)=>(i(),o("div",J,[_.value?(i(),o("div",$,a[0]||(a[0]=[s("div",{class:"loader is-loading"},null,-1),s("p",{class:"mt-4"},"Loading product details...",-1)]))):h.value?(i(),o("div",q,[a[1]||(a[1]=s("strong",null,"Error:",-1)),G(" "+n(h.value),1)])):e.value?(i(),o("div",K,[s("div",X,[s("div",ss,[s("div",as,[s("div",ts,[s("div",{class:"level-item"},[s("button",{class:"button is-white",onClick:L},a[2]||(a[2]=[s("span",{class:"icon"},[s("i",{class:"fas fa-arrow-left"})],-1),s("span",null,"Back to Products",-1)]))]),s("div",es,[s("div",null,[s("h1",ls,n(e.value.name),1),s("p",os,[s("span",is,[a[3]||(a[3]=s("span",{class:"icon"},[s("i",{class:"fas fa-link"})],-1)),s("span",null,n(e.value.slug),1)])])])])]),s("div",ns,[s("div",cs,[s("span",{class:v(["tag is-large",D(e.value.status)])},[s("span",ds,[s("i",{class:v(["fas",{"fa-check-circle":e.value.status===1||e.value.status==="approved","fa-clock":e.value.status===0||e.value.status==="pending","fa-times-circle":e.value.status===2||e.value.status==="rejected"}])},null,2)]),s("span",null,n(N(e.value.status)),1)],2)])])])])]),s("div",rs,[s("div",us,[s("div",vs,[a[11]||(a[11]=f('<header class="card-header" data-v-a25f8a51><p class="card-header-title" data-v-a25f8a51><span class="icon-text" data-v-a25f8a51><span class="icon has-text-primary" data-v-a25f8a51><i class="fas fa-info-circle" data-v-a25f8a51></i></span><span data-v-a25f8a51>Basic Information</span></span></p></header>',1)),s("div",ps,[s("div",gs,[s("div",fs,[s("div",ms,[a[4]||(a[4]=s("label",{class:"label has-text-grey"},"Product ID",-1)),s("div",hs,[s("code",_s,n(e.value.id),1)])])]),s("div",ys,[s("div",xs,[a[7]||(a[7]=s("label",{class:"label has-text-grey"},"Company",-1)),s("div",bs,[s("span",Is,[a[6]||(a[6]=s("span",{class:"icon"},[s("i",{class:"fas fa-building"})],-1)),s("span",null,n(S.value),1),x.value?(i(),o("button",{key:0,class:"button is-small is-text ml-2",onClick:z,title:"View company details"},a[5]||(a[5]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-external-link-alt"})],-1)]))):d("",!0)])])])]),s("div",ks,[s("div",ws,[a[9]||(a[9]=s("label",{class:"label has-text-grey"},"Category",-1)),s("div",As,[s("span",Ds,[a[8]||(a[8]=s("span",{class:"icon"},[s("i",{class:"fas fa-folder"})],-1)),s("span",null,n(U.value),1)])])])]),s("div",Ns,[s("div",Cs,[a[10]||(a[10]=s("label",{class:"label has-text-grey"},"Description",-1)),s("div",Ms,[s("p",Ps,n(e.value.description||"No description provided"),1)])])])])])])]),s("div",js,[s("div",Ts,[a[16]||(a[16]=f('<header class="card-header" data-v-a25f8a51><p class="card-header-title" data-v-a25f8a51><span class="icon-text" data-v-a25f8a51><span class="icon has-text-success" data-v-a25f8a51><i class="fas fa-dollar-sign" data-v-a25f8a51></i></span><span data-v-a25f8a51>Pricing &amp; Inventory</span></span></p></header>',1)),s("div",Es,[s("div",Ss,[s("div",Us,[s("div",zs,[a[13]||(a[13]=s("label",{class:"label has-text-grey"},"Price",-1)),s("div",Ls,[s("span",Zs,[a[12]||(a[12]=s("span",{class:"icon"},[s("i",{class:"fas fa-hryvnia-sign"})],-1)),s("span",null,n(e.value.priceAmount)+" "+n(e.value.priceCurrency),1)])])])]),s("div",Rs,[s("div",Bs,[a[15]||(a[15]=s("label",{class:"label has-text-grey"},"Stock",-1)),s("div",Os,[s("span",{class:v(["tag is-large",e.value.stock>0?"is-info":"is-danger"])},[a[14]||(a[14]=s("span",{class:"icon"},[s("i",{class:"fas fa-boxes"})],-1)),s("span",null,n(e.value.stock)+" units",1)],2)])])])])])])]),s("div",Hs,[s("div",Qs,[a[20]||(a[20]=f('<header class="card-header" data-v-a25f8a51><p class="card-header-title" data-v-a25f8a51><span class="icon-text" data-v-a25f8a51><span class="icon has-text-info" data-v-a25f8a51><i class="fas fa-chart-line" data-v-a25f8a51></i></span><span data-v-a25f8a51>Status</span></span></p></header>',1)),s("div",Vs,[s("div",Gs,[a[17]||(a[17]=s("label",{class:"label has-text-grey"},"Product Status",-1)),s("div",Ws,[s("span",{class:v(["tag is-large",D(e.value.status)])},[s("span",Ys,[s("i",{class:v(["fas",{"fa-check-circle":e.value.status===1||e.value.status==="approved","fa-clock":e.value.status===0||e.value.status==="pending","fa-times-circle":e.value.status===2||e.value.status==="rejected"}])},null,2)]),s("span",null,n(N(e.value.status)),1)],2)])]),e.value.approvedAt?(i(),o("div",Fs,[a[19]||(a[19]=s("label",{class:"label has-text-grey"},"Approved At",-1)),s("div",Js,[s("span",$s,[a[18]||(a[18]=s("span",{class:"icon"},[s("i",{class:"fas fa-calendar-check"})],-1)),s("span",null,n(I(e.value.approvedAt)),1)])])])):d("",!0)])])]),b.value&&Object.keys(b.value).length>0?(i(),o("div",qs,[s("div",Ks,[a[22]||(a[22]=f('<header class="card-header" data-v-a25f8a51><p class="card-header-title" data-v-a25f8a51><span class="icon-text" data-v-a25f8a51><span class="icon has-text-warning" data-v-a25f8a51><i class="fas fa-tags" data-v-a25f8a51></i></span><span data-v-a25f8a51>Attributes</span></span></p></header>',1)),s("div",Xs,[s("div",sa,[s("table",aa,[a[21]||(a[21]=s("thead",null,[s("tr",null,[s("th",null,"Attribute"),s("th",null,"Values")])],-1)),s("tbody",null,[(i(!0),o(k,null,w(b.value,(l,c)=>(i(),o("tr",{key:c},[s("td",null,[s("strong",null,n(c),1)]),s("td",null,[Array.isArray(l)?(i(),o("span",ta,[(i(!0),o(k,null,w(l,M=>(i(),o("span",{key:M,class:"tag is-light"},n(M),1))),128))])):(i(),o("span",ea,n(l),1))])]))),128))])])])])])])):d("",!0),s("div",la,[s("div",oa,[s("header",ia,[s("p",na,[s("span",ca,[a[23]||(a[23]=s("span",{class:"icon has-text-link"},[s("i",{class:"fas fa-images"})],-1)),a[24]||(a[24]=s("span",null,"Product Images",-1)),r.value.length>0?(i(),o("span",da,n(r.value.length),1)):d("",!0)])])]),s("div",ra,[r.value.length>0?(i(),o("div",ua,[(i(!0),o(k,null,w(r.value,l=>(i(),o("div",{key:l.id,class:"column is-3"},[s("div",va,[s("div",pa,[s("figure",ga,[s("img",{src:l.imageUrl,alt:l.altText||"Product image",onError:C,class:"is-rounded"},null,40,fa)])]),s("div",ma,[s("div",ha,[s("div",_a,[s("div",ya,[s("span",{class:v(["tag is-small",l.isMain?"is-primary":"is-light"])},[s("span",xa,[s("i",{class:v(["fas",l.isMain?"fa-star":"fa-image"])},null,2)]),s("span",null,n(l.isMain?"Main":`#${l.order}`),1)],2)])])])])])]))),128))])):(i(),o("div",ba,a[25]||(a[25]=[s("div",{class:"notification is-light"},[s("span",{class:"icon is-large has-text-grey-light"},[s("i",{class:"fas fa-images fa-3x"})]),s("p",{class:"has-text-grey mt-3"},[s("strong",null,"No images available"),s("br"),s("small",null,"This product doesn't have any images yet.")])],-1)])))])])]),e.value.metaTitle||e.value.metaDescription||e.value.metaImage?(i(),o("div",Ia,[s("div",ka,[a[29]||(a[29]=f('<header class="card-header" data-v-a25f8a51><p class="card-header-title" data-v-a25f8a51><span class="icon-text" data-v-a25f8a51><span class="icon has-text-warning" data-v-a25f8a51><i class="fas fa-search" data-v-a25f8a51></i></span><span data-v-a25f8a51>SEO Meta</span></span></p></header>',1)),s("div",wa,[e.value.metaTitle?(i(),o("div",Aa,[a[26]||(a[26]=s("label",{class:"label has-text-grey"},"Meta Title",-1)),s("div",Da,[s("p",Na,n(e.value.metaTitle),1)])])):d("",!0),e.value.metaDescription?(i(),o("div",Ca,[a[27]||(a[27]=s("label",{class:"label has-text-grey"},"Meta Description",-1)),s("div",Ma,[s("p",Pa,n(e.value.metaDescription),1)])])):d("",!0),e.value.metaImage?(i(),o("div",ja,[a[28]||(a[28]=s("label",{class:"label has-text-grey"},"Meta Image",-1)),s("div",Ta,[s("figure",Ea,[s("img",{src:e.value.metaImage,alt:e.value.name,onError:C,class:"is-rounded"},null,40,Sa)])])])):d("",!0)])])])):d("",!0),s("div",Ua,[s("div",za,[a[34]||(a[34]=f('<header class="card-header" data-v-a25f8a51><p class="card-header-title" data-v-a25f8a51><span class="icon-text" data-v-a25f8a51><span class="icon has-text-grey" data-v-a25f8a51><i class="fas fa-clock" data-v-a25f8a51></i></span><span data-v-a25f8a51>Timestamps</span></span></p></header>',1)),s("div",La,[s("div",Za,[a[31]||(a[31]=s("label",{class:"label has-text-grey"},"Created At",-1)),s("div",Ra,[s("span",Ba,[a[30]||(a[30]=s("span",{class:"icon"},[s("i",{class:"fas fa-calendar-plus"})],-1)),s("span",null,n(I(e.value.createdAt)),1)])])]),e.value.updatedAt?(i(),o("div",Oa,[a[33]||(a[33]=s("label",{class:"label has-text-grey"},"Updated At",-1)),s("div",Ha,[s("span",Qa,[a[32]||(a[32]=s("span",{class:"icon"},[s("i",{class:"fas fa-calendar-edit"})],-1)),s("span",null,n(I(e.value.updatedAt)),1)])])])):d("",!0)])])])])])):d("",!0)]))}},Fa=H(Va,[["__scopeId","data-v-a25f8a51"]]);export{Fa as default};
