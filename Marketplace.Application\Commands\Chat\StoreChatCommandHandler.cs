﻿using AutoMapper;
using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.Chat;

public class StoreChatCommandHandler : IRequestHandler<StoreChatCommand, Guid>
{
    private readonly IChatRepository _repository;
    private readonly IMapper _mapper;

    public StoreChatCommandHandler(IChatRepository repository, IMapper mapper)
    {
        _repository = repository;
        _mapper = mapper;
    }

    public async Task<Guid> Handle(StoreChatCommand request, CancellationToken cancellationToken)
    {
        var item = _mapper.Map<Domain.Entities.Chat>(request);
        await _repository.AddAsync(item, cancellationToken);
        return item.Id;
    }
}

