import{_ as ds,g as u,h as cs,f as us,i as vs,c as n,a as s,b as y,w as k,r as fs,d as K,t as l,k as b,n as f,s as ps,F as Q,p as X,z as ms,C as _s,e as hs,o}from"./index-BKy0rL_2.js";import{s as D}from"./seller-requests-aoHB3xeQ.js";import{S as gs}from"./StatusBadge-DZXrI7cG.js";import{C as ys}from"./ConfirmDialog-BQ115uZp.js";/* empty css                                                                    */const bs={class:"seller-request-detail"},Rs={class:"level"},qs={class:"level-right"},ks={class:"level-item"},Ds={key:0,class:"has-text-centered py-6"},As={key:1,class:"notification is-danger"},Cs={key:2,class:"notification is-warning"},Ns={key:3},Ss={class:"card mb-4"},ws={class:"card-content"},js={class:"columns"},xs={class:"column is-8"},Is={class:"request-title"},Ts={class:"request-subtitle"},Bs={class:"ml-2"},Ps={class:"column is-4 has-text-right"},Es={key:0,class:"buttons is-right"},Fs={key:1,class:"notification is-success is-light"},$s={key:2,class:"notification is-danger is-light"},Ms={class:"columns"},Us={class:"column is-4"},Vs={class:"card"},Ls={class:"card-content"},Os={class:"user-avatar"},zs=["src","alt"],Ws={class:"info-group"},Gs={class:"info-value"},Hs={class:"info-group"},Js={class:"info-value"},Ks=["href"],Qs={key:1},Xs={class:"info-group"},Ys={class:"info-value"},Zs={class:"info-group"},se={class:"info-value"},ee={class:"info-group"},te={class:"info-value"},ae={key:1},le={class:"column is-8"},oe={class:"card"},ne={class:"card-content"},ie={class:"columns"},re={class:"column"},de={class:"info-group"},ce={class:"info-value"},ue={class:"info-group"},ve={class:"info-value"},fe={class:"info-group"},pe={class:"info-value"},me={class:"info-group"},_e={class:"info-value"},he={class:"column"},ge={class:"info-group"},ye={class:"info-value"},be={class:"info-group"},Re={class:"info-value"},qe={class:"info-group"},ke={class:"info-value"},De={key:0,class:"info-group"},Ae={class:"image is-128x128"},Ce=["src"],Ne={class:"card mt-4"},Se={class:"card-content"},we={class:"columns"},je={class:"column"},xe={class:"info-group"},Ie={class:"info-value"},Te={class:"info-group"},Be={class:"info-value"},Pe={class:"column"},Ee={class:"info-group"},Fe={class:"info-value"},$e={class:"info-group"},Me={class:"info-value"},Ue={class:"info-group"},Ve={class:"info-value"},Le={class:"card mt-4"},Oe={class:"card-content"},ze={key:0,class:"table-container"},We={class:"table is-fullwidth"},Ge={key:1,class:"has-text-centered py-4"},He={class:"card mt-4"},Je={class:"card-content"},Ke={key:0,class:"info-group"},Qe={class:"info-value"},Xe={key:1,class:"has-text-centered py-4"},Ye={key:0,class:"card mt-4"},Ze={class:"card-content"},st={class:"documents-list"},et={class:"document-icon"},tt={class:"icon"},at={class:"document-info"},lt={class:"document-name"},ot={class:"document-type"},nt={class:"document-actions"},it=["href"],rt={key:1,class:"card mt-4"},dt={class:"card-content"},ct={class:"modal-card"},ut={class:"modal-card-body"},vt={class:"field mt-4"},ft={class:"control"},pt={class:"modal-card-foot"},mt={__name:"SellerRequestDetail",setup(_t){const Y=us();hs();const R=u(!0),r=u(null),t=u({}),d=u(!1),p=u(!1),m=u(!1),c=u(""),q=cs(()=>Y.params.id),Z=async()=>{R.value=!0,r.value=null;try{const a=await D.getSellerRequest(q.value);t.value=a.data}catch(a){console.error("Error fetching seller request:",a),r.value="Failed to load seller request data. Please try again."}finally{R.value=!1}},ss=()=>{var v;const a=(v=t.value)==null?void 0:v.companyRequestData;if(!a)return"N/A";const e=[a.addressStreet,a.addressCity,a.addressRegion,a.addressPostalCode].filter(Boolean);return e.length>0?e.join(", "):"N/A"},es=a=>["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][a]||"Unknown",_=a=>{if(typeof a=="string")return a.toLowerCase();switch(a){case 0:return"pending";case 1:return"approved";case 2:return"rejected";default:return"pending"}},h=a=>a?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(new Date(a)):"",ts=a=>{a.target.src="https://via.placeholder.com/150?text=No+Image"},as=a=>a?(a=a.toLowerCase(),a.includes("pdf")?"fas fa-file-pdf":a.includes("word")||a.includes("doc")?"fas fa-file-word":a.includes("excel")||a.includes("xls")?"fas fa-file-excel":a.includes("image")||a.includes("jpg")||a.includes("png")?"fas fa-file-image":"fas fa-file"):"fas fa-file",ls=()=>{p.value=!0},os=()=>{c.value="",m.value=!0},g=()=>{p.value=!1,m.value=!1,c.value=""},ns=async()=>{d.value=!0;try{await D.approveSellerRequest(q.value),t.value.status=1,t.value.approvedAt=new Date,p.value=!1}catch(a){console.error("Error approving seller request:",a),r.value="Failed to approve seller request. Please try again."}finally{d.value=!1}},is=async()=>{d.value=!0;try{await D.rejectSellerRequest(q.value,c.value),t.value.status=2,t.value.rejectedAt=new Date,t.value.rejectionReason=c.value,m.value=!1,c.value=""}catch(a){console.error("Error rejecting seller request:",a),r.value="Failed to reject seller request. Please try again."}finally{d.value=!1}};return vs(()=>{Z()}),(a,e)=>{var A,C,N,S,w,j,x,I,T,B,P,E,F,$,M,U,V,L,O,z,W,G,H,J;const v=fs("router-link");return o(),n("div",bs,[s("div",Rs,[e[3]||(e[3]=s("div",{class:"level-left"},[s("div",{class:"level-item"},[s("h1",{class:"title"},"Seller Request Details")])],-1)),s("div",qs,[s("div",ks,[y(v,{to:"/admin/seller-requests",class:"button is-light"},{default:k(()=>e[2]||(e[2]=[s("span",{class:"icon"},[s("i",{class:"fas fa-arrow-left"})],-1),s("span",null,"Back to Seller Requests",-1)])),_:1})])])]),R.value?(o(),n("div",Ds,e[4]||(e[4]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),s("p",{class:"mt-2"},"Loading seller request details...",-1)]))):r.value?(o(),n("div",As,[s("button",{class:"delete",onClick:e[0]||(e[0]=i=>r.value=null)}),K(" "+l(r.value),1)])):t.value.id?(o(),n("div",Ns,[s("div",Ss,[s("div",ws,[s("div",js,[s("div",xs,[s("h2",Is,l(((A=t.value.companyRequestData)==null?void 0:A.name)||"Seller Request"),1),s("p",Ts,[y(gs,{status:_(t.value.status),type:"default"},null,8,["status"]),s("span",Bs,"Submitted on "+l(h(t.value.createdAt)),1)])]),s("div",Ps,[_(t.value.status)==="pending"?(o(),n("div",Es,[s("button",{class:f(["button is-success",{"is-loading":d.value}]),onClick:ls},e[7]||(e[7]=[s("span",{class:"icon"},[s("i",{class:"fas fa-check"})],-1),s("span",null,"Approve",-1)]),2),s("button",{class:f(["button is-danger",{"is-loading":d.value}]),onClick:os},e[8]||(e[8]=[s("span",{class:"icon"},[s("i",{class:"fas fa-times"})],-1),s("span",null,"Reject",-1)]),2)])):_(t.value.status)==="approved"?(o(),n("div",Fs,[s("p",null,[e[9]||(e[9]=s("span",{class:"icon"},[s("i",{class:"fas fa-check-circle"})],-1)),s("span",null,"Approved on "+l(h(t.value.updatedAt)),1)])])):_(t.value.status)==="rejected"?(o(),n("div",$s,[s("p",null,[e[10]||(e[10]=s("span",{class:"icon"},[s("i",{class:"fas fa-times-circle"})],-1)),s("span",null,"Rejected on "+l(h(t.value.updatedAt)),1)])])):b("",!0)])])])]),s("div",Ms,[s("div",Us,[s("div",Vs,[e[17]||(e[17]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Applicant Information")],-1)),s("div",Ls,[s("div",Os,[s("img",{src:((C=t.value.user)==null?void 0:C.avatar)||"https://via.placeholder.com/150",alt:`${((N=t.value.user)==null?void 0:N.firstName)||""} ${((S=t.value.user)==null?void 0:S.lastName)||""}`,onError:ts},null,40,zs)]),s("div",Ws,[e[11]||(e[11]=s("h3",{class:"info-label"},"Username",-1)),s("p",Gs,l(((w=t.value.user)==null?void 0:w.username)||"N/A"),1)]),s("div",Hs,[e[12]||(e[12]=s("h3",{class:"info-label"},"Email",-1)),s("p",Js,[(j=t.value.user)!=null&&j.email?(o(),n("a",{key:0,href:`mailto:${t.value.user.email}`},l(t.value.user.email),9,Ks)):(o(),n("span",Qs,"N/A"))])]),s("div",Xs,[e[13]||(e[13]=s("h3",{class:"info-label"},"Role",-1)),s("p",Ys,l(((x=t.value.user)==null?void 0:x.role)||"Not provided"),1)]),s("div",Zs,[e[14]||(e[14]=s("h3",{class:"info-label"},"Registered Since",-1)),s("p",se,l(h((I=t.value.user)==null?void 0:I.createdAt)),1)]),s("div",ee,[e[16]||(e[16]=s("h3",{class:"info-label"},"User Profile",-1)),s("p",te,[t.value.userId?(o(),ps(v,{key:0,to:`/admin/users/${t.value.userId}`,class:"button is-info is-small"},{default:k(()=>e[15]||(e[15]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-user"})],-1),s("span",null,"View Profile",-1)])),_:1},8,["to"])):(o(),n("span",ae,"N/A"))])])])])]),s("div",le,[s("div",oe,[e[26]||(e[26]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Company Information")],-1)),s("div",ne,[s("div",ie,[s("div",re,[s("div",de,[e[18]||(e[18]=s("h3",{class:"info-label"},"Company Name",-1)),s("p",ce,l(((T=t.value.companyRequestData)==null?void 0:T.name)||"N/A"),1)]),s("div",ue,[e[19]||(e[19]=s("h3",{class:"info-label"},"Description",-1)),s("p",ve,l(((B=t.value.companyRequestData)==null?void 0:B.description)||"N/A"),1)]),s("div",fe,[e[20]||(e[20]=s("h3",{class:"info-label"},"Contact Email",-1)),s("p",pe,l(((P=t.value.companyRequestData)==null?void 0:P.contactEmail)||"N/A"),1)]),s("div",me,[e[21]||(e[21]=s("h3",{class:"info-label"},"Contact Phone",-1)),s("p",_e,l(((E=t.value.companyRequestData)==null?void 0:E.contactPhone)||"N/A"),1)])]),s("div",he,[s("div",ge,[e[22]||(e[22]=s("h3",{class:"info-label"},"Address",-1)),s("p",ye,l(ss()),1)]),s("div",be,[e[23]||(e[23]=s("h3",{class:"info-label"},"Meta Title",-1)),s("p",Re,l(((F=t.value.companyRequestData)==null?void 0:F.metaTitle)||"N/A"),1)]),s("div",qe,[e[24]||(e[24]=s("h3",{class:"info-label"},"Meta Description",-1)),s("p",ke,l((($=t.value.companyRequestData)==null?void 0:$.metaDescription)||"N/A"),1)])])]),(M=t.value.companyRequestData)!=null&&M.imageUrl?(o(),n("div",De,[e[25]||(e[25]=s("h3",{class:"info-label"},"Company Image",-1)),s("figure",Ae,[s("img",{src:t.value.companyRequestData.imageUrl,alt:"Company image",class:"is-rounded"},null,8,Ce)])])):b("",!0)])]),s("div",Ne,[e[32]||(e[32]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Finance Information")],-1)),s("div",Se,[s("div",we,[s("div",je,[s("div",xe,[e[27]||(e[27]=s("h3",{class:"info-label"},"Bank Name",-1)),s("p",Ie,l(((U=t.value.financeRequestData)==null?void 0:U.bankName)||"N/A"),1)]),s("div",Te,[e[28]||(e[28]=s("h3",{class:"info-label"},"Bank Account",-1)),s("p",Be,l(((V=t.value.financeRequestData)==null?void 0:V.bankAccount)||"N/A"),1)])]),s("div",Pe,[s("div",Ee,[e[29]||(e[29]=s("h3",{class:"info-label"},"Bank Code",-1)),s("p",Fe,l(((L=t.value.financeRequestData)==null?void 0:L.bankCode)||"N/A"),1)]),s("div",$e,[e[30]||(e[30]=s("h3",{class:"info-label"},"Tax ID",-1)),s("p",Me,l(((O=t.value.financeRequestData)==null?void 0:O.taxId)||"N/A"),1)])])]),s("div",Ue,[e[31]||(e[31]=s("h3",{class:"info-label"},"Payment Details",-1)),s("p",Ve,l(((z=t.value.financeRequestData)==null?void 0:z.paymentDetails)||"N/A"),1)])])]),s("div",Le,[e[35]||(e[35]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Schedule Information")],-1)),s("div",Oe,[(W=t.value.scheduleRequestData)!=null&&W.daySchedules?(o(),n("div",ze,[s("table",We,[e[33]||(e[33]=s("thead",null,[s("tr",null,[s("th",null,"Day"),s("th",null,"Open Time"),s("th",null,"Close Time"),s("th",null,"Status")])],-1)),s("tbody",null,[(o(!0),n(Q,null,X(t.value.scheduleRequestData.daySchedules,i=>(o(),n("tr",{key:i.day},[s("td",null,l(es(i.day)),1),s("td",null,l(i.isClosed?"-":i.openTime),1),s("td",null,l(i.isClosed?"-":i.closeTime),1),s("td",null,[s("span",{class:f(["tag",i.isClosed?"is-danger":"is-success"])},l(i.isClosed?"Closed":"Open"),3)])]))),128))])])])):(o(),n("div",Ge,e[34]||(e[34]=[s("p",{class:"has-text-grey"},"No schedule information provided",-1)])))])]),s("div",He,[e[37]||(e[37]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Additional Information")],-1)),s("div",Je,[t.value.additionalInformation?(o(),n("div",Ke,[s("p",Qe,l(t.value.additionalInformation),1)])):(o(),n("div",Xe,e[36]||(e[36]=[s("p",{class:"has-text-grey"},"No additional information provided",-1)])))])]),t.value.documents&&t.value.documents.length>0?(o(),n("div",Ye,[e[39]||(e[39]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Documents")],-1)),s("div",Ze,[s("div",st,[(o(!0),n(Q,null,X(t.value.documents,(i,rs)=>(o(),n("div",{key:rs,class:"document-item"},[s("div",et,[s("span",tt,[s("i",{class:f(as(i.type))},null,2)])]),s("div",at,[s("p",lt,l(i.name),1),s("p",ot,l(i.type),1)]),s("div",nt,[s("a",{href:i.url,target:"_blank",rel:"noopener noreferrer",class:"button is-small is-info"},e[38]||(e[38]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-download"})],-1),s("span",null,"Download",-1)]),8,it)])]))),128))])])])):b("",!0),t.value.status==="rejected"&&t.value.rejectionReason?(o(),n("div",rt,[e[40]||(e[40]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Rejection Reason")],-1)),s("div",dt,[s("p",null,l(t.value.rejectionReason),1)])])):b("",!0)])])])):(o(),n("div",Cs,[e[6]||(e[6]=s("p",null,"Seller request not found.",-1)),y(v,{to:"/admin/seller-requests",class:"button is-primary mt-4"},{default:k(()=>e[5]||(e[5]=[K(" Back to Seller Requests ")])),_:1})])),y(ys,{"is-open":p.value,title:"Approve Seller Request",message:`Are you sure you want to approve ${((G=t.value.user)==null?void 0:G.firstName)||"this user"} ${((H=t.value.user)==null?void 0:H.lastName)||""}'s seller request for '${t.value.storeName||"this store"}'?`,"confirm-text":"Approve","cancel-text":"Cancel","confirm-button-class":"is-success",onConfirm:ns,onCancel:g},null,8,["is-open","message"]),s("div",{class:f(["modal",{"is-active":m.value}])},[s("div",{class:"modal-background",onClick:g}),s("div",ct,[s("header",{class:"modal-card-head"},[e[41]||(e[41]=s("p",{class:"modal-card-title"},"Reject Seller Request",-1)),s("button",{class:"delete","aria-label":"close",onClick:g})]),s("section",ut,[s("p",null,"Are you sure you want to reject "+l(((J=t.value.user)==null?void 0:J.firstName)||"this user")+"'s seller request for '"+l(t.value.storeName||"this store")+"'?",1),s("div",vt,[e[42]||(e[42]=s("label",{class:"label"},"Reason for Rejection (Optional)",-1)),s("div",ft,[ms(s("textarea",{class:"textarea","onUpdate:modelValue":e[1]||(e[1]=i=>c.value=i),placeholder:"Provide a reason for rejection"},"              ",512),[[_s,c.value]])])])]),s("footer",pt,[s("button",{class:f(["button is-danger",{"is-loading":d.value}]),onClick:is}," Reject ",2),s("button",{class:"button is-light",onClick:g},"Cancel")])])],2)])}}},qt=ds(mt,[["__scopeId","data-v-067a51ed"]]);export{qt as default};
