<template>
  <div class="box mb-4">
    <div class="columns is-multiline">
      <div class="column is-4">
        <div class="field">
          <label class="label">Search</label>
          <div class="control has-icons-left">
            <input 
              class="input" 
              type="text" 
              placeholder="Search by name, store name..." 
              v-model="filters.search"
              @input="debounceSearch">
            <span class="icon is-small is-left">
              <i class="fas fa-search"></i>
            </span>
          </div>
        </div>
      </div>
      
      <div class="column is-4">
        <div class="field">
          <label class="label">Status</label>
          <div class="control">
            <div class="select is-fullwidth">
              <select v-model="filters.status">
                <option value="">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
          </div>
        </div>
      </div>
      
      <div class="column is-4">
        <div class="field">
          <label class="label">Date Range</label>
          <div class="columns is-mobile">
            <div class="column is-6">
              <input 
                class="input" 
                type="date" 
                v-model="filters.startDate">
            </div>
            <div class="column is-6">
              <input 
                class="input" 
                type="date" 
                v-model="filters.endDate">
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="columns">
      <div class="column is-12">
        <div class="buttons is-right">
          <button class="button is-light" @click="resetFilters">
            <span class="icon"><i class="fas fa-undo"></i></span>
            <span>Reset</span>
          </button>
          <button class="button is-primary" @click="applyFilters">
            <span class="icon"><i class="fas fa-filter"></i></span>
            <span>Apply Filters</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue';

const emit = defineEmits(['filter-changed']);

// Filter state
const filters = reactive({
  search: '',
  status: '',
  startDate: '',
  endDate: ''
});

// Apply filters
const applyFilters = () => {
  // Create a clean filter object (remove null/empty values)
  const cleanFilters = {};
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== null && value !== '') {
      cleanFilters[key] = value;
    }
  });
  
  emit('filter-changed', cleanFilters);
};

// Reset filters
const resetFilters = () => {
  filters.search = '';
  filters.status = '';
  filters.startDate = '';
  filters.endDate = '';
  
  emit('filter-changed', {});
};

// Debounce search to avoid too many requests
let searchTimeout;
const debounceSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    applyFilters();
  }, 500);
};
</script>

<style scoped>
.mb-4 {
  margin-bottom: 1.5rem;
}
</style>
