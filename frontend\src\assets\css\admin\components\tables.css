/* ===== ADMIN TABLES SYSTEM ===== */
/* Based on Reports page table patterns */

/* ===== BASE TABLE ===== */
.admin-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--admin-white);
  border-radius: var(--admin-radius-lg);
  overflow: hidden;
  box-shadow: var(--admin-shadow-sm);
}

.admin-table-container {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  overflow-x: auto;
}

/* ===== TABLE HEADER ===== */
.admin-table thead {
  background: var(--admin-table-header-bg);
}

.admin-table th {
  padding: var(--admin-space-lg) var(--admin-space-md);
  text-align: left;
  font-weight: var(--admin-font-semibold);
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-700);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--admin-table-border);
  white-space: nowrap;
}

.admin-table th:first-child {
  padding-left: var(--admin-space-xl);
}

.admin-table th:last-child {
  padding-right: var(--admin-space-xl);
}

/* ===== TABLE BODY ===== */
.admin-table tbody tr {
  border-bottom: 1px solid var(--admin-border-light);
  transition: all var(--admin-transition-fast);
}

.admin-table tbody tr:hover {
  background-color: var(--admin-gray-50);
}

.admin-table tbody tr:last-child {
  border-bottom: none;
}

.admin-table td {
  padding: var(--admin-space-lg) var(--admin-space-md);
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-700);
  vertical-align: middle;
}

.admin-table td:first-child {
  padding-left: var(--admin-space-xl);
}

.admin-table td:last-child {
  padding-right: var(--admin-space-xl);
}

/* ===== TABLE VARIANTS ===== */

/* Striped Table */
.admin-table-striped tbody tr:nth-child(even) {
  background: var(--admin-gray-50);
}

.admin-table-striped tbody tr:nth-child(even):hover {
  background: var(--admin-gray-100);
}

/* Bordered Table */
.admin-table-bordered {
  border: 1px solid var(--admin-border-light);
}

.admin-table-bordered th,
.admin-table-bordered td {
  border-right: 1px solid var(--admin-border-light);
}

.admin-table-bordered th:last-child,
.admin-table-bordered td:last-child {
  border-right: none;
}

/* Compact Table */
.admin-table-compact th,
.admin-table-compact td {
  padding: var(--admin-space-sm) var(--admin-space-md);
}

/* ===== TABLE CELL TYPES ===== */

/* Numeric cells */
.admin-table-cell-numeric {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

/* Status cells */
.admin-table-cell-status {
  text-align: center;
}

/* Action cells */
.admin-table-cell-actions {
  text-align: right;
  white-space: nowrap;
}

.admin-table-actions {
  display: flex;
  gap: var(--admin-space-sm);
  justify-content: flex-end;
  align-items: center;
}

/* ===== STATUS BADGES IN TABLES ===== */
.admin-status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-full);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-status-badge.success {
  background: var(--admin-success-bg);
  color: var(--admin-success-dark);
}

.admin-status-badge.warning {
  background: var(--admin-warning-bg);
  color: var(--admin-warning-dark);
}

.admin-status-badge.danger {
  background: var(--admin-danger-bg);
  color: var(--admin-danger-dark);
}

.admin-status-badge.info {
  background: var(--admin-info-bg);
  color: var(--admin-info-dark);
}

.admin-status-badge.neutral {
  background: var(--admin-gray-100);
  color: var(--admin-gray-600);
}

/* ===== TABLE SORTING ===== */
.admin-table-sortable th {
  cursor: pointer;
  user-select: none;
  position: relative;
}

.admin-table-sortable th:hover {
  background: var(--admin-gray-100);
}

.admin-table-sort-icon {
  margin-left: var(--admin-space-xs);
  opacity: 0.5;
  transition: opacity var(--admin-transition-fast);
}

.admin-table-sortable th:hover .admin-table-sort-icon {
  opacity: 1;
}

.admin-table-sortable th.sorted .admin-table-sort-icon {
  opacity: 1;
  color: var(--admin-primary);
}

/* ===== TABLE PAGINATION ===== */
.admin-table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--admin-space-lg) 0;
  border-top: 1px solid var(--admin-border-color);
  margin-top: var(--admin-space-lg);
}

.admin-table-info {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
}

.admin-pagination {
  display: flex;
  gap: var(--admin-space-xs);
  align-items: center;
}

.admin-pagination-btn {
  padding: var(--admin-space-sm) var(--admin-space-md);
  border: 1px solid var(--admin-border-color);
  background: var(--admin-white);
  color: var(--admin-gray-600);
  border-radius: var(--admin-radius-md);
  cursor: pointer;
  transition: all var(--admin-transition-fast);
  font-size: var(--admin-text-sm);
}

.admin-pagination-btn:hover:not(:disabled) {
  background: var(--admin-gray-50);
  border-color: var(--admin-gray-300);
}

.admin-pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.admin-pagination-btn.active {
  background: var(--admin-primary);
  color: white;
  border-color: var(--admin-primary);
}

/* ===== TABLE FILTERS ===== */
.admin-table-filters {
  display: flex;
  gap: var(--admin-space-md);
  margin-bottom: var(--admin-space-lg);
  padding: var(--admin-space-lg);
  background: var(--admin-gray-50);
  border-radius: var(--admin-radius-lg);
}

.admin-table-search {
  flex: 1;
  max-width: 300px;
}

.admin-table-filter-select {
  min-width: 150px;
}

/* ===== TABLE LOADING STATES ===== */
.admin-table-loading {
  position: relative;
}

.admin-table-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.admin-table-empty {
  text-align: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.admin-table-empty i {
  font-size: 3rem;
  margin-bottom: var(--admin-space-lg);
  color: var(--admin-gray-400);
}

/* ===== TABLE RESPONSIVE ===== */
.admin-table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 768px) {
  .admin-table-container {
    padding: var(--admin-space-lg);
  }
  
  .admin-table th,
  .admin-table td {
    padding: var(--admin-space-sm);
  }
  
  .admin-table th:first-child,
  .admin-table td:first-child {
    padding-left: var(--admin-space-sm);
  }
  
  .admin-table th:last-child,
  .admin-table td:last-child {
    padding-right: var(--admin-space-sm);
  }
  
  .admin-table-pagination {
    flex-direction: column;
    gap: var(--admin-space-md);
    align-items: stretch;
  }
  
  .admin-pagination {
    justify-content: center;
  }
}

/* ===== TABLE UTILITIES ===== */
.admin-table-fixed {
  table-layout: fixed;
}

.admin-table-auto {
  table-layout: auto;
}

.admin-table-cell-truncate {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.admin-table-cell-wrap {
  white-space: normal;
  word-wrap: break-word;
}

.admin-table-cell-nowrap {
  white-space: nowrap;
}
