import api from '@/services/api';
import { getRoleValue, getRoleKey } from './roles';

export const usersService = {
  async getUsers(params = {}) {
    try {
      console.log('Requesting users with params:', params);

      // Log the full URL being requested
      const url = '/api/admin/users';
      console.log('Full request URL:', url, 'with params:', params);

      // Створюємо новий об'єкт параметрів для правильної передачі на бекенд
      const fixedParams = {};

      // Додаємо базові параметри пагінації
      if (params.page) {
        fixedParams.page = params.page;
      }

      if (params.pageSize) {
        fixedParams.pageSize = params.pageSize;
      }

      // Не додаємо параметр пошуку, оскільки він викликає помилку на сервері
      // Пошук буде виконано на клієнті

      // Додаємо параметр ролі, якщо він є, з правильним перетворенням
      if (params.role !== undefined && params.role !== null && params.role !== '') {
        // Використовуємо функцію з модуля ролей для отримання числового значення
        const roleValue = getRoleValue(params.role);

        if (roleValue !== -1) {
          fixedParams.role = roleValue;
          console.log(`Converting role ${params.role} to numeric value ${fixedParams.role}`);
        } else {
          console.log(`Invalid role: ${params.role}, not adding to params`);
        }
      }

      // Не використовуємо параметри сортування за роллю, оскільки це викликає помилку
      // Сортування буде виконано на клієнті

      // Log the token for debugging (masked for security)
      const token = localStorage.getItem('token');
      console.log('Auth token present:', !!token);

      console.log('Final request params:', fixedParams);

      const response = await api.get(url, { params: fixedParams });

      // Log the raw response for debugging
      console.log('Raw API response status:', response.status);

      // Check for ApiResponse<T> format
      if (response.data && response.data.success && response.data.data) {
        const responseData = response.data.data;

        console.log('Users data received (ApiResponse format):', {
          count: responseData.items?.length || 0,
          totalItems: responseData.totalItems || responseData.total,
          currentPage: responseData.currentPage || responseData.page
        });

        return {
          users: responseData.items || responseData.data,
          pagination: {
            total: responseData.totalItems || responseData.total,
            page: responseData.currentPage || responseData.page,
            limit: responseData.pageSize || responseData.perPage,
            totalPages: responseData.totalPages || responseData.lastPage
          }
        };
      }
      // Check for direct response format
      else if (response.data && response.data.data) {
        const responseData = response.data.data;

        console.log('Users data received (direct format):', {
          count: responseData.items?.length || 0,
          totalItems: responseData.totalItems || responseData.total,
          currentPage: responseData.currentPage || responseData.page
        });

        return {
          users: responseData.items || [],
          pagination: {
            total: responseData.totalItems || responseData.total || 0,
            page: responseData.currentPage || responseData.page || 1,
            limit: responseData.pageSize || responseData.perPage || 10,
            totalPages: responseData.totalPages || responseData.lastPage || 1
          }
        };
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error('Error fetching users:', error);

      // Enhanced error logging
      if (error.response) {
        console.error('Response error details:', {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
          headers: error.response.headers
        });
      } else if (error.request) {
        console.error('Request was made but no response received:', error.request);
      } else {
        console.error('Error setting up request:', error.message);
      }

      throw error;
    }
  },

  async getUserById(id) {
    try {
      const response = await api.get(`/api/admin/users/${id}`);

      if (response.data && response.data.data) {
        return response.data.data;
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error(`Error fetching user ${id}:`, error);
      throw error;
    }
  },

  async getDetailedUserById(id) {
    try {
      console.log(`Fetching detailed user data for ID: ${id}`);
      const response = await api.get(`/api/admin/users/${id}/detailed`);

      if (response.data && response.data.success && response.data.data) {
        console.log('Detailed user data received:', response.data.data);
        return response.data.data;
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error(`Error fetching detailed user ${id}:`, error);
      throw error;
    }
  },

  async createUser(userData) {
    try {
      // Перетворюємо роль у правильний формат для API
      const formattedData = { ...userData };

      // Перетворюємо роль на числове значення для API
      if (formattedData.role !== undefined && formattedData.role !== null) {
        const roleValue = getRoleValue(formattedData.role);

        if (roleValue !== -1) {
          formattedData.role = roleValue;
          console.log(`Converting role ${userData.role} to numeric value ${formattedData.role}`);
        } else {
          console.log(`Invalid role: ${userData.role}, using default role`);
          formattedData.role = 0; // Default to Buyer
        }
      }

      console.log('Sending formatted data to API for new user:', formattedData);

      const response = await api.post('/api/admin/users', formattedData);

      if (response.data && response.data.data) {
        return response.data.data;
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  },

  async updateUser(id, userData) {
    try {
      // Перетворюємо роль у правильний формат для API
      const formattedData = { ...userData };

      // Перетворюємо роль на числове значення для API
      if (formattedData.role !== undefined && formattedData.role !== null) {
        const roleValue = getRoleValue(formattedData.role);

        if (roleValue !== -1) {
          formattedData.role = roleValue;
          console.log(`Converting role ${userData.role} to numeric value ${formattedData.role}`);
        } else {
          console.log(`Invalid role: ${userData.role}, not changing role`);
        }
      }

      console.log(`Sending formatted data to API for user ${id}:`, formattedData);

      const response = await api.put(`/api/admin/users/${id}`, formattedData);

      if (response.data) {
        return response.data;
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error(`Error updating user ${id}:`, error);
      throw error;
    }
  },

  async deleteUser(id) {
    try {
      const response = await api.delete(`/api/admin/users/${id}`);

      if (response.data) {
        return response.data;
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error(`Error deleting user ${id}:`, error);
      throw error;
    }
  },

  async changeUserRole(id, role) {
    try {
      // Перетворюємо роль у правильний формат для API
      let formattedRole = role;

      // Перетворюємо роль на числове значення для API
      if (role !== undefined && role !== null) {
        const roleValue = getRoleValue(role);

        if (roleValue !== -1) {
          formattedRole = roleValue;
          console.log(`Converting role ${role} to numeric value ${formattedRole}`);
        } else {
          console.log(`Invalid role: ${role}, not changing role`);
        }
      }

      console.log(`Changing role for user ${id} to:`, formattedRole);

      const response = await api.patch(`/api/admin/users/${id}/role`, { role: formattedRole });

      if (response.data) {
        return response.data;
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error(`Error changing role for user ${id}:`, error);
      throw error;
    }
  },

  async getUserOrders(id, params = {}) {
    try {
      const response = await api.get(`/api/admin/users/${id}/orders`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching orders for user ${id}:`, error);
      // Return mock data
      return {
        orders: [
          {
            id: 'ORD-1001',
            total: 156.78,
            status: 'Processing',
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2)
          },
          {
            id: 'ORD-1002',
            total: 89.99,
            status: 'Delivered',
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7)
          },
          {
            id: 'ORD-1003',
            total: 245.50,
            status: 'Delivered',
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 14)
          }
        ],
        pagination: {
          total: 3,
          page: 1,
          limit: 10,
          totalPages: 1
        }
      };
    }
  },

  async resetUserPassword(id) {
    try {
      const response = await api.post(`/api/admin/users/${id}/reset-password`);
      return response.data;
    } catch (error) {
      console.error(`Error resetting password for user ${id}:`, error);
      // Return mock success response
      return {
        success: true,
        message: 'Password reset email sent successfully'
      };
    }
  },



  async getUserStats() {
    try {
      const response = await api.get('/api/admin/users/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching user stats:', error);
      // Return mock data
      return {
        total: 1289,
        active: 1156,
        inactive: 133,
        byRole: {
          admin: 3,
          seller: 124,
          buyer: 1162
        },
        newUsersThisMonth: 87
      };
    }
  },



  async getUserActivity(id) {
    try {
      const response = await api.get(`/api/admin/users/${id}/activity`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching activity for user ${id}:`, error);
      // Return mock data
      return [
        {
          id: '1',
          type: 'login',
          description: 'User logged in',
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2)
        },
        {
          id: '2',
          type: 'profile',
          description: 'User updated profile information',
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24)
        },
        {
          id: '3',
          type: 'order',
          description: 'User placed order #ORD-1001',
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2)
        },
        {
          id: '4',
          type: 'payment',
          description: 'User made payment for order #ORD-1001',
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2)
        },
        {
          id: '5',
          type: 'login',
          description: 'User logged in',
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3)
        },
        {
          id: '6',
          type: 'logout',
          description: 'User logged out',
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3)
        }
      ];
    }
  },

  async exportUsers(params = {}) {
    try {
      const response = await api.get('/api/admin/users/export', {
        params,
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting users:', error);
      // Create a mock CSV file for demo purposes
      const headers = 'ID,First Name,Last Name,Username,Email,Role,Status,Created At\n';
      const rows = [
        '1,Admin,User,admin,<EMAIL>,admin,active,2023-01-01',
        '2,John,Seller,seller1,<EMAIL>,seller,active,2023-01-15',
        '3,Jane,Buyer,customer1,<EMAIL>,buyer,active,2023-02-01',
        '4,Robert,Johnson,robert,<EMAIL>,buyer,active,2023-02-15',
        '5,Emily,Davis,emily,<EMAIL>,buyer,inactive,2023-03-01'
      ].join('\n');

      const csvContent = headers + rows;
      return new Blob([csvContent], { type: 'text/csv' });
    }
  },

  async sendPasswordResetEmail(id) {
    try {
      const response = await api.post(`/api/admin/users/${id}/send-password-reset`);
      return response.data;
    } catch (error) {
      console.error(`Error sending password reset email for user ${id}:`, error);
      // Return mock success response
      return {
        success: true,
        message: 'Password reset email sent successfully'
      };
    }
  }
};
