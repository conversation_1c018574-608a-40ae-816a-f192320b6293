import{_ as g,g as _,i as y,c as a,a as s,k as b,b as m,w as h,d as c,r as C,t as l,F as k,p as x,n as D,f as N,o}from"./index-BKy0rL_2.js";import{c as I}from"./chats-CiAazAGa.js";const A={class:"chat-detail"},B={class:"level"},w={class:"level-left"},L={class:"level-item"},M={class:"breadcrumb"},S={key:0,class:"has-text-centered"},V={key:1,class:"notification is-danger"},F={key:2},T={class:"columns"},U={class:"column is-4"},z={class:"card"},E={class:"card-content"},R={class:"field"},$={class:"info-value"},j={class:"field"},q={class:"info-value"},G={class:"field"},H={class:"info-value"},J={class:"field"},K={class:"info-value"},O={class:"column is-8"},P={class:"card"},Q={class:"card-content"},W={key:0,class:"chat-messages"},X={class:"message-header"},Y={class:"message-time"},Z={class:"message-content"},ss={key:1,class:"has-text-centered py-4"},es={__name:"ChatDetail",setup(ts){const f=N(),t=_({}),r=_(!1),d=_(null),p=async()=>{r.value=!0,d.value=null;try{const i=await I.getChatById(f.params.id);t.value=i}catch(i){d.value=i.message||"Failed to load chat details"}finally{r.value=!1}},u=i=>new Date(i).toLocaleString();return y(()=>{p()}),(i,e)=>{const v=C("router-link");return o(),a("div",A,[s("div",B,[s("div",w,[s("div",L,[s("nav",M,[s("ul",null,[s("li",null,[m(v,{to:"/admin/chats"},{default:h(()=>e[1]||(e[1]=[c("Chats")])),_:1})]),e[2]||(e[2]=s("li",{class:"is-active"},[s("a",null,"Chat Details")],-1))])])])])]),r.value&&!t.value.id?(o(),a("div",S,e[3]||(e[3]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-3x"})],-1),s("p",{class:"mt-3"},"Loading chat details...",-1)]))):d.value?(o(),a("div",V,[s("button",{class:"delete",onClick:e[0]||(e[0]=n=>d.value=null)}),c(" "+l(d.value),1)])):t.value.id?(o(),a("div",F,[s("div",T,[s("div",U,[s("div",z,[e[8]||(e[8]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Chat Information")],-1)),s("div",E,[s("div",R,[e[4]||(e[4]=s("label",{class:"label"},"Buyer",-1)),s("p",$,[m(v,{to:{name:"AdminUserDetail",params:{id:t.value.buyerId}}},{default:h(()=>[c(l(t.value.buyerName),1)]),_:1},8,["to"])])]),s("div",j,[e[5]||(e[5]=s("label",{class:"label"},"Seller",-1)),s("p",q,[m(v,{to:{name:"AdminUserDetail",params:{id:t.value.sellerId}}},{default:h(()=>[c(l(t.value.sellerName),1)]),_:1},8,["to"])])]),s("div",G,[e[6]||(e[6]=s("label",{class:"label"},"Created At",-1)),s("p",H,l(u(t.value.createdAt)),1)]),s("div",J,[e[7]||(e[7]=s("label",{class:"label"},"Last Message",-1)),s("p",K,l(u(t.value.lastMessageAt)),1)])])])]),s("div",O,[s("div",P,[e[10]||(e[10]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Messages")],-1)),s("div",Q,[t.value.messages&&t.value.messages.length>0?(o(),a("div",W,[(o(!0),a(k,null,x(t.value.messages,n=>(o(),a("div",{key:n.id,class:D(["message-item",{"is-buyer":n.senderId===t.value.buyerId}])},[s("div",X,[s("strong",null,l(n.senderName),1),s("span",Y,l(u(n.createdAt)),1)]),s("div",Z,l(n.content),1)],2))),128))])):(o(),a("div",ss,e[9]||(e[9]=[s("p",{class:"has-text-grey"},"No messages in this chat",-1)])))])])])])])):b("",!0)])}}},os=g(es,[["__scopeId","data-v-6d0733bf"]]);export{os as default};
