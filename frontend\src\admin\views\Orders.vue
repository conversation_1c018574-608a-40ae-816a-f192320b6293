<template>
  <div class="admin-orders">
    <div class="admin-page-header">
      <div class="admin-header-content">
        <div class="admin-header-left">
          <h1 class="admin-page-title">
            <i class="fas fa-shopping-cart"></i>
            Orders
          </h1>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="admin-order-filters">
      <SearchAndFilters
        :filters="filters"
        :filter-fields="filterFields"
        search-label="Search Orders"
        search-placeholder="Search by order ID, customer name, or email..."
        search-column-class="is-4"
        :total-items="totalItems"
        item-name="orders"
        :loading="loading"
        @search-changed="handleSearchChange"
        @filter-changed="handleFilterChange"
        @reset-filters="handleResetFilters"
      />
    </div>

    <!-- Loading -->
    <div v-if="loading && isFirstLoad" class="admin-loading-state">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading orders...</p>
    </div>

    <!-- Error -->
    <div v-else-if="error" class="admin-alert admin-alert-danger">
      <div class="admin-alert-content">
        <p>{{ error }}</p>
        <button class="admin-btn admin-btn-secondary admin-btn-sm" @click="fetchData">
          <i class="fas fa-redo"></i>
          <span>Retry</span>
        </button>
      </div>
    </div>

    <!-- No Results -->
    <div v-else-if="!loading && items.length === 0" class="admin-empty-state">
      <div class="admin-empty-icon">
        <i class="fas fa-shopping-cart"></i>
      </div>
      <h3 class="admin-empty-title">No orders found</h3>
      <p class="admin-empty-text">There are no orders matching your search criteria.</p>
    </div>

    <!-- Orders Table -->
    <div v-else class="admin-order-table">
      <div class="admin-table-container" :class="{ 'admin-table-loading': loading && !isFirstLoad }">
        <order-table
          v-memo="[items, loading, currentPage, totalItems]"
          :orders="items"
          :loading="loading"
          @view="viewOrderDetails"
            @edit="editOrderDetails"
            @update-status="updateOrderStatus" />
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div class="pagination-wrapper" v-if="totalPages > 1">
      <pagination
        :current-page="currentPage"
        :total-pages="totalPages"
        @page-changed="handlePageChange" />
    </div>

    <!-- Order Details Modal -->
    <order-details-modal
      :is-open="isOrderDetailsModalOpen"
      :order="selectedOrder"
      @close="closeOrderDetailsModal"
      @update-status="updateOrderStatus" />
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import SearchAndFilters from '@/admin/components/common/SearchAndFilters.vue';
import OrderTable from '@/admin/components/orders/OrderTable.vue';
import OrderDetailsModal from '@/admin/components/orders/OrderDetailsModal.vue';
import Pagination from '@/admin/components/common/Pagination.vue';
import { ordersService } from '@/admin/services/orders';
import { useAdminSearch } from '@/composables/useAdminSearch';
import { eventBus, EVENTS, updateFlags } from '@/admin/utils/eventBus';

// Router
const router = useRouter();

// Filter configuration (matching backend enums exactly)
const filterFields = [
  {
    key: 'status',
    label: 'Order Status',
    type: 'select',
    columnClass: 'is-3',
    allOption: 'All Order Status',
    options: [
      { value: 0, label: 'Processing' },  // OrderStatus.Processing = 0
      { value: 1, label: 'Pending' },     // OrderStatus.Pending = 1
      { value: 2, label: 'Shipped' },     // OrderStatus.Shipped = 2
      { value: 3, label: 'Delivered' },   // OrderStatus.Delivered = 3
      { value: 4, label: 'Cancelled' }    // OrderStatus.Cancelled = 4
    ]
  },
  {
    key: 'paymentStatus',
    label: 'Payment Status',
    type: 'select',
    columnClass: 'is-3',
    allOption: 'All Payment Status',
    options: [
      { value: 0, label: 'Pending' },     // PaymentStatus.Pending = 0
      { value: 1, label: 'Completed' },   // PaymentStatus.Completed = 1
      { value: 2, label: 'Refunded' },    // PaymentStatus.Refunded = 2
      { value: 3, label: 'Failed' }       // PaymentStatus.Failed = 3
    ]
  },
  {
    key: 'sortBy',
    label: 'Sort By',
    type: 'select',
    columnClass: 'is-3',
    allOption: false,
    options: [
      { value: 'CreatedAt', label: 'Order Date' },
      { value: 'TotalPrice', label: 'Total Amount' },
      { value: 'CustomerName', label: 'Customer Name' }
    ]
  },
  {
    key: 'sortOrder',
    label: 'Order',
    type: 'select',
    columnClass: 'is-3',
    options: [
      { value: 'desc', label: 'Descending' },
      { value: 'asc', label: 'Ascending' }
    ]
  }
];

// Use the admin search composable
const {
  items,
  loading,
  error,
  isFirstLoad,
  currentPage,
  totalPages,
  totalItems,
  filters,
  fetchData,
  handlePageChange
} = useAdminSearch({
  fetchFunction: ordersService.getOrders,
  defaultFilters: {
    status: '',
    paymentStatus: '',
    sortBy: 'CreatedAt',
    sortOrder: 'desc'
  },
  debounceTime: 300,
  defaultPageSize: 15,
  clientSideSearch: false
});

// Modal state
const isOrderDetailsModalOpen = ref(false);
const selectedOrder = ref(null);

// Event handlers (matching Companies pattern)
const handleSearchChange = (searchValue) => {
  filters.search = searchValue;
};

const handleFilterChange = (filterKey, filterValue) => {
  console.log(`🔄 Filter changed: ${filterKey} = "${filterValue}"`);
  filters[filterKey] = filterValue;

  // Trigger data fetch when filter changes
  fetchData(1); // Reset to page 1 when filtering
};

const handleResetFilters = () => {
  Object.keys(filters).forEach(key => {
    if (key === 'search') {
      filters[key] = '';
    } else if (key === 'sortBy') {
      filters[key] = 'CreatedAt';
    } else if (key === 'sortOrder') {
      filters[key] = 'desc';
    } else {
      filters[key] = '';
    }
  });
};

// View order details
const viewOrderDetails = async (orderId) => {
  // Navigate to the new order view page
  router.push(`/admin/orders/${orderId}/view`);
};

// Edit order details
const editOrderDetails = async (orderId) => {
  // Navigate to the order edit page
  router.push(`/admin/orders/${orderId}/edit`);
};

// Close order details modal
const closeOrderDetailsModal = () => {
  isOrderDetailsModalOpen.value = false;
  selectedOrder.value = null;
};

// Update order status
const updateOrderStatus = async (orderId, newStatus) => {
  try {
    await ordersService.updateOrderStatus(orderId, newStatus);

    // If the order details modal is open, update the selected order status
    if (selectedOrder.value && selectedOrder.value.id === orderId) {
      selectedOrder.value.status = newStatus;
    }

    // Refresh the orders list
    fetchData(currentPage.value);
  } catch (error) {
    console.error('Error updating order status:', error);
  }
};

// Order status functions (matching backend enums)
const getOrderStatusText = (status) => {
  const statusMap = {
    // Numeric values (backend enum values)
    0: 'Processing',  // OrderStatus.Processing = 0
    1: 'Pending',     // OrderStatus.Pending = 1
    2: 'Shipped',     // OrderStatus.Shipped = 2
    3: 'Delivered',   // OrderStatus.Delivered = 3
    4: 'Cancelled',   // OrderStatus.Cancelled = 4
    // String values (for backward compatibility)
    'Processing': 'Processing',
    'Pending': 'Pending',
    'Shipped': 'Shipped',
    'Delivered': 'Delivered',
    'Cancelled': 'Cancelled'
  };
  return statusMap[status] || status || 'Unknown';
};

const getOrderStatusClass = (status) => {
  const classMap = {
    // Numeric values (backend enum values)
    0: 'is-info',     // Processing
    1: 'is-warning',  // Pending
    2: 'is-primary',  // Shipped
    3: 'is-success',  // Delivered
    4: 'is-danger',   // Cancelled
    // String values (for backward compatibility)
    'Processing': 'is-info',
    'Pending': 'is-warning',
    'Shipped': 'is-primary',
    'Delivered': 'is-success',
    'Cancelled': 'is-danger'
  };
  return classMap[status] || 'is-light';
};

// Payment status functions (matching backend enums)
const getPaymentStatusText = (status) => {
  const statusMap = {
    // Numeric values (backend enum values)
    0: 'Pending',     // PaymentStatus.Pending = 0
    1: 'Completed',   // PaymentStatus.Completed = 1
    2: 'Refunded',    // PaymentStatus.Refunded = 2
    3: 'Failed',      // PaymentStatus.Failed = 3
    // String values (for backward compatibility)
    'Pending': 'Pending',
    'Completed': 'Completed',
    'Refunded': 'Refunded',
    'Failed': 'Failed'
  };
  return statusMap[status] || status || 'Unknown';
};

const getPaymentStatusClass = (status) => {
  const classMap = {
    // Numeric values (backend enum values)
    0: 'is-warning',  // Pending
    1: 'is-success',  // Completed
    2: 'is-info',     // Refunded
    3: 'is-danger',   // Failed
    // String values (for backward compatibility)
    'Pending': 'is-warning',
    'Completed': 'is-success',
    'Refunded': 'is-info',
    'Failed': 'is-danger'
  };
  return classMap[status] || 'is-light';
};

// Event bus listeners
let unsubscribeOrderUpdated;
let unsubscribeOrderStatusChanged;

// Lifecycle hooks
onMounted(() => {
  // Subscribe to order update events
  unsubscribeOrderUpdated = eventBus.on(EVENTS.ORDER_UPDATED, () => {
    console.log('Order updated event received, refreshing orders list');
    fetchData(currentPage.value);
  });

  unsubscribeOrderStatusChanged = eventBus.on(EVENTS.ORDER_STATUS_CHANGED, () => {
    console.log('Order status changed event received, refreshing orders list');
    fetchData(currentPage.value);
  });

  // Check if orders need to be refreshed due to persistent flag
  if (updateFlags.shouldRefreshOrders()) {
    console.log('Orders marked for refresh, refreshing data');
    fetchData(1); // Reset to first page
    updateFlags.clearOrdersRefreshFlag();
  }
});

onActivated(() => {
  // This hook is called when the component is activated (when navigating back to this page)
  console.log('Orders page activated, checking for updates');

  // Check if orders need to be refreshed
  if (updateFlags.shouldRefreshOrders()) {
    console.log('Orders marked for refresh on activation, refreshing data');
    fetchData(currentPage.value);
    updateFlags.clearOrdersRefreshFlag();
  }
});

onUnmounted(() => {
  // Clean up event listeners
  if (unsubscribeOrderUpdated) {
    unsubscribeOrderUpdated();
  }
  if (unsubscribeOrderStatusChanged) {
    unsubscribeOrderStatusChanged();
  }
});
</script>

<style scoped>
.admin-orders {
  padding: 1rem;
}

.pagination-wrapper {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}
</style>
