<template>
  <nav class="pagination is-centered" role="navigation" aria-label="pagination">
    <a
      class="pagination-previous"
      :disabled="currentPage <= 1"
      @click="changePage(currentPage - 1)">
      <span class="icon">
        <i class="fas fa-chevron-left"></i>
      </span>
      <span>Previous</span>
    </a>
    <a
      class="pagination-next"
      :disabled="currentPage >= totalPages"
      @click="changePage(currentPage + 1)">
      <span>Next</span>
      <span class="icon">
        <i class="fas fa-chevron-right"></i>
      </span>
    </a>
    <ul class="pagination-list">
      <!-- First page -->
      <li v-if="showFirstPage">
        <a
          class="pagination-link"
          :class="{ 'is-current': currentPage === 1 }"
          @click="changePage(1)">
          1
        </a>
      </li>

      <!-- Ellipsis if needed -->
      <li v-if="showLeftEllipsis">
        <span class="pagination-ellipsis">&hellip;</span>
      </li>

      <!-- Pages around current page -->
      <li v-for="page in visiblePages" :key="page">
        <a
          class="pagination-link"
          :class="{ 'is-current': currentPage === page }"
          @click="changePage(page)">
          {{ page }}
        </a>
      </li>

      <!-- Ellipsis if needed -->
      <li v-if="showRightEllipsis">
        <span class="pagination-ellipsis">&hellip;</span>
      </li>

      <!-- Last page -->
      <li v-if="showLastPage">
        <a
          class="pagination-link"
          :class="{ 'is-current': currentPage === totalPages }"
          @click="changePage(totalPages)">
          {{ totalPages }}
        </a>
      </li>
    </ul>
  </nav>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  currentPage: {
    type: Number,
    required: true
  },
  totalPages: {
    type: Number,
    required: true
  },
  maxVisiblePages: {
    type: Number,
    default: 5
  }
});

const emit = defineEmits(['page-changed']);

// Calculate visible pages
const visiblePages = computed(() => {
  if (props.totalPages <= props.maxVisiblePages) {
    // If total pages is less than max visible, show all pages
    return Array.from({ length: props.totalPages }, (_, i) => i + 1);
  }

  // Calculate start and end of visible pages
  let start = Math.max(props.currentPage - Math.floor(props.maxVisiblePages / 2), 1);
  let end = start + props.maxVisiblePages - 1;

  // Adjust if end exceeds total pages
  if (end > props.totalPages) {
    end = props.totalPages;
    start = Math.max(end - props.maxVisiblePages + 1, 1);
  }

  return Array.from({ length: end - start + 1 }, (_, i) => start + i);
});

// Determine if we need to show first page and left ellipsis
const showFirstPage = computed(() => {
  return props.totalPages > props.maxVisiblePages && visiblePages.value[0] > 1;
});

const showLeftEllipsis = computed(() => {
  return showFirstPage.value && visiblePages.value[0] > 2;
});

// Determine if we need to show last page and right ellipsis
const showLastPage = computed(() => {
  return props.totalPages > props.maxVisiblePages &&
         visiblePages.value[visiblePages.value.length - 1] < props.totalPages;
});

const showRightEllipsis = computed(() => {
  return showLastPage.value &&
         visiblePages.value[visiblePages.value.length - 1] < props.totalPages - 1;
});

// Change page handler
const changePage = (page) => {
  if (page >= 1 && page <= props.totalPages && page !== props.currentPage) {
    emit('page-changed', page);
  }
};
</script>

<style scoped>
.pagination {
  margin-top: var(--admin-space-xl);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--admin-space-xs);
}

.pagination-link,
.pagination-previous,
.pagination-next {
  padding: var(--admin-space-sm) var(--admin-space-md);
  border: 1px solid var(--admin-border-color);
  background: var(--admin-white);
  color: var(--admin-gray-600);
  border-radius: var(--admin-radius-md);
  cursor: pointer;
  transition: all var(--admin-transition-fast);
  font-size: var(--admin-text-sm);
  min-width: 40px;
  text-align: center;
}

.pagination-link.is-current {
  background-color: var(--admin-primary);
  border-color: var(--admin-primary);
  color: white;
  font-weight: var(--admin-font-semibold);
}

.pagination-link:hover:not(.is-current),
.pagination-previous:hover:not([disabled]),
.pagination-next:hover:not([disabled]) {
  background-color: var(--admin-gray-50);
  border-color: var(--admin-gray-300);
  color: var(--admin-gray-700);
}

.pagination-previous[disabled],
.pagination-next[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--admin-gray-100);
}

.pagination-ellipsis {
  color: var(--admin-gray-400);
  padding: var(--admin-space-sm);
}
</style>
