<template>
  <div class="app-container">
    <!-- Global Loading Component -->

    <global-loading :is-loading="isLoading" :message="loadingMessage" />

    <header class="header" v-if="showHeader">
      <div class="header-content">

        <div class="logo-container">
          <router-link to="/">
            <img src="@/assets/images/logo.svg" alt="Klondike" class="logo" />
          </router-link>
        </div>
        
        <div class="header-left">
            <category-menu-button/>
        </div>

        <div class="search-container">
          <input type="text" class="search-input" placeholder="Пошук" />
          <button class="search-btn">
            <i class="fas fa-search"></i>
          </button>
        </div>

        <div class="header-actions">
          <router-link to="/wishlist" class="header-action-btn">
            <i class="far fa-heart"></i>
          </router-link>

          <router-link to="/user/profile" class="header-action-btn" v-if="isLoggedIn">
            <i class="far fa-user"></i>
          </router-link>

          <router-link to="/login" class="header-action-btn" v-else>
            <i class="far fa-user"></i>
          </router-link>

          <router-link to="/cart" class="header-action-btn cart-btn">
            <i class="fas fa-shopping-cart"></i>
          </router-link>
        </div>
      </div>
    </header>

    <error-boundary>
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" :key="$route.fullPath" />
        </transition>
      </router-view>
    </error-boundary>

    <footer class="footer" v-if="showFooter">
      <div class="footer-content">
        <div class="footer-logo">
          <img src="@/assets/images/logo.svg" alt="Klondike" class="logo" />
        </div>

        <div class="footer-section">
          <h3 class="footer-title">Інформація про компанію</h3>
          <ul class="footer-links">
            <li><a href="#">Про нас</a></li>
            <li><a href="#">Контакти</a></li>
            <li><a href="#">Магазини</a></li>
            <li><a href="#">Вакансії</a></li>
          </ul>
        </div>

        <div class="footer-section">
          <h3 class="footer-title">Допомога покупцеві</h3>
          <ul class="footer-links">
            <li><a href="#">Центр допомоги клієнтам</a></li>
            <li><a href="#">Доставка та оплата</a></li>
            <li><a href="#">Обмін і повернення товару</a></li>
            <li><a href="#">Гарантії</a></li>
            <li><a href="#">Сервісні центри</a></li>
          </ul>
        </div>

        <div class="footer-section">
          <h3 class="footer-title">Номер телефону</h3>
          <ul class="footer-links">
            <li><a href="#">Пошта</a></li>
          </ul>
          <div class="social-links">
            <a href="#" class="social-link"><i class="fab fa-telegram"></i></a>
            <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
            <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
            <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
          </div>
        </div>
      </div>

      <div class="copyright">
        Всі права захищені
      </div>
    </footer>
  </div>
</template>

<script>
import { computed, ref } from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
import GlobalLoading from '@/components/GlobalLoading.vue';
import ErrorBoundary from '@/components/ErrorBoundary.vue';
import CategoryMenuButton from '@/components/common/CategoryMenuButton.vue';

export default {
  name: 'App',
  components: {
    GlobalLoading,
    ErrorBoundary,
    CategoryMenuButton
  },
  setup() {
    const store = useStore();
    const router = useRouter();
    const route = useRoute();

    const isLoggedIn = computed(() => store.getters['auth/isLoggedIn']);
    const currentUser = computed(() => store.getters['auth/user']);

    // Global loading state
    const isLoading = computed(() => store.getters['loading/isLoading']);
    const loadingMessage = computed(() => store.getters['loading/loadingMessage']);

    // Determine if header and footer should be shown based on the route
    const showHeader = computed(() => {
      // Hide header on login and register pages
      return !['Login', 'Register'].includes(route.name);
    });

    const showFooter = computed(() => {
      // Hide footer on login and register pages
      return !['Login', 'Register'].includes(route.name);
    });
    const dashboardLink = computed(() => {
      if (!currentUser.value) return '/';

      // Get the role from the user object
      const role = currentUser.value.role;

      // Check if admin using direct role check
      let isAdminByRole = false;

      if (typeof role === 'string') {
        isAdminByRole = role === 'Admin';
      } else if (typeof role === 'number') {
        // Buyer = 0, Seller = 1, SellerOwner = 2, Moderator = 3, Admin = 4
        isAdminByRole = role === 4;
      } else if (role && typeof role === 'object') {
        if (role.hasOwnProperty('value')) {
          isAdminByRole = role.value === 'Admin' || role.value === 4;
        }
        if (role.hasOwnProperty('name')) {
          isAdminByRole = role.name === 'Admin';
        }
      }

      const isAdminByGetter = store.getters['auth/isAdmin'];
      const isAdmin = isAdminByRole || isAdminByGetter;

      console.log('App.vue - User role:', currentUser.value.role);
      console.log('App.vue - Is admin by role?', isAdminByRole);
      console.log('App.vue - Is admin by getter?', isAdminByGetter);

      return isAdmin ? '/admin/dashboard' : '/dashboard';
    });

    const logout = async () => {
      await store.dispatch('auth/logout');
      router.push('/login');
    };

    return {
      isLoggedIn,
      currentUser,
      dashboardLink,
      logout,
      showHeader,
      showFooter,
      isLoading,
      loadingMessage
    };
  }
};
</script>

<style>
@import './assets/css/main-menu.css';
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: Arial, sans-serif;
}

.header {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 15px 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.header-left {
  margin-right: 20px;
}

.catalog-btn {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 15px;
  display: flex;
  align-items: center;
  font-size: 14px;
  cursor: pointer;
}

.catalog-btn i {
  margin-right: 8px;
}

.logo-container {
  margin-right: 20px;
}

.logo {
  height: 40px;
}

.search-container {
  flex: 1;
  display: flex;
  margin: 0 20px;
}

.search-input {
  flex: 1;
  border: 1px solid #ddd;
  border-right: none;
  border-radius: 4px 0 0 4px;
  padding: 8px 15px;
  font-size: 14px;
}

.search-btn {
  background-color: #ff7700;
  border: none;
  border-radius: 0 4px 4px 0;
  color: white;
  padding: 0 20px;
  cursor: pointer;
}

.header-actions {
  display: flex;
  align-items: center;
}

.header-action-btn {
  color: #333;
  font-size: 20px;
  margin-left: 20px;
  text-decoration: none;
  position: relative;
}

.cart-btn {
  color: #333;
}

.footer {
  background-color: #f8f9fa;
  padding: 40px 0 20px;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  padding: 0 20px;
}

.footer-logo {
  width: 100%;
  margin-bottom: 30px;
}

.footer-section {
  flex: 1;
  min-width: 200px;
  margin-bottom: 20px;
}

.footer-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: #666;
  text-decoration: none;
  font-size: 14px;
}

.social-links {
  display: flex;
  margin-top: 15px;
}

.social-link {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: #666;
  text-decoration: none;
}

.copyright {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ddd;
  margin-top: 20px;
  color: #999;
  font-size: 12px;
  max-width: 1200px;
  margin: 20px auto 0;
  padding: 20px;
}

/* Route transition animations */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

@media (max-width: 768px) {
  .header-content {
    flex-wrap: wrap;
  }

  .logo-container {
    order: 1;
    margin: 0 auto 15px;
  }

  .header-left {
    order: 2;
    margin-right: 0;
  }

  .search-container {
    order: 3;
    width: 100%;
    margin: 15px 0;
  }

  .header-actions {
    order: 4;
    margin-left: auto;
  }

  .footer-section {
    width: 50%;
    flex: none;
  }
}
</style>
