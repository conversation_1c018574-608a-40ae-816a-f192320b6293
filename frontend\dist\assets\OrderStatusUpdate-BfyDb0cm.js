import{_ as A,h as P,c as i,o,a as t,d as x,k as p,F as C,p as I,t as c,z as N,C as V,g as k,x as T,n as b,D as m,A as U}from"./index-BKy0rL_2.js";import{g as w,a as E,O as M,P as R,b as B,c as Q,e as q,E as D,u as F}from"./orders-HBUhG7wP.js";const z={class:"order-items-table"},H={class:"card"},L={class:"card-content"},j={class:"table-container"},Y={class:"table is-fullwidth is-striped is-hoverable"},G={key:0,class:"has-text-centered"},J={class:"media"},K={key:0,class:"media-left"},W={class:"image is-48x48"},X=["src","alt"],Z={key:1,class:"media-left"},tt={class:"media-content"},st={class:"title is-6"},et={key:0,class:"subtitle is-7 has-text-grey"},at={class:"has-text-right"},lt={class:"tag is-light"},nt={class:"has-text-centered"},it={key:0,class:"field has-addons has-addons-centered"},ot={class:"control"},dt=["onClick","disabled"],rt={class:"control"},ct=["onUpdate:modelValue","onChange"],ut={class:"control"},mt=["onClick"],vt={key:1,class:"tag is-primary"},ht={class:"has-text-right"},pt={class:"has-text-primary"},yt={key:0,class:"has-text-centered"},gt=["onClick"],_t={key:0},bt=["colspan"],ft={key:0,class:"has-text-centered mt-4"},St={class:"box mt-4 has-background-light"},$t={class:"columns"},Ot={class:"column is-4 has-text-right"},kt={class:"field is-grouped is-grouped-multiline is-justify-content-flex-end"},Pt={class:"control"},xt={class:"tags has-addons"},Ct={class:"tag is-light"},It={key:0,class:"control"},Nt={class:"tags has-addons"},Tt={class:"tag is-light"},Ut={class:"control"},wt={class:"tags has-addons"},Et={class:"tag is-primary is-light"},qt={__name:"OrderItemsTable",props:{items:{type:Array,default:()=>[]},editable:{type:Boolean,default:!1},tax:{type:Number,default:0},shipping:{type:Number,default:0},discount:{type:Number,default:0}},emits:["update-quantity","remove-item","add-item"],setup(a,{emit:f}){const l=a,y=f,h=P(()=>l.items.reduce((n,s)=>n+(s.price||s.unitPrice||0)*(s.quantity||0),0)),u=P(()=>h.value+l.shipping-l.discount),r=n=>!n&&n!==0?"$0.00":new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(n),S=n=>{n.target.style.display="none",n.target.parentElement.innerHTML='<div class="placeholder-image"><i class="fas fa-image"></i></div>'},$=n=>{const s=l.items[n].quantity+1;g(n,s)},O=n=>{const s=Math.max(1,l.items[n].quantity-1);g(n,s)},g=(n,s)=>{s<=0?v(n):y("update-quantity",n,s)},v=n=>{y("remove-item",n)};return(n,s)=>(o(),i("div",z,[t("div",H,[s[15]||(s[15]=t("div",{class:"card-header"},[t("p",{class:"card-header-title"},[t("span",{class:"icon"},[t("i",{class:"fas fa-shopping-cart"})]),x(" Order Items ")])],-1)),t("div",L,[t("div",j,[t("table",Y,[t("thead",null,[t("tr",null,[s[1]||(s[1]=t("th",null,"Product",-1)),s[2]||(s[2]=t("th",{class:"has-text-right"},"Price per Unit",-1)),s[3]||(s[3]=t("th",{class:"has-text-centered"},"Quantity",-1)),s[4]||(s[4]=t("th",{class:"has-text-right"},"Total Price",-1)),a.editable?(o(),i("th",G,"Actions")):p("",!0)])]),t("tbody",null,[(o(!0),i(C,null,I(a.items,(e,d)=>(o(),i("tr",{key:e.id||d},[t("td",null,[t("div",J,[e.productImage?(o(),i("div",K,[t("figure",W,[t("img",{src:e.productImage,alt:e.productName,class:"is-rounded",onError:S},null,40,X)])])):(o(),i("div",Z,s[5]||(s[5]=[t("figure",{class:"image is-48x48"},[t("div",{class:"placeholder-image"},[t("i",{class:"fas fa-image"})])],-1)]))),t("div",tt,[t("p",st,c(e.productName||"Unknown Product"),1),e.productId?(o(),i("p",et," ID: "+c(e.productId),1)):p("",!0)])])]),t("td",at,[t("span",lt,c(r(e.price||e.unitPrice)),1)]),t("td",nt,[a.editable?(o(),i("div",it,[t("div",ot,[t("button",{class:"button is-small",onClick:_=>O(d),disabled:e.quantity<=1},s[6]||(s[6]=[t("span",{class:"icon is-small"},[t("i",{class:"fas fa-minus"})],-1)]),8,dt)]),t("div",rt,[N(t("input",{class:"input is-small has-text-centered",type:"number","onUpdate:modelValue":_=>e.quantity=_,onChange:_=>g(d,e.quantity),min:"0",style:{width:"60px"}},null,40,ct),[[V,e.quantity,void 0,{number:!0}]])]),t("div",ut,[t("button",{class:"button is-small",onClick:_=>$(d)},s[7]||(s[7]=[t("span",{class:"icon is-small"},[t("i",{class:"fas fa-plus"})],-1)]),8,mt)])])):(o(),i("span",vt,c(e.quantity),1))]),t("td",ht,[t("strong",pt,c(r(e.total||e.price*e.quantity)),1)]),a.editable?(o(),i("td",yt,[t("button",{class:"button is-small is-danger is-outlined",onClick:_=>v(d),title:"Remove item"},s[8]||(s[8]=[t("span",{class:"icon is-small"},[t("i",{class:"fas fa-trash"})],-1)]),8,gt)])):p("",!0)]))),128)),!a.items||a.items.length===0?(o(),i("tr",_t,[t("td",{colspan:a.editable?5:4,class:"has-text-centered has-text-grey"},s[9]||(s[9]=[t("div",{class:"py-4"},[t("span",{class:"icon is-large"},[t("i",{class:"fas fa-shopping-cart fa-2x"})]),t("p",{class:"mt-2"},"No items in this order")],-1)]),8,bt)])):p("",!0)])])]),a.editable?(o(),i("div",ft,[t("button",{class:"button is-primary is-outlined",onClick:s[0]||(s[0]=e=>n.$emit("add-item"))},s[10]||(s[10]=[t("span",{class:"icon"},[t("i",{class:"fas fa-plus"})],-1),t("span",null,"Add Item",-1)]))])):p("",!0),t("div",St,[t("div",$t,[s[14]||(s[14]=t("div",{class:"column is-8"},[t("p",{class:"title is-6"},"Order Summary")],-1)),t("div",Ot,[t("div",kt,[t("div",Pt,[t("div",xt,[s[11]||(s[11]=t("span",{class:"tag is-dark"},"Subtotal",-1)),t("span",Ct,c(r(h.value)),1)])]),a.shipping>0?(o(),i("div",It,[t("div",Nt,[s[12]||(s[12]=t("span",{class:"tag is-dark"},"Shipping",-1)),t("span",Tt,c(r(a.shipping)),1)])])):p("",!0),t("div",Ut,[t("div",wt,[s[13]||(s[13]=t("span",{class:"tag is-primary"},"Total",-1)),t("span",Et,c(r(u.value)),1)])])])])])])])])]))}},gs=A(qt,[["__scopeId","data-v-cacf4cc5"]]),Dt={class:"order-status-update"},At={class:"modal-card"},Vt={class:"modal-card-body"},Mt={class:"field"},Rt={class:"control"},Bt=["value"],Qt={class:"field"},Ft={class:"control"},zt=["value"],Ht={class:"field"},Lt={class:"control"},jt={class:"field"},Yt={class:"control"},Gt={class:"select is-fullwidth"},Jt=["disabled"],Kt=["value"],Wt={class:"field"},Xt={class:"control"},Zt={class:"select is-fullwidth"},ts=["disabled"],ss=["value"],es={class:"modal-card-foot"},as=["disabled"],ls=["disabled"],ns={class:"card"},is={class:"card-content"},os={class:"field"},ds={class:"control"},rs={class:"field"},cs={class:"control"},us={class:"field"},ms={class:"control"},vs=["disabled"],hs={__name:"OrderStatusUpdate",props:{orderId:{type:[String,Number],required:!0},currentOrderStatus:{type:[String,Number],required:!0},currentPaymentStatus:{type:[String,Number],required:!0},customerName:{type:String,default:""},statusHistory:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["update-order-status","update-payment-status"],setup(a,{emit:f}){const l=a,y=f,h=k(!1),u=k(l.currentOrderStatus),r=k(l.currentPaymentStatus),S=M,$=R,O=P(()=>u.value!==l.currentOrderStatus||r.value!==l.currentPaymentStatus),g=()=>{h.value=!0,u.value=l.currentOrderStatus,r.value=l.currentPaymentStatus},v=()=>{h.value=!1,u.value=l.currentOrderStatus,r.value=l.currentPaymentStatus},n=async()=>{try{u.value!==l.currentOrderStatus&&(y("update-order-status",l.orderId,u.value),q.emit(D.ORDER_UPDATED,{orderId:l.orderId,type:"order_status",newValue:u.value})),r.value!==l.currentPaymentStatus&&(y("update-payment-status",l.orderId,r.value),q.emit(D.ORDER_UPDATED,{orderId:l.orderId,type:"payment_status",newValue:r.value})),(u.value!==l.currentOrderStatus||r.value!==l.currentPaymentStatus)&&F.markOrdersForRefresh(),v()}catch(s){console.error("Error saving status changes:",s)}};return T(()=>l.currentOrderStatus,s=>{u.value=s}),T(()=>l.currentPaymentStatus,s=>{r.value=s}),(s,e)=>(o(),i("div",Dt,[t("div",{class:b(["modal",{"is-active":h.value}])},[t("div",{class:"modal-background",onClick:v}),t("div",At,[t("header",{class:"modal-card-head"},[e[2]||(e[2]=t("p",{class:"modal-card-title"},[t("span",{class:"icon"},[t("i",{class:"fas fa-edit"})]),x(" Update Order Status ")],-1)),t("button",{class:"delete","aria-label":"close",onClick:v})]),t("section",Vt,[t("div",Mt,[e[3]||(e[3]=t("label",{class:"label"},"Order ID",-1)),t("div",Rt,[t("input",{class:"input",type:"text",value:a.orderId,readonly:""},null,8,Bt)])]),t("div",Qt,[e[4]||(e[4]=t("label",{class:"label"},"Customer",-1)),t("div",Ft,[t("input",{class:"input",type:"text",value:a.customerName||"N/A",readonly:""},null,8,zt)])]),t("div",Ht,[e[5]||(e[5]=t("label",{class:"label"},"Current Status",-1)),t("div",Lt,[t("span",{class:b(["tag is-medium",m(w)(a.currentOrderStatus)])},c(m(E)(a.currentOrderStatus)),3)])]),t("div",jt,[e[6]||(e[6]=t("label",{class:"label"},"New Status",-1)),t("div",Yt,[t("div",Gt,[N(t("select",{"onUpdate:modelValue":e[0]||(e[0]=d=>u.value=d),disabled:a.loading},[(o(!0),i(C,null,I(m(S),d=>(o(),i("option",{key:d.value,value:d.value},c(d.label),9,Kt))),128))],8,Jt),[[U,u.value]])])])]),t("div",Wt,[e[7]||(e[7]=t("label",{class:"label"},"Payment Status",-1)),t("div",Xt,[t("div",Zt,[N(t("select",{"onUpdate:modelValue":e[1]||(e[1]=d=>r.value=d),disabled:a.loading},[(o(!0),i(C,null,I(m($),d=>(o(),i("option",{key:d.value,value:d.value},c(d.label),9,ss))),128))],8,ts),[[U,r.value]])])])])]),t("footer",es,[t("button",{class:b(["button is-success",{"is-loading":a.loading}]),onClick:n,disabled:a.loading||!O.value}," Update Status ",10,as),t("button",{class:"button",onClick:v,disabled:a.loading}," Cancel ",8,ls)])])],2),t("div",ns,[e[11]||(e[11]=t("div",{class:"card-header"},[t("p",{class:"card-header-title"},[t("span",{class:"icon"},[t("i",{class:"fas fa-cog"})]),x(" Order Status ")])],-1)),t("div",is,[t("div",os,[e[8]||(e[8]=t("label",{class:"label"},"Current Status",-1)),t("div",ds,[t("span",{class:b(["tag is-medium",m(w)(a.currentOrderStatus)])},c(m(E)(a.currentOrderStatus)),3)])]),t("div",rs,[e[9]||(e[9]=t("label",{class:"label"},"Payment Status",-1)),t("div",cs,[t("span",{class:b(["tag is-medium",m(B)(a.currentPaymentStatus)])},c(m(Q)(a.currentPaymentStatus)),3)])]),t("div",us,[t("div",ms,[t("button",{class:"button is-primary is-fullwidth",onClick:g,disabled:a.loading},e[10]||(e[10]=[t("span",{class:"icon"},[t("i",{class:"fas fa-edit"})],-1),t("span",null,"Update Status",-1)]),8,vs)])])])])]))}},_s=A(hs,[["__scopeId","data-v-84fc61b6"]]);export{gs as O,_s as a};
