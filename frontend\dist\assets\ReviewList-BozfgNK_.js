import{_ as $,g as _,h as w,c as i,a as e,b as S,s as j,k as R,t as v,D as a,n as A,F as D,p as F,z as q,M as G,w as H,r as J,o}from"./index-BKy0rL_2.js";import{r as p}from"./reviews-DqCCHDB1.js";import{u as K,S as Q}from"./useAdminSearch-CW-fG_RO.js";import{P as W}from"./Pagination-Nji0QHvJ.js";const X={class:"review-list"},Y={class:"level"},Z={class:"level-right"},ee={class:"level-item"},te={class:"buttons"},le=["disabled"],se={key:0,class:"has-text-centered py-6"},ae={key:1,class:"notification is-danger"},ne={key:2,class:"card"},oe={class:"card-content"},ie={class:"table is-fullwidth is-hoverable"},re={class:"checkbox"},de=["checked","indeterminate"],ue={class:"checkbox"},ce=["value"],ve={class:"stars"},ge={class:"has-text-grey"},me={class:"buttons"},he=["onClick","disabled"],fe={__name:"ReviewList",setup(pe){const b=[{key:"rating",label:"Rating",type:"select",columnClass:"is-3",options:[{value:"",label:"All Ratings"},{value:"1",label:"1 Star"},{value:"2",label:"2 Stars"},{value:"3",label:"3 Stars"},{value:"4",label:"4 Stars"},{value:"5",label:"5 Stars"}]},{key:"sortBy",label:"Sort By",type:"select",columnClass:"is-3",options:[{value:"CreatedAt",label:"Date Created"},{value:"UpdatedAt",label:"Date Updated"},{value:"Comment",label:"Comment"}]},{key:"sortOrder",label:"Order",type:"select",columnClass:"is-3",options:[{value:"desc",label:"Descending"},{value:"asc",label:"Ascending"}]}],{items:g,loading:f,error:h,isFirstLoad:y,currentPage:x,totalPages:k,totalItems:P,filters:d,fetchData:m,handlePageChange:B}=K({fetchFunction:p.getReviews,defaultFilters:{rating:"",sortBy:"CreatedAt",sortOrder:"desc"},debounceTime:300,defaultPageSize:15,clientSideSearch:!1}),u=_(!1),n=_([]),C=w(()=>g.value.length>0&&n.value.length===g.value.length),L=w(()=>n.value.length>0&&n.value.length<g.value.length),U=l=>{d.search=l},N=(l,t)=>{d[l]=t},V=()=>{Object.keys(d).forEach(l=>{var t,c,s;l==="search"?d[l]="":d[l]=((s=(c=(t=b.find(r=>r.key===l))==null?void 0:t.options)==null?void 0:c[0])==null?void 0:s.value)||""}),m(1)},M=()=>{C.value?n.value=[]:n.value=g.value.map(l=>l.id)},O=async l=>{if(confirm("Are you sure you want to delete this review?")){u.value=!0;try{await p.deleteReview(l),await m()}catch(t){h.value=t.message||"Failed to delete review"}finally{u.value=!1}}},z=async()=>{if(confirm(`Are you sure you want to delete ${n.value.length} selected reviews?`)){u.value=!0;try{await p.bulkDeleteReviews(n.value),await m()}catch(l){h.value=l.message||"Failed to delete reviews"}finally{u.value=!1}}},T=l=>l?new Date(l).toLocaleDateString():"N/A",E=(l,t)=>l?l.length>t?l.substring(0,t)+"...":l:"",I=l=>{if(!l.rating)return 0;const{service:t=0,deliveryTime:c=0,accuracy:s=0}=l.rating;return Math.round((t+c+s)/3)};return(l,t)=>{const c=J("router-link");return o(),i("div",X,[e("div",Y,[t[3]||(t[3]=e("div",{class:"level-left"},[e("div",{class:"level-item"},[e("h1",{class:"title"},"Reviews Management")])],-1)),e("div",Z,[e("div",ee,[e("div",te,[n.value.length>0?(o(),i("button",{key:0,class:"button is-danger",onClick:z,disabled:n.value.length===0||u.value},[t[2]||(t[2]=e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1)),e("span",null,"Delete Selected ("+v(n.value.length)+")",1)],8,le)):R("",!0)])])])]),S(Q,{filters:a(d),"filter-fields":b,"search-label":"Search Reviews","search-placeholder":"Search by comment, product name, or user name...","search-column-class":"is-4","total-items":a(P),"item-name":"reviews",loading:a(f),onSearchChanged:U,onFilterChanged:N,onResetFilters:V},null,8,["filters","total-items","loading"]),a(f)&&a(y)?(o(),i("div",se,t[4]||(t[4]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading reviews...",-1)]))):a(h)?(o(),i("div",ae,[e("p",null,v(a(h)),1),e("button",{class:"button is-light mt-2",onClick:t[0]||(t[0]=(...s)=>a(m)&&a(m)(...s))},t[5]||(t[5]=[e("span",{class:"icon"},[e("i",{class:"fas fa-redo"})],-1),e("span",null,"Retry",-1)]))])):(o(),i("div",ne,[e("div",oe,[e("div",{class:A(["table-container",{"is-loading":a(f)&&!a(y)}])},[e("table",ie,[e("thead",null,[e("tr",null,[e("th",null,[e("label",re,[e("input",{type:"checkbox",onChange:M,checked:C.value,indeterminate:L.value},null,40,de)])]),t[6]||(t[6]=e("th",null,"Product",-1)),t[7]||(t[7]=e("th",null,"User",-1)),t[8]||(t[8]=e("th",null,"Rating",-1)),t[9]||(t[9]=e("th",null,"Comment",-1)),t[10]||(t[10]=e("th",null,"Created",-1)),t[11]||(t[11]=e("th",null,"Actions",-1))])]),e("tbody",null,[(o(!0),i(D,null,F(a(g),s=>(o(),i("tr",{key:s.id},[e("td",null,[e("label",ue,[q(e("input",{type:"checkbox",value:s.id,"onUpdate:modelValue":t[1]||(t[1]=r=>n.value=r)},null,8,ce),[[G,n.value]])])]),e("td",null,[e("strong",null,v(s.productName||"Unknown Product"),1)]),e("td",null,v(s.userName||"Unknown User"),1),e("td",null,[e("div",ve,[(o(),i(D,null,F(5,r=>e("span",{key:r,class:A(["star",{"is-filled":r<=I(s)}])}," ★ ",2)),64))])]),e("td",null,[e("span",ge,v(E(s.comment,50)),1)]),e("td",null,v(T(s.createdAt)),1),e("td",null,[e("div",me,[S(c,{to:{name:"AdminReviewDetail",params:{id:s.id}},class:"button is-small is-info"},{default:H(()=>t[12]||(t[12]=[e("span",{class:"icon"},[e("i",{class:"fas fa-eye"})],-1),e("span",null,"View",-1)])),_:2},1032,["to"]),e("button",{class:"button is-small is-danger",onClick:r=>O(s.id),disabled:u.value},t[13]||(t[13]=[e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1),e("span",null,"Delete",-1)]),8,he)])])]))),128))])])],2)])])),a(k)>1?(o(),j(W,{key:3,"current-page":a(x),"total-pages":a(k),onPageChanged:a(B)},null,8,["current-page","total-pages","onPageChanged"])):R("",!0)])}}},_e=$(fe,[["__scopeId","data-v-18351437"]]);export{_e as default};
