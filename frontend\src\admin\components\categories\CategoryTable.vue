<template>
  <div class="category-table">
    <div class="table-container">
      <table class="table is-fullwidth is-striped">
        <thead>
          <tr>
            <th width="60">Image</th>
            <th>Category</th>
            <th>Hierarchy</th>
            <th>Products</th>
            <th>SEO</th>
            <th width="120">Actions</th>
          </tr>
        </thead>
        <tbody v-if="!loading && categories.length > 0">
          <tr v-for="category in categories" :key="category.id" class="category-row">
            <!-- Image -->
            <td>
              <figure v-if="category.image" class="image is-48x48">
                <img :src="category.image" :alt="category.name" class="is-rounded">
              </figure>
              <span v-else class="icon is-large has-text-grey-light">
                <i class="fas fa-folder fa-2x"></i>
              </span>
            </td>

            <!-- Category Info -->
            <td>
              <div class="content">
                <div>
                  <strong class="title is-6">{{ category.name }}</strong>
                  <br>
                  <small class="has-text-grey">{{ category.slug }}</small>
                  <br>
                  <p v-if="category.description" class="is-size-7 has-text-grey mt-1">
                    {{ truncateText(category.description, 80) }}
                  </p>
                </div>
              </div>
            </td>

            <!-- Hierarchy -->
            <td>
              <div class="tags">
                <span v-if="!category.parentId" class="tag is-primary is-small">
                  <span class="icon is-small">
                    <i class="fas fa-layer-group"></i>
                  </span>
                  <span>Root</span>
                </span>
                <span v-else class="tag is-info is-small">
                  <span class="icon is-small">
                    <i class="fas fa-sitemap"></i>
                  </span>
                  <span>Child</span>
                </span>
              </div>
            </td>

            <!-- Products -->
            <td>
              <div class="level is-mobile">
                <div class="level-left">
                  <div class="level-item">
                    <span class="tag is-medium" :class="getProductCountClass(category.productCount)">
                      <span class="icon is-small">
                        <i class="fas fa-cube"></i>
                      </span>
                      <span>{{ category.productCount || 0 }}</span>
                    </span>
                  </div>
                </div>
                <div class="level-right" v-if="category.productCount > 0">
                  <div class="level-item">
                    <button
                      class="button is-small is-text"
                      @click="$emit('view-products', category)"
                      title="View products">
                      <span class="icon is-small">
                        <i class="fas fa-external-link-alt"></i>
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </td>

            <!-- SEO -->
            <td>
              <div class="tags">
                <span v-if="category.metaTitle" class="tag is-success is-small" title="Has Meta Title">
                  <span class="icon is-small">
                    <i class="fas fa-heading"></i>
                  </span>
                </span>
                <span v-if="category.metaDescription" class="tag is-info is-small" title="Has Meta Description">
                  <span class="icon is-small">
                    <i class="fas fa-align-left"></i>
                  </span>
                </span>
                <span v-if="category.metaImage" class="tag is-warning is-small" title="Has Meta Image">
                  <span class="icon is-small">
                    <i class="fas fa-image"></i>
                  </span>
                </span>
                <span v-if="!category.metaTitle && !category.metaDescription && !category.metaImage"
                      class="tag is-light is-small">
                  No SEO
                </span>
              </div>
            </td>

            <!-- Actions -->
            <td>
              <div class="buttons are-small">
                <button
                  class="button is-primary is-small"
                  @click="$emit('edit', category)"
                  title="Edit category">
                  <span class="icon is-small">
                    <i class="fas fa-edit"></i>
                  </span>
                </button>
                <button
                  class="button is-danger is-small"
                  @click="$emit('delete', category)"
                  :disabled="!canDeleteCategory(category)"
                  :title="getDeleteTooltip(category)">
                  <span class="icon is-small">
                    <i class="fas fa-trash"></i>
                  </span>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else-if="loading">
          <tr>
            <td colspan="6" class="has-text-centered">
              <div class="loader-wrapper">
                <div class="loader is-loading"></div>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else>
          <tr>
            <td colspan="6" class="has-text-centered">
              No categories found.
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  categories: {
    type: Array,
    required: true
  },
  allCategories: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

defineEmits(['edit', 'delete', 'view-products']);

const truncateText = (text, maxLength) => {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

const getProductCountClass = (count) => {
  if (!count || count === 0) return 'is-light';
  if (count < 5) return 'is-warning';
  if (count < 20) return 'is-info';
  return 'is-success';
};

// Check if category has children
const hasChildren = (categoryId) => {
  return props.allCategories.some(cat => cat.parentId === categoryId);
};

// Check if category can be deleted
const canDeleteCategory = (category) => {
  const hasProducts = (category.productCount || 0) > 0;
  const hasChildCategories = hasChildren(category.id);
  return !hasProducts && !hasChildCategories;
};

// Get tooltip text for delete button
const getDeleteTooltip = (category) => {
  const hasProducts = (category.productCount || 0) > 0;
  const hasChildCategories = hasChildren(category.id);

  if (hasProducts && hasChildCategories) {
    return 'Cannot delete: category has products and subcategories';
  } else if (hasProducts) {
    return 'Cannot delete: category has products';
  } else if (hasChildCategories) {
    return 'Cannot delete: category has subcategories';
  }
  return 'Delete category';
};
</script>

<style scoped>
.category-row {
  transition: background-color 0.2s ease;
}

.category-row:hover {
  background-color: #f8f9fa;
}

.image img {
  object-fit: cover;
}

.buttons {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.category-row:hover .buttons {
  opacity: 1;
}

.tags .tag {
  margin-right: 0.25rem;
  margin-bottom: 0.25rem;
}

.level.is-mobile {
  margin-bottom: 0;
}

.content {
  margin-bottom: 0;
}
</style>

<style scoped>
.loader-wrapper {
  padding: 2rem;
  display: flex;
  justify-content: center;
}

.loader {
  height: 80px;
  width: 80px;
}
</style>
