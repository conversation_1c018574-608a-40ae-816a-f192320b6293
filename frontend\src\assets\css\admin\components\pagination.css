/* ===== ADMIN PAGINATION SYSTEM ===== */
/* Pagination component styles */

.admin-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-space-sm);
  margin: var(--admin-space-xl) 0;
  padding: var(--admin-space-lg);
}

/* Pagination Buttons */
.admin-pagination-btn {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  padding: var(--admin-space-sm) var(--admin-space-md);
  background: var(--admin-white);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  color: var(--admin-gray-700);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  cursor: pointer;
  transition: all var(--admin-transition);
  text-decoration: none;
}

.admin-pagination-btn:hover:not(:disabled) {
  background: var(--admin-gray-50);
  border-color: var(--admin-gray-300);
  color: var(--admin-gray-900);
}

.admin-pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--admin-gray-100);
  color: var(--admin-gray-400);
}

.admin-pagination-prev i {
  font-size: var(--admin-text-xs);
}

.admin-pagination-next i {
  font-size: var(--admin-text-xs);
}

/* Pagination List */
.admin-pagination-list {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

/* Pagination Links */
.admin-pagination-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--admin-white);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  color: var(--admin-gray-700);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  cursor: pointer;
  transition: all var(--admin-transition);
  text-decoration: none;
}

.admin-pagination-link:hover {
  background: var(--admin-gray-50);
  border-color: var(--admin-gray-300);
  color: var(--admin-gray-900);
}

/* Current Page */
.admin-pagination-current {
  background: var(--admin-orange-500) !important;
  border-color: var(--admin-orange-500) !important;
  color: var(--admin-white) !important;
}

.admin-pagination-current:hover {
  background: var(--admin-orange-600) !important;
  border-color: var(--admin-orange-600) !important;
}

/* Ellipsis */
.admin-pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: var(--admin-gray-500);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-pagination {
    flex-wrap: wrap;
    gap: var(--admin-space-xs);
    padding: var(--admin-space-md);
  }
  
  .admin-pagination-btn {
    padding: var(--admin-space-xs) var(--admin-space-sm);
    font-size: var(--admin-text-xs);
  }
  
  .admin-pagination-btn span {
    display: none;
  }
  
  .admin-pagination-link {
    width: 36px;
    height: 36px;
    font-size: var(--admin-text-xs);
  }
  
  .admin-pagination-ellipsis {
    width: 36px;
    height: 36px;
    font-size: var(--admin-text-xs);
  }
}

@media (max-width: 480px) {
  .admin-pagination {
    justify-content: space-between;
  }
  
  .admin-pagination-list {
    order: 2;
    flex: 1;
    justify-content: center;
    margin: 0 var(--admin-space-sm);
  }
  
  .admin-pagination-prev {
    order: 1;
  }
  
  .admin-pagination-next {
    order: 3;
  }
}
