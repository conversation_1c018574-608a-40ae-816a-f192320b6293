<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Companies Issue</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Debug Companies Issue</h1>
    
    <div class="test-section">
        <h2>Test 1: Direct API Call</h2>
        <button onclick="testDirectAPI()">Test Direct API</button>
        <div id="direct-api-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Simulate Service Call</h2>
        <button onclick="testServiceCall()">Test Service Logic</button>
        <div id="service-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Check Product Data</h2>
        <button onclick="testProductData()">Test Product Data</button>
        <div id="product-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 4: Check Categories</h2>
        <button onclick="testCategories()">Test Categories</button>
        <div id="categories-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5296';
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            element.innerHTML += `<div class="${className}">${message}</div>`;
        }
        
        function logPre(elementId, data) {
            const element = document.getElementById(elementId);
            element.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
        }
        
        async function testDirectAPI() {
            const resultId = 'direct-api-result';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                log(resultId, '🧪 Testing direct API call...');
                
                const response = await axios.get(`${API_BASE}/api/companies?pageSize=5`);
                log(resultId, '✅ API call successful', 'success');
                log(resultId, `📊 Response status: ${response.status}`);
                log(resultId, `📦 Data structure: ${typeof response.data}`);
                
                if (response.data && response.data.data) {
                    log(resultId, `📋 Companies count: ${response.data.data.length}`, 'success');
                    log(resultId, `📄 Total: ${response.data.total}`);
                    
                    if (response.data.data.length > 0) {
                        const firstCompany = response.data.data[0];
                        log(resultId, `🏢 First company: ${firstCompany.name}`, 'success');
                        logPre(resultId, firstCompany);
                    }
                } else {
                    log(resultId, '❌ Unexpected response structure', 'error');
                    logPre(resultId, response.data);
                }
                
            } catch (error) {
                log(resultId, `❌ API call failed: ${error.message}`, 'error');
                console.error('API Error:', error);
            }
        }
        
        async function testServiceCall() {
            const resultId = 'service-result';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                log(resultId, '🔧 Simulating service call logic...');
                
                // Simulate the exact logic from companies service
                const apiParams = { pageSize: 200 };
                log(resultId, `📋 API params: ${JSON.stringify(apiParams)}`);
                
                const response = await axios.get(`${API_BASE}/api/companies`, { params: apiParams });
                log(resultId, '✅ Service call successful', 'success');
                
                // This is the exact logic from the service
                let companiesData = [];
                if (response.data && response.data.data && Array.isArray(response.data.data)) {
                    companiesData = response.data.data;
                    log(resultId, `✅ Found companies in response.data: ${companiesData.length}`, 'success');
                } else if (response.data && response.data.companies && Array.isArray(response.data.companies)) {
                    companiesData = response.data.companies;
                    log(resultId, `✅ Found companies in response.companies: ${companiesData.length}`, 'success');
                } else if (Array.isArray(response.data)) {
                    companiesData = response.data;
                    log(resultId, `✅ Response is direct array: ${companiesData.length}`, 'success');
                } else {
                    log(resultId, '⚠️ Unexpected API response structure', 'warning');
                    logPre(resultId, response.data);
                }
                
                log(resultId, `📊 Final companies array length: ${companiesData.length}`);
                
                if (companiesData.length > 0) {
                    log(resultId, '🎯 Sample companies:');
                    companiesData.slice(0, 3).forEach((company, index) => {
                        log(resultId, `  ${index + 1}. ${company.name} (${company.id})`);
                    });
                }
                
            } catch (error) {
                log(resultId, `❌ Service simulation failed: ${error.message}`, 'error');
                console.error('Service Error:', error);
            }
        }
        
        async function testProductData() {
            const resultId = 'product-result';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                log(resultId, '📦 Testing product data...');
                
                const productId = '5f1e1b91-3e83-4d0d-af05-04703e501d89';
                const response = await axios.get(`${API_BASE}/api/products/${productId}`);
                
                log(resultId, '✅ Product data retrieved', 'success');
                log(resultId, `📋 Product name: ${response.data.name}`);
                log(resultId, `🏢 Company ID: ${response.data.companyId}`);
                log(resultId, `🏷️ Category ID: ${response.data.categoryId}`);
                
                // Test if we can get the company for this product
                if (response.data.companyId) {
                    try {
                        const companiesResponse = await axios.get(`${API_BASE}/api/companies?pageSize=200`);
                        const company = companiesResponse.data.data.find(c => c.id === response.data.companyId);
                        
                        if (company) {
                            log(resultId, `✅ Company found: ${company.name}`, 'success');
                        } else {
                            log(resultId, `❌ Company not found for ID: ${response.data.companyId}`, 'error');
                        }
                    } catch (companyError) {
                        log(resultId, `❌ Error fetching company: ${companyError.message}`, 'error');
                    }
                }
                
            } catch (error) {
                log(resultId, `❌ Product test failed: ${error.message}`, 'error');
                console.error('Product Error:', error);
            }
        }
        
        async function testCategories() {
            const resultId = 'categories-result';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                log(resultId, '🏷️ Testing categories...');
                
                const response = await axios.get(`${API_BASE}/api/categories/all?pageSize=50`);
                log(resultId, '✅ Categories retrieved', 'success');
                
                if (response.data && response.data.data) {
                    log(resultId, `📋 Categories count: ${response.data.data.length}`, 'success');
                    
                    if (response.data.data.length > 0) {
                        log(resultId, '🎯 Sample categories:');
                        response.data.data.slice(0, 5).forEach((category, index) => {
                            log(resultId, `  ${index + 1}. ${category.name} (${category.id})`);
                        });
                    }
                } else {
                    log(resultId, '❌ Unexpected categories response structure', 'error');
                    logPre(resultId, response.data);
                }
                
            } catch (error) {
                log(resultId, `❌ Categories test failed: ${error.message}`, 'error');
                console.error('Categories Error:', error);
            }
        }
        
        // Auto-run first test
        window.onload = () => {
            testDirectAPI();
        };
    </script>
</body>
</html>
