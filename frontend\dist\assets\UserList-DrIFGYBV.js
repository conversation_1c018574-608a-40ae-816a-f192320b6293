import{_ as ce,g,B as Q,h as F,u as ve,x as G,i as me,c as v,a as e,b as V,z as $,C as P,A as H,D as r,t as c,n as L,k as y,d as J,F as pe,p as fe,w as ge,r as he,o as m}from"./index-BKy0rL_2.js";import{R as u,a as h,g as _e,b as X,u as N,c as I,d as q}from"./users-CtljIT9s.js";import{P as ye}from"./Pagination-DcbqxmDq.js";import{C as Re}from"./ConfirmDialog-BQ115uZp.js";const Ee={class:"user-list"},we={class:"card mb-4"},be={class:"card-content"},Ue={class:"columns is-multiline"},$e={class:"column is-5"},Ce={class:"field"},ke={class:"control has-icons-left"},Le={class:"column is-5"},Ae={class:"field"},De={class:"control"},Me={class:"select is-fullwidth"},Se=["value"],xe=["value"],Pe=["value"],Ne=["value"],Oe=["value"],Te={class:"column is-2"},Be={class:"field",style:{"margin-top":"1.9rem"}},Fe={class:"buttons is-right"},Ve={class:"card"},Ie={class:"card-content"},qe={key:0,class:"level mb-4"},Ye={class:"level-left"},We={class:"level-item"},ze={key:0},Ke={key:0,class:"tag is-info is-light mr-1"},je={key:1,class:"tag is-info is-light"},Qe={key:1,class:"has-text-centered py-6"},Ge={key:2,class:"has-text-centered py-6"},He={key:3},Je={class:"table is-fullwidth is-hoverable"},Xe={class:"buttons are-small"},Ze=["onClick"],es=["onClick"],ss={key:0,class:"table-loading-overlay"},ls={class:"modal-card"},os={class:"modal-card-head"},ts={class:"modal-card-title"},as={class:"modal-card-body"},rs={class:"field"},ns={class:"control"},is={class:"field"},us={class:"control"},ds={key:0,class:"field"},cs={class:"control"},vs={class:"field"},ms={class:"control"},ps={class:"select is-fullwidth"},fs=["value"],gs=["value"],hs=["value"],_s=["value"],ys=["value"],Rs={class:"modal-card-foot"},Es=["disabled"],ws={__name:"UserList",setup(bs){const Y=ve(),d=g([]),R=g(!1),b=g(1),A=g(1),C=g(0),W=g(10),D=g(!1),E=g(!1),O=g(!1),a=Q({username:"",email:"",password:"",role:u.BUYER}),M=g(!1),w=g(null),n=Q({search:"",role:""}),T=F(()=>Y.getters["auth/isAdmin"]),S=F(()=>Y.getters["auth/isModerator"]),z=F(()=>E.value?a.username&&a.email&&a.role:a.username&&a.email&&a.password&&a.role),Z=o=>T.value?!0:S.value&&o.role==="admin"?!1:!!S.value,ee=o=>T.value?!0:S.value&&o.role==="admin"?!1:!!S.value,U=g(!0),k=async(o=1)=>{(U.value||b.value!==o)&&(R.value=!0),b.value=o;try{const s={page:b.value,pageSize:W.value},f=n.search&&n.search.trim()!==""?n.search.trim().toLowerCase():"";if(n.role&&n.role.trim()!==""){const l={admin:4,seller:1,buyer:0,moderator:3,sellerowner:2},t=n.role.toLowerCase();l.hasOwnProperty(t)&&(s.role=l[t]),console.log("Adding role parameter:",n.role,"converted to:",s.role)}console.log("API parameters:",s);const p=await N.getUsers(s);if(console.log("Response from API:",p),p.users){let l=p.users.map(t=>{const i=I(t.role);return console.log(`Processing user ${t.username}: original role = ${t.role}, converted role = ${i}`),{...t,originalRole:t.role,role:i}});if(console.log("Before sorting:",l.map(t=>`${t.username} (${t.role})`)),l.sort((t,i)=>{console.log(`Sorting: ${t.username} (${t.role}) vs ${i.username} (${i.role})`);const _=q(t.role,i.role);return console.log(`Sort result: ${_}`),_}),console.log("After sorting:",l.map(t=>`${t.username} (${t.role})`)),f){console.log("Filtering users by search query:",f);const t=l.length;l=l.filter(i=>{const _=i.username&&i.username.toLowerCase().includes(f),de=i.email&&i.email.toLowerCase().includes(f),j=_||de;return j&&console.log(`User ${i.username} matches search query`),j}),console.log(`Users after search filtering: ${l.length} (filtered from ${t})`)}d.value=l,console.log("Final processed users:",d.value.length),console.log("Users by role:",d.value.reduce((t,i)=>{const _=typeof i.role=="string"?i.role.toLowerCase():"unknown";return t[_]=(t[_]||0)+1,t},{}))}else d.value=[];p.pagination&&(f?(C.value=d.value.length,A.value=Math.ceil(C.value/W.value)||1):(C.value=p.pagination.total,A.value=p.pagination.totalPages),b.value=p.pagination.page)}catch(s){console.error("Error fetching users:",s),s.response?console.error("Server response error:",{status:s.response.status,statusText:s.response.statusText,data:s.response.data}):s.request?console.error("No response received:",s.request):console.error("Request setup error:",s.message),d.value=[],A.value=1,C.value=0,alert("Failed to fetch users. Please check your connection and try again.")}finally{R.value=!1,U.value&&(U.value=!1)}},se=()=>{n.search="",n.role=""};let B=null;G(()=>n.search,o=>{console.log("Search changed:",o),B&&clearTimeout(B),B=setTimeout(()=>{k(1)},300)}),G(()=>n.role,()=>{console.log("Role changed:",n.role),k(1)});const le=o=>{k(o)},oe=o=>o?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(new Date(o)):"";console.log("Role order for sorting:",u,h);const te=o=>X(o),K=()=>{E.value=!1,a.username="",a.email="",a.password="",a.role="Buyer",D.value=!0},ae=o=>{E.value=!0,a.id=o.id,a.username=o.username,a.email=o.email,a.password="",a.role=o.originalRole!==void 0?o.originalRole:o.role,D.value=!0},x=()=>{D.value=!1},re=async()=>{if(z.value){O.value=!0;try{if(E.value){const o={username:a.username,email:a.email,role:a.role};a.password&&(o.password=a.password),console.log("Updating user with data:",o),await N.updateUser(a.id,o);const s=d.value.findIndex(f=>f.id===a.id);if(s!==-1){const f=o.role,p=I(o.role);d.value[s]={...d.value[s],...o,originalRole:f,role:p},console.log("Before resorting after update:",d.value.map(l=>`${l.username} (${l.role})`)),d.value.sort((l,t)=>{console.log(`Resorting after update: ${l.username} (${l.role}) vs ${t.username} (${t.role})`);const i=q(l.role,t.role);return console.log(`Sort result: ${i}`),i}),console.log("After resorting after update:",d.value.map(l=>`${l.username} (${l.role})`))}}else{const o={username:a.username,email:a.email,password:a.password,role:a.role};console.log("Creating user with data:",o);const s=await N.createUser(o);if(b.value===1){const f=s.role,p=I(s.role),l={...s,originalRole:f,role:p};d.value.unshift(l),console.log("Before resorting after add:",d.value.map(t=>`${t.username} (${t.role})`)),d.value.sort((t,i)=>{console.log(`Resorting after add: ${t.username} (${t.role}) vs ${i.username} (${i.role})`);const _=q(t.role,i.role);return console.log(`Sort result: ${_}`),_}),console.log("After resorting after add:",d.value.map(t=>`${t.username} (${t.role})`))}else k(1)}x()}catch(o){console.error("Error saving user:",o),alert("Failed to save user. Please check the console for details.")}finally{O.value=!1}}},ne=o=>{w.value=o,M.value=!0},ie=()=>{M.value=!1,w.value=null},ue=async()=>{if(w.value)try{await N.deleteUser(w.value.id),d.value=d.value.filter(o=>o.id!==w.value.id),M.value=!1,w.value=null}catch(o){console.error("Error deleting user:",o)}};return me(()=>{k()}),(o,s)=>{var p;const f=he("router-link");return m(),v("div",Ee,[e("div",{class:"level"},[s[7]||(s[7]=e("div",{class:"level-left"},[e("div",{class:"level-item"},[e("h1",{class:"title"},"Users")])],-1)),e("div",{class:"level-right"},[e("div",{class:"level-item"},[e("button",{class:"button is-primary",onClick:K},s[6]||(s[6]=[e("span",{class:"icon"},[e("i",{class:"fas fa-plus"})],-1),e("span",null,"Add User",-1)]))])])]),e("div",we,[e("div",be,[e("div",Ue,[e("div",$e,[e("div",Ce,[s[9]||(s[9]=e("label",{class:"label"},"Search",-1)),e("div",ke,[$(e("input",{class:"input",type:"text",placeholder:"Name, email, username...","onUpdate:modelValue":s[0]||(s[0]=l=>n.search=l)},null,512),[[P,n.search]]),s[8]||(s[8]=e("span",{class:"icon is-small is-left"},[e("i",{class:"fas fa-search"})],-1))])])]),e("div",Le,[e("div",Ae,[s[11]||(s[11]=e("label",{class:"label"},"Role",-1)),e("div",De,[e("div",Me,[$(e("select",{"onUpdate:modelValue":s[1]||(s[1]=l=>n.role=l)},[s[10]||(s[10]=e("option",{value:""},"All Roles",-1)),e("option",{value:r(u).ADMIN},c(r(h)[r(u).ADMIN]),9,Se),e("option",{value:r(u).MODERATOR},c(r(h)[r(u).MODERATOR]),9,xe),e("option",{value:r(u).SELLER},c(r(h)[r(u).SELLER]),9,Pe),e("option",{value:r(u).SELLER_OWNER},c(r(h)[r(u).SELLER_OWNER]),9,Ne),e("option",{value:r(u).BUYER},c(r(h)[r(u).BUYER]),9,Oe)],512),[[H,n.role]])])])])]),e("div",Te,[e("div",Be,[e("div",Fe,[e("button",{class:L(["button is-light",{"is-loading":R.value}]),onClick:se}," Reset Filters ",2)])])])])])]),e("div",Ve,[e("div",Ie,[!R.value||d.value.length>0?(m(),v("div",qe,[e("div",Ye,[e("div",We,[e("p",null,[e("strong",null,c(C.value),1),s[13]||(s[13]=J(" users found ")),n.search||n.role?(m(),v("span",ze,[s[12]||(s[12]=J(" with filters: ")),n.search?(m(),v("span",Ke," Search: "+c(n.search),1)):y("",!0),n.role?(m(),v("span",je," Role: "+c(te(n.role)),1)):y("",!0)])):y("",!0)])])])])):y("",!0),R.value&&U.value?(m(),v("div",Qe,s[14]||(s[14]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading users...",-1)]))):!R.value&&!d.value.length?(m(),v("div",Ge,[s[16]||(s[16]=e("span",{class:"icon is-large"},[e("i",{class:"fas fa-users fa-2x"})],-1)),s[17]||(s[17]=e("p",{class:"mt-2"},"No users found",-1)),s[18]||(s[18]=e("p",{class:"mt-2"},"Try adjusting your filters or add a new user",-1)),e("div",{class:"mt-4"},[e("button",{class:"button is-primary",onClick:K},s[15]||(s[15]=[e("span",{class:"icon"},[e("i",{class:"fas fa-plus"})],-1),e("span",null,"Add User",-1)]))])])):(m(),v("div",He,[e("div",{class:L(["table-container",{"is-loading":R.value&&!U.value}])},[e("table",Je,[s[22]||(s[22]=e("thead",null,[e("tr",null,[e("th",null,"Username"),e("th",null,"Email"),e("th",null,"Role"),e("th",null,"Registered"),e("th",null,"Actions")])],-1)),e("tbody",null,[(m(!0),v(pe,null,fe(d.value,l=>(m(),v("tr",{key:l.id},[e("td",null,c(l.username),1),e("td",null,c(l.email),1),e("td",null,[e("span",{class:L(["tag",r(_e)(l.role)])},c(r(X)(l.role)),3)]),e("td",null,c(oe(l.createdAt||l.emailConfirmedAt)),1),e("td",null,[e("div",Xe,[V(f,{to:`/admin/users/${l.id}`,class:"button is-info",title:"View"},{default:ge(()=>s[19]||(s[19]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-eye"})],-1)])),_:2},1032,["to"]),ee(l)?(m(),v("button",{key:0,class:"button is-primary",onClick:t=>ae(l),title:"Edit"},s[20]||(s[20]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-edit"})],-1)]),8,Ze)):y("",!0),Z(l)?(m(),v("button",{key:1,class:"button is-danger",onClick:t=>ne(l),title:"Delete"},s[21]||(s[21]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-trash"})],-1)]),8,es)):y("",!0)])])]))),128))])]),R.value&&!U.value?(m(),v("div",ss,s[23]||(s[23]=[e("div",{class:"loading-spinner"},[e("i",{class:"fas fa-spinner fa-pulse"})],-1)]))):y("",!0)],2),V(ye,{"current-page":b.value,"total-pages":A.value,onPageChanged:le},null,8,["current-page","total-pages"])]))])]),e("div",{class:L(["modal",{"is-active":D.value}])},[e("div",{class:"modal-background",onClick:x}),e("div",ls,[e("header",os,[e("p",ts,c(E.value?"Edit User":"Add User"),1),e("button",{class:"delete","aria-label":"close",onClick:x})]),e("section",as,[e("div",rs,[s[24]||(s[24]=e("label",{class:"label"},"Username*",-1)),e("div",ns,[$(e("input",{class:"input",type:"text","onUpdate:modelValue":s[2]||(s[2]=l=>a.username=l),required:"",placeholder:"Enter username"},null,512),[[P,a.username]])])]),e("div",is,[s[25]||(s[25]=e("label",{class:"label"},"Email*",-1)),e("div",us,[$(e("input",{class:"input",type:"email","onUpdate:modelValue":s[3]||(s[3]=l=>a.email=l),required:"",placeholder:"Enter email"},null,512),[[P,a.email]])])]),E.value?y("",!0):(m(),v("div",ds,[s[26]||(s[26]=e("label",{class:"label"},"Password*",-1)),e("div",cs,[$(e("input",{class:"input",type:"password","onUpdate:modelValue":s[4]||(s[4]=l=>a.password=l),required:"",placeholder:"Enter password"},null,512),[[P,a.password]])])])),e("div",vs,[s[27]||(s[27]=e("label",{class:"label"},"Role*",-1)),e("div",ms,[e("div",ps,[$(e("select",{"onUpdate:modelValue":s[5]||(s[5]=l=>a.role=l),required:""},[T.value?(m(),v("option",{key:0,value:r(u).ADMIN},c(r(h)[r(u).ADMIN]),9,fs)):y("",!0),e("option",{value:r(u).MODERATOR},c(r(h)[r(u).MODERATOR]),9,gs),e("option",{value:r(u).SELLER},c(r(h)[r(u).SELLER]),9,hs),e("option",{value:r(u).SELLER_OWNER},c(r(h)[r(u).SELLER_OWNER]),9,_s),e("option",{value:r(u).BUYER},c(r(h)[r(u).BUYER]),9,ys)],512),[[H,a.role]])])])])]),e("footer",Rs,[e("button",{class:L(["button is-primary",{"is-loading":O.value}]),onClick:re,disabled:!z.value},c(E.value?"Update User":"Create User"),11,Es),e("button",{class:"button",onClick:x},"Cancel")])])],2),V(Re,{"is-open":M.value,title:"Delete User",message:`Are you sure you want to delete ${(p=w.value)==null?void 0:p.username}? This action cannot be undone.`,"confirm-text":"Delete","cancel-text":"Cancel",onConfirm:ue,onCancel:ie},null,8,["is-open","message"])])}}},Ls=ce(ws,[["__scopeId","data-v-094d42dd"]]);export{Ls as default};
