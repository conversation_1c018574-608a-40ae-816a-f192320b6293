import api from './api.js'

/**
 * Unified Reports Service
 * Provides methods for all report types and export functionality
 */
class ReportsService {
  constructor() {
    this.baseUrl = '/api/admin/reports'
  }

  /**
   * Get Financial Report
   * @param {Object} params - Query parameters
   * @param {Date} params.startDate - Start date for the report
   * @param {Date} params.endDate - End date for the report
   * @returns {Promise} API response with financial report data
   */
  async getFinancialReport(params = {}) {
    try {
      const queryParams = this._buildQueryParams(params)
      const response = await api.get(`${this.baseUrl}/financial${queryParams}`)
      return response.data
    } catch (error) {
      console.error('Error fetching financial report:', error)
      throw error
    }
  }

  /**
   * Get Sales Report
   * @param {Object} params - Query parameters
   * @param {Date} params.startDate - Start date for the report
   * @param {Date} params.endDate - End date for the report
   * @param {string} params.sortBy - Sort field
   * @param {string} params.sortOrder - Sort direction (asc/desc)
   * @returns {Promise} API response with sales report data
   */
  async getSalesReport(params = {}) {
    try {
      const queryParams = this._buildQueryParams(params)
      const response = await api.get(`${this.baseUrl}/sales${queryParams}`)
      return response.data
    } catch (error) {
      console.error('Error fetching sales report:', error)
      throw error
    }
  }

  /**
   * Get Products Report
   * @param {Object} params - Query parameters
   * @param {Date} params.startDate - Start date for the report
   * @param {Date} params.endDate - End date for the report
   * @param {string} params.categoryId - Category filter
   * @param {number} params.limit - Page size limit
   * @returns {Promise} API response with products report data
   */
  async getProductsReport(params = {}) {
    try {
      const queryParams = this._buildQueryParams(params)
      const response = await api.get(`${this.baseUrl}/products${queryParams}`)
      return response.data
    } catch (error) {
      console.error('Error fetching products report:', error)
      throw error
    }
  }

  /**
   * Get Users Report
   * @param {Object} params - Query parameters
   * @param {Date} params.startDate - Start date for the report
   * @param {Date} params.endDate - End date for the report
   * @param {string} params.userType - User role filter
   * @returns {Promise} API response with users report data
   */
  async getUsersReport(params = {}) {
    try {
      const queryParams = this._buildQueryParams(params)
      const response = await api.get(`${this.baseUrl}/users${queryParams}`)
      return response.data
    } catch (error) {
      console.error('Error fetching users report:', error)
      throw error
    }
  }

  /**
   * Get Orders Report
   * @param {Object} params - Query parameters
   * @param {Date} params.startDate - Start date for the report
   * @param {Date} params.endDate - End date for the report
   * @param {string} params.status - Order status filter
   * @returns {Promise} API response with orders report data
   */
  async getOrdersReport(params = {}) {
    try {
      const queryParams = this._buildQueryParams(params)
      const response = await api.get(`${this.baseUrl}/orders${queryParams}`)
      return response.data
    } catch (error) {
      console.error('Error fetching orders report:', error)
      throw error
    }
  }

  /**
   * Export Report
   * @param {string} reportType - Type of report (financial, sales, products, users, orders)
   * @param {string} format - Export format (excel, csv)
   * @param {Object} params - Query parameters for filtering
   * @returns {Promise} Blob response for file download
   */
  async exportReport(reportType, format, params = {}) {
    try {
      const queryParams = this._buildQueryParams(params)
      const response = await api.get(
        `${this.baseUrl}/${reportType}/export/${format}${queryParams}`,
        {
          responseType: 'blob'
        }
      )
      return response.data
    } catch (error) {
      console.error(`Error exporting ${reportType} report as ${format}:`, error)
      throw error
    }
  }

  /**
   * Download exported report file
   * @param {Blob} blob - File blob data
   * @param {string} filename - Name for the downloaded file
   * @param {string} format - File format (excel, csv)
   */
  downloadFile(blob, filename, format = 'excel') {
    const extension = this.getFileExtension(format)
    const fullFilename = filename.includes('.') ? filename : `${filename}.${extension}`

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fullFilename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }

  /**
   * Get file extension for format
   * @param {string} format - Export format
   * @returns {string} File extension
   */
  getFileExtension(format) {
    const extensions = {
      excel: 'xlsx',
      csv: 'csv',
      pdf: 'pdf'
    }
    return extensions[format] || 'xlsx'
  }

  /**
   * Generate filename for export
   * @param {string} reportType - Type of report
   * @param {string} format - Export format
   * @param {Object} options - Additional options
   * @returns {string} Generated filename
   */
  generateFilename(reportType, format = 'excel', options = {}) {
    const timestamp = this._formatDate(new Date())
    const extension = this.getFileExtension(format)

    // Use custom filename if provided
    if (options.filename) {
      return options.filename.includes('.') ? options.filename : `${options.filename}.${extension}`
    }

    // Generate default filename
    const reportName = reportType.charAt(0).toUpperCase() + reportType.slice(1)
    return `${reportName}_Report_${timestamp}.${extension}`
  }

  /**
   * Export Financial Report
   * @param {string} format - Export format (excel, csv)
   * @param {Object} params - Query parameters
   */
  async exportFinancialReport(format = 'excel', params = {}) {
    const blob = await this.exportReport('financial', format, params)
    const filename = `financial-report-${this._formatDate(new Date())}.${format === 'excel' ? 'xlsx' : 'csv'}`
    this.downloadFile(blob, filename)
  }

  /**
   * Export Sales Report
   * @param {string} format - Export format (excel, csv)
   * @param {Object} params - Query parameters
   */
  async exportSalesReport(format = 'excel', params = {}) {
    const blob = await this.exportReport('sales', format, params)
    const filename = `sales-report-${this._formatDate(new Date())}.${format === 'excel' ? 'xlsx' : 'csv'}`
    this.downloadFile(blob, filename)
  }

  /**
   * Export Products Report
   * @param {string} format - Export format (excel, csv)
   * @param {Object} params - Query parameters
   */
  async exportProductsReport(format = 'excel', params = {}) {
    const blob = await this.exportReport('products', format, params)
    const filename = `products-report-${this._formatDate(new Date())}.${format === 'excel' ? 'xlsx' : 'csv'}`
    this.downloadFile(blob, filename)
  }

  /**
   * Export Users Report
   * @param {string} format - Export format (excel, csv)
   * @param {Object} params - Query parameters
   */
  async exportUsersReport(format = 'excel', params = {}) {
    const blob = await this.exportReport('users', format, params)
    const filename = `users-report-${this._formatDate(new Date())}.${format === 'excel' ? 'xlsx' : 'csv'}`
    this.downloadFile(blob, filename)
  }

  /**
   * Export Orders Report
   * @param {string} format - Export format (excel, csv)
   * @param {Object} params - Query parameters
   */
  async exportOrdersReport(format = 'excel', params = {}) {
    const blob = await this.exportReport('orders', format, params)
    const filename = `orders-report-${this._formatDate(new Date())}.${format === 'excel' ? 'xlsx' : 'csv'}`
    this.downloadFile(blob, filename)
  }

  /**
   * Build query parameters string from object
   * @param {Object} params - Parameters object
   * @returns {string} Query string
   * @private
   */
  _buildQueryParams(params) {
    const searchParams = new URLSearchParams()
    
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        if (params[key] instanceof Date) {
          searchParams.append(key, params[key].toISOString())
        } else {
          searchParams.append(key, params[key].toString())
        }
      }
    })
    
    const queryString = searchParams.toString()
    return queryString ? `?${queryString}` : ''
  }

  /**
   * Format date for filename
   * @param {Date} date - Date to format
   * @returns {string} Formatted date string
   * @private
   */
  _formatDate(date) {
    return date.toISOString().split('T')[0]
  }

  // ===== FORMATTING METHODS =====

  /**
   * Format currency value
   * @param {number} value - Value to format
   * @param {string} currency - Currency code (default: UAH)
   * @returns {string} Formatted currency string
   */
  formatCurrency(value, currency = 'UAH') {
    if (typeof value !== 'number' || isNaN(value)) return '0,00 ₴'

    return new Intl.NumberFormat('uk-UA', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value)
  }

  /**
   * Format number value
   * @param {number} value - Value to format
   * @returns {string} Formatted number string
   */
  formatNumber(value) {
    if (typeof value !== 'number' || isNaN(value)) return '0'

    return new Intl.NumberFormat('uk-UA', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value)
  }

  /**
   * Format percentage value
   * @param {number} value - Value to format
   * @returns {string} Formatted percentage string
   */
  formatPercentage(value) {
    if (typeof value !== 'number' || isNaN(value)) return '0%'

    return `${value.toFixed(2)}%`
  }

  /**
   * Format date value
   * @param {string|Date} value - Date to format
   * @returns {string} Formatted date string
   */
  formatDate(value) {
    if (!value) return '-'

    const date = typeof value === 'string' ? new Date(value) : value
    if (isNaN(date.getTime())) return '-'

    return date.toLocaleDateString('uk-UA', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  }

  /**
   * Format file size
   * @param {number} bytes - Size in bytes
   * @returns {string} Formatted file size string
   */
  formatFileSize(bytes) {
    if (typeof bytes !== 'number' || bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
  }

  /**
   * Format time ago
   * @param {string|Date} date - Date to format
   * @returns {string} Time ago string
   */
  formatTimeAgo(date) {
    if (!date) return '-'

    const now = new Date()
    const past = typeof date === 'string' ? new Date(date) : date
    if (isNaN(past.getTime())) return '-'

    const diffMs = now - past
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return 'щойно'
    if (diffMins < 60) return `${diffMins} хв тому`
    if (diffHours < 24) return `${diffHours} год тому`
    if (diffDays < 30) return `${diffDays} дн тому`

    return this.formatDate(past)
  }

  /**
   * Format metric value based on type
   * @param {any} value - Value to format
   * @param {string} type - Type of formatting (currency, number, percentage, date)
   * @returns {string} Formatted value
   */
  formatMetricValue(value, type) {
    switch (type) {
      case 'currency':
        return this.formatCurrency(value)
      case 'percentage':
        return this.formatPercentage(value)
      case 'number':
        return this.formatNumber(value)
      case 'date':
        return this.formatDate(value)
      default:
        return String(value || '-')
    }
  }

  /**
   * Get change icon based on percentage
   * @param {number} changePercentage - Change percentage
   * @returns {string} Icon class
   */
  getChangeIcon(changePercentage) {
    if (changePercentage > 0) return 'fas fa-arrow-up'
    if (changePercentage < 0) return 'fas fa-arrow-down'
    return 'fas fa-minus'
  }

  /**
   * Get change color based on percentage
   * @param {number} changePercentage - Change percentage
   * @returns {string} Color class
   */
  getChangeColor(changePercentage) {
    if (changePercentage > 0) return 'text-success'
    if (changePercentage < 0) return 'text-danger'
    return 'text-muted'
  }
}

// Create and export singleton instance
export const reportsService = new ReportsService()
export default reportsService
