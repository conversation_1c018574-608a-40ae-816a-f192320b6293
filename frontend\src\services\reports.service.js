import api from './api.service'

class ReportsService {
  constructor() {
    this.baseUrl = '/api/admin/reports'
  }

  /**
   * Get report data based on parameters
   * @param {Object} params - Report parameters
   * @param {string} params.reportType - Type of report (financial, sales, products, users, orders)
   * @param {string} params.startDate - Start date in ISO format
   * @param {string} params.endDate - End date in ISO format
   * @param {Object} params.additionalFilters - Additional filters specific to report type
   * @returns {Promise<Object>} Report data
   */
  async getReportData(params) {
    try {
      const { reportType, ...queryParams } = params
      
      const response = await api.get(`${this.baseUrl}/${reportType}`, {
        params: queryParams
      })
      
      return response.data
    } catch (error) {
      console.error('Error fetching report data:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch report data')
    }
  }

  /**
   * Get financial report data
   * @param {Object} params - Financial report parameters
   * @returns {Promise<Object>} Financial report data
   */
  async getFinancialReport(params) {
    try {
      const response = await api.get(`${this.baseUrl}/financial`, { params })
      return response.data
    } catch (error) {
      console.error('Error fetching financial report:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch financial report')
    }
  }

  /**
   * Get sales report data
   * @param {Object} params - Sales report parameters
   * @returns {Promise<Object>} Sales report data
   */
  async getSalesReport(params) {
    try {
      const response = await api.get(`${this.baseUrl}/sales`, { params })
      return response.data
    } catch (error) {
      console.error('Error fetching sales report:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch sales report')
    }
  }

  /**
   * Get products report data
   * @param {Object} params - Products report parameters
   * @returns {Promise<Object>} Products report data
   */
  async getProductsReport(params) {
    try {
      const response = await api.get(`${this.baseUrl}/products`, { params })
      return response.data
    } catch (error) {
      console.error('Error fetching products report:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch products report')
    }
  }

  /**
   * Get users report data
   * @param {Object} params - Users report parameters
   * @returns {Promise<Object>} Users report data
   */
  async getUsersReport(params) {
    try {
      const response = await api.get(`${this.baseUrl}/users`, { params })
      return response.data
    } catch (error) {
      console.error('Error fetching users report:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch users report')
    }
  }

  /**
   * Get orders report data
   * @param {Object} params - Orders report parameters
   * @returns {Promise<Object>} Orders report data
   */
  async getOrdersReport(params) {
    try {
      const response = await api.get(`${this.baseUrl}/orders`, { params })
      return response.data
    } catch (error) {
      console.error('Error fetching orders report:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch orders report')
    }
  }

  /**
   * Export report to specified format
   * @param {string} reportType - Type of report
   * @param {string} format - Export format (excel, pdf, csv)
   * @param {Object} params - Report parameters
   * @returns {Promise<Blob>} File blob for download
   */
  async exportReport(reportType, format, params) {
    try {
      const response = await api.post(`${this.baseUrl}/${reportType}/export`, params, {
        params: { format },
        responseType: 'blob'
      })
      
      return response.data
    } catch (error) {
      console.error('Error exporting report:', error)
      throw new Error(error.response?.data?.message || 'Failed to export report')
    }
  }

  /**
   * Save report template
   * @param {Object} template - Report template data
   * @returns {Promise<Object>} Saved template
   */
  async saveTemplate(template) {
    try {
      const response = await api.post(`${this.baseUrl}/templates`, template)
      return response.data
    } catch (error) {
      console.error('Error saving report template:', error)
      throw new Error(error.response?.data?.message || 'Failed to save report template')
    }
  }

  /**
   * Get saved report templates
   * @returns {Promise<Array>} List of saved templates
   */
  async getTemplates() {
    try {
      const response = await api.get(`${this.baseUrl}/templates`)
      return response.data
    } catch (error) {
      console.error('Error fetching report templates:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch report templates')
    }
  }

  /**
   * Delete report template
   * @param {string} templateId - Template ID
   * @returns {Promise<void>}
   */
  async deleteTemplate(templateId) {
    try {
      await api.delete(`${this.baseUrl}/templates/${templateId}`)
    } catch (error) {
      console.error('Error deleting report template:', error)
      throw new Error(error.response?.data?.message || 'Failed to delete report template')
    }
  }

  /**
   * Get dashboard summary data
   * @returns {Promise<Object>} Dashboard summary
   */
  async getDashboardSummary() {
    try {
      const response = await api.get(`${this.baseUrl}/dashboard-summary`)
      return response.data
    } catch (error) {
      console.error('Error fetching dashboard summary:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch dashboard summary')
    }
  }

  /**
   * Get comparison data for periods
   * @param {Object} params - Comparison parameters
   * @returns {Promise<Object>} Comparison data
   */
  async getComparisonData(params) {
    try {
      const response = await api.get(`${this.baseUrl}/comparison`, { params })
      return response.data
    } catch (error) {
      console.error('Error fetching comparison data:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch comparison data')
    }
  }

  /**
   * Download exported report file
   * @param {Blob} blob - File blob
   * @param {string} filename - Filename for download
   * @param {string} format - File format
   */
  downloadFile(blob, filename, format) {
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    
    // Set appropriate filename with extension
    const extension = this.getFileExtension(format)
    link.download = `${filename}.${extension}`
    
    // Trigger download
    document.body.appendChild(link)
    link.click()
    
    // Cleanup
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }

  /**
   * Get file extension for format
   * @param {string} format - Export format
   * @returns {string} File extension
   */
  getFileExtension(format) {
    const extensions = {
      excel: 'xlsx',
      pdf: 'pdf',
      csv: 'csv'
    }
    return extensions[format] || 'txt'
  }

  /**
   * Format currency value
   * @param {number} value - Numeric value
   * @param {string} currency - Currency code (default: UAH)
   * @returns {string} Formatted currency string
   */
  formatCurrency(value, currency = 'UAH') {
    return new Intl.NumberFormat('uk-UA', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(value)
  }

  /**
   * Format percentage value
   * @param {number} value - Numeric value
   * @param {number} decimals - Number of decimal places
   * @returns {string} Formatted percentage string
   */
  formatPercentage(value, decimals = 1) {
    return new Intl.NumberFormat('uk-UA', {
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(value / 100)
  }

  /**
   * Format number with thousands separator
   * @param {number} value - Numeric value
   * @returns {string} Formatted number string
   */
  formatNumber(value) {
    return new Intl.NumberFormat('uk-UA').format(value)
  }

  /**
   * Calculate percentage change between two values
   * @param {number} current - Current value
   * @param {number} previous - Previous value
   * @returns {number} Percentage change
   */
  calculatePercentageChange(current, previous) {
    if (previous === 0) return current > 0 ? 100 : 0
    return ((current - previous) / previous) * 100
  }

  /**
   * Generate report filename
   * @param {string} reportType - Type of report
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @returns {string} Generated filename
   */
  generateFilename(reportType, startDate, endDate) {
    const start = new Date(startDate).toISOString().split('T')[0]
    const end = new Date(endDate).toISOString().split('T')[0]
    const timestamp = new Date().toISOString().split('T')[0]
    
    return `${reportType}_report_${start}_to_${end}_${timestamp}`
  }

  /**
   * Validate date range
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Object} Validation result
   */
  validateDateRange(startDate, endDate) {
    const errors = []
    
    if (!startDate) {
      errors.push('Start date is required')
    }
    
    if (!endDate) {
      errors.push('End date is required')
    }
    
    if (startDate && endDate && startDate > endDate) {
      errors.push('Start date must be before end date')
    }
    
    if (startDate && startDate > new Date()) {
      errors.push('Start date cannot be in the future')
    }
    
    if (endDate && endDate > new Date()) {
      errors.push('End date cannot be in the future')
    }
    
    // Check if date range is too large (more than 1 year)
    if (startDate && endDate) {
      const diffTime = Math.abs(endDate - startDate)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays > 365) {
        errors.push('Date range cannot exceed 1 year')
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

// Create and export singleton instance
export const reportsService = new ReportsService()
export default reportsService
