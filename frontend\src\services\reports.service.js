import api from './api.js'

/**
 * Unified Reports Service
 * Provides methods for all report types and export functionality
 */
class ReportsService {
  constructor() {
    this.baseUrl = '/api/admin/reports'
  }

  /**
   * Get Financial Report
   * @param {Object} params - Query parameters
   * @param {Date} params.startDate - Start date for the report
   * @param {Date} params.endDate - End date for the report
   * @returns {Promise} API response with financial report data
   */
  async getFinancialReport(params = {}) {
    try {
      const queryParams = this._buildQueryParams(params)
      const response = await api.get(`${this.baseUrl}/financial${queryParams}`)
      return response.data
    } catch (error) {
      console.error('Error fetching financial report:', error)
      throw error
    }
  }

  /**
   * Get Sales Report
   * @param {Object} params - Query parameters
   * @param {Date} params.startDate - Start date for the report
   * @param {Date} params.endDate - End date for the report
   * @param {string} params.sortBy - Sort field
   * @param {string} params.sortOrder - Sort direction (asc/desc)
   * @returns {Promise} API response with sales report data
   */
  async getSalesReport(params = {}) {
    try {
      const queryParams = this._buildQueryParams(params)
      const response = await api.get(`${this.baseUrl}/sales${queryParams}`)
      return response.data
    } catch (error) {
      console.error('Error fetching sales report:', error)
      throw error
    }
  }

  /**
   * Get Products Report
   * @param {Object} params - Query parameters
   * @param {Date} params.startDate - Start date for the report
   * @param {Date} params.endDate - End date for the report
   * @param {string} params.categoryId - Category filter
   * @param {number} params.limit - Page size limit
   * @returns {Promise} API response with products report data
   */
  async getProductsReport(params = {}) {
    try {
      const queryParams = this._buildQueryParams(params)
      const response = await api.get(`${this.baseUrl}/products${queryParams}`)
      return response.data
    } catch (error) {
      console.error('Error fetching products report:', error)
      throw error
    }
  }

  /**
   * Get Users Report
   * @param {Object} params - Query parameters
   * @param {Date} params.startDate - Start date for the report
   * @param {Date} params.endDate - End date for the report
   * @param {string} params.userType - User role filter
   * @returns {Promise} API response with users report data
   */
  async getUsersReport(params = {}) {
    try {
      const queryParams = this._buildQueryParams(params)
      const response = await api.get(`${this.baseUrl}/users${queryParams}`)
      return response.data
    } catch (error) {
      console.error('Error fetching users report:', error)
      throw error
    }
  }

  /**
   * Get Orders Report
   * @param {Object} params - Query parameters
   * @param {Date} params.startDate - Start date for the report
   * @param {Date} params.endDate - End date for the report
   * @param {string} params.status - Order status filter
   * @returns {Promise} API response with orders report data
   */
  async getOrdersReport(params = {}) {
    try {
      const queryParams = this._buildQueryParams(params)
      const response = await api.get(`${this.baseUrl}/orders${queryParams}`)
      return response.data
    } catch (error) {
      console.error('Error fetching orders report:', error)
      throw error
    }
  }

  /**
   * Export Report
   * @param {string} reportType - Type of report (financial, sales, products, users, orders)
   * @param {string} format - Export format (excel, csv)
   * @param {Object} params - Query parameters for filtering
   * @returns {Promise} Blob response for file download
   */
  async exportReport(reportType, format, params = {}) {
    try {
      const queryParams = this._buildQueryParams(params)
      const response = await api.get(
        `${this.baseUrl}/${reportType}/export/${format}${queryParams}`,
        {
          responseType: 'blob'
        }
      )
      return response.data
    } catch (error) {
      console.error(`Error exporting ${reportType} report as ${format}:`, error)
      throw error
    }
  }

  /**
   * Download exported report file
   * @param {Blob} blob - File blob data
   * @param {string} filename - Name for the downloaded file
   */
  downloadFile(blob, filename) {
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }

  /**
   * Export Financial Report
   * @param {string} format - Export format (excel, csv)
   * @param {Object} params - Query parameters
   */
  async exportFinancialReport(format = 'excel', params = {}) {
    const blob = await this.exportReport('financial', format, params)
    const filename = `financial-report-${this._formatDate(new Date())}.${format === 'excel' ? 'xlsx' : 'csv'}`
    this.downloadFile(blob, filename)
  }

  /**
   * Export Sales Report
   * @param {string} format - Export format (excel, csv)
   * @param {Object} params - Query parameters
   */
  async exportSalesReport(format = 'excel', params = {}) {
    const blob = await this.exportReport('sales', format, params)
    const filename = `sales-report-${this._formatDate(new Date())}.${format === 'excel' ? 'xlsx' : 'csv'}`
    this.downloadFile(blob, filename)
  }

  /**
   * Export Products Report
   * @param {string} format - Export format (excel, csv)
   * @param {Object} params - Query parameters
   */
  async exportProductsReport(format = 'excel', params = {}) {
    const blob = await this.exportReport('products', format, params)
    const filename = `products-report-${this._formatDate(new Date())}.${format === 'excel' ? 'xlsx' : 'csv'}`
    this.downloadFile(blob, filename)
  }

  /**
   * Export Users Report
   * @param {string} format - Export format (excel, csv)
   * @param {Object} params - Query parameters
   */
  async exportUsersReport(format = 'excel', params = {}) {
    const blob = await this.exportReport('users', format, params)
    const filename = `users-report-${this._formatDate(new Date())}.${format === 'excel' ? 'xlsx' : 'csv'}`
    this.downloadFile(blob, filename)
  }

  /**
   * Export Orders Report
   * @param {string} format - Export format (excel, csv)
   * @param {Object} params - Query parameters
   */
  async exportOrdersReport(format = 'excel', params = {}) {
    const blob = await this.exportReport('orders', format, params)
    const filename = `orders-report-${this._formatDate(new Date())}.${format === 'excel' ? 'xlsx' : 'csv'}`
    this.downloadFile(blob, filename)
  }

  /**
   * Build query parameters string from object
   * @param {Object} params - Parameters object
   * @returns {string} Query string
   * @private
   */
  _buildQueryParams(params) {
    const searchParams = new URLSearchParams()
    
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        if (params[key] instanceof Date) {
          searchParams.append(key, params[key].toISOString())
        } else {
          searchParams.append(key, params[key].toString())
        }
      }
    })
    
    const queryString = searchParams.toString()
    return queryString ? `?${queryString}` : ''
  }

  /**
   * Format date for filename
   * @param {Date} date - Date to format
   * @returns {string} Formatted date string
   * @private
   */
  _formatDate(date) {
    return date.toISOString().split('T')[0]
  }
}

// Create and export singleton instance
export const reportsService = new ReportsService()
export default reportsService
