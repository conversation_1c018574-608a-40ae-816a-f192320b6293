// Функції для роботи з аутентифікацією

/**
 * Отримує роль поточного користувача
 * @returns {string} Роль користувача у вигляді рядка ('admin', 'moderator', 'seller', 'buyer')
 */
export function getUserRole() {
  try {
    const userStr = localStorage.getItem('user');
    if (!userStr) return null;
    
    const user = JSON.parse(userStr);
    if (!user) return null;
    
    // Отримуємо роль з об'єкта користувача
    const role = user.role;
    
    // Обробляємо різні формати ролі, які можуть прийти з бекенду
    if (typeof role === 'string') {
      // Роль у вигляді рядка "Admin"
      return role.toLowerCase();
    } else if (typeof role === 'number') {
      // Роль у вигляді числа (4 = Admin в enum Role)
      // Buyer = 0, Seller = 1, SellerOwner = 2, Moderator = 3, Admin = 4
      const roleMap = {
        0: 'buyer',
        1: 'seller',
        2: 'sellerowner',
        3: 'moderator',
        4: 'admin'
      };
      return roleMap[role] || 'buyer';
    } else if (role && typeof role === 'object') {
      // Роль у вигляді об'єкта з властивістю value
      if (role.hasOwnProperty('value')) {
        if (typeof role.value === 'string') {
          return role.value.toLowerCase();
        } else if (typeof role.value === 'number') {
          const roleMap = {
            0: 'buyer',
            1: 'seller',
            2: 'sellerowner',
            3: 'moderator',
            4: 'admin'
          };
          return roleMap[role.value] || 'buyer';
        }
      }
      // Роль у вигляді об'єкта з властивістю name
      if (role.hasOwnProperty('name')) {
        return role.name.toLowerCase();
      }
    }
    
    // За замовчуванням повертаємо роль покупця
    return 'buyer';
  } catch (error) {
    console.error('Error getting user role:', error);
    return 'buyer';
  }
}

/**
 * Перевіряє, чи є поточний користувач адміністратором
 * @returns {boolean} true, якщо користувач є адміністратором
 */
export function isAdmin() {
  return getUserRole() === 'admin';
}

/**
 * Перевіряє, чи є поточний користувач модератором
 * @returns {boolean} true, якщо користувач є модератором
 */
export function isModerator() {
  return getUserRole() === 'moderator';
}

/**
 * Перевіряє, чи є поточний користувач продавцем
 * @returns {boolean} true, якщо користувач є продавцем
 */
export function isSeller() {
  const role = getUserRole();
  return role === 'seller' || role === 'sellerowner';
}

/**
 * Перевіряє, чи є поточний користувач покупцем
 * @returns {boolean} true, якщо користувач є покупцем
 */
export function isBuyer() {
  return getUserRole() === 'buyer';
}

/**
 * Перевіряє, чи має поточний користувач доступ до адміністративної панелі
 * @returns {boolean} true, якщо користувач має доступ до адміністративної панелі
 */
export function hasAdminAccess() {
  const role = getUserRole();
  return role === 'admin' || role === 'moderator';
}

/**
 * Отримує ID поточного користувача
 * @returns {string|null} ID користувача або null, якщо користувач не авторизований
 */
export function getUserId() {
  try {
    const userStr = localStorage.getItem('user');
    if (!userStr) return null;
    
    const user = JSON.parse(userStr);
    return user?.id || null;
  } catch (error) {
    console.error('Error getting user ID:', error);
    return null;
  }
}

/**
 * Отримує ім'я користувача
 * @returns {string|null} Ім'я користувача або null, якщо користувач не авторизований
 */
export function getUsername() {
  try {
    const userStr = localStorage.getItem('user');
    if (!userStr) return null;
    
    const user = JSON.parse(userStr);
    return user?.username || null;
  } catch (error) {
    console.error('Error getting username:', error);
    return null;
  }
}

/**
 * Перевіряє, чи авторизований користувач
 * @returns {boolean} true, якщо користувач авторизований
 */
export function isAuthenticated() {
  return !!localStorage.getItem('token');
}

export default {
  getUserRole,
  isAdmin,
  isModerator,
  isSeller,
  isBuyer,
  hasAdminAccess,
  getUserId,
  getUsername,
  isAuthenticated
};
