﻿using AutoMapper;
using Marketplace.Application.Commands.Auth;
using Marketplace.Application.Responses;
using Marketplace.Application.Services.Auth;
using Marketplace.Domain.Repositories;
using Marketplace.Infrastructure.Services.Auth;
using MediatR;

public class LoginCommandHandler : IRequestHandler<LoginCommand, AuthResponse>
{
    private readonly IUserRepository _userRepository;
    private readonly IJwtTokenService _jwtTokenService;
    private readonly IMapper _mapper;
    private readonly IPasswordHasher _passwordHasher;

    public LoginCommandHandler(
        IUserRepository userRepository,
        IJwtTokenService jwtTokenService,
        IMapper mapper,
        IPasswordHasher passwordHasher)
    {
        _userRepository = userRepository;
        _jwtTokenService = jwtTokenService;
        _mapper = mapper;
        _passwordHasher = passwordHasher;
    }

    public async Task<AuthResponse> Handle(LoginCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Try to find user by email or username
            var user = await _userRepository.GetByEmailAsync(request.Email, cancellationToken);

            if (user == null)
            {
                return null;
            }

            bool passwordVerified = VerifyPassword(request.Password, user.Password.Value);

            if (!passwordVerified)
            {
                return null;
            }

            var token = await _jwtTokenService.GenerateTokenAsync(user, cancellationToken);

            return new AuthResponse(token, _mapper.Map<UserResponse>(user));
        }
        catch (Exception)
        {
            return null;
        }
    }

    private bool VerifyPassword(string providedPassword, string hashedPassword)
    {
        // Use our custom password hasher to verify the password
        return _passwordHasher.VerifyPassword(hashedPassword, providedPassword);
    }
}