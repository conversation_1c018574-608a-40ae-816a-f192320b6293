import{_ as M,g as m,x as Y,c as u,o as c,a as s,d as E,z as S,C as O,F as G,p as K,k as W,t as f,n as x,h as X,f as Z,i as ss,b as A,w as F,r as es,D as $,e as ts}from"./index-BKy0rL_2.js";import{a as as,O as ls}from"./OrderStatusUpdate-BfyDb0cm.js";import{p as os}from"./products-A8mTMjnr.js";import{g as ns,a as is,b as rs,c as ds,o as D,i as us,f as cs,p as vs,h as ps,e as R,E as B,u as L}from"./orders-HBUhG7wP.js";const ms={class:"modal-card"},fs={class:"modal-card-body"},ys={class:"field"},_s={class:"control has-icons-left"},hs={key:0,class:"has-text-centered py-4"},gs={key:1,class:"products-list"},bs=["onClick"],Ss={class:"media"},Os={key:0,class:"media-left"},ks={class:"image is-48x48"},As=["src","alt"],xs={key:1,class:"media-left"},Cs={class:"media-content"},Is={class:"content"},$s={class:"has-text-grey"},Ds={class:"tag is-primary is-light"},Es={key:2,class:"has-text-centered py-4"},ws={class:"has-text-grey"},Us={key:3,class:"has-text-centered py-4"},Ns={__name:"ProductSelector",props:{isOpen:{type:Boolean,default:!1}},emits:["close","select"],setup(w,{emit:U}){const C=w,i=U,t=m(""),v=m([]),y=m(!1),g=m(null),r=()=>{i("close"),t.value="",v.value=[]},b=o=>{i("select",o),r()},k=async()=>{if(g.value&&clearTimeout(g.value),t.value.length<2){v.value=[];return}g.value=setTimeout(async()=>{y.value=!0;try{const o=await os.getAll({search:t.value,pageSize:20});o.success&&o.data?v.value=o.data:v.value=[]}catch(o){console.error("Error searching products:",o),v.value=[]}finally{y.value=!1}},300)},_=o=>!o&&o!==0?"$0.00":new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(o),I=o=>o===0?"is-danger":o<10?"is-warning":"is-success",p=o=>{o.target.style.display="none",o.target.parentElement.innerHTML='<div class="placeholder-image"><i class="fas fa-image"></i></div>'};return Y(()=>C.isOpen,o=>{o||(t.value="",v.value=[])}),(o,n)=>(c(),u("div",{class:x(["modal",{"is-active":w.isOpen}])},[s("div",{class:"modal-background",onClick:r}),s("div",ms,[s("header",{class:"modal-card-head"},[n[1]||(n[1]=s("p",{class:"modal-card-title"},[s("span",{class:"icon"},[s("i",{class:"fas fa-search"})]),E(" Select Product ")],-1)),s("button",{class:"delete",onClick:r})]),s("section",fs,[s("div",ys,[s("div",_s,[S(s("input",{class:"input",type:"text",placeholder:"Search products...","onUpdate:modelValue":n[0]||(n[0]=d=>t.value=d),onInput:k},null,544),[[O,t.value]]),n[2]||(n[2]=s("span",{class:"icon is-small is-left"},[s("i",{class:"fas fa-search"})],-1))])]),y.value?(c(),u("div",hs,n[3]||(n[3]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),s("p",{class:"mt-2"},"Loading products...",-1)]))):v.value.length>0?(c(),u("div",gs,[(c(!0),u(G,null,K(v.value,d=>(c(),u("div",{key:d.id,class:"product-item",onClick:P=>b(d)},[s("div",Ss,[d.image?(c(),u("div",Os,[s("figure",ks,[s("img",{src:d.image,alt:d.name,onError:p},null,40,As)])])):(c(),u("div",xs,n[4]||(n[4]=[s("div",{class:"placeholder-image"},[s("i",{class:"fas fa-image"})],-1)]))),s("div",Cs,[s("div",Is,[s("p",null,[s("strong",null,f(d.name),1),n[5]||(n[5]=s("br",null,null,-1)),s("small",$s,f(d.categoryName||"No Category"),1),n[6]||(n[6]=s("br",null,null,-1)),s("span",Ds,f(_(d.priceAmount)),1),d.stock!==void 0?(c(),u("span",{key:0,class:x(["tag",I(d.stock)])}," Stock: "+f(d.stock),3)):W("",!0)])])])])],8,bs))),128))])):!y.value&&t.value?(c(),u("div",Es,[n[7]||(n[7]=s("span",{class:"icon is-large has-text-grey-light"},[s("i",{class:"fas fa-search fa-2x"})],-1)),s("p",ws,'No products found for "'+f(t.value)+'"',1)])):(c(),u("div",Us,n[8]||(n[8]=[s("span",{class:"icon is-large has-text-grey-light"},[s("i",{class:"fas fa-box fa-2x"})],-1),s("p",{class:"has-text-grey"},"Start typing to search for products",-1)])))]),s("footer",{class:"modal-card-foot"},[s("button",{class:"button",onClick:r},"Cancel")])])],2))}},Ps=M(Ns,[["__scopeId","data-v-400f7733"]]),Ts={class:"admin-order-edit"},Vs={class:"level"},Fs={class:"level-left"},Rs={class:"level-item"},Bs={class:"breadcrumb","aria-label":"breadcrumbs"},Ls={class:"level-right"},Ms={class:"level-item"},qs={class:"buttons"},zs=["disabled"],Qs=["disabled"],Hs={key:0,class:"has-text-centered py-6"},Js={key:1,class:"notification is-danger"},js={key:2,class:"notification is-warning"},Ys={key:3},Gs={class:"card mb-4"},Ks={class:"card-content"},Ws={class:"level"},Xs={class:"level-left"},Zs={class:"level-item"},se={class:"title is-4"},ee={class:"subtitle is-6"},te={class:"level-right"},ae={class:"level-item"},le={class:"tags"},oe={class:"columns"},ne={class:"column is-8"},ie={class:"card"},re={class:"card-content"},de={class:"columns"},ue={class:"column is-6"},ce={class:"field"},ve={class:"control"},pe=["value"],me={class:"field"},fe={class:"control"},ye=["value"],_e={class:"field"},he={class:"control"},ge=["value"],be={class:"column is-6"},Se={class:"field"},Oe={class:"control"},ke=["value"],Ae={class:"field"},xe={class:"control"},Ce=["value"],Ie={class:"field"},$e={class:"control"},De=["value"],Ee={class:"card mt-4"},we={class:"card-content"},Ue={class:"columns"},Ne={class:"column is-6"},Pe={class:"field"},Te={class:"control"},Ve={class:"field"},Fe={class:"control"},Re={class:"column is-6"},Be={class:"field"},Le={class:"control"},Me={class:"field"},qe={class:"control"},ze={class:"card mt-4"},Qe={class:"card-content"},He={class:"field"},Je={class:"control"},je={class:"column is-4"},Ye={class:"mt-4"},Ge={__name:"OrderEdit",setup(w){const U=Z(),C=ts(),i=m(null),t=m({}),v=m(!1),y=m(!1),g=m(!1),r=m(null),b=m(!1),k=m(!1),_=X(()=>U.params.id),I=async()=>{if(!_.value){r.value="Order ID is required";return}v.value=!0,r.value=null;try{console.log("Fetching order details for editing, ID:",_.value);const a=await D.getOrderById(_.value);a?(i.value=a,t.value=JSON.parse(JSON.stringify(a)),t.value.shippingAddress||(t.value.shippingAddress={address1:"",city:"",country:""}),(!t.value.items||!Array.isArray(t.value.items))&&(t.value.items=[]),t.value.notes||(t.value.notes=""),console.log("Order details loaded for editing:",a)):r.value="Order not found"}catch(a){console.error("Error fetching order details:",a),r.value=a.message||"Failed to load order details"}finally{v.value=!1}},p=()=>{b.value=!0},o=()=>!t.value.items||!Array.isArray(t.value.items)?0:t.value.items.reduce((e,l)=>e+((l==null?void 0:l.price)||0)*((l==null?void 0:l.quantity)||0),0)+(t.value.shipping||0)-(t.value.discount||0),n=(a,e)=>{t.value.items&&Array.isArray(t.value.items)&&t.value.items[a]&&(t.value.items[a].quantity=e,t.value.items[a].total=t.value.items[a].price*e,p())},d=a=>{t.value.items&&Array.isArray(t.value.items)&&(t.value.items.splice(a,1),p())},P=()=>{k.value=!0},q=a=>{t.value.items||(t.value.items=[]);const e={id:Date.now().toString(),productId:a.id,productName:a.name,quantity:1,price:a.priceAmount||0,total:a.priceAmount||0};t.value.items.push(e),p()},z=async(a,e)=>{g.value=!0;try{await D.updateOrderStatus(a,e),t.value.status=e,p(),R.emit(B.ORDER_UPDATED,{orderId:a,type:"status",newValue:e}),L.markOrdersForRefresh(),console.log("Order status updated successfully")}catch(l){console.error("Error updating order status:",l),r.value="Failed to update order status"}finally{g.value=!1}},Q=async(a,e)=>{g.value=!0;try{console.log("Updating payment status:",{orderId:a,newStatus:e}),await D.updatePaymentStatus(a,e),t.value.paymentStatus=e,p(),console.log("Payment status updated successfully")}catch(l){console.error("Error updating payment status:",l),r.value="Failed to update payment status"}finally{g.value=!1}},H=async()=>{const a=[];if(us(t.value.status)||a.push("Invalid order status"),cs(t.value.paymentStatus)||a.push("Invalid payment status"),(!t.value.items||t.value.items.length===0)&&a.push("Order must have at least one item"),t.value.items.filter(l=>!l.id||!l.quantity||l.quantity<=0||!l.price||l.price<=0).length>0&&a.push("All items must have valid ID, quantity, and price"),a.length>0){r.value="Validation errors: "+a.join(", ");return}y.value=!0;try{const l={...t.value,status:ps(t.value.status),paymentStatus:vs(t.value.paymentStatus)};await D.update(_.value,l),b.value=!1,console.log("✅ Order saved successfully"),R.emit(B.ORDER_UPDATED,{orderId:_.value,type:"full_update",orderData:l}),L.markOrdersForRefresh(),C.push(`/admin/orders/${_.value}/view`)}catch(l){console.error("❌ Error saving order:",l),r.value="Failed to save order changes: "+(l.message||"Unknown error")}finally{y.value=!1}},J=()=>{b.value?confirm("You have unsaved changes. Are you sure you want to cancel?")&&N():N()},N=()=>{C.push(`/admin/orders/${_.value}/view`)},T=a=>a?new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",V=a=>a?new Date(a).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"}):"N/A",j=a=>!a&&a!==0?"$0.00":new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a);return ss(()=>{I()}),(a,e)=>{const l=es("router-link");return c(),u("div",Ts,[s("div",Vs,[s("div",Fs,[s("div",Rs,[s("nav",Bs,[s("ul",null,[s("li",null,[A(l,{to:"/admin/orders"},{default:F(()=>e[6]||(e[6]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-list"})],-1),s("span",null,"Orders",-1)])),_:1})]),s("li",null,[A(l,{to:`/admin/orders/${_.value}/view`},{default:F(()=>e[7]||(e[7]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-eye"})],-1),s("span",null,"View Order",-1)])),_:1},8,["to"])]),e[8]||(e[8]=s("li",{class:"is-active"},[s("a",{href:"#","aria-current":"page"},[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-edit"})]),s("span",null,"Edit Order")])],-1))])])])]),s("div",Ls,[s("div",Ms,[s("div",qs,[s("button",{class:x(["button is-success",{"is-loading":y.value}]),onClick:H,disabled:y.value||!b.value},e[9]||(e[9]=[s("span",{class:"icon"},[s("i",{class:"fas fa-save"})],-1),s("span",null,"Save Changes",-1)]),10,zs),s("button",{class:"button is-light",onClick:J,disabled:y.value},e[10]||(e[10]=[s("span",{class:"icon"},[s("i",{class:"fas fa-times"})],-1),s("span",null,"Cancel",-1)]),8,Qs)])])])]),v.value?(c(),u("div",Hs,e[11]||(e[11]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),s("p",{class:"mt-2"},"Loading order details...",-1)]))):r.value?(c(),u("div",Js,[s("p",null,f(r.value),1),s("button",{class:"button is-light mt-2",onClick:I},e[12]||(e[12]=[s("span",{class:"icon"},[s("i",{class:"fas fa-redo"})],-1),s("span",null,"Retry",-1)]))])):i.value?(c(),u("div",Ys,[s("div",Gs,[s("div",Ks,[s("div",Ws,[s("div",Xs,[s("div",Zs,[s("div",null,[s("h1",se," Edit Order #"+f(i.value.id),1),s("p",ee," Placed on "+f(T(i.value.createdAt))+" at "+f(V(i.value.createdAt)),1)])])]),s("div",te,[s("div",ae,[s("div",le,[s("span",{class:x(["tag is-medium",$(ns)(t.value.status)])},f($(is)(t.value.status)),3),s("span",{class:x(["tag is-medium",$(rs)(t.value.paymentStatus)])},f($(ds)(t.value.paymentStatus)),3)])])])])])]),s("div",oe,[s("div",ne,[s("div",ie,[e[21]||(e[21]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},[s("span",{class:"icon"},[s("i",{class:"fas fa-receipt"})]),E(" Order Information ")])],-1)),s("div",re,[s("div",de,[s("div",ue,[s("div",ce,[e[15]||(e[15]=s("label",{class:"label"},"Order ID",-1)),s("div",ve,[s("input",{class:"input",type:"text",value:i.value.id,readonly:""},null,8,pe)])]),s("div",me,[e[16]||(e[16]=s("label",{class:"label"},"Customer Name",-1)),s("div",fe,[s("input",{class:"input",type:"text",value:i.value.customerName,readonly:""},null,8,ye)])]),s("div",_e,[e[17]||(e[17]=s("label",{class:"label"},"Customer Email",-1)),s("div",he,[s("input",{class:"input",type:"email",value:i.value.customerEmail,readonly:""},null,8,ge)])])]),s("div",be,[s("div",Se,[e[18]||(e[18]=s("label",{class:"label"},"Order Date",-1)),s("div",Oe,[s("input",{class:"input",type:"text",value:T(i.value.createdAt),readonly:""},null,8,ke)])]),s("div",Ae,[e[19]||(e[19]=s("label",{class:"label"},"Order Time",-1)),s("div",xe,[s("input",{class:"input",type:"text",value:V(i.value.createdAt),readonly:""},null,8,Ce)])]),s("div",Ie,[e[20]||(e[20]=s("label",{class:"label"},"Total Amount",-1)),s("div",$e,[s("input",{class:"input",type:"text",value:j(o()),readonly:""},null,8,De)])])])])])]),s("div",Ee,[e[26]||(e[26]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},[s("span",{class:"icon"},[s("i",{class:"fas fa-shipping-fast"})]),E(" Shipping Address ")])],-1)),s("div",we,[s("div",Ue,[s("div",Ne,[s("div",Pe,[e[22]||(e[22]=s("label",{class:"label"},"Address Line",-1)),s("div",Te,[S(s("input",{class:"input",type:"text","onUpdate:modelValue":e[0]||(e[0]=h=>t.value.shippingAddress.address1=h),onInput:p},null,544),[[O,t.value.shippingAddress.address1]])])]),s("div",Ve,[e[23]||(e[23]=s("label",{class:"label"},"City",-1)),s("div",Fe,[S(s("input",{class:"input",type:"text","onUpdate:modelValue":e[1]||(e[1]=h=>t.value.shippingAddress.city=h),onInput:p},null,544),[[O,t.value.shippingAddress.city]])])])]),s("div",Re,[s("div",Be,[e[24]||(e[24]=s("label",{class:"label"},"Country",-1)),s("div",Le,[S(s("input",{class:"input",type:"text","onUpdate:modelValue":e[2]||(e[2]=h=>t.value.shippingAddress.country=h),onInput:p},null,544),[[O,t.value.shippingAddress.country]])])]),s("div",Me,[e[25]||(e[25]=s("label",{class:"label"},"Shipping Method",-1)),s("div",qe,[S(s("input",{class:"input",type:"text","onUpdate:modelValue":e[3]||(e[3]=h=>t.value.shippingMethodName=h),onInput:p},null,544),[[O,t.value.shippingMethodName]])])])])])])]),s("div",ze,[e[27]||(e[27]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},[s("span",{class:"icon"},[s("i",{class:"fas fa-sticky-note"})]),E(" Notes ")])],-1)),s("div",Qe,[s("div",He,[s("div",Je,[S(s("textarea",{class:"textarea","onUpdate:modelValue":e[4]||(e[4]=h=>t.value.notes=h),onInput:p,rows:"3",placeholder:"Add notes about this order..."},null,544),[[O,t.value.notes]])])])])])]),s("div",je,[A(as,{"order-id":i.value.id,"current-order-status":t.value.status,"current-payment-status":t.value.paymentStatus,"customer-name":i.value.customerName,"status-history":i.value.statusHistory||[],loading:g.value,onUpdateOrderStatus:z,onUpdatePaymentStatus:Q},null,8,["order-id","current-order-status","current-payment-status","customer-name","status-history","loading"])])]),s("div",Ye,[A(ls,{items:t.value.items||[],tax:0,shipping:t.value.shipping||0,discount:t.value.discount||0,editable:!0,onUpdateQuantity:n,onRemoveItem:d,onAddItem:P},null,8,["items","shipping","discount"])])])):(c(),u("div",js,[e[14]||(e[14]=s("p",null,"Order not found.",-1)),s("button",{class:"button is-light mt-2",onClick:N},e[13]||(e[13]=[s("span",{class:"icon"},[s("i",{class:"fas fa-arrow-left"})],-1),s("span",null,"Back to Orders",-1)]))])),A(Ps,{"is-open":k.value,onClose:e[5]||(e[5]=h=>k.value=!1),onSelect:q},null,8,["is-open"])])}}},st=M(Ge,[["__scopeId","data-v-3ff5ef23"]]);export{st as default};
