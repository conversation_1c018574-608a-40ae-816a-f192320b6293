<template>
  <div class="admin-user-detail">
    <div class="admin-page-header">
      <div class="admin-header-content">
        <div class="admin-header-left">
          <h1 class="admin-page-title">
            <i class="fas fa-user"></i>
            User Details
          </h1>
        </div>
        <div class="admin-header-right">
          <router-link to="/admin/users" class="admin-btn admin-btn-secondary">
            <i class="fas fa-arrow-left"></i>
            <span>Back to Users</span>
          </router-link>
        </div>
      </div>
    </div>

    <div v-if="loading" class="admin-loading-state">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading user details...</p>
    </div>
    <div v-else-if="error" class="admin-alert admin-alert-danger">
      <button class="admin-alert-close" @click="error = null">
        <i class="fas fa-times"></i>
      </button>
      {{ error }}
    </div>
    <div v-else-if="!user.id" class="admin-alert admin-alert-warning">
      <p>User not found.</p>
      <router-link to="/admin/users" class="admin-btn admin-btn-primary">
        Back to Users
      </router-link>
    </div>
    <div v-else>
      <!-- User Header -->
      <div class="admin-card admin-user-header">
        <div class="admin-card-content">
          <div class="admin-user-header-content">
            <div class="admin-user-info">
              <h2 class="admin-user-title">{{ user.firstName }} {{ user.lastName }}</h2>
              <p class="admin-user-subtitle">{{ user.email }}</p>
              <div class="admin-user-badges">
                <span class="admin-badge" :class="getStatusClass(user.status)">
                  {{ formatStatus(user.status) }}
                </span>
                <span class="admin-badge admin-badge-secondary">
                  {{ formatRole(user.roleString || user.role) }}
                </span>
              </div>
            </div>
            <div class="admin-user-actions">
              <button class="admin-btn admin-btn-primary" @click="openEditModal">
                <i class="fas fa-edit"></i>
                <span>Edit</span>
              </button>
              <button class="admin-btn admin-btn-danger" @click="confirmDelete">
                <i class="fas fa-trash"></i>
                <span>Delete</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="admin-user-content">
        <!-- User Profile -->
        <div class="admin-user-profile">
          <div class="admin-card">
            <div class="admin-card-header">
              <h3 class="admin-card-title">
                <i class="fas fa-user-circle"></i>
                Profile
              </h3>
            </div>
            <div class="admin-card-content">
              <div class="admin-user-avatar">
                <img
                  :src="user.avatar || 'https://via.placeholder.com/150'"
                  :alt="`${user.firstName} ${user.lastName}`"
                  @error="handleImageError">
              </div>

              <div class="admin-info-group">
                <h4 class="admin-info-label">Username</h4>
                <p class="admin-info-value">{{ user.username }}</p>
              </div>

              <div class="admin-info-group">
                <h4 class="admin-info-label">Role</h4>
                <p class="admin-info-value">
                  <span class="admin-badge" :class="getRoleClass(user.roleString || user.role)">
                    {{ formatRole(user.roleString || user.role) }}
                  </span>
                </p>
              </div>

              <div class="admin-info-group">
                <h4 class="admin-info-label">Registered</h4>
                <p class="admin-info-value">{{ formatDate(user.createdAt) }}</p>
              </div>

              <div class="admin-info-group">
                <h4 class="admin-info-label">Last Login</h4>
                <p class="admin-info-value">{{ user.lastSeenAt ? formatDate(user.lastSeenAt) : 'Never' }}</p>
              </div>

              <div class="admin-info-group" v-if="user.emailConfirmed">
                <h4 class="admin-info-label">Email Confirmed</h4>
                <p class="admin-info-value">
                  <span class="admin-badge admin-badge-success">
                    <i class="fas fa-check"></i>
                    <span>{{ formatDate(user.emailConfirmedAt) }}</span>
                  </span>
                </p>
              </div>

              <div class="info-group" v-if="user.isApproved">
                <h3 class="info-label">Approved</h3>
                <p class="info-value">
                  <span class="tag is-success">
                    <span class="icon">
                      <i class="fas fa-check"></i>
                    </span>
                    <span>{{ formatDate(user.approvedAt) }}</span>
                  </span>
                  <br>
                  <small v-if="user.approvedByUsername">by {{ user.approvedByUsername }}</small>
                </p>
              </div>
            </div>
          </div>

          <!-- User Statistics -->
          <div class="card mt-4" v-if="user.statistics">
            <div class="card-header">
              <p class="card-header-title">Statistics</p>
            </div>
            <div class="card-content">
              <div class="columns is-multiline">
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Total Orders</h3>
                    <p class="info-value">{{ user.statistics.totalOrders }}</p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Total Spent</h3>
                    <p class="info-value">{{ formatCurrency(user.statistics.totalSpent) }}</p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Reviews Given</h3>
                    <p class="info-value">{{ user.statistics.totalReviews }}</p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Average Rating</h3>
                    <p class="info-value">{{ user.statistics.averageRatingGiven.toFixed(1) }}/5</p>
                  </div>
                </div>
                <div class="column is-6" v-if="user.statistics.totalCompanies > 0">
                  <div class="info-group">
                    <h3 class="info-label">Companies</h3>
                    <p class="info-value">{{ user.statistics.totalCompanies }}</p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Active Chats</h3>
                    <p class="info-value">{{ user.statistics.activeChats }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Contact Information</p>
            </div>
            <div class="card-content">
              <div class="info-group">
                <h3 class="info-label">Email</h3>
                <p class="info-value">
                  <a :href="`mailto:${user.email}`">{{ user.email }}</a>
                </p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Phone</h3>
                <p class="info-value">{{ user.phone || 'Not provided' }}</p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Address</h3>
                <address class="address">
                  <p v-if="user.address?.address1">{{ user.address.address1 }}</p>
                  <p v-if="user.address?.address2">{{ user.address.address2 }}</p>
                  <p v-if="user.address?.city || user.address?.state || user.address?.postalCode">
                    {{ user.address?.city || '' }}
                    {{ user.address?.city && user.address?.state ? ',' : '' }}
                    {{ user.address?.state || '' }}
                    {{ user.address?.postalCode ? user.address.postalCode : '' }}
                  </p>
                  <p v-if="user.address?.country">{{ user.address.country }}</p>
                  <p v-if="!user.address?.address1">Not provided</p>
                </address>
              </div>
            </div>
          </div>
        </div>

        <!-- User Activity -->
        <div class="column is-8">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Orders</p>
              <div class="card-header-icon">
                <router-link
                  :to="`/admin/orders?userId=${user.id}`"
                  class="button is-small is-primary is-outlined">
                  <span>View All</span>
                  <span class="icon is-small">
                    <i class="fas fa-arrow-right"></i>
                  </span>
                </router-link>
              </div>
            </div>
            <div class="card-content">
              <div v-if="loadingOrders" class="has-text-centered py-4">
                <span class="icon">
                  <i class="fas fa-spinner fa-pulse"></i>
                </span>
                <p class="mt-2">Loading orders...</p>
              </div>
              <div v-else-if="!userOrders.length" class="has-text-centered py-4">
                <span class="icon is-large">
                  <i class="fas fa-shopping-cart fa-2x"></i>
                </span>
                <p class="mt-2">No orders found for this user</p>
              </div>
              <div v-else>
                <div class="table-container">
                  <table class="table is-fullwidth is-hoverable">
                    <thead>
                      <tr>
                        <th>Order ID</th>
                        <th>Date</th>
                        <th>Total</th>
                        <th>Status</th>
                        <th>Items</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="order in userOrders" :key="order.id">
                        <td>{{ order.id }}</td>
                        <td>{{ formatDate(order.createdAt) }}</td>
                        <td>{{ formatCurrency(order.totalAmount) }} {{ order.currency }}</td>
                        <td>
                          <span class="tag" :class="getOrderStatusClass(order.status)">
                            {{ order.status }}
                          </span>
                        </td>
                        <td>
                          <span class="tag is-info">
                            {{ order.itemsCount }} items
                          </span>
                        </td>
                        <td>
                          <div class="buttons are-small">
                            <router-link
                              :to="`/admin/orders/${order.id}`"
                              class="button is-info"
                              title="View">
                              <span class="icon is-small">
                                <i class="fas fa-eye"></i>
                              </span>
                            </router-link>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <!-- Companies -->
          <div class="card mt-4" v-if="user.companies && user.companies.length > 0">
            <div class="card-header">
              <p class="card-header-title">Companies</p>
            </div>
            <div class="card-content">
              <div class="table-container">
                <table class="table is-fullwidth is-hoverable">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Role</th>
                      <th>Status</th>
                      <th>Created</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="company in user.companies" :key="company.id">
                      <td>
                        <strong>{{ company.name }}</strong>
                        <br>
                        <small v-if="company.description">{{ company.description }}</small>
                      </td>
                      <td>
                        <span class="tag" :class="company.isOwner ? 'is-warning' : 'is-info'">
                          {{ company.isOwner ? 'Owner' : 'Member' }}
                        </span>
                      </td>
                      <td>
                        <span class="tag" :class="company.isApproved ? 'is-success' : 'is-warning'">
                          {{ company.isApproved ? 'Approved' : 'Pending' }}
                        </span>
                      </td>
                      <td>{{ formatDate(company.approvedAt) }}</td>
                      <td>
                        <div class="buttons are-small">
                          <router-link
                            :to="`/admin/companies/${company.id}`"
                            class="button is-info"
                            title="View Company">
                            <span class="icon is-small">
                              <i class="fas fa-eye"></i>
                            </span>
                          </router-link>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Reviews -->
          <div class="card mt-4" v-if="user.reviews && user.reviews.length > 0">
            <div class="card-header">
              <p class="card-header-title">Recent Reviews</p>
            </div>
            <div class="card-content">
              <div class="table-container">
                <table class="table is-fullwidth is-hoverable">
                  <thead>
                    <tr>
                      <th>Product</th>
                      <th>Rating</th>
                      <th>Comment</th>
                      <th>Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="review in user.reviews" :key="review.id">
                      <td>
                        <router-link :to="`/admin/products/${review.productId}`">
                          {{ review.productName }}
                        </router-link>
                      </td>
                      <td>
                        <div class="stars">
                          <span v-for="i in 5" :key="i" class="star" :class="{ 'is-active': i <= review.rating }">
                            <i class="fas fa-star"></i>
                          </span>
                        </div>
                      </td>
                      <td>{{ review.comment || 'No comment' }}</td>
                      <td>{{ formatDate(review.createdAt) }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Seller Request -->
          <div class="card mt-4" v-if="user.sellerRequest">
            <div class="card-header">
              <p class="card-header-title">Seller Request</p>
            </div>
            <div class="card-content">
              <div class="columns">
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Status</h3>
                    <p class="info-value">
                      <span class="tag" :class="getSellerRequestStatusClass(user.sellerRequest.status)">
                        {{ user.sellerRequest.status }}
                      </span>
                    </p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Requested</h3>
                    <p class="info-value">{{ formatDate(user.sellerRequest.createdAt) }}</p>
                  </div>
                </div>
                <div class="column is-6" v-if="user.sellerRequest.processedAt">
                  <div class="info-group">
                    <h3 class="info-label">Processed</h3>
                    <p class="info-value">{{ formatDate(user.sellerRequest.processedAt) }}</p>
                  </div>
                </div>
                <div class="column is-6" v-if="user.sellerRequest.processedByUsername">
                  <div class="info-group">
                    <h3 class="info-label">Processed By</h3>
                    <p class="info-value">{{ user.sellerRequest.processedByUsername }}</p>
                  </div>
                </div>
                <div class="column is-12" v-if="user.sellerRequest.reason">
                  <div class="info-group">
                    <h3 class="info-label">Reason</h3>
                    <p class="info-value">{{ user.sellerRequest.reason }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="user.role === 'seller'" class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Seller Information</p>
            </div>
            <div class="card-content">
              <div class="info-group">
                <h3 class="info-label">Store Name</h3>
                <p class="info-value">{{ user.sellerInfo?.storeName || 'Not set' }}</p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Store Description</h3>
                <p class="info-value">{{ user.sellerInfo?.storeDescription || 'Not provided' }}</p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Products</h3>
                <p class="info-value">{{ user.sellerInfo?.productCount || 0 }}</p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Sales</h3>
                <p class="info-value">{{ user.sellerInfo?.salesCount || 0 }} orders</p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Revenue</h3>
                <p class="info-value">{{ formatCurrency(user.sellerInfo?.totalRevenue || 0) }}</p>
              </div>
            </div>
          </div>

          <!-- Activity Log -->
          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Activity Log</p>
            </div>
            <div class="card-content">
              <div v-if="loadingActivity" class="has-text-centered py-4">
                <span class="icon">
                  <i class="fas fa-spinner fa-pulse"></i>
                </span>
                <p class="mt-2">Loading activity...</p>
              </div>
              <div v-else-if="!userActivity.length" class="has-text-centered py-4">
                <p>No activity recorded for this user</p>
              </div>
              <div v-else class="activity-list">
                <div v-for="activity in userActivity" :key="activity.id" class="activity-item">
                  <div class="activity-icon">
                    <span class="icon" :class="getActivityIconClass(activity.type)">
                      <i :class="getActivityIcon(activity.type)"></i>
                    </span>
                  </div>
                  <div class="activity-content">
                    <p class="activity-text">{{ activity.description }}</p>
                    <p class="activity-date">{{ formatDateTime(activity.createdAt) }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal" :class="{ 'is-active': showEditModal }">
      <div class="modal-background" @click="closeEditModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Edit User</p>
          <button class="delete" aria-label="close" @click="closeEditModal"></button>
        </header>
        <section class="modal-card-body">
          <div class="field">
            <label class="label">First Name*</label>
            <div class="control">
              <input
                class="input"
                type="text"
                v-model="userForm.firstName"
                required
                placeholder="Enter first name">
            </div>
          </div>

          <div class="field">
            <label class="label">Last Name*</label>
            <div class="control">
              <input
                class="input"
                type="text"
                v-model="userForm.lastName"
                required
                placeholder="Enter last name">
            </div>
          </div>

          <div class="field">
            <label class="label">Username*</label>
            <div class="control">
              <input
                class="input"
                type="text"
                v-model="userForm.username"
                required
                placeholder="Enter username">
            </div>
          </div>

          <div class="field">
            <label class="label">Email*</label>
            <div class="control">
              <input
                class="input"
                type="email"
                v-model="userForm.email"
                required
                placeholder="Enter email">
            </div>
          </div>

          <div class="field">
            <label class="label">Phone</label>
            <div class="control">
              <input
                class="input"
                type="tel"
                v-model="userForm.phone"
                placeholder="Enter phone number">
            </div>
          </div>

          <div class="field">
            <label class="label">Role*</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="userForm.role" required>
                  <option value="buyer">Buyer</option>
                  <option value="seller">Seller</option>
                  <option value="sellerowner">Seller Owner</option>
                  <option value="moderator">Moderator</option>
                  <option value="admin">Admin</option>
                </select>
              </div>
            </div>
          </div>



          <div class="field">
            <label class="label">New Password</label>
            <div class="control">
              <input
                class="input"
                type="password"
                v-model="userForm.password"
                placeholder="Leave blank to keep current password">
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-primary"
            @click="updateUser"
            :class="{ 'is-loading': saving }"
            :disabled="!isFormValid">
            Update User
          </button>
          <button class="button" @click="closeEditModal">Cancel</button>
        </footer>
      </div>
    </div>



    <!-- Delete Confirmation Modal -->
    <confirm-dialog
      :is-open="showDeleteModal"
      title="Delete User"
      :message="`Are you sure you want to delete ${user.firstName} ${user.lastName}? This action cannot be undone.`"
      confirm-text="Delete"
      cancel-text="Cancel"
      @confirm="deleteUser"
      @cancel="cancelDelete" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { usersService } from '@/admin/services/users';
import { ordersService } from '@/admin/services/orders';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';

const route = useRoute();
const router = useRouter();

// State
const loading = ref(true);
const loadingOrders = ref(false);
const loadingActivity = ref(false);
const error = ref(null);
const user = ref({});
const userOrders = ref([]);
const userActivity = ref([]);

// Edit user state
const showEditModal = ref(false);
const saving = ref(false);
const userForm = reactive({
  firstName: '',
  lastName: '',
  username: '',
  email: '',
  phone: '',
  role: '',
  password: ''
});



// Delete state
const showDeleteModal = ref(false);

// Computed properties
const userId = computed(() => route.params.id);

const isFormValid = computed(() => {
  return userForm.firstName && userForm.lastName && userForm.username && userForm.email && userForm.role;
});

// Fetch user data
const fetchUser = async () => {
  loading.value = true;
  error.value = null;

  try {
    const data = await usersService.getDetailedUserById(userId.value);
    user.value = data;

    // Set related data from detailed response
    if (data.orders) {
      userOrders.value = data.orders;
    }
    if (data.recentNotifications) {
      userActivity.value = data.recentNotifications.map(notification => ({
        id: notification.id,
        type: 'notification',
        description: notification.title,
        createdAt: notification.createdAt
      }));
    }
  } catch (err) {
    console.error('Error fetching user:', err);
    error.value = 'Failed to load user data. Please try again.';
  } finally {
    loading.value = false;
  }
};

// Fetch user orders
const fetchUserOrders = async () => {
  loadingOrders.value = true;

  try {
    const response = await ordersService.getOrdersByCustomer(userId.value, { limit: 5 });

    if (response.orders) {
      userOrders.value = response.orders;
    } else {
      userOrders.value = [];
    }
  } catch (err) {
    console.error('Error fetching user orders:', err);
  } finally {
    loadingOrders.value = false;
  }
};

// Fetch user activity
const fetchUserActivity = async () => {
  loadingActivity.value = true;

  try {
    const activity = await usersService.getUserActivity(userId.value);
    userActivity.value = activity;
  } catch (err) {
    console.error('Error fetching user activity:', err);
  } finally {
    loadingActivity.value = false;
  }
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(dateString));
};

// Format date and time
const formatDateTime = (dateString) => {
  if (!dateString) return '';

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(dateString));
};

// Format currency
const formatCurrency = (value) => {
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH'
  }).format(value);
};

// Convert role to a readable string format
const formatRole = (role) => {
  if (!role) return 'Unknown';

  // Convert role to string and handle different role formats
  let roleStr;

  if (typeof role === 'string') {
    roleStr = role;
  } else if (typeof role === 'number') {
    // Map numeric roles to string values
    // Buyer = 0, Seller = 1, SellerOwner = 2, Moderator = 3, Admin = 4
    const roleMap = {
      0: 'Buyer',
      1: 'Seller',
      2: 'Seller Owner',
      3: 'Moderator',
      4: 'Admin'
    };
    roleStr = roleMap[role] || 'Unknown';
  } else if (role && typeof role === 'object') {
    // Handle case when role is an object
    if (role.hasOwnProperty('name')) {
      roleStr = role.name;
    } else if (role.hasOwnProperty('value')) {
      if (typeof role.value === 'string') {
        roleStr = role.value;
      } else if (typeof role.value === 'number') {
        const roleMap = {
          0: 'Buyer',
          1: 'Seller',
          2: 'Seller Owner',
          3: 'Moderator',
          4: 'Admin'
        };
        roleStr = roleMap[role.value] || 'Unknown';
      } else {
        roleStr = 'Unknown';
      }
    } else {
      roleStr = 'Unknown';
    }
  } else {
    roleStr = 'Unknown';
  }

  // Capitalize first letter
  return roleStr.charAt(0).toUpperCase() + roleStr.slice(1);
};

// Get role class
const getRoleClass = (role) => {
  if (!role) return 'is-light';

  // Convert role to string and handle different role formats
  let roleStr;

  if (typeof role === 'string') {
    roleStr = role;
  } else if (typeof role === 'number') {
    // Map numeric roles to string values
    // Buyer = 0, Seller = 1, SellerOwner = 2, Moderator = 3, Admin = 4
    const roleMap = {
      0: 'buyer',
      1: 'seller',
      2: 'sellerowner',
      3: 'moderator',
      4: 'admin'
    };
    roleStr = roleMap[role] || 'unknown';
  } else if (role && typeof role === 'object') {
    // Handle case when role is an object
    if (role.hasOwnProperty('name')) {
      roleStr = role.name;
    } else if (role.hasOwnProperty('value')) {
      if (typeof role.value === 'string') {
        roleStr = role.value;
      } else if (typeof role.value === 'number') {
        const roleMap = {
          0: 'buyer',
          1: 'seller',
          2: 'sellerowner',
          3: 'moderator',
          4: 'admin'
        };
        roleStr = roleMap[role.value] || 'unknown';
      } else {
        roleStr = 'unknown';
      }
    } else {
      roleStr = 'unknown';
    }
  } else {
    roleStr = 'unknown';
  }

  // Convert to lowercase for case-insensitive comparison
  const roleLower = typeof roleStr === 'string' ? roleStr.toLowerCase() : 'unknown';

  switch (roleLower) {
    case 'admin':
      return 'is-danger';
    case 'seller':
    case 'sellerowner':
      return 'is-info';
    case 'buyer':
      return 'is-success';
    case 'moderator':
      return 'is-warning';
    default:
      return 'is-light';
  }
};

// Get activity icon
const getActivityIcon = (type) => {
  switch (type) {
    case 'login':
      return 'fas fa-sign-in-alt';
    case 'logout':
      return 'fas fa-sign-out-alt';
    case 'order':
      return 'fas fa-shopping-cart';
    case 'payment':
      return 'fas fa-credit-card';
    case 'profile':
      return 'fas fa-user-edit';
    case 'product':
      return 'fas fa-box';
    default:
      return 'fas fa-history';
  }
};

// Get activity icon class
const getActivityIconClass = (type) => {
  switch (type) {
    case 'login':
      return 'has-text-success';
    case 'logout':
      return 'has-text-info';
    case 'order':
      return 'has-text-primary';
    case 'payment':
      return 'has-text-success';
    case 'profile':
      return 'has-text-warning';
    case 'product':
      return 'has-text-info';
    default:
      return 'has-text-grey';
  }
};

// Get order status class
const getOrderStatusClass = (status) => {
  switch (status?.toLowerCase()) {
    case 'pending':
      return 'is-warning';
    case 'processing':
      return 'is-info';
    case 'shipped':
      return 'is-primary';
    case 'delivered':
      return 'is-success';
    case 'cancelled':
      return 'is-danger';
    default:
      return 'is-light';
  }
};

// Get seller request status class
const getSellerRequestStatusClass = (status) => {
  switch (status?.toLowerCase()) {
    case 'pending':
      return 'is-warning';
    case 'approved':
      return 'is-success';
    case 'rejected':
      return 'is-danger';
    default:
      return 'is-light';
  }
};

// Handle image error
const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/150?text=No+Image';
};

// Open edit modal
const openEditModal = () => {
  // Fill form with user data
  userForm.id = user.value.id;
  userForm.firstName = user.value.firstName;
  userForm.lastName = user.value.lastName;
  userForm.username = user.value.username;
  userForm.email = user.value.email;
  userForm.phone = user.value.phone || '';

  // Handle role mapping properly
  let roleValue = '';
  if (user.value.roleString) {
    roleValue = user.value.roleString.toLowerCase();
  } else if (typeof user.value.role === 'string') {
    roleValue = user.value.role.toLowerCase();
  } else if (typeof user.value.role === 'number') {
    // Map numeric roles to string values
    const roleMap = {
      0: 'buyer',
      1: 'seller',
      2: 'sellerowner',
      3: 'moderator',
      4: 'admin'
    };
    roleValue = roleMap[user.value.role] || 'buyer';
  }

  userForm.role = roleValue;
  userForm.password = '';

  showEditModal.value = true;
};

// Close edit modal
const closeEditModal = () => {
  showEditModal.value = false;
};

// Update user
const updateUser = async () => {
  if (!isFormValid.value) return;

  saving.value = true;

  try {
    const userData = {
      firstName: userForm.firstName,
      lastName: userForm.lastName,
      username: userForm.username,
      email: userForm.email,
      phone: userForm.phone,
      role: userForm.role
    };

    // Add password only if it's provided
    if (userForm.password) {
      userData.password = userForm.password;
    }

    const response = await usersService.updateUser(userForm.id, userData);

    if (response && response.success) {
      // Update user data
      user.value = {
        ...user.value,
        ...userData
      };

      // Show success message
      alert('User updated successfully');

      // Close modal
      closeEditModal();
    } else {
      throw new Error('Failed to update user');
    }
  } catch (err) {
    console.error('Error updating user:', err);
    alert('Failed to update user. Please try again.');
  } finally {
    saving.value = false;
  }
};



// Confirm delete
const confirmDelete = () => {
  showDeleteModal.value = true;
};

// Cancel delete
const cancelDelete = () => {
  showDeleteModal.value = false;
};

// Delete user
const deleteUser = async () => {
  try {
    await usersService.deleteUser(userId.value);
    router.push('/admin/users');
  } catch (err) {
    console.error('Error deleting user:', err);
  }
};

// Lifecycle hooks
onMounted(() => {
  fetchUser();
  // fetchUserOrders and fetchUserActivity are now included in fetchUser
});
</script>

<style scoped>
.user-detail {
  padding: 1rem;
}

.title {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.py-4 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.admin-user-detail {
  padding: var(--admin-space-2xl);
  background: var(--admin-bg-primary);
  min-height: 100vh;
}

.admin-user-content {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: var(--admin-space-2xl);
  max-width: 1400px;
  margin: 0 auto;
}

.admin-user-profile {
  position: sticky;
  top: var(--admin-space-2xl);
  height: fit-content;
}

.admin-user-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--admin-space-lg);
}

.admin-user-info {
  flex: 1;
}

.admin-user-title {
  font-size: var(--admin-text-2xl);
  font-weight: var(--admin-font-bold);
  color: var(--admin-gray-900);
  margin: 0 0 var(--admin-space-sm) 0;
}

.admin-user-subtitle {
  font-size: var(--admin-text-base);
  color: var(--admin-gray-600);
  margin: 0 0 var(--admin-space-md) 0;
}

.admin-user-badges {
  display: flex;
  gap: var(--admin-space-sm);
  flex-wrap: wrap;
}

.admin-user-actions {
  display: flex;
  gap: var(--admin-space-sm);
  flex-shrink: 0;
}

.admin-user-avatar {
  width: 120px;
  height: 120px;
  margin: 0 auto var(--admin-space-lg);
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid var(--admin-border-light);
}

.admin-user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.admin-info-group {
  margin-bottom: var(--admin-space-lg);
}

.admin-info-group:last-child {
  margin-bottom: 0;
}

.admin-info-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-700);
  margin: 0 0 var(--admin-space-xs) 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-info-value {
  font-size: var(--admin-text-base);
  color: var(--admin-gray-900);
  margin: 0;
}

.admin-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-600);
}

.admin-spinner {
  font-size: 2rem;
  color: var(--admin-primary);
  margin-bottom: var(--admin-space-md);
}

.admin-loading-text {
  font-size: var(--admin-text-sm);
  margin: 0;
}

@media (max-width: 1024px) {
  .admin-user-content {
    grid-template-columns: 1fr;
    gap: var(--admin-space-lg);
  }

  .admin-user-profile {
    position: static;
  }
}

@media (max-width: 768px) {
  .admin-user-header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .admin-user-actions {
    justify-content: stretch;
  }

  .admin-user-actions .admin-btn {
    flex: 1;
  }
}
</style>
