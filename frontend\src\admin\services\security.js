import api from '@/services/api';

export const securityService = {
  async getLogs(params = {}) {
    try {
      const response = await api.get('/api/admin/security/logs', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching logs:', error);
      throw new Error(error.response?.data?.message || 'Failed to load security logs');
    }
  },

  async getAuditLogs(params = {}) {
    try {
      const response = await api.get('/api/admin/security/audit-logs', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      throw new Error(error.response?.data?.message || 'Failed to load audit logs');
    }
  },

  async getLoginAttempts(params = {}) {
    try {
      const response = await api.get('/api/admin/security/login-attempts', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching login attempts:', error);
      throw new Error(error.response?.data?.message || 'Failed to load login attempts');
    }
  },

  async getSecuritySettings() {
    try {
      const response = await api.get('/api/admin/security/security-settings');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching security settings:', error);
      throw new Error(error.response?.data?.message || 'Failed to load security settings');
    }
  },

  async updateSecuritySettings(settings) {
    try {
      const response = await api.put('/api/admin/security/security-settings', settings);
      return response.data;
    } catch (error) {
      console.error('Error updating security settings:', error);
      throw new Error(error.response?.data?.message || 'Failed to update security settings');
    }
  },

  async getSecurityStats() {
    try {
      const response = await api.get('/api/admin/security/stats');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching security stats:', error);
      throw new Error(error.response?.data?.message || 'Failed to load security statistics');
    }
  },

  async blockIpAddress(ipAddress, reason) {
    try {
      const response = await api.post('/api/admin/security/block-ip', { ipAddress, reason });
      return response.data;
    } catch (error) {
      console.error('Error blocking IP address:', error);
      throw new Error(error.response?.data?.message || 'Failed to block IP address');
    }
  },

  async unblockIpAddress(ipAddress) {
    try {
      const response = await api.post('/api/admin/security/unblock-ip', { ipAddress });
      return response.data;
    } catch (error) {
      console.error('Error unblocking IP address:', error);
      throw new Error(error.response?.data?.message || 'Failed to unblock IP address');
    }
  },

  async getBlockedIps(params = {}) {
    try {
      const response = await api.get('/api/admin/security/blocked-ips', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching blocked IPs:', error);
      throw new Error(error.response?.data?.message || 'Failed to load blocked IP addresses');
    }
  },

  async exportLogs(params = {}) {
    try {
      const response = await api.get('/api/admin/security/export-logs', { 
        params,
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting logs:', error);
      throw new Error(error.response?.data?.message || 'Failed to export logs');
    }
  },

  async clearLogs(logType, olderThan) {
    try {
      const response = await api.post('/api/admin/security/clear-logs', { logType, olderThan });
      return response.data;
    } catch (error) {
      console.error('Error clearing logs:', error);
      throw new Error(error.response?.data?.message || 'Failed to clear logs');
    }
  },

  async runSecurityScan() {
    try {
      const response = await api.post('/api/admin/security/scan');
      return response.data;
    } catch (error) {
      console.error('Error running security scan:', error);
      throw new Error(error.response?.data?.message || 'Failed to run security scan');
    }
  },

  async getSecurityScanResults() {
    try {
      const response = await api.get('/api/admin/security/scan-results');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching security scan results:', error);
      throw new Error(error.response?.data?.message || 'Failed to load security scan results');
    }
  }
};
