/* ===== ADMIN PRODUCTS PAGE STYLES ===== */
/* Based on Reports page design patterns */

/* ===== PRODUCTS PAGE LAYOUT ===== */
.admin-products {
  padding: var(--admin-space-2xl);
  background-color: var(--admin-bg-primary);
  min-height: 100vh;
}

.admin-products .admin-page-header {
  background: var(--admin-gradient-primary);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-3xl);
  margin-bottom: var(--admin-space-2xl);
  color: white;
  box-shadow: var(--admin-shadow-lg);
}

.admin-products .admin-page-title {
  font-size: var(--admin-text-4xl);
  font-weight: var(--admin-font-bold);
  color: white;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-lg);
}

/* ===== PRODUCT FILTERS ===== */
.admin-product-filters {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  margin-bottom: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
}

.admin-product-filters .field {
  margin-bottom: var(--admin-space-lg);
}

.admin-product-filters .label {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-700);
  margin-bottom: var(--admin-space-sm);
  font-size: var(--admin-text-sm);
}

.admin-product-filters .input,
.admin-product-filters .select select {
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-md);
  font-size: var(--admin-text-sm);
  transition: all var(--admin-transition-fast);
}

.admin-product-filters .input:focus,
.admin-product-filters .select select:focus {
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px var(--admin-primary-bg);
  outline: none;
}

.admin-product-filters .control.has-icons-left .icon {
  color: var(--admin-gray-400);
}

.admin-product-filters .buttons {
  margin-top: var(--admin-space-xl);
  display: flex;
  justify-content: center;
  gap: var(--admin-space-md);
}

/* ===== PRODUCT TABLE ===== */
.admin-product-table {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  margin-bottom: var(--admin-space-2xl);
}

.admin-product-table .table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.admin-product-table .table th {
  background: var(--admin-gray-50);
  color: var(--admin-gray-700);
  font-weight: var(--admin-font-semibold);
  padding: var(--admin-space-lg);
  text-align: left;
  border-bottom: 2px solid var(--admin-border-color);
  font-size: var(--admin-text-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-product-table .table td {
  padding: var(--admin-space-lg);
  border-bottom: 1px solid var(--admin-border-light);
  vertical-align: middle;
  font-size: var(--admin-text-sm);
}

.admin-product-table .table tr:hover {
  background: var(--admin-gray-25);
}

.admin-product-table .table tr:last-child td {
  border-bottom: none;
}

/* ===== PRODUCT IMAGE CELL ===== */
.admin-product-image {
  width: 48px;
  height: 48px;
  border-radius: var(--admin-radius-lg);
  object-fit: cover;
  border: 2px solid var(--admin-border-color);
  background: var(--admin-gray-100);
}

.image-cell {
  width: 60px;
  text-align: center;
}

/* ===== PRODUCT INFO CELL ===== */
.admin-product-info {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-product-name {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin-bottom: var(--admin-space-xs);
  line-height: 1.4;
}

.admin-product-sku {
  font-size: var(--admin-text-xs);
  color: var(--admin-gray-500);
  font-family: var(--admin-font-mono);
  background: var(--admin-gray-100);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  display: inline-block;
  width: fit-content;
}

.product-name {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin-bottom: var(--admin-space-xs);
}

.product-sku {
  font-size: var(--admin-text-xs);
  color: var(--admin-gray-500);
  font-family: var(--admin-font-mono);
}

/* ===== PRODUCT PRICE ===== */
.admin-product-price {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-success-dark);
  background: var(--admin-success-bg);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-md);
  border: 1px solid var(--admin-success-light);
  display: inline-block;
}

/* ===== PRODUCT STOCK ===== */
.admin-product-stock {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-full);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-product-stock.in-stock {
  background: var(--admin-success-bg);
  color: var(--admin-success-dark);
  border: 1px solid var(--admin-success-light);
}

.admin-product-stock.low-stock {
  background: var(--admin-warning-bg);
  color: var(--admin-warning-dark);
  border: 1px solid var(--admin-warning-light);
}

.admin-product-stock.out-of-stock {
  background: var(--admin-danger-bg);
  color: var(--admin-danger-dark);
  border: 1px solid var(--admin-danger-light);
}

/* ===== PRODUCT STATUS ===== */
.admin-product-status {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-full);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-product-status.approved {
  background: var(--admin-success-bg);
  color: var(--admin-success-dark);
  border: 1px solid var(--admin-success-light);
}

.admin-product-status.pending {
  background: var(--admin-warning-bg);
  color: var(--admin-warning-dark);
  border: 1px solid var(--admin-warning-light);
}

.admin-product-status.rejected {
  background: var(--admin-danger-bg);
  color: var(--admin-danger-dark);
  border: 1px solid var(--admin-danger-light);
}

/* ===== PRODUCT ACTION BUTTONS ===== */
.admin-product-actions {
  display: flex;
  gap: var(--admin-space-xs);
  align-items: center;
}

.admin-product-actions .button {
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-md);
  border: 1px solid transparent;
  font-size: var(--admin-text-xs);
  transition: all var(--admin-transition-fast);
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-product-actions .button.is-info {
  background: var(--admin-info);
  color: white;
  border-color: var(--admin-info);
}

.admin-product-actions .button.is-info:hover {
  background: var(--admin-info-dark);
  border-color: var(--admin-info-dark);
  transform: translateY(-1px);
}

.admin-product-actions .button.is-primary {
  background: var(--admin-primary);
  color: white;
  border-color: var(--admin-primary);
}

.admin-product-actions .button.is-primary:hover {
  background: var(--admin-primary-dark);
  border-color: var(--admin-primary-dark);
  transform: translateY(-1px);
}

.admin-product-actions .button.is-danger {
  background: var(--admin-danger);
  color: white;
  border-color: var(--admin-danger);
}

.admin-product-actions .button.is-danger:hover {
  background: var(--admin-danger-dark);
  border-color: var(--admin-danger-dark);
  transform: translateY(-1px);
}

/* ===== LOADING STATES ===== */
.admin-product-table .admin-loading {
  text-align: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.admin-product-table .admin-loading i {
  font-size: 2rem;
  color: var(--admin-primary);
  margin-bottom: var(--admin-space-lg);
  animation: spin 1s linear infinite;
}

.admin-product-table .admin-empty {
  text-align: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.admin-product-table .admin-empty i {
  font-size: 3rem;
  color: var(--admin-gray-300);
  margin-bottom: var(--admin-space-lg);
}

.admin-product-table .admin-empty-text {
  font-size: var(--admin-text-lg);
  margin-bottom: var(--admin-space-sm);
}

.admin-product-table .admin-empty-subtext {
  color: var(--admin-gray-400);
  font-size: var(--admin-text-sm);
}

/* ===== PAGINATION WRAPPER ===== */
.admin-products .pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: var(--admin-space-2xl);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .admin-product-table .table {
    font-size: var(--admin-text-xs);
  }
  
  .admin-product-table .table th,
  .admin-product-table .table td {
    padding: var(--admin-space-md);
  }
  
  .admin-product-image {
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 768px) {
  .admin-products {
    padding: var(--admin-space-lg);
  }
  
  .admin-products .admin-page-header {
    padding: var(--admin-space-2xl);
  }
  
  .admin-products .admin-page-title {
    font-size: var(--admin-text-2xl);
  }
  
  .admin-product-filters {
    padding: var(--admin-space-lg);
  }
  
  .admin-product-table {
    padding: var(--admin-space-lg);
    overflow-x: auto;
  }
  
  .admin-product-table .table {
    min-width: 700px;
  }
  
  .admin-product-actions {
    flex-direction: column;
    gap: var(--admin-space-xs);
  }
  
  .admin-product-actions .button {
    width: 100%;
    min-width: auto;
  }
}
