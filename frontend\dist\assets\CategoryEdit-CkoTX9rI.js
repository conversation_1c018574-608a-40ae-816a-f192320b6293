import{_ as H,g as r,B as J,h as Q,x as W,i as K,f as X,c as i,a as e,b,w as h,r as Y,k as _,d as C,t as g,m as P,H as Z,z as m,C as y,n as f,F as ee,p as se,I as T,e as te,o as n}from"./index-BKy0rL_2.js";const ae={class:"category-edit"},le={key:0,class:"has-text-centered py-6"},oe={key:1,class:"has-text-centered py-6"},ie={key:2},ne={class:"level mb-5"},re={class:"level-left"},de={class:"level-item"},ue={class:"breadcrumb","aria-label":"breadcrumbs"},ce={class:"level-right"},pe={class:"level-item"},ve={class:"buttons"},ge={key:0,class:"notification is-danger"},fe={key:1,class:"notification is-success"},me={class:"card"},ye={class:"card-content"},be={class:"field"},he={class:"control"},_e={class:"field"},Ce={class:"control"},we={class:"field"},xe={class:"control"},ke={class:"field"},Ie={class:"control"},Ue={class:"dropdown-trigger"},De={class:"field has-addons"},Se={class:"control is-expanded"},Pe={class:"control"},Te={class:"icon"},Ve={class:"dropdown-menu",role:"menu"},Ee={class:"dropdown-content"},Fe=["onMousedown"],Me={class:"category-item"},Be={class:"category-name"},Oe={class:"category-slug has-text-grey is-size-7"},$e={key:0,class:"help"},Le={class:"field"},ze={key:0,class:"mb-3"},Ne={class:"image is-128x128"},Re=["src","alt"],qe={class:"tabs is-small"},Ae={key:1,class:"field"},Ge={class:"file has-name is-fullwidth"},je={class:"file-label"},He={key:0,class:"file-name"},Je={key:2,class:"field"},Qe={class:"control"},We={class:"field"},Ke={class:"control"},Xe={class:"field"},Ye={class:"control"},Ze={class:"help"},es={class:"field"},ss={class:"control"},ts={class:"help"},as={class:"card-footer"},ls={class:"card-footer-item"},os={class:"buttons is-fullwidth"},is=["disabled"],ns={__name:"CategoryEdit",setup(rs){const V=X(),$=te(),I=r(!1),c=r(""),x=r(""),U=r(!1),u=r(null),k=r([]),t=J({name:"",slug:"",description:"",parentId:"",imageUrl:"",displayOrder:0,metaTitle:"",metaDescription:""}),w=r("file"),D=r(null),S=r(null),p=r(!1),d=r(""),v=r(null),L=Q(()=>{const a=k.value.filter(o=>{var l;return o.id!==((l=u.value)==null?void 0:l.id)});if(!d.value.trim())return a;const s=d.value.toLowerCase().trim();return a.filter(o=>o.name.toLowerCase().includes(s)||o.slug.toLowerCase().includes(s))}),E=async a=>{I.value=!0,c.value="";try{const s=await T.getById(a);if(u.value=s,Object.keys(t).forEach(o=>{o in s&&(t[o]=s[o]||"")}),s.parentId&&k.value.length>0){const o=k.value.find(l=>l.id===s.parentId);o&&(v.value=o,d.value=o.name)}}catch(s){console.error("Error fetching category:",s),c.value="Failed to load category. Please try again."}finally{I.value=!1}},z=async()=>{try{const a=await T.getAll({pageSize:1e3});k.value=a.data||[]}catch(a){console.error("Error fetching categories:",a)}},N=()=>{var a;t.name&&(!t.slug||t.slug===F(((a=u.value)==null?void 0:a.name)||""))&&(t.slug=F(t.name))},F=a=>a.toString().toLowerCase().trim().replace(/\s+/g,"-").replace(/&/g,"-and-").replace(/[^\w\-]+/g,"").replace(/\-\-+/g,"-").replace(/^-+/,"").replace(/-+$/,""),R=a=>{const s=a.target.files[0];if(!s)return;if(!s.type.startsWith("image/")){c.value="Please select an image file";return}if(s.size>5*1024*1024){c.value="File size must be less than 5MB";return}D.value=s;const o=new FileReader;o.onload=l=>{t.imageUrl=l.target.result},o.readAsDataURL(s)},q=()=>{t.imageUrl="",D.value=null,S.value&&(S.value.value="")},M=a=>{a?(v.value=a,t.parentId=a.id,d.value=a.name):(v.value=null,t.parentId="",d.value=""),p.value=!1},B=()=>{p.value=!0},A=()=>{p.value?p.value=!1:(v.value&&d.value===v.value.name&&(d.value=""),B())},G=()=>{p.value||(p.value=!0)},j=()=>{setTimeout(()=>{p.value=!1,v.value?d.value=v.value.name:d.value=""},200)},O=async()=>{c.value="",x.value="",U.value=!0;try{const a={...t};a.displayOrder=parseInt(a.displayOrder)||0,a.parentId===""&&(a.parentId=null),await T.updateCategory(u.value.id,a),x.value="Category updated successfully!",setTimeout(()=>{$.push(`/admin/categories/${u.value.id}`)},1500)}catch(a){console.error("Error updating category:",a),c.value="Failed to update category. Please try again."}finally{U.value=!1}};return W(()=>V.params.id,a=>{a&&k.value.length>0&&E(a)}),K(async()=>{await z();const a=V.params.id;a&&await E(a)}),(a,s)=>{const o=Y("router-link");return n(),i("div",ae,[I.value?(n(),i("div",le,s[13]||(s[13]=[e("div",{class:"loader-wrapper"},[e("div",{class:"loader is-loading"}),e("p",{class:"mt-3"},"Loading category...")],-1)]))):!u.value&&!I.value?(n(),i("div",oe,[s[15]||(s[15]=e("span",{class:"icon is-large has-text-grey-light"},[e("i",{class:"fas fa-folder-open fa-3x"})],-1)),s[16]||(s[16]=e("p",{class:"title is-5 has-text-grey"},"Category not found",-1)),s[17]||(s[17]=e("p",{class:"subtitle is-6 has-text-grey"},"The requested category does not exist",-1)),b(o,{to:"/admin/categories",class:"button is-primary"},{default:h(()=>s[14]||(s[14]=[e("span",{class:"icon"},[e("i",{class:"fas fa-arrow-left"})],-1),e("span",null,"Back to Categories",-1)])),_:1})])):(n(),i("div",ie,[e("div",ne,[e("div",re,[e("div",de,[e("div",null,[e("nav",ue,[e("ul",null,[e("li",null,[b(o,{to:"/admin"},{default:h(()=>s[18]||(s[18]=[C("Dashboard")])),_:1})]),e("li",null,[b(o,{to:"/admin/categories"},{default:h(()=>s[19]||(s[19]=[C("Categories")])),_:1})]),e("li",null,[b(o,{to:`/admin/categories/${u.value.id}`},{default:h(()=>[C(g(u.value.name),1)]),_:1},8,["to"])]),s[20]||(s[20]=e("li",{class:"is-active"},[e("a",{href:"#","aria-current":"page"},"Edit")],-1))])]),s[21]||(s[21]=e("h1",{class:"title is-4"},"Edit Category",-1)),s[22]||(s[22]=e("p",{class:"subtitle is-6"},"Update category information",-1))])])]),e("div",ce,[e("div",pe,[e("div",ve,[b(o,{to:`/admin/categories/${u.value.id}`,class:"button is-light"},{default:h(()=>s[23]||(s[23]=[e("span",{class:"icon"},[e("i",{class:"fas fa-eye"})],-1),e("span",null,"View Category",-1)])),_:1},8,["to"]),b(o,{to:"/admin/categories",class:"button is-light"},{default:h(()=>s[24]||(s[24]=[e("span",{class:"icon"},[e("i",{class:"fas fa-arrow-left"})],-1),e("span",null,"Back to Categories",-1)])),_:1})])])])]),c.value?(n(),i("div",ge,[e("button",{class:"delete",onClick:s[0]||(s[0]=l=>c.value="")}),C(" "+g(c.value),1)])):_("",!0),x.value?(n(),i("div",fe,[e("button",{class:"delete",onClick:s[1]||(s[1]=l=>x.value="")}),C(" "+g(x.value),1)])):_("",!0),e("div",me,[s[43]||(s[43]=e("div",{class:"card-header"},[e("p",{class:"card-header-title"},[e("span",{class:"icon"},[e("i",{class:"fas fa-edit"})]),e("span",null,"Category Information")])],-1)),e("div",ye,[e("form",{onSubmit:P(O,["prevent"])},[e("div",be,[s[25]||(s[25]=e("label",{class:"label"},[C("Name "),e("span",{class:"has-text-danger"},"*")],-1)),e("div",he,[m(e("input",{class:"input",type:"text",placeholder:"Category name","onUpdate:modelValue":s[2]||(s[2]=l=>t.name=l),onInput:N,required:""},null,544),[[y,t.name]])])]),e("div",_e,[s[26]||(s[26]=e("label",{class:"label"},[C("Slug "),e("span",{class:"has-text-danger"},"*")],-1)),e("div",Ce,[m(e("input",{class:"input",type:"text",placeholder:"category-slug","onUpdate:modelValue":s[3]||(s[3]=l=>t.slug=l),required:""},null,512),[[y,t.slug]])]),s[27]||(s[27]=e("p",{class:"help"},"URL-friendly version of the name. Auto-generated but can be edited.",-1))]),e("div",we,[s[28]||(s[28]=e("label",{class:"label"},"Description",-1)),e("div",xe,[m(e("textarea",{class:"textarea",placeholder:"Category description","onUpdate:modelValue":s[4]||(s[4]=l=>t.description=l),rows:"3"},null,512),[[y,t.description]])])]),e("div",ke,[s[30]||(s[30]=e("label",{class:"label"},"Parent Category",-1)),e("div",Ie,[e("div",{class:f(["dropdown",{"is-active":p.value}])},[e("div",Ue,[e("div",De,[e("div",Se,[m(e("input",{class:"input",type:"text",placeholder:"Search for parent category (leave empty for top level)...","onUpdate:modelValue":s[5]||(s[5]=l=>d.value=l),onInput:G,onFocus:B,onBlur:j},null,544),[[y,d.value]])]),e("div",Pe,[e("button",{class:"button",type:"button",onClick:A},[e("span",Te,[e("i",{class:f(["fas fa-chevron-down",{"fa-rotate-180":p.value}])},null,2)])])])])]),e("div",Ve,[e("div",Ee,[e("a",{class:f(["dropdown-item",{"is-active":!t.parentId}]),onMousedown:s[6]||(s[6]=P(l=>M(null),["prevent"]))},s[29]||(s[29]=[e("div",{class:"category-item"},[e("div",{class:"category-name"},"None (Top Level)"),e("div",{class:"category-slug has-text-grey is-size-7"},"Root category")],-1)]),34),(n(!0),i(ee,null,se(L.value,l=>(n(),i("a",{key:l.id,class:f(["dropdown-item",{"is-active":t.parentId===l.id}]),onMousedown:P(ds=>M(l),["prevent"])},[e("div",Me,[e("div",Be,g(l.name),1),e("div",Oe,g(l.slug),1)])],42,Fe))),128))])])],2)]),v.value?(n(),i("p",$e," Selected: "+g(v.value.name),1)):_("",!0)]),e("div",Le,[s[35]||(s[35]=e("label",{class:"label"},"Category Image",-1)),t.imageUrl?(n(),i("div",ze,[e("figure",Ne,[e("img",{src:t.imageUrl,alt:t.name,class:"is-rounded"},null,8,Re)]),e("button",{type:"button",class:"button is-small is-danger mt-2",onClick:q},s[31]||(s[31]=[e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1),e("span",null,"Remove Image",-1)]))])):_("",!0),e("div",qe,[e("ul",null,[e("li",{class:f({"is-active":w.value==="file"})},[e("a",{onClick:s[7]||(s[7]=l=>w.value="file")},"Upload File")],2),e("li",{class:f({"is-active":w.value==="url"})},[e("a",{onClick:s[8]||(s[8]=l=>w.value="url")},"Image URL")],2)])]),w.value==="file"?(n(),i("div",Ae,[e("div",Ge,[e("label",je,[e("input",{class:"file-input",type:"file",accept:"image/*",onChange:R,ref_key:"fileInput",ref:S},null,544),s[32]||(s[32]=e("span",{class:"file-cta"},[e("span",{class:"icon"},[e("i",{class:"fas fa-upload"})]),e("span",{class:"file-label"},"Choose image...")],-1)),D.value?(n(),i("span",He,g(D.value.name),1)):_("",!0)])]),s[33]||(s[33]=e("p",{class:"help"},"Supported formats: JPG, PNG, GIF. Max size: 5MB",-1))])):_("",!0),w.value==="url"?(n(),i("div",Je,[e("div",Qe,[m(e("input",{class:"input",type:"url",placeholder:"https://example.com/image.jpg","onUpdate:modelValue":s[9]||(s[9]=l=>t.imageUrl=l)},null,512),[[y,t.imageUrl]])]),s[34]||(s[34]=e("p",{class:"help"},"Enter a direct link to an image",-1))])):_("",!0)]),e("div",We,[s[36]||(s[36]=e("label",{class:"label"},"Display Order",-1)),e("div",Ke,[m(e("input",{class:"input",type:"number",min:"0",placeholder:"0","onUpdate:modelValue":s[10]||(s[10]=l=>t.displayOrder=l)},null,512),[[y,t.displayOrder,void 0,{number:!0}]])]),s[37]||(s[37]=e("p",{class:"help"},"Categories with lower numbers will be displayed first.",-1))]),s[40]||(s[40]=Z('<div class="field" data-v-40b8e038><label class="label" data-v-40b8e038><span class="icon-text" data-v-40b8e038><span class="icon" data-v-40b8e038><i class="fas fa-search" data-v-40b8e038></i></span><span data-v-40b8e038>SEO Settings</span></span></label></div>',1)),e("div",Xe,[s[38]||(s[38]=e("label",{class:"label"},"Meta Title",-1)),e("div",Ye,[m(e("input",{class:"input",type:"text",placeholder:"SEO title for search engines","onUpdate:modelValue":s[11]||(s[11]=l=>t.metaTitle=l),maxlength:"60"},null,512),[[y,t.metaTitle]])]),e("p",Ze,[e("span",{class:f({"has-text-danger":t.metaTitle&&t.metaTitle.length>60})},g(t.metaTitle?t.metaTitle.length:0)+"/60 characters ",3)])]),e("div",es,[s[39]||(s[39]=e("label",{class:"label"},"Meta Description",-1)),e("div",ss,[m(e("textarea",{class:"textarea",placeholder:"SEO description for search engines","onUpdate:modelValue":s[12]||(s[12]=l=>t.metaDescription=l),maxlength:"160",rows:"3"},null,512),[[y,t.metaDescription]])]),e("p",ts,[e("span",{class:f({"has-text-danger":t.metaDescription&&t.metaDescription.length>160})},g(t.metaDescription?t.metaDescription.length:0)+"/160 characters ",3)])])],32)]),e("footer",as,[e("div",ls,[e("div",os,[e("button",{class:f(["button is-primary is-fullwidth",{"is-loading":U.value}]),onClick:O,disabled:U.value},s[41]||(s[41]=[e("span",{class:"icon"},[e("i",{class:"fas fa-save"})],-1),e("span",null,"Update Category",-1)]),10,is),b(o,{to:`/admin/categories/${u.value.id}`,class:"button is-light is-fullwidth"},{default:h(()=>s[42]||(s[42]=[e("span",{class:"icon"},[e("i",{class:"fas fa-times"})],-1),e("span",null,"Cancel",-1)])),_:1},8,["to"])])])])])]))])}}},cs=H(ns,[["__scopeId","data-v-40b8e038"]]);export{cs as default};
