# Marketplace Frontend

This is the Vue.js frontend for the Marketplace application.

## Features

- User authentication with JWT
- Registration with validation
- Login with validation
- User dashboard
- Admin dashboard
- Profile management

## Project Setup

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## Authentication Flow

1. **Registration**: Users can register with their name, email, and password. The form includes validation for all fields.
2. **Email Verification**: After registration, users receive an email with a verification link.
3. **Login**: Users can log in with their credentials. The system validates the input and sends credentials to the backend.
4. **JWT Storage**: Upon successful login, the JWT token is stored in localStorage.
5. **Authorization**: The token is included in the Authorization header for protected API requests.
6. **Role-based Redirection**: After login, users are redirected based on their role (admin to admin dashboard, others to user dashboard).

## Project Structure

- `src/views/`: Page components
- `src/components/`: Reusable UI components
- `src/services/`: API services
- `src/store/`: Vuex store modules
- `src/router/`: Vue Router configuration
- `src/assets/`: Static assets like CSS and images

## API Integration

The frontend communicates with the backend API at `http://localhost:5296`. API requests are proxied through Vite's development server.
