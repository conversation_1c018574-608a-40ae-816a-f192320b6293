<template>
  <div class="admin-page">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <h1 class="admin-page-title">
          <i class="fas fa-vial admin-page-icon"></i>
          Admin Design System Test
        </h1>
        <p class="admin-page-subtitle">Testing all admin design components and functionality</p>
      </div>
      <div class="admin-page-actions">
        <button class="admin-btn admin-btn-primary" @click="testFunction">
          <i class="fas fa-play"></i>
          Run Tests
        </button>
        <button class="admin-btn admin-btn-secondary" @click="resetTests">
          <i class="fas fa-redo"></i>
          Reset
        </button>
      </div>
    </div>

    <!-- Stats Cards Test -->
    <div class="admin-stats-grid">
      <div class="admin-stat-card">
        <div class="admin-stat-content">
          <div class="admin-stat-info">
            <div class="admin-stat-label">Total Users</div>
            <div class="admin-stat-value">1,234</div>
          </div>
          <div class="admin-stat-icon admin-stat-icon-primary">
            <i class="fas fa-users"></i>
          </div>
        </div>
      </div>
      
      <div class="admin-stat-card">
        <div class="admin-stat-content">
          <div class="admin-stat-info">
            <div class="admin-stat-label">Orders</div>
            <div class="admin-stat-value">567</div>
          </div>
          <div class="admin-stat-icon admin-stat-icon-success">
            <i class="fas fa-shopping-cart"></i>
          </div>
        </div>
      </div>
      
      <div class="admin-stat-card">
        <div class="admin-stat-content">
          <div class="admin-stat-info">
            <div class="admin-stat-label">Revenue</div>
            <div class="admin-stat-value">$89,012</div>
          </div>
          <div class="admin-stat-icon admin-stat-icon-warning">
            <i class="fas fa-dollar-sign"></i>
          </div>
        </div>
      </div>
      
      <div class="admin-stat-card">
        <div class="admin-stat-content">
          <div class="admin-stat-info">
            <div class="admin-stat-label">Products</div>
            <div class="admin-stat-value">345</div>
          </div>
          <div class="admin-stat-icon admin-stat-icon-info">
            <i class="fas fa-box"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- SearchAndFilters Test -->
    <search-and-filters
      v-model:search="testSearch"
      v-model:filters="testFilters"
      :filter-options="testFilterOptions"
      :loading="testLoading"
      @clear-filters="clearTestFilters"
      @apply-filters="applyTestFilters">
    </search-and-filters>

    <!-- Table Test -->
    <div class="admin-card">
      <div class="admin-card-header">
        <h3 class="admin-card-title">
          <i class="fas fa-table"></i>
          Test Data Table
        </h3>
      </div>
      <div class="admin-card-content">
        <!-- Loading State Test -->
        <div v-if="testLoading" class="admin-loading-state">
          <div class="admin-spinner">
            <i class="fas fa-spinner fa-pulse"></i>
          </div>
          <p class="admin-loading-text">Loading test data...</p>
        </div>

        <!-- Error State Test -->
        <div v-else-if="testError" class="admin-error-state">
          <div class="admin-error-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <h3 class="admin-error-title">Test Error</h3>
          <p class="admin-error-message">This is a test error message</p>
          <button class="admin-btn admin-btn-primary" @click="testError = false">
            <i class="fas fa-redo"></i>
            Retry
          </button>
        </div>

        <!-- Empty State Test -->
        <div v-else-if="testData.length === 0" class="admin-empty-state">
          <div class="admin-empty-icon">
            <i class="fas fa-inbox"></i>
          </div>
          <h3 class="admin-empty-title">No Test Data</h3>
          <p class="admin-empty-message">There are no test items to display</p>
          <button class="admin-btn admin-btn-primary" @click="generateTestData">
            <i class="fas fa-plus"></i>
            Generate Test Data
          </button>
        </div>

        <!-- Table Content -->
        <div v-else class="admin-table-container">
          <table class="admin-table">
            <thead class="admin-table-header">
              <tr>
                <th class="admin-table-th">ID</th>
                <th class="admin-table-th">Name</th>
                <th class="admin-table-th">Status</th>
                <th class="admin-table-th">Date</th>
                <th class="admin-table-th admin-table-th-actions">Actions</th>
              </tr>
            </thead>
            <tbody class="admin-table-body">
              <tr v-for="item in testData" :key="item.id" class="admin-table-row">
                <td class="admin-table-td">
                  <span class="admin-order-id">{{ item.id }}</span>
                </td>
                <td class="admin-table-td">{{ item.name }}</td>
                <td class="admin-table-td">
                  <span class="admin-badge" :class="getBadgeClass(item.status)">
                    {{ item.status }}
                  </span>
                </td>
                <td class="admin-table-td">
                  <span class="admin-date">{{ formatDate(item.date) }}</span>
                </td>
                <td class="admin-table-td admin-table-actions">
                  <div class="admin-action-buttons">
                    <button class="admin-btn admin-btn-sm admin-btn-info" title="View">
                      <i class="fas fa-eye"></i>
                    </button>
                    <button class="admin-btn admin-btn-sm admin-btn-primary" title="Edit">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button class="admin-btn admin-btn-sm admin-btn-danger" title="Delete">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Pagination Test -->
      <div class="admin-card-footer">
        <pagination
          :current-page="currentPage"
          :total-pages="totalPages"
          @page-changed="handlePageChange" />
      </div>
    </div>

    <!-- Modal Test -->
    <div v-if="showTestModal" class="admin-modal">
      <div class="admin-modal-backdrop" @click="showTestModal = false"></div>
      <div class="admin-modal-content">
        <div class="admin-modal-header">
          <h3 class="admin-modal-title">
            <i class="fas fa-test-tube"></i>
            Test Modal
          </h3>
          <button class="admin-modal-close" @click="showTestModal = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="admin-modal-body">
          <div class="admin-form-grid">
            <div class="admin-form-field">
              <label class="admin-form-label">Test Input</label>
              <div class="admin-form-control">
                <input type="text" class="admin-form-input" placeholder="Enter test value">
              </div>
            </div>
            <div class="admin-form-field">
              <label class="admin-form-label">Test Select</label>
              <div class="admin-form-control">
                <select class="admin-form-select">
                  <option>Option 1</option>
                  <option>Option 2</option>
                  <option>Option 3</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        <div class="admin-modal-footer">
          <button class="admin-btn admin-btn-primary">
            <i class="fas fa-save"></i>
            Save
          </button>
          <button class="admin-btn admin-btn-secondary" @click="showTestModal = false">
            <i class="fas fa-times"></i>
            Cancel
          </button>
        </div>
      </div>
    </div>

    <!-- Test Controls -->
    <div class="admin-card">
      <div class="admin-card-header">
        <h3 class="admin-card-title">
          <i class="fas fa-cogs"></i>
          Test Controls
        </h3>
      </div>
      <div class="admin-card-content">
        <div class="admin-form-grid admin-form-grid-4">
          <button class="admin-btn admin-btn-primary" @click="testLoading = !testLoading">
            Toggle Loading
          </button>
          <button class="admin-btn admin-btn-danger" @click="testError = !testError">
            Toggle Error
          </button>
          <button class="admin-btn admin-btn-info" @click="showTestModal = true">
            Show Modal
          </button>
          <button class="admin-btn admin-btn-warning" @click="clearTestData">
            Clear Data
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import SearchAndFilters from '@/admin/components/common/SearchAndFilters.vue'
import Pagination from '@/admin/components/common/Pagination.vue'

// Test data
const testSearch = ref('')
const testFilters = ref({})
const testLoading = ref(false)
const testError = ref(false)
const showTestModal = ref(false)
const currentPage = ref(1)
const totalPages = ref(5)

const testData = ref([
  { id: 'TST-001', name: 'Test Item 1', status: 'active', date: new Date() },
  { id: 'TST-002', name: 'Test Item 2', status: 'pending', date: new Date() },
  { id: 'TST-003', name: 'Test Item 3', status: 'inactive', date: new Date() }
])

const testFilterOptions = ref([
  {
    key: 'status',
    label: 'Status',
    type: 'select',
    options: [
      { value: 'active', label: 'Active' },
      { value: 'pending', label: 'Pending' },
      { value: 'inactive', label: 'Inactive' }
    ]
  },
  {
    key: 'category',
    label: 'Category',
    type: 'select',
    options: [
      { value: 'test', label: 'Test Category' },
      { value: 'demo', label: 'Demo Category' }
    ]
  }
])

// Methods
const testFunction = () => {
  console.log('Running admin design tests...')
  testLoading.value = true
  setTimeout(() => {
    testLoading.value = false
    console.log('Tests completed!')
  }, 2000)
}

const resetTests = () => {
  testSearch.value = ''
  testFilters.value = {}
  testLoading.value = false
  testError.value = false
  showTestModal.value = false
  generateTestData()
}

const generateTestData = () => {
  testData.value = [
    { id: 'TST-001', name: 'Test Item 1', status: 'active', date: new Date() },
    { id: 'TST-002', name: 'Test Item 2', status: 'pending', date: new Date() },
    { id: 'TST-003', name: 'Test Item 3', status: 'inactive', date: new Date() }
  ]
}

const clearTestData = () => {
  testData.value = []
}

const clearTestFilters = () => {
  testFilters.value = {}
}

const applyTestFilters = (filters) => {
  console.log('Applying filters:', filters)
}

const getBadgeClass = (status) => {
  const classes = {
    'active': 'is-success',
    'pending': 'is-warning', 
    'inactive': 'is-danger'
  }
  return classes[status] || 'is-light'
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

const handlePageChange = (page) => {
  currentPage.value = page
  console.log('Page changed to:', page)
}
</script>

<style scoped>
/* Additional test-specific styles if needed */
.admin-test-section {
  margin-bottom: var(--admin-spacing-lg);
}
</style>
