﻿﻿using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.Cart;

public class ClearCartCommandHandler : IRequestHandler<ClearCartCommand, bool>
{
    private readonly ICartRepository _cartRepository;
    private readonly ICartItemRepository _cartItemRepository;

    public ClearCartCommandHandler(
        ICartRepository cartRepository,
        ICartItemRepository cartItemRepository)
    {
        _cartRepository = cartRepository;
        _cartItemRepository = cartItemRepository;
    }

    public async Task<bool> Handle(ClearCartCommand request, CancellationToken cancellationToken)
    {
        var cart = await _cartRepository.GetByUserIdAsync(request.UserId, cancellationToken);
        
        if (cart == null)
        {
            return false;
        }

        await _cartItemRepository.DeleteByCartIdAsync(cart.Id, cancellationToken);
        
        // Оновлюємо дату оновлення кошика
        cart.Update();
        await _cartRepository.UpdateAsync(cart, cancellationToken);
        
        return true;
    }
}
