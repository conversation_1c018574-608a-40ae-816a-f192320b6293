<template>
  <div class="cart-page">
    <div class="cart-header">
      <router-link to="/" class="back-link">
        <i class="fas fa-arrow-left"></i>
      </router-link>
      <h1 class="cart-title">Кошик <span class="cart-count">{{ totalItems }} товари </span><span v-if="discountToPercent != 0"  class="cart-count">(-{{discountToPercent}}%)</span></h1>
    </div>

    <div class="cart-content">
      <div class="cart-items">
        <button class="clear-cart-btn">
          <i class="fas fa-trash"></i> Видалити все
        </button>
        <div v-for="cartItem in cartItems" class="cart-item">
          <button class="remove-item">
            <i class="fas fa-times"></i>
          </button>
          <div class="item-details">
            <div class="item-image">
              <img v-if="cartItem.productImage" :src="cartItem.productImage || '@assets/images/icons/placeholder-icon.svg'" :alt="cartItem.productName" />
            </div>
            <div class="item-info">
              <div v-if="cartItem.productStock > 0" class="item-availability">
              <span class="availability-icon">✓</span>
              <span class="availability-text">В наявності</span>
              </div>
              <h3 v-if="cartItem.productName" class="item-name">{{ cartItem.productName }}</h3>
              <p v-if="cartItem.id" class="item-code">Код товару: {{ cartItem.id }}</p>
              <div class="item-actions">
                <button class="add-to-favorites"
                        @click="addToWishlist(cartItem.productId)">
                  <span class="add-to-favorites-heart"><i class="far fa-heart"></i></span>
                  <span class="add-to-favirites-text">В обрані</span>
                </button>
              </div>
            </div>
            <div class="item-price-controls">
              <div class="price-container">
                <div v-if="cartItem.totalPrice" class="price-current">{{ Math.round(cartItem.totalPrice) }} ₴</div>
                <div v-if="cartItem.oldPrice" class="price-original">{{ cartItem.oldPrice}} ₴</div>
              </div>
              <div class="quantity-controls">
                <button class="quantity-btn minus"
                        @click="decreaseQuantity(cartItem)">
                  <i class="fas fa-minus"></i>
                </button>
                <input type="number" class="quantity-input" :value="cartItem.quantity" min="1" />
                <button class="quantity-btn plus"
                        @click="increaseQuantity(cartItem)">
                  <i class="fas fa-plus"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="cart-summary">
        <h2 class="summary-title">Разом</h2>
        <div class="summary-row">
          <span>{{ totalItems }} товари на суму</span>
          <span class="summary-value">{{ originalTotal }} ₴</span>
        </div>
        <div class="summary-row">
          <span>Знижка</span>
          <span class="summary-value discount">{{ discount }} ₴</span>
        </div>
        <div class="summary-row">
          <span>Вартість доставки</span>
          <span class="summary-value free">Безкоштовно</span>
        </div>
        <div class="summary-divider"></div>
        <div class="summary-row total">
          <span>До оплати</span>
          <span class="summary-value">{{ subtotal }} ₴</span>
        </div>
        <button class="checkout-btn">Перейти до оформлення</button>
        <div class="promo-code">
          <span>Промокод</span>
          <div class="promo-input-container">
            <input type="text" class="promo-input" placeholder="Введіть промокод" />
            <button class="apply-promo-btn">Додати</button>
          </div>
        </div>
      </div>
    </div>

    <div class="recommended-products">
      <h2 class="section-title">Рекомендовані товари</h2>
      <div class="container">
        <div class="products-grid">
          <div v-for="product in recommendedProducts"  :key="product.id" class="product-card">
            <div class="product-badge" v-if="product.badge">{{ product.badge }}</div>
            <div class="product-image">
              <img :src="product.image" :alt="product.name" />
            </div>
            <div class="product-info">
              <h3 class="product-name">{{ product.name }}</h3>
              <div class="product-availability" v-if="product.stock > 0">
                <span class="availability-icon">✓</span>
                <span class="availability-text">В наявності</span>
              </div>
              <div class="product-unavailability" v-if="product.stock == 0">
                <span class="availability-icon">✖</span>
                <span class="availability-text">Немає в наявності</span>
              </div>
              <div class="product-price">
                <div class="price-old" v-if="product.oldPrice">{{ product.oldPrice }} ₴</div>
                <div class="price-discount" v-if="cartItem.discount">-{{ product.discount }}%</div>
              </div>
              <div class="price-current">{{ product.priceAmount }} ₴</div>
            </div>
            <div class="product-actions">
              <button class="wishlist-btn">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                </svg>
              </button>
              <button class="cart-btn">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="9" cy="21" r="1"></circle>
                  <circle cx="20" cy="21" r="1"></circle>
                  <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import wishlistService from '../../services/wishlist.service';
import ProductService from '../../services/product.service';
import CartService from '../../services/cart.service';
import cartService from '../../services/cart.service';
import { number } from 'yup';

export default {
  name: 'Cart',
  data() {
    return {
      cartItems: [],
      recommendedProducts: [],
      error: null,
    };
  },
  computed: {
    totalItems() {
      return this.cartItems.reduce((total, item) => total + item.quantity, 0);
    },
    subtotal() {
      return  Number.isNaN(Number(this.cartItems.reduce((total, item) => total + item.total, 0))) ? this.originalTotal 
      : this.cartItems.reduce((total, item) => total + item.total, 0);
    },
    originalTotal() {
      return this.cartItems.reduce((total, item) => total + Math.round(item.totalPrice), 0);
    },
    discount() {
      return this.subtotal == this.originalTotal ? 0 : this.orginalTotal - this.subtotal;
    },
    discountToPercent() {
      return this.discount == 0 ? 0 : 100 * (this.orginalTotal - this.subtotal) / this.originalTotal;
    }
  },
  async mounted()
  {
    await this.fetchCart();
    await this.fetchProducts();
  },
  methods: {
    
    async fetchProducts(params) {
      try {
        const response = await ProductService.getAll(params = {});
        this.products = response.data.data;
        this.error = null;
      } catch (error) {
        this.error = 'Failed to load products. Please try again.';
        console.error(error);
      }
    },
    
    async fetchCart()
    {
        try {
          const response = await CartService.getCart();
          console.log(response);
          this.cartItems = response.data.data.items;
          this.error = null;
        } catch(error) {
          this.error = 'Failed to load cart. Please try again.'
        }
    },

    async increaseQuantity(item) {
      if(item.quantity < item.productStock)
      {
        await cartService.changeItemCount(item.id, item.quantity+1);
      }
      await this.fetchCart();
    },
    async decreaseQuantity(item) {
      if (item.quantity > 1) {
        await cartService.changeItemCount(item.id, item.quantity-1);
      } else {
        await cartService.deleteItem(item.id);
      }
      await this.fetchCart();
    },
    async removeItem(itemId) {
      await cartService.deleteItem(itemId);
      await this.fetchCart();
    },
    async clearCart() {
      cartService.deleteCart();
    },
    async addToWishlist(productId)
    {
        await wishlistService.addToWishlist(productId);
    },
    addToFavorites(itemId) {
      // Implementation for adding to favorites
      console.log(`Added item ${itemId} to favorites`);
    },
    applyPromoCode(code) {
      // Implementation for applying promo code
      console.log(`Applied promo code: ${code}`);
    },
    proceedToCheckout() {
      // Implementation for checkout process
      console.log('Proceeding to checkout');
    }
  }
};
</script>

<style scoped>

.add-to-favorites-heart {
  margin-right: 5px;
}

.product-availability {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #00a046;
}

.availability-icon {
  margin-right: 4px;
}

.cart-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.cart-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.back-link {
  font-size: 18px;
  color: #333;
  margin-right: 15px;
  text-decoration: none;
}

.cart-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  flex-grow: 1;
}

.cart-count {
  font-size: 16px;
  font-weight: normal;
  color: #666;
}

.clear-cart-btn {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  margin-top: 7px;
  margin-bottom: 7px;
}

.clear-cart-btn i {
  margin-right: 5px;
}

.cart-content {
  display: flex;
  flex-direction: row;
  gap: 20px;
}

.cart-items {
  flex: 1;
}

.cart-item {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
  padding: 15px;
}

.item-availability {
  color: #4caf50;
  font-size: 14px;
  margin-bottom: 10px;
}

.item-availability i {
  margin-right: 5px;
}

.item-details {
  display: flex;
  position: relative;
}

.item-image {
  width: 100px;
  height: 100px;
  margin-right: 15px;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 5px 0;
}

.item-code {
  color: #999;
  font-size: 12px;
  margin: 0 0 10px 0;
}

.item-actions {
  display: flex;
}

.add-to-favorites {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  display: flex;
  align-items: center;
}

.add-to-favorites i {
  margin-right: 5px;
}

.item-price-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 20px;
  min-width: 120px;
}

.price-container {
  text-align: right;
  margin-bottom: 10px;
}

.price-current {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.price-original {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

.quantity-controls {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.quantity-btn {
  background: none;
  border: none;
  color: #333;
  cursor: pointer;
  font-size: 12px;
  padding: 5px 10px;
}

.quantity-input {
  width: 40px;
  border: none;
  text-align: center;
  font-size: 14px;
}

.remove-item {
  position: absolute;
  top: 0;
  right: 0;
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 16px;
}

.cart-summary {
  width: 300px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  align-self: flex-start;
}

.summary-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 15px 0;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
}

.summary-value {
  font-weight: 500;
  color: #333;
}

.summary-value.discount {
  color: #999;
}

.summary-value.free {
  color: #4caf50;
}

.summary-divider {
  height: 1px;
  background-color: #eee;
  margin: 15px 0;
}

.summary-row.total {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.checkout-btn {
  width: 100%;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  margin: 15px 0;
}

.promo-code {
  margin-top: 20px;
}

.promo-input-container {
  display: flex;
  margin-top: 5px;
}

.promo-input {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  padding: 8px 12px;
  font-size: 14px;
}

.apply-promo-btn {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-left: none;
  border-radius: 0 4px 4px 0;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
}

.recommended-products {
  margin-top: 40px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
}

.product-carousel {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  padding-bottom: 20px;
}

.product-card {
  width: 250px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  flex-shrink: 0;
}

.product-image {
  height: 200px;
  position: relative;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-options-btn,
.add-to-favorites-btn {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.product-options-btn {
  top: 10px;
  right: 10px;
}

.add-to-favorites-btn {
  top: 10px;
  right: 50px;
}

.product-discount {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: #ff5722;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.product-info {
  padding: 15px;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 10px 0;
  height: 40px;
  overflow: hidden;
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-availability {
  color: #4caf50;
  font-size: 12px;
  margin-bottom: 10px;
}

.product-availability i {
  margin-right: 5px;
}

.product-price {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.current-price {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-right: 8px;
}

.original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

.product-actions {
  display: flex;
  justify-content: flex-end;
}

.add-to-cart-btn {
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

@media (max-width: 768px) {
  .cart-content {
    flex-direction: column;
  }
  
  .cart-summary {
    width: 100%;
  }
  
  .item-details {
    flex-wrap: wrap;
  }
  
  .item-price-controls {
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-left: 0;
    margin-top: 15px;
  }
}

.top-products-section {
  margin-bottom: 48px;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 24px;
  color: #000;
  text-align: left;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.product-card {
  position: relative;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
  height: 100%;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.product-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #ff7a00;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.product-image {
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.product-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.product-info {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.3;
  margin-bottom: 8px;
  color: #333;
  display: -webkit-box;
  line-clamp: 3;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 54px;
}

.product-availability {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #00a046;
}

.product-unavailability {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #a00000;
}

.availability-icon {
  margin-right: 4px;
}

.product-price {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.price-old {
  font-size: 12px;
  color: #888;
  text-decoration: line-through;
  margin-right: 8px;
}

.price-discount {
  font-size: 12px;
  color: white;
  background: #ff5252;
  padding: 2px 4px;
  border-radius: 4px;
}

.price-current {
  font-size: 18px;
  font-weight: 700;
  color: #000;
  margin-bottom: 12px;
}

.product-actions {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
}

.wishlist-btn, .cart-btn {
  background: none;
  border: 1px solid #ddd;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.wishlist-btn:hover {
  color: #ff5252;
  border-color: #ff5252;
}

.cart-btn {
  background: #0066cc;
  color: white;
  border: none;
}

.cart-btn:hover {
  background: #0055aa;
}

.cart-btn svg, .wishlist-btn svg {
  width: 18px;
  height: 18px;
}

</style>
