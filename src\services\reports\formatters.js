/**
 * Unified Formatters and Utilities
 * Centralized formatting functions for consistent data presentation
 */

class FormattersService {
  constructor() {
    this.defaultCurrency = 'UAH'
    this.defaultLocale = 'uk-UA'
  }

  // ==================== CURRENCY FORMATTING ====================

  /**
   * Format currency value
   * @param {number} value - Numeric value
   * @param {string} currency - Currency code (default: UAH)
   * @param {string} locale - Locale code (default: uk-UA)
   * @returns {string} Formatted currency string
   */
  formatCurrency(value, currency = this.defaultCurrency, locale = this.defaultLocale) {
    if (value === null || value === undefined || isNaN(value)) {
      return '—'
    }

    try {
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value)
    } catch (error) {
      console.warn('Currency formatting error:', error)
      return `${value.toFixed(2)} ${currency}`
    }
  }

  /**
   * Format currency with compact notation for large numbers
   * @param {number} value - Numeric value
   * @param {string} currency - Currency code
   * @returns {string} Formatted compact currency string
   */
  formatCurrencyCompact(value, currency = this.defaultCurrency) {
    if (value === null || value === undefined || isNaN(value)) {
      return '—'
    }

    try {
      return new Intl.NumberFormat(this.defaultLocale, {
        style: 'currency',
        currency: currency,
        notation: 'compact',
        maximumFractionDigits: 1
      }).format(value)
    } catch (error) {
      return this.formatCurrency(value, currency)
    }
  }

  // ==================== PERCENTAGE FORMATTING ====================

  /**
   * Format percentage value
   * @param {number} value - Numeric value (0-100 or 0-1 based on isDecimal)
   * @param {number} decimals - Number of decimal places
   * @param {boolean} isDecimal - Whether value is decimal (0-1) or percentage (0-100)
   * @returns {string} Formatted percentage string
   */
  formatPercentage(value, decimals = 1, isDecimal = false) {
    if (value === null || value === undefined || isNaN(value)) {
      return '—'
    }

    const percentValue = isDecimal ? value : value / 100

    try {
      return new Intl.NumberFormat(this.defaultLocale, {
        style: 'percent',
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
      }).format(percentValue)
    } catch (error) {
      return `${(percentValue * 100).toFixed(decimals)}%`
    }
  }

  /**
   * Format percentage change with sign and color indication
   * @param {number} value - Percentage change value
   * @param {number} decimals - Number of decimal places
   * @returns {Object} Formatted percentage with metadata
   */
  formatPercentageChange(value, decimals = 1) {
    if (value === null || value === undefined || isNaN(value)) {
      return { text: '—', trend: 'neutral', color: 'gray' }
    }

    const formatted = this.formatPercentage(Math.abs(value), decimals, false)
    const sign = value > 0 ? '+' : value < 0 ? '-' : ''
    const trend = value > 0 ? 'up' : value < 0 ? 'down' : 'neutral'
    const color = value > 0 ? 'green' : value < 0 ? 'red' : 'gray'

    return {
      text: `${sign}${formatted}`,
      trend,
      color,
      value
    }
  }

  // ==================== NUMBER FORMATTING ====================

  /**
   * Format number with thousands separator
   * @param {number} value - Numeric value
   * @param {number} decimals - Number of decimal places
   * @returns {string} Formatted number string
   */
  formatNumber(value, decimals = 0) {
    if (value === null || value === undefined || isNaN(value)) {
      return '—'
    }

    try {
      return new Intl.NumberFormat(this.defaultLocale, {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
      }).format(value)
    } catch (error) {
      return value.toFixed(decimals)
    }
  }

  /**
   * Format number with compact notation
   * @param {number} value - Numeric value
   * @param {number} decimals - Number of decimal places
   * @returns {string} Formatted compact number string
   */
  formatNumberCompact(value, decimals = 1) {
    if (value === null || value === undefined || isNaN(value)) {
      return '—'
    }

    try {
      return new Intl.NumberFormat(this.defaultLocale, {
        notation: 'compact',
        maximumFractionDigits: decimals
      }).format(value)
    } catch (error) {
      return this.formatNumber(value, decimals)
    }
  }

  // ==================== DATE FORMATTING ====================

  /**
   * Format date value
   * @param {Date|string} date - Date value
   * @param {string} format - Format type (short, medium, long, full)
   * @returns {string} Formatted date string
   */
  formatDate(date, format = 'medium') {
    if (!date) return '—'

    const dateObj = date instanceof Date ? date : new Date(date)
    
    if (isNaN(dateObj.getTime())) {
      return '—'
    }

    const formatOptions = {
      short: { year: 'numeric', month: 'short', day: 'numeric' },
      medium: { year: 'numeric', month: 'short', day: 'numeric' },
      long: { year: 'numeric', month: 'long', day: 'numeric' },
      full: { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }
    }

    try {
      return new Intl.DateTimeFormat(this.defaultLocale, formatOptions[format]).format(dateObj)
    } catch (error) {
      return dateObj.toLocaleDateString()
    }
  }

  /**
   * Format datetime value
   * @param {Date|string} datetime - Datetime value
   * @param {boolean} includeSeconds - Whether to include seconds
   * @returns {string} Formatted datetime string
   */
  formatDateTime(datetime, includeSeconds = false) {
    if (!datetime) return '—'

    const dateObj = datetime instanceof Date ? datetime : new Date(datetime)
    
    if (isNaN(dateObj.getTime())) {
      return '—'
    }

    const options = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }

    if (includeSeconds) {
      options.second = '2-digit'
    }

    try {
      return new Intl.DateTimeFormat(this.defaultLocale, options).format(dateObj)
    } catch (error) {
      return dateObj.toLocaleString()
    }
  }

  /**
   * Format relative time (e.g., "2 hours ago")
   * @param {Date|string} date - Date value
   * @returns {string} Relative time string
   */
  formatRelativeTime(date) {
    if (!date) return '—'

    const dateObj = date instanceof Date ? date : new Date(date)
    
    if (isNaN(dateObj.getTime())) {
      return '—'
    }

    const now = new Date()
    const diffMs = now - dateObj
    const diffSecs = Math.floor(diffMs / 1000)
    const diffMins = Math.floor(diffSecs / 60)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffSecs < 60) return 'щойно'
    if (diffMins < 60) return `${diffMins} хв тому`
    if (diffHours < 24) return `${diffHours} год тому`
    if (diffDays < 7) return `${diffDays} дн тому`
    
    return this.formatDate(dateObj, 'short')
  }

  // ==================== STATUS FORMATTING ====================

  /**
   * Format status with appropriate styling
   * @param {string} status - Status value
   * @param {Object} statusMap - Custom status mapping
   * @returns {Object} Formatted status with styling
   */
  formatStatus(status, statusMap = null) {
    if (!status) return { text: '—', class: 'status-unknown', color: 'gray' }

    const defaultStatusMap = {
      active: { text: 'Активний', class: 'status-active', color: 'green' },
      inactive: { text: 'Неактивний', class: 'status-inactive', color: 'red' },
      pending: { text: 'Очікує', class: 'status-pending', color: 'orange' },
      completed: { text: 'Завершено', class: 'status-completed', color: 'green' },
      cancelled: { text: 'Скасовано', class: 'status-cancelled', color: 'red' },
      processing: { text: 'Обробляється', class: 'status-processing', color: 'blue' },
      shipped: { text: 'Відправлено', class: 'status-shipped', color: 'blue' },
      delivered: { text: 'Доставлено', class: 'status-delivered', color: 'green' },
      returned: { text: 'Повернено', class: 'status-returned', color: 'orange' },
      out_of_stock: { text: 'Немає в наявності', class: 'status-out-of-stock', color: 'red' },
      low_stock: { text: 'Мало в наявності', class: 'status-low-stock', color: 'orange' }
    }

    const map = statusMap || defaultStatusMap
    const statusKey = status.toLowerCase().replace(/\s+/g, '_')
    
    return map[statusKey] || { 
      text: status, 
      class: 'status-unknown', 
      color: 'gray' 
    }
  }

  // ==================== UTILITY FUNCTIONS ====================

  /**
   * Calculate percentage change between two values
   * @param {number} current - Current value
   * @param {number} previous - Previous value
   * @returns {number} Percentage change
   */
  calculatePercentageChange(current, previous) {
    if (previous === 0) return current > 0 ? 100 : 0
    if (current === null || current === undefined || previous === null || previous === undefined) {
      return 0
    }
    return ((current - previous) / previous) * 100
  }

  /**
   * Get file extension for export format
   * @param {string} format - Export format
   * @returns {string} File extension
   */
  getFileExtension(format) {
    const extensions = {
      excel: 'xlsx',
      pdf: 'pdf',
      csv: 'csv',
      json: 'json'
    }
    return extensions[format] || 'txt'
  }

  /**
   * Generate filename for export
   * @param {string} reportType - Report type
   * @param {string} format - Export format
   * @param {Object} filters - Applied filters
   * @returns {string} Generated filename
   */
  generateFilename(reportType, format, filters = {}) {
    const { startDate, endDate } = filters
    const start = startDate ? new Date(startDate).toISOString().split('T')[0] : 'all'
    const end = endDate ? new Date(endDate).toISOString().split('T')[0] : 'time'
    const timestamp = new Date().toISOString().split('T')[0]
    const extension = this.getFileExtension(format)
    
    return `${reportType}_report_${start}_to_${end}_${timestamp}.${extension}`
  }

  /**
   * Truncate text to specified length
   * @param {string} text - Text to truncate
   * @param {number} maxLength - Maximum length
   * @param {string} suffix - Suffix for truncated text
   * @returns {string} Truncated text
   */
  truncateText(text, maxLength = 50, suffix = '...') {
    if (!text || text.length <= maxLength) return text
    return text.substring(0, maxLength - suffix.length) + suffix
  }

  /**
   * Format file size
   * @param {number} bytes - File size in bytes
   * @param {number} decimals - Number of decimal places
   * @returns {string} Formatted file size
   */
  formatFileSize(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i]
  }

  /**
   * Validate and format phone number
   * @param {string} phone - Phone number
   * @returns {string} Formatted phone number
   */
  formatPhoneNumber(phone) {
    if (!phone) return '—'
    
    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, '')
    
    // Format Ukrainian phone number
    if (cleaned.length === 12 && cleaned.startsWith('380')) {
      return `+${cleaned.slice(0, 3)} (${cleaned.slice(3, 5)}) ${cleaned.slice(5, 8)}-${cleaned.slice(8, 10)}-${cleaned.slice(10)}`
    }
    
    return phone
  }

  /**
   * Format email for display
   * @param {string} email - Email address
   * @param {number} maxLength - Maximum display length
   * @returns {string} Formatted email
   */
  formatEmail(email, maxLength = 30) {
    if (!email) return '—'
    
    if (email.length <= maxLength) return email
    
    const [local, domain] = email.split('@')
    if (local.length > maxLength - domain.length - 4) {
      return `${local.substring(0, maxLength - domain.length - 7)}...@${domain}`
    }
    
    return email
  }
}

// Create and export singleton instance
export const formatters = new FormattersService()
export default formatters
