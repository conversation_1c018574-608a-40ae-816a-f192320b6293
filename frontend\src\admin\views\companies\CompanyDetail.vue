<template>
  <div class="admin-company-detail">
    <div class="admin-page-header">
      <div class="admin-header-content">
        <div class="admin-header-left">
          <nav class="admin-breadcrumb">
            <router-link to="/admin/companies" class="admin-breadcrumb-link">Companies</router-link>
            <span class="admin-breadcrumb-separator">/</span>
            <span class="admin-breadcrumb-current">{{ company.name || 'Company Details' }}</span>
          </nav>
          <h1 class="admin-page-title">
            <i class="fas fa-building"></i>
            {{ company.name || 'Company Details' }}
          </h1>
        </div>
        <div class="admin-header-right">
          <button class="admin-btn admin-btn-primary" @click="fetchCompany" :disabled="loading">
            <i class="fas fa-sync-alt"></i>
            <span>Refresh</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div v-if="loading && !company.id" class="admin-loading-state">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading company details...</p>
    </div>

    <!-- Error -->
    <div v-else-if="error" class="admin-alert admin-alert-danger">
      <button class="admin-alert-close" @click="error = null">
        <i class="fas fa-times"></i>
      </button>
      {{ error }}
    </div>

    <!-- Company Details -->
    <div v-else-if="company.id" class="admin-company-detail-content">
      <div class="admin-company-detail-grid">
        <!-- Main Info -->
        <div class="admin-company-main-info">
          <div class="admin-card">
            <div class="admin-card-header">
              <h3 class="admin-card-title">Company Information</h3>
            </div>
            <div class="admin-card-content">
              <div class="columns">
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Name</label>
                    <p class="info-value">{{ company.name }}</p>
                  </div>
                  <div class="field">
                    <label class="label">Slug</label>
                    <p class="info-value">{{ company.slug }}</p>
                  </div>
                  <div class="field">
                    <label class="label">Contact Email</label>
                    <p class="info-value">{{ company.contactEmail }}</p>
                  </div>
                  <div class="field">
                    <label class="label">Contact Phone</label>
                    <p class="info-value">{{ company.contactPhone }}</p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Status</label>
                    <p class="info-value">
                      <span class="tag" :class="getStatusClass(company.isApproved)">
                        {{ company.isApproved ? 'Approved' : 'Pending' }}
                      </span>
                      <span v-if="company.isFeatured" class="tag is-info ml-1">Featured</span>
                    </p>
                  </div>
                  <div class="field" v-if="company.approvedAt">
                    <label class="label">Approved At</label>
                    <p class="info-value">{{ formatDateTime(company.approvedAt) }}</p>
                  </div>
                  <div class="field" v-if="company.approvedByUserId">
                    <label class="label">Approved By</label>
                    <p class="info-value">{{ company.approvedByUserId }}</p>
                  </div>
                </div>
              </div>

              <div class="field" v-if="company.description">
                <label class="label">Description</label>
                <p class="info-value">{{ company.description }}</p>
              </div>

              <div class="field">
                <label class="label">Address</label>
                <div class="content">
                  <p>{{ company.addressStreet }}</p>
                  <p>{{ company.addressCity }}, {{ company.addressRegion }} {{ company.addressPostalCode }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Finance Information -->
          <div class="card mt-4" v-if="company.finance">
            <div class="card-header">
              <p class="card-header-title">Finance Information</p>
            </div>
            <div class="card-content">
              <div class="columns">
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Bank Name</label>
                    <p class="info-value">{{ company.finance.bankName || 'Not provided' }}</p>
                  </div>
                  <div class="field">
                    <label class="label">Bank Account</label>
                    <p class="info-value">{{ company.finance.bankAccount || 'Not provided' }}</p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Bank Code</label>
                    <p class="info-value">{{ company.finance.bankCode || 'Not provided' }}</p>
                  </div>
                  <div class="field">
                    <label class="label">Tax ID</label>
                    <p class="info-value">{{ company.finance.taxId || 'Not provided' }}</p>
                  </div>
                </div>
              </div>
              <div class="field" v-if="company.finance.paymentDetails">
                <label class="label">Payment Details</label>
                <p class="info-value">{{ company.finance.paymentDetails }}</p>
              </div>
            </div>
          </div>

          <!-- Schedule Information -->
          <div class="card mt-4" v-if="company.schedule && company.schedule.length > 0">
            <div class="card-header">
              <p class="card-header-title">Schedule Information</p>
            </div>
            <div class="card-content">
              <div class="table-container">
                <table class="table is-fullwidth">
                  <thead>
                    <tr>
                      <th>Day</th>
                      <th>Open Time</th>
                      <th>Close Time</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="schedule in company.schedule" :key="schedule.day">
                      <td>{{ getDayName(schedule.day) }}</td>
                      <td>{{ schedule.isClosed ? '-' : schedule.openTime }}</td>
                      <td>{{ schedule.isClosed ? '-' : schedule.closeTime }}</td>
                      <td>
                        <span class="tag" :class="schedule.isClosed ? 'is-danger' : 'is-success'">
                          {{ schedule.isClosed ? 'Closed' : 'Open' }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="column is-4">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Actions</p>
            </div>
            <div class="card-content">
              <div class="buttons is-fullwidth">
                <button
                  class="button is-success is-fullwidth"
                  v-if="!company.isApproved"
                  @click="approveCompany"
                  :class="{ 'is-loading': actionLoading }">
                  <span class="icon"><i class="fas fa-check"></i></span>
                  <span>Approve Company</span>
                </button>

                <button
                  class="button is-danger is-fullwidth"
                  v-if="!company.isApproved"
                  @click="showRejectModal = true"
                  :disabled="actionLoading">
                  <span class="icon"><i class="fas fa-times"></i></span>
                  <span>Reject Company</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Company Image -->
          <div class="card mt-4" v-if="company.imageUrl">
            <div class="card-header">
              <p class="card-header-title">Company Image</p>
            </div>
            <div class="card-content">
              <figure class="image">
                <img :src="company.imageUrl" :alt="company.name">
              </figure>
            </div>
          </div>

          <!-- SEO Information -->
          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">SEO Information</p>
            </div>
            <div class="card-content">
              <div class="field">
                <label class="label">Meta Title</label>
                <p class="info-value">{{ company.metaTitle || 'Not set' }}</p>
              </div>
              <div class="field">
                <label class="label">Meta Description</label>
                <p class="info-value">{{ company.metaDescription || 'Not set' }}</p>
              </div>
              <div class="field" v-if="company.metaImage">
                <label class="label">Meta Image</label>
                <figure class="image is-128x128">
                  <img :src="company.metaImage" :alt="company.name">
                </figure>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Reject Modal -->
    <div class="modal" :class="{ 'is-active': showRejectModal }">
      <div class="modal-background" @click="showRejectModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Reject Company</p>
          <button class="delete" @click="showRejectModal = false"></button>
        </header>
        <section class="modal-card-body">
          <div class="field">
            <label class="label">Rejection Reason</label>
            <div class="control">
              <textarea
                class="textarea"
                v-model="rejectionReason"
                placeholder="Enter reason for rejection...">
              </textarea>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-danger"
            @click="rejectCompany"
            :class="{ 'is-loading': actionLoading }">
            Reject Company
          </button>
          <button class="button" @click="showRejectModal = false">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { companiesService } from '@/admin/services/companies';

const route = useRoute();

// Reactive data
const company = ref({});
const loading = ref(false);
const error = ref(null);
const actionLoading = ref(false);
const showRejectModal = ref(false);
const rejectionReason = ref('');

// Methods
const fetchCompany = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await companiesService.getDetailedCompany(route.params.id);
    company.value = response.data;
  } catch (err) {
    error.value = err.message || 'Failed to load company details';
  } finally {
    loading.value = false;
  }
};

const approveCompany = async () => {
  actionLoading.value = true;
  try {
    await companiesService.approveCompany(company.value.id);
    await fetchCompany(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to approve company';
  } finally {
    actionLoading.value = false;
  }
};

const rejectCompany = async () => {
  if (!rejectionReason.value.trim()) {
    error.value = 'Please provide a rejection reason';
    return;
  }

  actionLoading.value = true;
  try {
    await companiesService.rejectCompany(company.value.id, rejectionReason.value);
    showRejectModal.value = false;
    rejectionReason.value = '';
    await fetchCompany(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to reject company';
  } finally {
    actionLoading.value = false;
  }
};

const suspendCompany = async () => {
  actionLoading.value = true;
  try {
    await companiesService.suspendCompany(company.value.id);
    await fetchCompany(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to suspend company';
  } finally {
    actionLoading.value = false;
  }
};

// Utility methods
const formatDateTime = (dateString) => {
  return new Date(dateString).toLocaleString();
};

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString();
};

const formatStatus = (status) => {
  return status?.charAt(0).toUpperCase() + status?.slice(1) || 'Unknown';
};

const formatRole = (role) => {
  return role?.charAt(0).toUpperCase() + role?.slice(1) || 'Unknown';
};

const getStatusClass = (isApproved) => {
  return isApproved ? 'is-success' : 'is-warning';
};

const getRoleClass = (role) => {
  switch (role?.toLowerCase()) {
    case 'owner': return 'is-primary';
    case 'admin': return 'is-info';
    case 'member': return 'is-light';
    default: return 'is-light';
  }
};

const getDayName = (dayNumber) => {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days[dayNumber] || 'Unknown';
};

// Lifecycle
onMounted(() => {
  fetchCompany();
});
</script>

<style scoped>
.company-detail {
  padding: 1rem;
}

.info-value {
  font-weight: 500;
  color: #363636;
}

.table-container {
  overflow-x: auto;
}
</style>
