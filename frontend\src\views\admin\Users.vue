<template>
  <div class="admin-page-container">
    <div class="page-header">
      <div class="level-left">
        <div class="level-item">
          <h1 class="page-title">Users</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <button class="button is-primary" @click="showAddModal = true">
            <span class="icon"><i class="fas fa-plus"></i></span>
            <span>Add User</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Search and Filter -->
    <div class="box">
      <div class="field has-addons">
        <div class="control is-expanded">
          <input class="input" type="text" placeholder="Search users..." v-model="searchQuery">
        </div>
        <div class="control">
          <button class="button is-info" @click="searchUsers">
            <span class="icon"><i class="fas fa-search"></i></span>
          </button>
        </div>
      </div>

      <div class="columns">
        <div class="column">
          <div class="field">
            <label class="label">Role</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="filters.role">
                  <option value="">All Roles</option>
                  <option value="Buyer">Buyer</option>
                  <option value="Seller">Seller</option>
                  <option value="Moderator">Moderator</option>
                  <option value="Admin">Admin</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <div class="column">
          <div class="field">
            <label class="label">Status</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="filters.status">
                  <option value="">All Statuses</option>
                  <option value="active">Active</option>
                  <option value="pending">Pending</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <div class="column">
          <div class="field">
            <label class="label">Sort By</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="filters.sortBy">
                  <option value="username">Username</option>
                  <option value="email">Email</option>
                  <option value="createdAt">Date Joined</option>
                  <option value="lastSeenAt">Last Seen</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Users Table -->
    <div class="box">
      <div class="table-container">
        <table class="table is-fullwidth is-striped">
          <thead>
            <tr>
              <th>ID</th>
              <th>Username</th>
              <th>Email</th>
              <th>Role</th>
              <th>Status</th>
              <th>Last Seen</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="user in users" :key="user.id">
              <td>{{ user.id.substring(0, 8) }}</td>
              <td>{{ user.username }}</td>
              <td>{{ user.email }}</td>
              <td>
                <span class="tag" :class="getRoleTagClass(user.role)">
                  {{ user.role }}
                </span>
              </td>
              <td>
                <span class="tag" :class="getStatusTagClass(user.status)">
                  {{ user.status }}
                </span>
              </td>
              <td>{{ formatDate(user.lastSeenAt) }}</td>
              <td>
                <div class="buttons are-small">
                  <button class="button is-info" @click="editUser(user)">
                    <span class="icon"><i class="fas fa-edit"></i></span>
                  </button>
                  <button class="button is-danger" @click="confirmDelete(user)">
                    <span class="icon"><i class="fas fa-trash"></i></span>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <nav class="pagination is-centered" role="navigation" aria-label="pagination">
        <a class="pagination-previous" :disabled="currentPage === 1" @click="changePage(currentPage - 1)">Previous</a>
        <a class="pagination-next" :disabled="currentPage === totalPages" @click="changePage(currentPage + 1)">Next</a>
        <ul class="pagination-list">
          <li v-for="page in paginationItems" :key="page.value">
            <a
              v-if="page.type === 'page'"
              class="pagination-link"
              :class="{ 'is-current': page.value === currentPage }"
              @click="changePage(page.value)"
            >
              {{ page.value }}
            </a>
            <span v-else class="pagination-ellipsis">&hellip;</span>
          </li>
        </ul>
      </nav>
    </div>

    <!-- Add/Edit User Modal -->
    <div class="modal" :class="{ 'is-active': showAddModal || showEditModal }">
      <div class="modal-background" @click="closeModals"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">{{ showEditModal ? 'Edit User' : 'Add User' }}</p>
          <button class="delete" aria-label="close" @click="closeModals"></button>
        </header>
        <section class="modal-card-body">
          <div class="field">
            <label class="label">Username</label>
            <div class="control">
              <input class="input" type="text" placeholder="Username" v-model="userForm.username">
            </div>
          </div>

          <div class="field">
            <label class="label">Email</label>
            <div class="control">
              <input class="input" type="email" placeholder="Email" v-model="userForm.email">
            </div>
          </div>

          <div class="field">
            <label class="label">Password</label>
            <div class="control">
              <input class="input" type="password" placeholder="Password" v-model="userForm.password">
            </div>
            <p class="help" v-if="showEditModal">Leave blank to keep current password</p>
          </div>

          <div class="field">
            <label class="label">Role</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="userForm.role">
                  <option value="Buyer">Buyer</option>
                  <option value="Seller">Seller</option>
                  <option value="Moderator">Moderator</option>
                  <option value="Admin">Admin</option>
                </select>
              </div>
            </div>
          </div>

          <div class="field">
            <label class="label">Status</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="userForm.status">
                  <option value="active">Active</option>
                  <option value="pending">Pending</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button class="button is-success" @click="saveUser">Save</button>
          <button class="button" @click="closeModals">Cancel</button>
        </footer>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal" :class="{ 'is-active': showDeleteModal }">
      <div class="modal-background" @click="showDeleteModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Confirm Delete</p>
          <button class="delete" aria-label="close" @click="showDeleteModal = false"></button>
        </header>
        <section class="modal-card-body">
          Are you sure you want to delete the user "{{ userToDelete?.username }}"? This action cannot be undone.
        </section>
        <footer class="modal-card-foot">
          <button class="button is-danger" @click="deleteUser">Delete</button>
          <button class="button" @click="showDeleteModal = false">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// Mock data - would be replaced with API calls
const users = ref([
  { id: 'a1b2c3d4e5f6g7h8', username: 'johndoe', email: '<EMAIL>', role: 'Admin', status: 'active', lastSeenAt: new Date() },
  { id: 'b2c3d4e5f6g7h8i9', username: 'janesmith', email: '<EMAIL>', role: 'Seller', status: 'active', lastSeenAt: new Date(Date.now() - 86400000) },
  { id: 'c3d4e5f6g7h8i9j0', username: 'mikejohnson', email: '<EMAIL>', role: 'Buyer', status: 'active', lastSeenAt: new Date(Date.now() - 172800000) },
  { id: 'd4e5f6g7h8i9j0k1', username: 'sarahwilliams', email: '<EMAIL>', role: 'Moderator', status: 'active', lastSeenAt: new Date(Date.now() - 259200000) },
  { id: 'e5f6g7h8i9j0k1l2', username: 'davidbrown', email: '<EMAIL>', role: 'Seller', status: 'pending', lastSeenAt: new Date(Date.now() - 345600000) },
  { id: 'f6g7h8i9j0k1l2m3', username: 'emilydavis', email: '<EMAIL>', role: 'Buyer', status: 'inactive', lastSeenAt: new Date(Date.now() - 432000000) },
  { id: 'g7h8i9j0k1l2m3n4', username: 'robertmiller', email: '<EMAIL>', role: 'Buyer', status: 'active', lastSeenAt: new Date(Date.now() - 518400000) },
  { id: 'h8i9j0k1l2m3n4o5', username: 'jenniferwilson', email: '<EMAIL>', role: 'Seller', status: 'active', lastSeenAt: new Date(Date.now() - 604800000) },
  { id: 'i9j0k1l2m3n4o5p6', username: 'michaelmoore', email: '<EMAIL>', role: 'Buyer', status: 'pending', lastSeenAt: new Date(Date.now() - 691200000) },
  { id: 'j0k1l2m3n4o5p6q7', username: 'lisataylor', email: '<EMAIL>', role: 'Buyer', status: 'active', lastSeenAt: new Date(Date.now() - 777600000) }
]);

// Pagination
const currentPage = ref(1);
const itemsPerPage = ref(10);
const totalItems = ref(100);

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage.value));

const paginationItems = computed(() => {
  const items = [];
  const maxVisiblePages = 5;

  if (totalPages.value <= maxVisiblePages) {
    // Show all pages
    for (let i = 1; i <= totalPages.value; i++) {
      items.push({ type: 'page', value: i });
    }
  } else {
    // Always show first page
    items.push({ type: 'page', value: 1 });

    // Calculate start and end of visible pages
    let startPage = Math.max(2, currentPage.value - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages.value - 1, startPage + maxVisiblePages - 3);

    // Adjust if we're near the beginning
    if (startPage > 2) {
      items.push({ type: 'ellipsis' });
    }

    // Add visible pages
    for (let i = startPage; i <= endPage; i++) {
      items.push({ type: 'page', value: i });
    }

    // Adjust if we're near the end
    if (endPage < totalPages.value - 1) {
      items.push({ type: 'ellipsis' });
    }

    // Always show last page
    items.push({ type: 'page', value: totalPages.value });
  }

  return items;
});

// Search and filters
const searchQuery = ref('');
const filters = ref({
  role: '',
  status: '',
  sortBy: 'username'
});

// Modals
const showAddModal = ref(false);
const showEditModal = ref(false);
const showDeleteModal = ref(false);
const userToDelete = ref(null);
const userForm = ref({
  id: null,
  username: '',
  email: '',
  password: '',
  role: 'Buyer',
  status: 'active'
});

// Methods
const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

const getRoleTagClass = (role) => {
  switch (role) {
    case 'Admin':
      return 'is-danger';
    case 'Moderator':
      return 'is-warning';
    case 'Seller':
      return 'is-success';
    case 'Buyer':
      return 'is-info';
    default:
      return 'is-light';
  }
};

const getStatusTagClass = (status) => {
  switch (status) {
    case 'active':
      return 'is-success';
    case 'pending':
      return 'is-warning';
    case 'inactive':
      return 'is-danger';
    default:
      return 'is-light';
  }
};

const searchUsers = () => {
  // Here you would call your API with search parameters
  console.log('Searching for:', searchQuery.value, 'with filters:', filters.value);
};

const changePage = (page) => {
  currentPage.value = page;
  // Here you would fetch the data for the new page
};

const editUser = (user) => {
  userForm.value = {
    id: user.id,
    username: user.username,
    email: user.email,
    password: '',
    role: user.role,
    status: user.status
  };
  showEditModal.value = true;
};

const confirmDelete = (user) => {
  userToDelete.value = user;
  showDeleteModal.value = true;
};

const deleteUser = () => {
  // Here you would call your API to delete the user
  console.log('Deleting user:', userToDelete.value);

  // Mock implementation - remove from local array
  const index = users.value.findIndex(u => u.id === userToDelete.value.id);
  if (index !== -1) {
    users.value.splice(index, 1);
  }

  showDeleteModal.value = false;
  userToDelete.value = null;
};

const saveUser = () => {
  if (userForm.value.id) {
    // Update existing user
    console.log('Updating user:', userForm.value);

    // Mock implementation - update in local array
    const index = users.value.findIndex(u => u.id === userForm.value.id);
    if (index !== -1) {
      users.value[index] = {
        ...users.value[index],
        username: userForm.value.username,
        email: userForm.value.email,
        role: userForm.value.role,
        status: userForm.value.status
      };
    }
  } else {
    // Create new user
    console.log('Creating user:', userForm.value);

    // Mock implementation - add to local array
    users.value.unshift({
      id: Math.random().toString(36).substring(2, 10) + Math.random().toString(36).substring(2, 10),
      username: userForm.value.username,
      email: userForm.value.email,
      role: userForm.value.role,
      status: userForm.value.status,
      lastSeenAt: new Date()
    });
  }

  closeModals();
};

const closeModals = () => {
  showAddModal.value = false;
  showEditModal.value = false;
  userForm.value = {
    id: null,
    username: '',
    email: '',
    password: '',
    role: 'Buyer',
    status: 'active'
  };
};

onMounted(() => {
  // Here you would fetch initial data from your API
  // Example: fetchUsers();
});
</script>

<style scoped>
.level {
  margin-bottom: 1.5rem;
}

.pagination {
  margin-top: 1.5rem;
}
</style>
