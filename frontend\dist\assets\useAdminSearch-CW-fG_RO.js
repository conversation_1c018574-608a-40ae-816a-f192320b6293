import{_ as ye,g as f,h as q,x as M,c as m,o as v,a as u,k as N,n as W,t as _,z as T,C as z,F as X,p as Z,A as Pe,d as oe,B as be}from"./index-BKy0rL_2.js";const Se={class:"search-and-filters"},_e={class:"card mb-4"},Ie={class:"card-content"},Ae={class:"columns is-multiline"},Ce={class:"field"},Fe={class:"label"},Ue={class:"control has-icons-left"},Oe=["placeholder"],we={class:"field"},Be={class:"label"},ke={class:"control"},Ee={key:0,class:"select is-fullwidth"},Ne=["onUpdate:modelValue","onChange"],Te={key:0,value:""},Ve=["value"],je=["placeholder","onUpdate:modelValue","onInput"],xe=["onUpdate:modelValue","onChange"],De=["placeholder","onUpdate:modelValue","onInput"],Re={key:0,class:"column is-2"},$e={class:"field",style:{"margin-top":"1.9rem"}},ze={class:"buttons is-right"},qe={key:0,class:"level mb-4"},Me={class:"level-left"},Le={class:"level-item"},Je={key:0},Qe={__name:"SearchAndFilters",props:{filters:{type:Object,required:!0},filterFields:{type:Array,default:()=>[]},searchLabel:{type:String,default:"Search"},searchPlaceholder:{type:String,default:"Search..."},searchColumnClass:{type:String,default:"is-5"},showResetButton:{type:Boolean,default:!0},resetButtonText:{type:String,default:"Reset Filters"},showStatusBar:{type:Boolean,default:!0},totalItems:{type:Number,default:0},itemName:{type:String,default:"items"},loading:{type:Boolean,default:!1}},emits:["search-changed","filter-changed","reset-filters"],setup(y,{emit:L}){const S=y,I=L,i=f({...S.filters}),O=q(()=>Object.values(i.value).some(l=>l!==""&&l!==null&&l!==void 0)),K=q(()=>Object.entries(i.value).filter(([l,s])=>s!==""&&s!==null&&s!==void 0).reduce((l,[s,t])=>(l[s]=t,l),{})),ee=()=>{I("search-changed",i.value.search)},w=(l,s)=>{I("filter-changed",l,s)},J=()=>{Object.keys(i.value).forEach(l=>{i.value[l]=""}),I("reset-filters")},B=l=>{if(l==="search")return"Search";const s=S.filterFields.find(t=>t.key===l);return s?s.label:l},k=(l,s)=>{const t=S.filterFields.find(o=>o.key===l);if(t&&t.type==="select"&&t.options){const o=t.options.find(P=>P.value===s);return o?o.label:s}return s};return M(()=>S.filters,l=>{i.value={...l}},{deep:!0,immediate:!0}),M(i,l=>{l.search!==S.filters.search&&I("search-changed",l.search),Object.keys(l).forEach(s=>{s!=="search"&&l[s]!==S.filters[s]&&I("filter-changed",s,l[s])})},{deep:!0}),(l,s)=>(v(),m("div",Se,[u("div",_e,[u("div",Ie,[u("div",Ae,[u("div",{class:W(["column",y.searchColumnClass])},[u("div",Ce,[u("label",Fe,_(y.searchLabel),1),u("div",Ue,[T(u("input",{class:"input",type:"text",placeholder:y.searchPlaceholder,"onUpdate:modelValue":s[0]||(s[0]=t=>i.value.search=t),onInput:ee},null,40,Oe),[[z,i.value.search]]),s[1]||(s[1]=u("span",{class:"icon is-small is-left"},[u("i",{class:"fas fa-search"})],-1))])])],2),(v(!0),m(X,null,Z(y.filterFields,t=>(v(),m("div",{key:t.key,class:W(["column",t.columnClass||"is-3"])},[u("div",we,[u("label",Be,_(t.label),1),u("div",ke,[t.type==="select"?(v(),m("div",Ee,[T(u("select",{"onUpdate:modelValue":o=>i.value[t.key]=o,onChange:o=>w(t.key,i.value[t.key])},[t.allOption?(v(),m("option",Te,_(t.allOption),1)):N("",!0),(v(!0),m(X,null,Z(t.options,o=>(v(),m("option",{key:o.value,value:o.value},_(o.label),9,Ve))),128))],40,Ne),[[Pe,i.value[t.key]]])])):t.type==="text"?T((v(),m("input",{key:1,class:"input",type:"text",placeholder:t.placeholder,"onUpdate:modelValue":o=>i.value[t.key]=o,onInput:o=>w(t.key,i.value[t.key])},null,40,je)),[[z,i.value[t.key]]]):t.type==="date"?T((v(),m("input",{key:2,class:"input",type:"date","onUpdate:modelValue":o=>i.value[t.key]=o,onChange:o=>w(t.key,i.value[t.key])},null,40,xe)),[[z,i.value[t.key]]]):t.type==="number"?T((v(),m("input",{key:3,class:"input",type:"number",placeholder:t.placeholder,"onUpdate:modelValue":o=>i.value[t.key]=o,onInput:o=>w(t.key,i.value[t.key])},null,40,De)),[[z,i.value[t.key]]]):N("",!0)])])],2))),128)),y.showResetButton?(v(),m("div",Re,[u("div",$e,[u("div",ze,[u("button",{class:W(["button is-light",{"is-loading":y.loading}]),onClick:J},_(y.resetButtonText),3)])])])):N("",!0)])])]),y.showStatusBar&&(y.totalItems>0||O.value)?(v(),m("div",qe,[u("div",Me,[u("div",Le,[u("p",null,[u("strong",null,_(y.totalItems),1),oe(" "+_(y.itemName)+" found ",1),O.value?(v(),m("span",Je,[s[2]||(s[2]=oe(" with filters: ")),(v(!0),m(X,null,Z(K.value,(t,o)=>(v(),m("span",{key:o,class:"tag is-info is-light mr-1"},_(B(o))+": "+_(k(o,t)),1))),128))])):N("",!0)])])])])):N("",!0)]))}},He=ye(Qe,[["__scopeId","data-v-bfd2fe63"]]);function We(y={}){const{fetchFunction:L,defaultFilters:S={},debounceTime:I=300,defaultPageSize:i=15,clientSideSearch:O=!1,enableVirtualScrolling:K=!1,virtualScrollThreshold:ee=100,enableInfiniteScroll:w=!1,enableOptimisticUpdates:J=!1}=y,B=f([]),k=f(!1),l=f(null),s=f(!0),t=f(0),o=f(0),P=f(1),A=f(1),C=f(0),V=f(i),j=f(!1),x=f(!1),D=f(new Map),Q=f(new Set),h=be({search:"",...S}),Y=f("createdAt"),G=f("desc"),ne=q(()=>Object.values(h).some(a=>a!==""&&a!==null&&a!==void 0)),E=q(()=>Object.entries(h).filter(([a,p])=>p!==""&&p!==null&&p!==void 0).reduce((a,[p,b])=>(a[p]=b,a),{})),F=async(a=1,p={})=>{var ae,se,le;const{append:b=!1,force:U=!1,silent:R=!1}=p,pe=++o.value,$=`${a}-${JSON.stringify(E.value)}`;if(Q.value.has($)&&!U){console.log("🔄 Duplicate request prevented:",$);return}`${a}${JSON.stringify(E.value)}${Y.value}${G.value}`;const me=Date.now();if(!U&&t.value&&me-t.value<3e4){console.log("📦 Using cached data");return}Q.value.add($),R||(b?j.value=!0:(s.value||P.value!==a)&&(k.value=!0)),b||(P.value=a),l.value=null;try{const n={page:P.value,pageSize:V.value,orderBy:Y.value,descending:G.value==="desc"};h.search&&h.search.trim()&&(n.filter=h.search.trim(),n.search=h.search.trim()),O?Object.entries(E.value).forEach(([d,r])=>{d!=="search"&&(d==="sortBy"?n.orderBy=r:d==="sortOrder"?n.descending=r==="desc":d==="status"?(console.log("🔄 Converting status filter (client-side):",r),n.status=r):d==="categoryId"?n.categoryId=r:d==="categoryIds"?Array.isArray(r)&&r.length>0&&(n.categoryIds=r.join(",")):d==="stock"?n.stock=r:n[d]=r)}):Object.entries(E.value).forEach(([d,r])=>{d!=="search"&&(d==="sortBy"?n.orderBy=r:d==="sortOrder"?n.descending=r==="desc":d==="status"?(console.log("🔄 Converting status filter:",r),n.status=r):d==="categoryId"?n.categoryId=r:d==="categoryIds"?Array.isArray(r)&&r.length>0&&(n.categoryIds=r.join(",")):d==="stock"?n.stock=r:n[d]=r)}),console.log("Fetching data with params:",n);const e=await L(n);if(pe!==o.value){console.log("🚫 Request outdated, ignoring response");return}let g=[],c={};if(console.log("Processing response:",e),e&&(e.data&&Array.isArray(e.data)&&e.pagination?(console.log("Using updated service format with pagination"),g=e.data,c={total:e.pagination.total||e.total,page:e.pagination.page||e.currentPage,totalPages:e.pagination.totalPages||e.totalPages,perPage:e.pagination.pageSize||e.pagination.limit}):e.data&&Array.isArray(e.data)&&(e.total!==void 0||e.totalPages!==void 0)?(console.log("Using data array with separate pagination"),g=e.data,c={total:e.total||e.totalItems,page:e.currentPage||e.page,totalPages:e.totalPages||e.lastPage,perPage:e.pageSize||e.perPage}):e.categories&&Array.isArray(e.categories)?(console.log("Using categories service format"),g=e.categories,c={total:e.totalCount||e.total,page:1,totalPages:1,perPage:g.length}):e.items&&Array.isArray(e.items)?(console.log("Using items array format"),g=e.items,c={total:e.total||e.totalItems||g.length,page:e.currentPage||e.page||1,totalPages:e.totalPages||e.lastPage||1,perPage:e.pageSize||e.perPage||g.length}):e.success&&e.data&&e.data.data?(console.log("Using ApiResponse<PaginatedResponse> format"),g=e.data.data,c={total:e.data.total,page:e.data.currentPage,totalPages:e.data.lastPage,perPage:e.data.perPage}):e.users?(console.log("Using Users service format"),g=e.users,c=e.pagination||{}):Array.isArray(e)?(console.log("Using direct array format"),g=e,c={total:e.length,page:1,totalPages:1,perPage:e.length}):e.data?(console.log("Using legacy data format"),Array.isArray(e.data)?(g=e.data,c=e.pagination||{total:e.data.length,page:1,totalPages:1,perPage:e.data.length}):e.data.data&&(g=e.data.data,c=e.data)):(console.warn("Unknown response format:",e),g=[],c={})),O&&h.search){const d=h.search.toLowerCase();g=g.filter(r=>ue(r,d))}if(B.value=g,O&&h.search)C.value=g.length,A.value=Math.ceil(C.value/V.value)||1;else{const d=c.total||c.totalItems||c.Total||g.length;C.value=d;const r=c.perPage||c.pageSize||V.value,ve=Math.ceil(d/r);A.value=c.totalPages||c.lastPage||c.LastPage||ve||1,P.value=c.page||c.currentPage||c.CurrentPage||a,console.log("Pagination updated:",{totalItems:C.value,totalPages:A.value,currentPage:P.value,itemsPerPage:r,paginationData:c})}console.log("Data fetched successfully:",{itemsCount:B.value.length,totalItems:C.value,totalPages:A.value,currentPage:P.value})}catch(n){console.error("Error fetching data:",n);let e="Failed to load data";n.code==="ECONNREFUSED"||n.message.includes("ECONNREFUSED")?e="Cannot connect to server. Please ensure the backend is running.":((ae=n.response)==null?void 0:ae.status)===401?e="Authentication required. Please log in.":((se=n.response)==null?void 0:se.status)===403?e="Access denied. You do not have permission to view this data.":((le=n.response)==null?void 0:le.status)===404?e="API endpoint not found.":n.message&&(e=n.message),l.value=e,B.value=[],A.value=1,C.value=0}finally{k.value=!1,j.value=!1,Q.value.delete($),s.value&&(s.value=!1)}},re=(a,p)=>{J&&(D.value.set(a,p),setTimeout(()=>{D.value.delete(a)},3e4))},ie=a=>{D.value.delete(a)},ce=async()=>{if(!w||j.value||x.value)return;const a=P.value+1;if(a>A.value){x.value=!0;return}await F(a,{append:!0})},de=async()=>{D.value.clear(),x.value=!1,await F(1,{force:!0})},ue=(a,p)=>["name","username","email","title","description","contactEmail","contactPhone","slug","id","customerName","customerEmail","customerPhone","orderId","orderNumber","customerId"].some(U=>{const R=ge(a,U);return R&&R.toString().toLowerCase().includes(p)}),ge=(a,p)=>p.split(".").reduce((b,U)=>b&&b[U]!==void 0?b[U]:null,a),he=()=>{Object.keys(h).forEach(a=>{a==="search"?h[a]="":h[a]=S[a]||""}),P.value=1,F(1)},fe=a=>{F(a)};let H=null;const te=()=>{H&&clearTimeout(H),H=setTimeout(()=>{P.value=1,F(1)},I)};return M(()=>h.search,()=>{console.log("Search changed:",h.search),te()}),Object.keys(S).forEach(a=>{a!=="search"&&M(()=>h[a],(p,b)=>{p!==b&&(console.log(`Filter ${a} changed:`,h[a]),P.value=1,F(1))})}),{items:B,loading:k,error:l,isFirstLoad:s,isLoadingMore:j,allItemsLoaded:x,currentPage:P,totalPages:A,totalItems:C,pageSize:V,filters:h,hasFilters:ne,activeFilters:E,sortBy:Y,sortOrder:G,fetchData:F,resetFilters:he,handlePageChange:fe,debouncedSearch:te,loadMore:ce,forceRefresh:de,addOptimisticUpdate:re,removeOptimisticUpdate:ie}}export{He as S,We as u};
