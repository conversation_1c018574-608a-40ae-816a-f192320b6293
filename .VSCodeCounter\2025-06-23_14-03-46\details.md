# Details

Date : 2025-06-23 14:03:46

Directory d:\\Diplomkla_VS_Code\\NAXYI – копія\\Marketplace

Total : 1190 files,  79265 codes, 3600 comments, 13963 blanks, all 96828 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [Marketplace.Application/ApplicationServiceExtensions.cs](/Marketplace.Application/ApplicationServiceExtensions.cs) | C# | 26 | 3 | 8 | 37 |
| [Marketplace.Application/Behaviors/ValidationBehavior.cs](/Marketplace.Application/Behaviors/ValidationBehavior.cs) | C# | 25 | 0 | 7 | 32 |
| [Marketplace.Application/Commands/Address/BulkDeleteAddressCommand.cs](/Marketplace.Application/Commands/Address/BulkDeleteAddressCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Address/BulkDeleteAddressCommandHandler.cs](/Marketplace.Application/Commands/Address/BulkDeleteAddressCommandHandler.cs) | C# | 31 | 1 | 7 | 39 |
| [Marketplace.Application/Commands/Address/DeleteAddressCommand.cs](/Marketplace.Application/Commands/Address/DeleteAddressCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Address/DeleteAddressCommandHandler.cs](/Marketplace.Application/Commands/Address/DeleteAddressCommandHandler.cs) | C# | 23 | 1 | 9 | 33 |
| [Marketplace.Application/Commands/Address/StoreAddressCommand.cs](/Marketplace.Application/Commands/Address/StoreAddressCommand.cs) | C# | 9 | 0 | 3 | 12 |
| [Marketplace.Application/Commands/Address/StoreAddressCommandHandler.cs](/Marketplace.Application/Commands/Address/StoreAddressCommandHandler.cs) | C# | 24 | 1 | 8 | 33 |
| [Marketplace.Application/Commands/Address/UpdateAddressCommand.cs](/Marketplace.Application/Commands/Address/UpdateAddressCommand.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Commands/Address/UpdateAddressCommandHandler.cs](/Marketplace.Application/Commands/Address/UpdateAddressCommandHandler.cs) | C# | 40 | 14 | 12 | 66 |
| [Marketplace.Application/Commands/Auth/ConfirmEmailCommand.cs](/Marketplace.Application/Commands/Auth/ConfirmEmailCommand.cs) | C# | 7 | 0 | 2 | 9 |
| [Marketplace.Application/Commands/Auth/ConfirmEmailCommandHandler.cs](/Marketplace.Application/Commands/Auth/ConfirmEmailCommandHandler.cs) | C# | 25 | 0 | 6 | 31 |
| [Marketplace.Application/Commands/Auth/GoogleLoginCommand.cs](/Marketplace.Application/Commands/Auth/GoogleLoginCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/Auth/GoogleLoginCommandHandler.cs](/Marketplace.Application/Commands/Auth/GoogleLoginCommandHandler.cs) | C# | 50 | 0 | 7 | 57 |
| [Marketplace.Application/Commands/Auth/LoginCommand.cs](/Marketplace.Application/Commands/Auth/LoginCommand.cs) | C# | 8 | 5 | 2 | 15 |
| [Marketplace.Application/Commands/Auth/LoginCommandHandler.cs](/Marketplace.Application/Commands/Auth/LoginCommandHandler.cs) | C# | 51 | 2 | 9 | 62 |
| [Marketplace.Application/Commands/Auth/PatchProfileCommand.cs](/Marketplace.Application/Commands/Auth/PatchProfileCommand.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Commands/Auth/PatchProfileCommandHandler.cs](/Marketplace.Application/Commands/Auth/PatchProfileCommandHandler.cs) | C# | 58 | 3 | 11 | 72 |
| [Marketplace.Application/Commands/Auth/RegisterCommand.cs](/Marketplace.Application/Commands/Auth/RegisterCommand.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Application/Commands/Auth/RegisterCommandHandler.cs](/Marketplace.Application/Commands/Auth/RegisterCommandHandler.cs) | C# | 66 | 6 | 12 | 84 |
| [Marketplace.Application/Commands/Auth/RequestPasswordResetCommand.cs](/Marketplace.Application/Commands/Auth/RequestPasswordResetCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/Auth/RequestPasswordResetCommandHandler.cs](/Marketplace.Application/Commands/Auth/RequestPasswordResetCommandHandler.cs) | C# | 43 | 2 | 6 | 51 |
| [Marketplace.Application/Commands/Auth/ResetPasswordCommand.cs](/Marketplace.Application/Commands/Auth/ResetPasswordCommand.cs) | C# | 8 | 0 | 2 | 10 |
| [Marketplace.Application/Commands/Auth/ResetPasswordCommandHandler.cs](/Marketplace.Application/Commands/Auth/ResetPasswordCommandHandler.cs) | C# | 26 | 1 | 6 | 33 |
| [Marketplace.Application/Commands/Auth/UpdatePasswordCommand.cs](/Marketplace.Application/Commands/Auth/UpdatePasswordCommand.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Application/Commands/Auth/UpdatePasswordCommandHandler.cs](/Marketplace.Application/Commands/Auth/UpdatePasswordCommandHandler.cs) | C# | 32 | 1 | 6 | 39 |
| [Marketplace.Application/Commands/Auth/UpdateProfileCommand.cs](/Marketplace.Application/Commands/Auth/UpdateProfileCommand.cs) | C# | 16 | 0 | 3 | 19 |
| [Marketplace.Application/Commands/Auth/UpdateProfileCommandHandler.cs](/Marketplace.Application/Commands/Auth/UpdateProfileCommandHandler.cs) | C# | 74 | 4 | 11 | 89 |
| [Marketplace.Application/Commands/CartItem/DeleteCartItemCommand.cs](/Marketplace.Application/Commands/CartItem/DeleteCartItemCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/CartItem/DeleteCartItemCommandHandler.cs](/Marketplace.Application/Commands/CartItem/DeleteCartItemCommandHandler.cs) | C# | 32 | 2 | 10 | 44 |
| [Marketplace.Application/Commands/CartItem/StoreCartItemCommand.cs](/Marketplace.Application/Commands/CartItem/StoreCartItemCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/CartItem/StoreCartItemCommandHandler.cs](/Marketplace.Application/Commands/CartItem/StoreCartItemCommandHandler.cs) | C# | 50 | 5 | 10 | 65 |
| [Marketplace.Application/Commands/CartItem/UpdateCartItemCommand.cs](/Marketplace.Application/Commands/CartItem/UpdateCartItemCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/CartItem/UpdateCartItemCommandHandler.cs](/Marketplace.Application/Commands/CartItem/UpdateCartItemCommandHandler.cs) | C# | 32 | 1 | 9 | 42 |
| [Marketplace.Application/Commands/Cart/CheckoutCartCommand.cs](/Marketplace.Application/Commands/Cart/CheckoutCartCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/Cart/CheckoutCartCommandHandler.cs](/Marketplace.Application/Commands/Cart/CheckoutCartCommandHandler.cs) | C# | 89 | 8 | 23 | 120 |
| [Marketplace.Application/Commands/Cart/ClearCartCommand.cs](/Marketplace.Application/Commands/Cart/ClearCartCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Cart/ClearCartCommandHandler.cs](/Marketplace.Application/Commands/Cart/ClearCartCommandHandler.cs) | C# | 27 | 1 | 9 | 37 |
| [Marketplace.Application/Commands/Category/BulkDeleteCategoryCommand.cs](/Marketplace.Application/Commands/Category/BulkDeleteCategoryCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Category/BulkDeleteCategoryCommandHandler.cs](/Marketplace.Application/Commands/Category/BulkDeleteCategoryCommandHandler.cs) | C# | 25 | 0 | 7 | 32 |
| [Marketplace.Application/Commands/Category/DeleteCategoryCommand.cs](/Marketplace.Application/Commands/Category/DeleteCategoryCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Category/DeleteCategoryCommandHandler.cs](/Marketplace.Application/Commands/Category/DeleteCategoryCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Category/StoreCategoryCommand.cs](/Marketplace.Application/Commands/Category/StoreCategoryCommand.cs) | C# | 12 | 0 | 4 | 16 |
| [Marketplace.Application/Commands/Category/StoreCategoryCommandHandler.cs](/Marketplace.Application/Commands/Category/StoreCategoryCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Category/UpdateCategoryCommand.cs](/Marketplace.Application/Commands/Category/UpdateCategoryCommand.cs) | C# | 13 | 0 | 4 | 17 |
| [Marketplace.Application/Commands/Category/UpdateCategoryCommandHandler.cs](/Marketplace.Application/Commands/Category/UpdateCategoryCommandHandler.cs) | C# | 33 | 1 | 8 | 42 |
| [Marketplace.Application/Commands/Category/UploadCategoryImageCommand.cs](/Marketplace.Application/Commands/Category/UploadCategoryImageCommand.cs) | C# | 8 | 3 | 3 | 14 |
| [Marketplace.Application/Commands/Category/UploadCategoryImageCommandHandler.cs](/Marketplace.Application/Commands/Category/UploadCategoryImageCommandHandler.cs) | C# | 42 | 8 | 9 | 59 |
| [Marketplace.Application/Commands/Chat/CreateUserChatCommand.cs](/Marketplace.Application/Commands/Chat/CreateUserChatCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/Chat/CreateUserChatCommandHandler.cs](/Marketplace.Application/Commands/Chat/CreateUserChatCommandHandler.cs) | C# | 42 | 6 | 10 | 58 |
| [Marketplace.Application/Commands/Chat/DeleteChatCommand.cs](/Marketplace.Application/Commands/Chat/DeleteChatCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Chat/DeleteChatCommandHandler.cs](/Marketplace.Application/Commands/Chat/DeleteChatCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Chat/StoreChatCommand.cs](/Marketplace.Application/Commands/Chat/StoreChatCommand.cs) | C# | 7 | 0 | 2 | 9 |
| [Marketplace.Application/Commands/Chat/StoreChatCommandHandler.cs](/Marketplace.Application/Commands/Chat/StoreChatCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Chat/UpdateChatCommand.cs](/Marketplace.Application/Commands/Chat/UpdateChatCommand.cs) | C# | 8 | 0 | 2 | 10 |
| [Marketplace.Application/Commands/Chat/UpdateChatCommandHandler.cs](/Marketplace.Application/Commands/Chat/UpdateChatCommandHandler.cs) | C# | 22 | 11 | 8 | 41 |
| [Marketplace.Application/Commands/CompanyFinance/UpdateSellerCompanyFinanceCommand.cs](/Marketplace.Application/Commands/CompanyFinance/UpdateSellerCompanyFinanceCommand.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Commands/CompanyFinance/UpdateSellerCompanyFinanceCommandHandler.cs](/Marketplace.Application/Commands/CompanyFinance/UpdateSellerCompanyFinanceCommandHandler.cs) | C# | 50 | 4 | 11 | 65 |
| [Marketplace.Application/Commands/CompanySchedule/CreateSellerCompanyScheduleCommand.cs](/Marketplace.Application/Commands/CompanySchedule/CreateSellerCompanyScheduleCommand.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Commands/CompanySchedule/CreateSellerCompanyScheduleCommandHandler.cs](/Marketplace.Application/Commands/CompanySchedule/CreateSellerCompanyScheduleCommandHandler.cs) | C# | 42 | 4 | 11 | 57 |
| [Marketplace.Application/Commands/CompanySchedule/DeleteCompanyScheduleCommand.cs](/Marketplace.Application/Commands/CompanySchedule/DeleteCompanyScheduleCommand.cs) | C# | 10 | 0 | 4 | 14 |
| [Marketplace.Application/Commands/CompanySchedule/DeleteCompanyScheduleCommandHandler.cs](/Marketplace.Application/Commands/CompanySchedule/DeleteCompanyScheduleCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/CompanySchedule/DeleteSellerCompanyScheduleCommand.cs](/Marketplace.Application/Commands/CompanySchedule/DeleteSellerCompanyScheduleCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/CompanySchedule/DeleteSellerCompanyScheduleCommandHandler.cs](/Marketplace.Application/Commands/CompanySchedule/DeleteSellerCompanyScheduleCommandHandler.cs) | C# | 34 | 5 | 10 | 49 |
| [Marketplace.Application/Commands/CompanySchedule/StoreCompanyScheduleCommand.cs](/Marketplace.Application/Commands/CompanySchedule/StoreCompanyScheduleCommand.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Commands/CompanySchedule/StoreCompanyScheduleCommandHandler.cs](/Marketplace.Application/Commands/CompanySchedule/StoreCompanyScheduleCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/CompanySchedule/UpdateCompanyScheduleCommand.cs](/Marketplace.Application/Commands/CompanySchedule/UpdateCompanyScheduleCommand.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Commands/CompanySchedule/UpdateCompanyScheduleCommandHandler.cs](/Marketplace.Application/Commands/CompanySchedule/UpdateCompanyScheduleCommandHandler.cs) | C# | 28 | 11 | 9 | 48 |
| [Marketplace.Application/Commands/CompanySchedule/UpdateSellerCompanyScheduleCommand.cs](/Marketplace.Application/Commands/CompanySchedule/UpdateSellerCompanyScheduleCommand.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Commands/CompanySchedule/UpdateSellerCompanyScheduleCommandHandler.cs](/Marketplace.Application/Commands/CompanySchedule/UpdateSellerCompanyScheduleCommandHandler.cs) | C# | 38 | 5 | 11 | 54 |
| [Marketplace.Application/Commands/CompanyUser/AddSellerCompanyUserCommand.cs](/Marketplace.Application/Commands/CompanyUser/AddSellerCompanyUserCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/CompanyUser/AddSellerCompanyUserCommandHandler.cs](/Marketplace.Application/Commands/CompanyUser/AddSellerCompanyUserCommandHandler.cs) | C# | 59 | 5 | 13 | 77 |
| [Marketplace.Application/Commands/CompanyUser/DeleteCompanyUserCommand.cs](/Marketplace.Application/Commands/CompanyUser/DeleteCompanyUserCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/CompanyUser/DeleteCompanyUserCommandHandler.cs](/Marketplace.Application/Commands/CompanyUser/DeleteCompanyUserCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/CompanyUser/DeleteSellerCompanyUserCommand.cs](/Marketplace.Application/Commands/CompanyUser/DeleteSellerCompanyUserCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/CompanyUser/DeleteSellerCompanyUserCommandHandler.cs](/Marketplace.Application/Commands/CompanyUser/DeleteSellerCompanyUserCommandHandler.cs) | C# | 52 | 6 | 13 | 71 |
| [Marketplace.Application/Commands/CompanyUser/StoreCompanyUserCommand.cs](/Marketplace.Application/Commands/CompanyUser/StoreCompanyUserCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/CompanyUser/StoreCompanyUserCommandHandler.cs](/Marketplace.Application/Commands/CompanyUser/StoreCompanyUserCommandHandler.cs) | C# | 33 | 1 | 8 | 42 |
| [Marketplace.Application/Commands/CompanyUser/UpdateCompanyUserCommand.cs](/Marketplace.Application/Commands/CompanyUser/UpdateCompanyUserCommand.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Application/Commands/CompanyUser/UpdateCompanyUserCommandHandler.cs](/Marketplace.Application/Commands/CompanyUser/UpdateCompanyUserCommandHandler.cs) | C# | 39 | 3 | 11 | 53 |
| [Marketplace.Application/Commands/CompanyUser/UpdateSellerCompanyUserCommand.cs](/Marketplace.Application/Commands/CompanyUser/UpdateSellerCompanyUserCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/CompanyUser/UpdateSellerCompanyUserCommandHandler.cs](/Marketplace.Application/Commands/CompanyUser/UpdateSellerCompanyUserCommandHandler.cs) | C# | 54 | 5 | 12 | 71 |
| [Marketplace.Application/Commands/Company/ActivateSellerCompanyCommand.cs](/Marketplace.Application/Commands/Company/ActivateSellerCompanyCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Company/ActivateSellerCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/ActivateSellerCompanyCommandHandler.cs) | C# | 34 | 4 | 10 | 48 |
| [Marketplace.Application/Commands/Company/ApproveCompanyCommand.cs](/Marketplace.Application/Commands/Company/ApproveCompanyCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Company/ApproveCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/ApproveCompanyCommandHandler.cs) | C# | 26 | 2 | 6 | 34 |
| [Marketplace.Application/Commands/Company/BulkApproveCompanyCommand.cs](/Marketplace.Application/Commands/Company/BulkApproveCompanyCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Company/BulkApproveCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/BulkApproveCompanyCommandHandler.cs) | C# | 24 | 3 | 7 | 34 |
| [Marketplace.Application/Commands/Company/BulkDeleteCompanyCommand.cs](/Marketplace.Application/Commands/Company/BulkDeleteCompanyCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Company/BulkDeleteCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/BulkDeleteCompanyCommandHandler.cs) | C# | 25 | 0 | 7 | 32 |
| [Marketplace.Application/Commands/Company/BulkRejectCompanyCommand.cs](/Marketplace.Application/Commands/Company/BulkRejectCompanyCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Company/BulkRejectCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/BulkRejectCompanyCommandHandler.cs) | C# | 24 | 1 | 7 | 32 |
| [Marketplace.Application/Commands/Company/DeactivateSellerCompanyCommand.cs](/Marketplace.Application/Commands/Company/DeactivateSellerCompanyCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Company/DeactivateSellerCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/DeactivateSellerCompanyCommandHandler.cs) | C# | 34 | 4 | 10 | 48 |
| [Marketplace.Application/Commands/Company/DeleteCompanyCommand.cs](/Marketplace.Application/Commands/Company/DeleteCompanyCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Company/DeleteCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/DeleteCompanyCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Company/ModeratorApproveCompanyCommand.cs](/Marketplace.Application/Commands/Company/ModeratorApproveCompanyCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/Company/ModeratorApproveCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/ModeratorApproveCompanyCommandHandler.cs) | C# | 26 | 0 | 6 | 32 |
| [Marketplace.Application/Commands/Company/ModeratorBulkApproveCompaniesCommand.cs](/Marketplace.Application/Commands/Company/ModeratorBulkApproveCompaniesCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/Company/ModeratorBulkApproveCompaniesCommandHandler.cs](/Marketplace.Application/Commands/Company/ModeratorBulkApproveCompaniesCommandHandler.cs) | C# | 24 | 1 | 7 | 32 |
| [Marketplace.Application/Commands/Company/ModeratorBulkRejectCompaniesCommand.cs](/Marketplace.Application/Commands/Company/ModeratorBulkRejectCompaniesCommand.cs) | C# | 5 | 0 | 3 | 8 |
| [Marketplace.Application/Commands/Company/ModeratorBulkRejectCompaniesCommandHandler.cs](/Marketplace.Application/Commands/Company/ModeratorBulkRejectCompaniesCommandHandler.cs) | C# | 24 | 1 | 7 | 32 |
| [Marketplace.Application/Commands/Company/ModeratorRejectCompanyCommand.cs](/Marketplace.Application/Commands/Company/ModeratorRejectCompanyCommand.cs) | C# | 5 | 0 | 3 | 8 |
| [Marketplace.Application/Commands/Company/ModeratorRejectCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/ModeratorRejectCompanyCommandHandler.cs) | C# | 26 | 0 | 6 | 32 |
| [Marketplace.Application/Commands/Company/PatchSellerCompanyCommand.cs](/Marketplace.Application/Commands/Company/PatchSellerCompanyCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/Company/PatchSellerCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/PatchSellerCompanyCommandHandler.cs) | C# | 98 | 4 | 10 | 112 |
| [Marketplace.Application/Commands/Company/RejectCompanyCommand.cs](/Marketplace.Application/Commands/Company/RejectCompanyCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Company/RejectCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/RejectCompanyCommandHandler.cs) | C# | 26 | 0 | 6 | 32 |
| [Marketplace.Application/Commands/Company/StoreCompanyCommand.cs](/Marketplace.Application/Commands/Company/StoreCompanyCommand.cs) | C# | 17 | 0 | 4 | 21 |
| [Marketplace.Application/Commands/Company/StoreCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/StoreCompanyCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Company/UpdateCompanyCommand.cs](/Marketplace.Application/Commands/Company/UpdateCompanyCommand.cs) | C# | 21 | 0 | 4 | 25 |
| [Marketplace.Application/Commands/Company/UpdateCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/UpdateCompanyCommandHandler.cs) | C# | 39 | 1 | 9 | 49 |
| [Marketplace.Application/Commands/Company/UpdateDetailedCompanyCommand.cs](/Marketplace.Application/Commands/Company/UpdateDetailedCompanyCommand.cs) | C# | 42 | 3 | 10 | 55 |
| [Marketplace.Application/Commands/Company/UpdateDetailedCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/UpdateDetailedCompanyCommandHandler.cs) | C# | 111 | 12 | 19 | 142 |
| [Marketplace.Application/Commands/Company/UpdateSellerCompanyCommand.cs](/Marketplace.Application/Commands/Company/UpdateSellerCompanyCommand.cs) | C# | 18 | 0 | 3 | 21 |
| [Marketplace.Application/Commands/Company/UpdateSellerCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/UpdateSellerCompanyCommandHandler.cs) | C# | 48 | 4 | 10 | 62 |
| [Marketplace.Application/Commands/Company/UpdateSellerCompanySettingsCommand.cs](/Marketplace.Application/Commands/Company/UpdateSellerCompanySettingsCommand.cs) | C# | 14 | 0 | 3 | 17 |
| [Marketplace.Application/Commands/Company/UpdateSellerCompanySettingsCommandHandler.cs](/Marketplace.Application/Commands/Company/UpdateSellerCompanySettingsCommandHandler.cs) | C# | 44 | 4 | 10 | 58 |
| [Marketplace.Application/Commands/Company/UploadCompanyImageCommand.cs](/Marketplace.Application/Commands/Company/UploadCompanyImageCommand.cs) | C# | 8 | 3 | 3 | 14 |
| [Marketplace.Application/Commands/Company/UploadCompanyImageCommandHandler.cs](/Marketplace.Application/Commands/Company/UploadCompanyImageCommandHandler.cs) | C# | 45 | 8 | 9 | 62 |
| [Marketplace.Application/Commands/Company/UploadSellerCompanyImageCommand.cs](/Marketplace.Application/Commands/Company/UploadSellerCompanyImageCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/Company/UploadSellerCompanyImageCommandHandler.cs](/Marketplace.Application/Commands/Company/UploadSellerCompanyImageCommandHandler.cs) | C# | 52 | 7 | 13 | 72 |
| [Marketplace.Application/Commands/Coupon/ApplyCouponCommand.cs](/Marketplace.Application/Commands/Coupon/ApplyCouponCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/Coupon/ApplyCouponCommandHandler.cs](/Marketplace.Application/Commands/Coupon/ApplyCouponCommandHandler.cs) | C# | 48 | 7 | 12 | 67 |
| [Marketplace.Application/Commands/Coupon/DeleteCouponCommand.cs](/Marketplace.Application/Commands/Coupon/DeleteCouponCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Coupon/DeleteCouponCommandHandler.cs](/Marketplace.Application/Commands/Coupon/DeleteCouponCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Coupon/StoreCouponCommand.cs](/Marketplace.Application/Commands/Coupon/StoreCouponCommand.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Commands/Coupon/StoreCouponCommandHandler.cs](/Marketplace.Application/Commands/Coupon/StoreCouponCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Coupon/UpdateCouponCommand.cs](/Marketplace.Application/Commands/Coupon/UpdateCouponCommand.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Commands/Coupon/UpdateCouponCommandHandler.cs](/Marketplace.Application/Commands/Coupon/UpdateCouponCommandHandler.cs) | C# | 29 | 11 | 9 | 49 |
| [Marketplace.Application/Commands/Favorite/DeleteFavoriteCommand.cs](/Marketplace.Application/Commands/Favorite/DeleteFavoriteCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Favorite/DeleteFavoriteCommandHandler.cs](/Marketplace.Application/Commands/Favorite/DeleteFavoriteCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Favorite/StoreFavoriteCommand.cs](/Marketplace.Application/Commands/Favorite/StoreFavoriteCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/Favorite/StoreFavoriteCommandHandler.cs](/Marketplace.Application/Commands/Favorite/StoreFavoriteCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Favorite/UpdateFavoriteCommand.cs](/Marketplace.Application/Commands/Favorite/UpdateFavoriteCommand.cs) | C# | 7 | 0 | 2 | 9 |
| [Marketplace.Application/Commands/Favorite/UpdateFavoriteCommandHandler.cs](/Marketplace.Application/Commands/Favorite/UpdateFavoriteCommandHandler.cs) | C# | 30 | 1 | 8 | 39 |
| [Marketplace.Application/Commands/File/DeleteImageCommand.cs](/Marketplace.Application/Commands/File/DeleteImageCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/File/DeleteImageCommandHandler.cs](/Marketplace.Application/Commands/File/DeleteImageCommandHandler.cs) | C# | 60 | 11 | 15 | 86 |
| [Marketplace.Application/Commands/File/UploadImageCommand.cs](/Marketplace.Application/Commands/File/UploadImageCommand.cs) | C# | 9 | 3 | 3 | 15 |
| [Marketplace.Application/Commands/File/UploadImageCommandHandler.cs](/Marketplace.Application/Commands/File/UploadImageCommandHandler.cs) | C# | 73 | 10 | 13 | 96 |
| [Marketplace.Application/Commands/Message/DeleteMessageCommand.cs](/Marketplace.Application/Commands/Message/DeleteMessageCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Message/DeleteMessageCommandHandler.cs](/Marketplace.Application/Commands/Message/DeleteMessageCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Message/MarkMessageAsReadCommand.cs](/Marketplace.Application/Commands/Message/MarkMessageAsReadCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/Message/MarkMessageAsReadCommandHandler.cs](/Marketplace.Application/Commands/Message/MarkMessageAsReadCommandHandler.cs) | C# | 33 | 8 | 12 | 53 |
| [Marketplace.Application/Commands/Message/ModeratorUpdateMessageCommand.cs](/Marketplace.Application/Commands/Message/ModeratorUpdateMessageCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/Message/ModeratorUpdateMessageCommandHandler.cs](/Marketplace.Application/Commands/Message/ModeratorUpdateMessageCommandHandler.cs) | C# | 27 | 1 | 7 | 35 |
| [Marketplace.Application/Commands/Message/SendMessageCommand.cs](/Marketplace.Application/Commands/Message/SendMessageCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/Message/SendMessageCommandHandler.cs](/Marketplace.Application/Commands/Message/SendMessageCommandHandler.cs) | C# | 30 | 4 | 9 | 43 |
| [Marketplace.Application/Commands/Message/StoreMessageCommand.cs](/Marketplace.Application/Commands/Message/StoreMessageCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/Message/StoreMessageCommandHandler.cs](/Marketplace.Application/Commands/Message/StoreMessageCommandHandler.cs) | C# | 36 | 8 | 10 | 54 |
| [Marketplace.Application/Commands/Message/UpdateMessageCommand.cs](/Marketplace.Application/Commands/Message/UpdateMessageCommand.cs) | C# | 9 | 0 | 2 | 11 |
| [Marketplace.Application/Commands/Message/UpdateMessageCommandHandler.cs](/Marketplace.Application/Commands/Message/UpdateMessageCommandHandler.cs) | C# | 25 | 0 | 9 | 34 |
| [Marketplace.Application/Commands/Meta/DeleteMetaImageCommand.cs](/Marketplace.Application/Commands/Meta/DeleteMetaImageCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/Meta/DeleteMetaImageCommandHandler.cs](/Marketplace.Application/Commands/Meta/DeleteMetaImageCommandHandler.cs) | C# | 89 | 14 | 22 | 125 |
| [Marketplace.Application/Commands/Meta/UploadMetaImageCommand.cs](/Marketplace.Application/Commands/Meta/UploadMetaImageCommand.cs) | C# | 9 | 3 | 3 | 15 |
| [Marketplace.Application/Commands/Meta/UploadMetaImageCommandHandler.cs](/Marketplace.Application/Commands/Meta/UploadMetaImageCommandHandler.cs) | C# | 100 | 15 | 19 | 134 |
| [Marketplace.Application/Commands/Notification/DeleteNotificationCommand.cs](/Marketplace.Application/Commands/Notification/DeleteNotificationCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Notification/DeleteNotificationCommandHandler.cs](/Marketplace.Application/Commands/Notification/DeleteNotificationCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Notification/MarkAllNotificationsAsReadCommand.cs](/Marketplace.Application/Commands/Notification/MarkAllNotificationsAsReadCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Notification/MarkAllNotificationsAsReadCommandHandler.cs](/Marketplace.Application/Commands/Notification/MarkAllNotificationsAsReadCommandHandler.cs) | C# | 15 | 1 | 5 | 21 |
| [Marketplace.Application/Commands/Notification/MarkNotificationAsReadCommand.cs](/Marketplace.Application/Commands/Notification/MarkNotificationAsReadCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Notification/MarkNotificationAsReadCommandHandler.cs](/Marketplace.Application/Commands/Notification/MarkNotificationAsReadCommandHandler.cs) | C# | 22 | 5 | 10 | 37 |
| [Marketplace.Application/Commands/Notification/StoreNotificationCommand.cs](/Marketplace.Application/Commands/Notification/StoreNotificationCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/Notification/StoreNotificationCommandHandler.cs](/Marketplace.Application/Commands/Notification/StoreNotificationCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Notification/UpdateNotificationCommand.cs](/Marketplace.Application/Commands/Notification/UpdateNotificationCommand.cs) | C# | 9 | 0 | 2 | 11 |
| [Marketplace.Application/Commands/Notification/UpdateNotificationCommandHandler.cs](/Marketplace.Application/Commands/Notification/UpdateNotificationCommandHandler.cs) | C# | 25 | 0 | 9 | 34 |
| [Marketplace.Application/Commands/OrderCoupon/DeleteOrderCouponCommand.cs](/Marketplace.Application/Commands/OrderCoupon/DeleteOrderCouponCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/OrderCoupon/DeleteOrderCouponCommandHandler.cs](/Marketplace.Application/Commands/OrderCoupon/DeleteOrderCouponCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/OrderCoupon/StoreOrderCouponCommand.cs](/Marketplace.Application/Commands/OrderCoupon/StoreOrderCouponCommand.cs) | C# | 6 | 0 | 4 | 10 |
| [Marketplace.Application/Commands/OrderCoupon/StoreOrderCouponCommandHandler.cs](/Marketplace.Application/Commands/OrderCoupon/StoreOrderCouponCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/OrderCoupon/UpdateOrderCouponCommand.cs](/Marketplace.Application/Commands/OrderCoupon/UpdateOrderCouponCommand.cs) | C# | 7 | 0 | 2 | 9 |
| [Marketplace.Application/Commands/OrderCoupon/UpdateOrderCouponCommandHandler.cs](/Marketplace.Application/Commands/OrderCoupon/UpdateOrderCouponCommandHandler.cs) | C# | 22 | 11 | 8 | 41 |
| [Marketplace.Application/Commands/OrderItem/DeleteOrderItemCommand.cs](/Marketplace.Application/Commands/OrderItem/DeleteOrderItemCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/OrderItem/DeleteOrderItemCommandHandler.cs](/Marketplace.Application/Commands/OrderItem/DeleteOrderItemCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/OrderItem/StoreOrderItemCommand.cs](/Marketplace.Application/Commands/OrderItem/StoreOrderItemCommand.cs) | C# | 10 | 0 | 4 | 14 |
| [Marketplace.Application/Commands/OrderItem/StoreOrderItemCommandHandler.cs](/Marketplace.Application/Commands/OrderItem/StoreOrderItemCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/OrderItem/UpdateOrderItemCommand.cs](/Marketplace.Application/Commands/OrderItem/UpdateOrderItemCommand.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Application/Commands/OrderItem/UpdateOrderItemCommandHandler.cs](/Marketplace.Application/Commands/OrderItem/UpdateOrderItemCommandHandler.cs) | C# | 28 | 0 | 9 | 37 |
| [Marketplace.Application/Commands/Order/AddOrderNoteCommand.cs](/Marketplace.Application/Commands/Order/AddOrderNoteCommand.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Application/Commands/Order/AddOrderNoteCommandHandler.cs](/Marketplace.Application/Commands/Order/AddOrderNoteCommandHandler.cs) | C# | 49 | 0 | 9 | 58 |
| [Marketplace.Application/Commands/Order/CancelOrderCommand.cs](/Marketplace.Application/Commands/Order/CancelOrderCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Order/CancelOrderCommandHandler.cs](/Marketplace.Application/Commands/Order/CancelOrderCommandHandler.cs) | C# | 39 | 6 | 12 | 57 |
| [Marketplace.Application/Commands/Order/DeleteOrderCommand.cs](/Marketplace.Application/Commands/Order/DeleteOrderCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Order/DeleteOrderCommandHandler.cs](/Marketplace.Application/Commands/Order/DeleteOrderCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Order/StoreOrderCommand.cs](/Marketplace.Application/Commands/Order/StoreOrderCommand.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Commands/Order/StoreOrderCommandHandler.cs](/Marketplace.Application/Commands/Order/StoreOrderCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Order/UpdateOrderCommand.cs](/Marketplace.Application/Commands/Order/UpdateOrderCommand.cs) | C# | 19 | 0 | 3 | 22 |
| [Marketplace.Application/Commands/Order/UpdateOrderCommandHandler.cs](/Marketplace.Application/Commands/Order/UpdateOrderCommandHandler.cs) | C# | 142 | 8 | 23 | 173 |
| [Marketplace.Application/Commands/Payment/DeletePaymentCommand.cs](/Marketplace.Application/Commands/Payment/DeletePaymentCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Payment/DeletePaymentCommandHandler.cs](/Marketplace.Application/Commands/Payment/DeletePaymentCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Payment/InitiatePaymentCommand.cs](/Marketplace.Application/Commands/Payment/InitiatePaymentCommand.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Commands/Payment/InitiatePaymentCommandHandler.cs](/Marketplace.Application/Commands/Payment/InitiatePaymentCommandHandler.cs) | C# | 45 | 5 | 12 | 62 |
| [Marketplace.Application/Commands/Payment/StorePaymentCommand.cs](/Marketplace.Application/Commands/Payment/StorePaymentCommand.cs) | C# | 11 | 0 | 4 | 15 |
| [Marketplace.Application/Commands/Payment/StorePaymentCommandHandler.cs](/Marketplace.Application/Commands/Payment/StorePaymentCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Payment/UpdatePaymentCommand.cs](/Marketplace.Application/Commands/Payment/UpdatePaymentCommand.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Application/Commands/Payment/UpdatePaymentCommandHandler.cs](/Marketplace.Application/Commands/Payment/UpdatePaymentCommandHandler.cs) | C# | 29 | 0 | 9 | 38 |
| [Marketplace.Application/Commands/ProductImage/AddSellerProductImageCommand.cs](/Marketplace.Application/Commands/ProductImage/AddSellerProductImageCommand.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Application/Commands/ProductImage/AddSellerProductImageCommandHandler.cs](/Marketplace.Application/Commands/ProductImage/AddSellerProductImageCommandHandler.cs) | C# | 41 | 6 | 12 | 59 |
| [Marketplace.Application/Commands/ProductImage/DeleteProductImageCommand.cs](/Marketplace.Application/Commands/ProductImage/DeleteProductImageCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/ProductImage/DeleteProductImageCommandHandler.cs](/Marketplace.Application/Commands/ProductImage/DeleteProductImageCommandHandler.cs) | C# | 34 | 7 | 9 | 50 |
| [Marketplace.Application/Commands/ProductImage/DeleteSellerProductImageCommand.cs](/Marketplace.Application/Commands/ProductImage/DeleteSellerProductImageCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/ProductImage/DeleteSellerProductImageCommandHandler.cs](/Marketplace.Application/Commands/ProductImage/DeleteSellerProductImageCommandHandler.cs) | C# | 36 | 5 | 10 | 51 |
| [Marketplace.Application/Commands/ProductImage/SetMainProductImageCommand.cs](/Marketplace.Application/Commands/ProductImage/SetMainProductImageCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/ProductImage/SetMainProductImageCommandHandler.cs](/Marketplace.Application/Commands/ProductImage/SetMainProductImageCommandHandler.cs) | C# | 37 | 4 | 10 | 51 |
| [Marketplace.Application/Commands/ProductImage/StoreProductImageCommand.cs](/Marketplace.Application/Commands/ProductImage/StoreProductImageCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/ProductImage/StoreProductImageCommandHandler.cs](/Marketplace.Application/Commands/ProductImage/StoreProductImageCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/ProductImage/UpdateProductImageCommand.cs](/Marketplace.Application/Commands/ProductImage/UpdateProductImageCommand.cs) | C# | 6 | 0 | 2 | 8 |
| [Marketplace.Application/Commands/ProductImage/UpdateProductImageCommandHandler.cs](/Marketplace.Application/Commands/ProductImage/UpdateProductImageCommandHandler.cs) | C# | 26 | 0 | 9 | 35 |
| [Marketplace.Application/Commands/ProductImage/UpdateSellerProductImageCommand.cs](/Marketplace.Application/Commands/ProductImage/UpdateSellerProductImageCommand.cs) | C# | 9 | 0 | 3 | 12 |
| [Marketplace.Application/Commands/ProductImage/UpdateSellerProductImageCommandHandler.cs](/Marketplace.Application/Commands/ProductImage/UpdateSellerProductImageCommandHandler.cs) | C# | 41 | 5 | 11 | 57 |
| [Marketplace.Application/Commands/ProductImage/UploadMultipleProductImagesCommand.cs](/Marketplace.Application/Commands/ProductImage/UploadMultipleProductImagesCommand.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Application/Commands/ProductImage/UploadMultipleProductImagesCommandHandler.cs](/Marketplace.Application/Commands/ProductImage/UploadMultipleProductImagesCommandHandler.cs) | C# | 76 | 4 | 17 | 97 |
| [Marketplace.Application/Commands/ProductImage/UploadProductImageCommand.cs](/Marketplace.Application/Commands/ProductImage/UploadProductImageCommand.cs) | C# | 8 | 3 | 3 | 14 |
| [Marketplace.Application/Commands/ProductImage/UploadProductImageCommandHandler.cs](/Marketplace.Application/Commands/ProductImage/UploadProductImageCommandHandler.cs) | C# | 75 | 8 | 13 | 96 |
| [Marketplace.Application/Commands/ProductImage/UploadSellerProductImageCommand.cs](/Marketplace.Application/Commands/ProductImage/UploadSellerProductImageCommand.cs) | C# | 9 | 0 | 3 | 12 |
| [Marketplace.Application/Commands/ProductImage/UploadSellerProductImageCommandHandler.cs](/Marketplace.Application/Commands/ProductImage/UploadSellerProductImageCommandHandler.cs) | C# | 53 | 8 | 14 | 75 |
| [Marketplace.Application/Commands/Product/AddSellerProductAttributeCommand.cs](/Marketplace.Application/Commands/Product/AddSellerProductAttributeCommand.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Application/Commands/Product/AddSellerProductAttributeCommandHandler.cs](/Marketplace.Application/Commands/Product/AddSellerProductAttributeCommandHandler.cs) | C# | 33 | 6 | 11 | 50 |
| [Marketplace.Application/Commands/Product/ApproveProductCommand.cs](/Marketplace.Application/Commands/Product/ApproveProductCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Product/ApproveProductCommandHandler.cs](/Marketplace.Application/Commands/Product/ApproveProductCommandHandler.cs) | C# | 26 | 2 | 6 | 34 |
| [Marketplace.Application/Commands/Product/BulkApproveProductsCommand.cs](/Marketplace.Application/Commands/Product/BulkApproveProductsCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Product/BulkApproveProductsCommandHandler.cs](/Marketplace.Application/Commands/Product/BulkApproveProductsCommandHandler.cs) | C# | 24 | 3 | 7 | 34 |
| [Marketplace.Application/Commands/Product/BulkRejectProductsCommand.cs](/Marketplace.Application/Commands/Product/BulkRejectProductsCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Product/BulkRejectProductsCommandHandler.cs](/Marketplace.Application/Commands/Product/BulkRejectProductsCommandHandler.cs) | C# | 24 | 1 | 7 | 32 |
| [Marketplace.Application/Commands/Product/CreateSellerProductCommand.cs](/Marketplace.Application/Commands/Product/CreateSellerProductCommand.cs) | C# | 18 | 0 | 3 | 21 |
| [Marketplace.Application/Commands/Product/CreateSellerProductCommandHandler.cs](/Marketplace.Application/Commands/Product/CreateSellerProductCommandHandler.cs) | C# | 49 | 3 | 9 | 61 |
| [Marketplace.Application/Commands/Product/DeleteProductCommand.cs](/Marketplace.Application/Commands/Product/DeleteProductCommand.cs) | C# | 10 | 0 | 4 | 14 |
| [Marketplace.Application/Commands/Product/DeleteProductCommandHandler.cs](/Marketplace.Application/Commands/Product/DeleteProductCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Product/DeleteSellerProductAttributeCommand.cs](/Marketplace.Application/Commands/Product/DeleteSellerProductAttributeCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/Product/DeleteSellerProductAttributeCommandHandler.cs](/Marketplace.Application/Commands/Product/DeleteSellerProductAttributeCommandHandler.cs) | C# | 33 | 6 | 11 | 50 |
| [Marketplace.Application/Commands/Product/DeleteSellerProductCommand.cs](/Marketplace.Application/Commands/Product/DeleteSellerProductCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/Product/DeleteSellerProductCommandHandler.cs](/Marketplace.Application/Commands/Product/DeleteSellerProductCommandHandler.cs) | C# | 30 | 4 | 9 | 43 |
| [Marketplace.Application/Commands/Product/ModeratorApproveProductCommand.cs](/Marketplace.Application/Commands/Product/ModeratorApproveProductCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/Product/ModeratorApproveProductCommandHandler.cs](/Marketplace.Application/Commands/Product/ModeratorApproveProductCommandHandler.cs) | C# | 26 | 0 | 6 | 32 |
| [Marketplace.Application/Commands/Product/ModeratorBulkApproveProductsCommand.cs](/Marketplace.Application/Commands/Product/ModeratorBulkApproveProductsCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/Product/ModeratorBulkApproveProductsCommandHandler.cs](/Marketplace.Application/Commands/Product/ModeratorBulkApproveProductsCommandHandler.cs) | C# | 24 | 1 | 7 | 32 |
| [Marketplace.Application/Commands/Product/ModeratorBulkRejectProductsCommand.cs](/Marketplace.Application/Commands/Product/ModeratorBulkRejectProductsCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/Product/ModeratorBulkRejectProductsCommandHandler.cs](/Marketplace.Application/Commands/Product/ModeratorBulkRejectProductsCommandHandler.cs) | C# | 24 | 1 | 7 | 32 |
| [Marketplace.Application/Commands/Product/ModeratorRejectProductCommand.cs](/Marketplace.Application/Commands/Product/ModeratorRejectProductCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/Product/ModeratorRejectProductCommandHandler.cs](/Marketplace.Application/Commands/Product/ModeratorRejectProductCommandHandler.cs) | C# | 26 | 0 | 6 | 32 |
| [Marketplace.Application/Commands/Product/PatchSellerProductCommand.cs](/Marketplace.Application/Commands/Product/PatchSellerProductCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/Product/PatchSellerProductCommandHandler.cs](/Marketplace.Application/Commands/Product/PatchSellerProductCommandHandler.cs) | C# | 89 | 5 | 11 | 105 |
| [Marketplace.Application/Commands/Product/RejectProductCommand.cs](/Marketplace.Application/Commands/Product/RejectProductCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Product/RejectProductCommandHandler.cs](/Marketplace.Application/Commands/Product/RejectProductCommandHandler.cs) | C# | 26 | 0 | 6 | 32 |
| [Marketplace.Application/Commands/Product/StoreProductCommand.cs](/Marketplace.Application/Commands/Product/StoreProductCommand.cs) | C# | 22 | 0 | 4 | 26 |
| [Marketplace.Application/Commands/Product/StoreProductCommandHandler.cs](/Marketplace.Application/Commands/Product/StoreProductCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Product/UpdateProductCommand.cs](/Marketplace.Application/Commands/Product/UpdateProductCommand.cs) | C# | 25 | 0 | 2 | 27 |
| [Marketplace.Application/Commands/Product/UpdateProductCommandHandler.cs](/Marketplace.Application/Commands/Product/UpdateProductCommandHandler.cs) | C# | 40 | 1 | 9 | 50 |
| [Marketplace.Application/Commands/Product/UpdateSellerProductAttributeCommand.cs](/Marketplace.Application/Commands/Product/UpdateSellerProductAttributeCommand.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Application/Commands/Product/UpdateSellerProductAttributeCommandHandler.cs](/Marketplace.Application/Commands/Product/UpdateSellerProductAttributeCommandHandler.cs) | C# | 33 | 6 | 11 | 50 |
| [Marketplace.Application/Commands/Product/UpdateSellerProductCommand.cs](/Marketplace.Application/Commands/Product/UpdateSellerProductCommand.cs) | C# | 18 | 0 | 3 | 21 |
| [Marketplace.Application/Commands/Product/UpdateSellerProductCommandHandler.cs](/Marketplace.Application/Commands/Product/UpdateSellerProductCommandHandler.cs) | C# | 53 | 5 | 11 | 69 |
| [Marketplace.Application/Commands/Rating/CreateUserRatingCommand.cs](/Marketplace.Application/Commands/Rating/CreateUserRatingCommand.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Commands/Rating/CreateUserRatingCommandHandler.cs](/Marketplace.Application/Commands/Rating/CreateUserRatingCommandHandler.cs) | C# | 37 | 3 | 9 | 49 |
| [Marketplace.Application/Commands/Rating/DeleteRatingCommand.cs](/Marketplace.Application/Commands/Rating/DeleteRatingCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Rating/DeleteRatingCommandHandler.cs](/Marketplace.Application/Commands/Rating/DeleteRatingCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Rating/DeleteUserRatingCommand.cs](/Marketplace.Application/Commands/Rating/DeleteUserRatingCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/Rating/DeleteUserRatingCommandHandler.cs](/Marketplace.Application/Commands/Rating/DeleteUserRatingCommandHandler.cs) | C# | 19 | 3 | 8 | 30 |
| [Marketplace.Application/Commands/Rating/ModeratorBulkDeleteRatingsCommand.cs](/Marketplace.Application/Commands/Rating/ModeratorBulkDeleteRatingsCommand.cs) | C# | 5 | 0 | 3 | 8 |
| [Marketplace.Application/Commands/Rating/ModeratorBulkDeleteRatingsCommandHandler.cs](/Marketplace.Application/Commands/Rating/ModeratorBulkDeleteRatingsCommandHandler.cs) | C# | 31 | 1 | 7 | 39 |
| [Marketplace.Application/Commands/Rating/ModeratorUpdateRatingCommand.cs](/Marketplace.Application/Commands/Rating/ModeratorUpdateRatingCommand.cs) | C# | 9 | 0 | 3 | 12 |
| [Marketplace.Application/Commands/Rating/ModeratorUpdateRatingCommandHandler.cs](/Marketplace.Application/Commands/Rating/ModeratorUpdateRatingCommandHandler.cs) | C# | 32 | 1 | 7 | 40 |
| [Marketplace.Application/Commands/Rating/StoreRatingCommand.cs](/Marketplace.Application/Commands/Rating/StoreRatingCommand.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Commands/Rating/StoreRatingCommandHandler.cs](/Marketplace.Application/Commands/Rating/StoreRatingCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Rating/UpdateRatingCommand.cs](/Marketplace.Application/Commands/Rating/UpdateRatingCommand.cs) | C# | 9 | 0 | 2 | 11 |
| [Marketplace.Application/Commands/Rating/UpdateRatingCommandHandler.cs](/Marketplace.Application/Commands/Rating/UpdateRatingCommandHandler.cs) | C# | 28 | 0 | 9 | 37 |
| [Marketplace.Application/Commands/Rating/UpdateUserRatingCommand.cs](/Marketplace.Application/Commands/Rating/UpdateUserRatingCommand.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Commands/Rating/UpdateUserRatingCommandHandler.cs](/Marketplace.Application/Commands/Rating/UpdateUserRatingCommandHandler.cs) | C# | 25 | 4 | 9 | 38 |
| [Marketplace.Application/Commands/Review/CreateUserReviewCommand.cs](/Marketplace.Application/Commands/Review/CreateUserReviewCommand.cs) | C# | 9 | 0 | 3 | 12 |
| [Marketplace.Application/Commands/Review/CreateUserReviewCommandHandler.cs](/Marketplace.Application/Commands/Review/CreateUserReviewCommandHandler.cs) | C# | 48 | 7 | 12 | 67 |
| [Marketplace.Application/Commands/Review/DeleteReviewCommand.cs](/Marketplace.Application/Commands/Review/DeleteReviewCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Review/DeleteReviewCommandHandler.cs](/Marketplace.Application/Commands/Review/DeleteReviewCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Review/DeleteUserReviewCommand.cs](/Marketplace.Application/Commands/Review/DeleteUserReviewCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/Review/DeleteUserReviewCommandHandler.cs](/Marketplace.Application/Commands/Review/DeleteUserReviewCommandHandler.cs) | C# | 19 | 3 | 8 | 30 |
| [Marketplace.Application/Commands/Review/ModeratorBulkDeleteReviewsCommand.cs](/Marketplace.Application/Commands/Review/ModeratorBulkDeleteReviewsCommand.cs) | C# | 5 | 0 | 3 | 8 |
| [Marketplace.Application/Commands/Review/ModeratorBulkDeleteReviewsCommandHandler.cs](/Marketplace.Application/Commands/Review/ModeratorBulkDeleteReviewsCommandHandler.cs) | C# | 31 | 1 | 7 | 39 |
| [Marketplace.Application/Commands/Review/ModeratorUpdateReviewCommand.cs](/Marketplace.Application/Commands/Review/ModeratorUpdateReviewCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/Review/ModeratorUpdateReviewCommandHandler.cs](/Marketplace.Application/Commands/Review/ModeratorUpdateReviewCommandHandler.cs) | C# | 27 | 1 | 7 | 35 |
| [Marketplace.Application/Commands/Review/StoreReviewCommand.cs](/Marketplace.Application/Commands/Review/StoreReviewCommand.cs) | C# | 9 | 0 | 4 | 13 |
| [Marketplace.Application/Commands/Review/StoreReviewCommandHandler.cs](/Marketplace.Application/Commands/Review/StoreReviewCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Review/UpdateReviewCommand.cs](/Marketplace.Application/Commands/Review/UpdateReviewCommand.cs) | C# | 7 | 0 | 2 | 9 |
| [Marketplace.Application/Commands/Review/UpdateReviewCommandHandler.cs](/Marketplace.Application/Commands/Review/UpdateReviewCommandHandler.cs) | C# | 25 | 0 | 9 | 34 |
| [Marketplace.Application/Commands/Review/UpdateUserReviewCommand.cs](/Marketplace.Application/Commands/Review/UpdateUserReviewCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/Review/UpdateUserReviewCommandHandler.cs](/Marketplace.Application/Commands/Review/UpdateUserReviewCommandHandler.cs) | C# | 20 | 4 | 9 | 33 |
| [Marketplace.Application/Commands/SellerRequest/AdminApproveSellerRequestCommand.cs](/Marketplace.Application/Commands/SellerRequest/AdminApproveSellerRequestCommand.cs) | C# | 3 | 5 | 3 | 11 |
| [Marketplace.Application/Commands/SellerRequest/AdminApproveSellerRequestCommandHandler.cs](/Marketplace.Application/Commands/SellerRequest/AdminApproveSellerRequestCommandHandler.cs) | C# | 181 | 26 | 30 | 237 |
| [Marketplace.Application/Commands/SellerRequest/AdminBulkApproveSellerRequestsCommand.cs](/Marketplace.Application/Commands/SellerRequest/AdminBulkApproveSellerRequestsCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/SellerRequest/AdminBulkApproveSellerRequestsCommandHandler.cs](/Marketplace.Application/Commands/SellerRequest/AdminBulkApproveSellerRequestsCommandHandler.cs) | C# | 56 | 5 | 13 | 74 |
| [Marketplace.Application/Commands/SellerRequest/AdminBulkRejectSellerRequestsCommand.cs](/Marketplace.Application/Commands/SellerRequest/AdminBulkRejectSellerRequestsCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/SellerRequest/AdminBulkRejectSellerRequestsCommandHandler.cs](/Marketplace.Application/Commands/SellerRequest/AdminBulkRejectSellerRequestsCommandHandler.cs) | C# | 60 | 5 | 13 | 78 |
| [Marketplace.Application/Commands/SellerRequest/AdminRejectSellerRequestCommand.cs](/Marketplace.Application/Commands/SellerRequest/AdminRejectSellerRequestCommand.cs) | C# | 3 | 6 | 3 | 12 |
| [Marketplace.Application/Commands/SellerRequest/AdminRejectSellerRequestCommandHandler.cs](/Marketplace.Application/Commands/SellerRequest/AdminRejectSellerRequestCommandHandler.cs) | C# | 44 | 6 | 9 | 59 |
| [Marketplace.Application/Commands/SellerRequest/ApproveSellerRequestCommand.cs](/Marketplace.Application/Commands/SellerRequest/ApproveSellerRequestCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/SellerRequest/ApproveSellerRequestCommandHandler.cs](/Marketplace.Application/Commands/SellerRequest/ApproveSellerRequestCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/SellerRequest/CreateCompanyRequestCommand.cs](/Marketplace.Application/Commands/SellerRequest/CreateCompanyRequestCommand.cs) | C# | 25 | 4 | 5 | 34 |
| [Marketplace.Application/Commands/SellerRequest/CreateCompanyRequestCommandHandler.cs](/Marketplace.Application/Commands/SellerRequest/CreateCompanyRequestCommandHandler.cs) | C# | 68 | 4 | 13 | 85 |
| [Marketplace.Application/Commands/SellerRequest/CreateUserSellerRequestCommand.cs](/Marketplace.Application/Commands/SellerRequest/CreateUserSellerRequestCommand.cs) | C# | 31 | 4 | 4 | 39 |
| [Marketplace.Application/Commands/SellerRequest/CreateUserSellerRequestCommandHandler.cs](/Marketplace.Application/Commands/SellerRequest/CreateUserSellerRequestCommandHandler.cs) | C# | 68 | 4 | 13 | 85 |
| [Marketplace.Application/Commands/SellerRequest/DeleteSellerRequestCommand.cs](/Marketplace.Application/Commands/SellerRequest/DeleteSellerRequestCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/SellerRequest/DeleteSellerRequestCommandHandler.cs](/Marketplace.Application/Commands/SellerRequest/DeleteSellerRequestCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/SellerRequest/ModeratorApproveSellerRequestCommand.cs](/Marketplace.Application/Commands/SellerRequest/ModeratorApproveSellerRequestCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/SellerRequest/ModeratorBulkApproveSellerRequestsCommand.cs](/Marketplace.Application/Commands/SellerRequest/ModeratorBulkApproveSellerRequestsCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/SellerRequest/ModeratorBulkApproveSellerRequestsCommandHandler.cs](/Marketplace.Application/Commands/SellerRequest/ModeratorBulkApproveSellerRequestsCommandHandler.cs) | C# | 15 | 1 | 5 | 21 |
| [Marketplace.Application/Commands/SellerRequest/ModeratorBulkRejectSellerRequestsCommand.cs](/Marketplace.Application/Commands/SellerRequest/ModeratorBulkRejectSellerRequestsCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/SellerRequest/ModeratorBulkRejectSellerRequestsCommandHandler.cs](/Marketplace.Application/Commands/SellerRequest/ModeratorBulkRejectSellerRequestsCommandHandler.cs) | C# | 37 | 4 | 8 | 49 |
| [Marketplace.Application/Commands/SellerRequest/ModeratorRejectSellerRequestCommand.cs](/Marketplace.Application/Commands/SellerRequest/ModeratorRejectSellerRequestCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/SellerRequest/ModeratorRejectSellerRequestCommandHandler.cs](/Marketplace.Application/Commands/SellerRequest/ModeratorRejectSellerRequestCommandHandler.cs) | C# | 26 | 0 | 6 | 32 |
| [Marketplace.Application/Commands/SellerRequest/RejectSellerRequestCommand.cs](/Marketplace.Application/Commands/SellerRequest/RejectSellerRequestCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/SellerRequest/RejectSellerRequestCommandHandler.cs](/Marketplace.Application/Commands/SellerRequest/RejectSellerRequestCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/SellerRequest/StoreSellerRequestCommand.cs](/Marketplace.Application/Commands/SellerRequest/StoreSellerRequestCommand.cs) | C# | 12 | 0 | 3 | 15 |
| [Marketplace.Application/Commands/SellerRequest/StoreSellerRequestCommandHandler.cs](/Marketplace.Application/Commands/SellerRequest/StoreSellerRequestCommandHandler.cs) | C# | 22 | 1 | 6 | 29 |
| [Marketplace.Application/Commands/SellerRequest/UpdateSellerRequestCommand.cs](/Marketplace.Application/Commands/SellerRequest/UpdateSellerRequestCommand.cs) | C# | 12 | 0 | 3 | 15 |
| [Marketplace.Application/Commands/SellerRequest/UpdateSellerRequestCommandHandler.cs](/Marketplace.Application/Commands/SellerRequest/UpdateSellerRequestCommandHandler.cs) | C# | 26 | 0 | 9 | 35 |
| [Marketplace.Application/Commands/ShippingMethod/DeleteShippingMethodCommand.cs](/Marketplace.Application/Commands/ShippingMethod/DeleteShippingMethodCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/ShippingMethod/DeleteShippingMethodCommandHandler.cs](/Marketplace.Application/Commands/ShippingMethod/DeleteShippingMethodCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/ShippingMethod/StoreShippingMethodCommand.cs](/Marketplace.Application/Commands/ShippingMethod/StoreShippingMethodCommand.cs) | C# | 10 | 0 | 4 | 14 |
| [Marketplace.Application/Commands/ShippingMethod/StoreShippingMethodCommandHandler.cs](/Marketplace.Application/Commands/ShippingMethod/StoreShippingMethodCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/ShippingMethod/UpdateShippingMethodCommand.cs](/Marketplace.Application/Commands/ShippingMethod/UpdateShippingMethodCommand.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Application/Commands/ShippingMethod/UpdateShippingMethodCommandHandler.cs](/Marketplace.Application/Commands/ShippingMethod/UpdateShippingMethodCommandHandler.cs) | C# | 29 | 0 | 9 | 38 |
| [Marketplace.Application/Commands/User/ChangePasswordCommand.cs](/Marketplace.Application/Commands/User/ChangePasswordCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/User/ChangePasswordCommandHandler.cs](/Marketplace.Application/Commands/User/ChangePasswordCommandHandler.cs) | C# | 31 | 1 | 8 | 40 |
| [Marketplace.Application/Commands/User/CreateUserCommand.cs](/Marketplace.Application/Commands/User/CreateUserCommand.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Application/Commands/User/CreateUserCommandHandler.cs](/Marketplace.Application/Commands/User/CreateUserCommandHandler.cs) | C# | 34 | 5 | 10 | 49 |
| [Marketplace.Application/Commands/User/DeleteUserCommand.cs](/Marketplace.Application/Commands/User/DeleteUserCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/User/DeleteUserCommandHandler.cs](/Marketplace.Application/Commands/User/DeleteUserCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/User/StoreUserCommand.cs](/Marketplace.Application/Commands/User/StoreUserCommand.cs) | C# | 9 | 0 | 4 | 13 |
| [Marketplace.Application/Commands/User/StoreUserCommandHandler.cs](/Marketplace.Application/Commands/User/StoreUserCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/User/UpdateUserCommand.cs](/Marketplace.Application/Commands/User/UpdateUserCommand.cs) | C# | 18 | 0 | 2 | 20 |
| [Marketplace.Application/Commands/User/UpdateUserCommandHandler.cs](/Marketplace.Application/Commands/User/UpdateUserCommandHandler.cs) | C# | 43 | 1 | 10 | 54 |
| [Marketplace.Application/Commands/User/UpdateUserProfileCommand.cs](/Marketplace.Application/Commands/User/UpdateUserProfileCommand.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Application/Commands/User/UpdateUserProfileCommandHandler.cs](/Marketplace.Application/Commands/User/UpdateUserProfileCommandHandler.cs) | C# | 57 | 3 | 11 | 71 |
| [Marketplace.Application/Commands/User/UpdateUserRoleCommand.cs](/Marketplace.Application/Commands/User/UpdateUserRoleCommand.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Commands/User/UpdateUserRoleCommandHandler.cs](/Marketplace.Application/Commands/User/UpdateUserRoleCommandHandler.cs) | C# | 23 | 2 | 8 | 33 |
| [Marketplace.Application/Commands/WishlistItem/DeleteWishlistItemCommand.cs](/Marketplace.Application/Commands/WishlistItem/DeleteWishlistItemCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/WishlistItem/DeleteWishlistItemCommandHandler.cs](/Marketplace.Application/Commands/WishlistItem/DeleteWishlistItemCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/WishlistItem/StoreWishlistItemCommand.cs](/Marketplace.Application/Commands/WishlistItem/StoreWishlistItemCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/WishlistItem/StoreWishlistItemCommandHandler.cs](/Marketplace.Application/Commands/WishlistItem/StoreWishlistItemCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/WishlistItem/UpdateWishlistItemCommand.cs](/Marketplace.Application/Commands/WishlistItem/UpdateWishlistItemCommand.cs) | C# | 7 | 0 | 2 | 9 |
| [Marketplace.Application/Commands/WishlistItem/UpdateWishlistItemCommandHandler.cs](/Marketplace.Application/Commands/WishlistItem/UpdateWishlistItemCommandHandler.cs) | C# | 22 | 11 | 8 | 41 |
| [Marketplace.Application/Commands/Wishlist/DeleteWishlistCommand.cs](/Marketplace.Application/Commands/Wishlist/DeleteWishlistCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Wishlist/DeleteWishlistCommandHandler.cs](/Marketplace.Application/Commands/Wishlist/DeleteWishlistCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Wishlist/StoreWishlistCommand.cs](/Marketplace.Application/Commands/Wishlist/StoreWishlistCommand.cs) | C# | 6 | 0 | 4 | 10 |
| [Marketplace.Application/Commands/Wishlist/StoreWishlistCommandHandler.cs](/Marketplace.Application/Commands/Wishlist/StoreWishlistCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Wishlist/UpdateWishlistCommand.cs](/Marketplace.Application/Commands/Wishlist/UpdateWishlistCommand.cs) | C# | 6 | 0 | 2 | 8 |
| [Marketplace.Application/Commands/Wishlist/UpdateWishlistCommandHandler.cs](/Marketplace.Application/Commands/Wishlist/UpdateWishlistCommandHandler.cs) | C# | 22 | 0 | 9 | 31 |
| [Marketplace.Application/Common/Interfaces/IFileStorage.cs](/Marketplace.Application/Common/Interfaces/IFileStorage.cs) | C# | 11 | 26 | 6 | 43 |
| [Marketplace.Application/Extensions/ExpressionExtensions.cs](/Marketplace.Application/Extensions/ExpressionExtensions.cs) | C# | 31 | 0 | 6 | 37 |
| [Marketplace.Application/Extensions/ValidationExtensions.cs](/Marketplace.Application/Extensions/ValidationExtensions.cs) | C# | 46 | 1 | 5 | 52 |
| [Marketplace.Application/Mappings/AddressMappingProfile.cs](/Marketplace.Application/Mappings/AddressMappingProfile.cs) | C# | 23 | 8 | 6 | 37 |
| [Marketplace.Application/Mappings/CartItemMappingProfile.cs](/Marketplace.Application/Mappings/CartItemMappingProfile.cs) | C# | 28 | 0 | 4 | 32 |
| [Marketplace.Application/Mappings/CartMappingProfile.cs](/Marketplace.Application/Mappings/CartMappingProfile.cs) | C# | 14 | 0 | 3 | 17 |
| [Marketplace.Application/Mappings/CategoryMappingProfile.cs](/Marketplace.Application/Mappings/CategoryMappingProfile.cs) | C# | 30 | 1 | 6 | 37 |
| [Marketplace.Application/Mappings/ChatMappingProfile.cs](/Marketplace.Application/Mappings/ChatMappingProfile.cs) | C# | 28 | 6 | 6 | 40 |
| [Marketplace.Application/Mappings/CompanyFinanceMappingProfile.cs](/Marketplace.Application/Mappings/CompanyFinanceMappingProfile.cs) | C# | 11 | 1 | 3 | 15 |
| [Marketplace.Application/Mappings/CompanyMappingProfile.cs](/Marketplace.Application/Mappings/CompanyMappingProfile.cs) | C# | 63 | 19 | 5 | 87 |
| [Marketplace.Application/Mappings/CompanyScheduleMappingProfile.cs](/Marketplace.Application/Mappings/CompanyScheduleMappingProfile.cs) | C# | 24 | 6 | 5 | 35 |
| [Marketplace.Application/Mappings/CompanyUserMappingProfile.cs](/Marketplace.Application/Mappings/CompanyUserMappingProfile.cs) | C# | 18 | 3 | 6 | 27 |
| [Marketplace.Application/Mappings/CouponMappingProfile.cs](/Marketplace.Application/Mappings/CouponMappingProfile.cs) | C# | 24 | 8 | 4 | 36 |
| [Marketplace.Application/Mappings/FavoriteMappingProfile.cs](/Marketplace.Application/Mappings/FavoriteMappingProfile.cs) | C# | 27 | 1 | 5 | 33 |
| [Marketplace.Application/Mappings/MessageMappingProfile.cs](/Marketplace.Application/Mappings/MessageMappingProfile.cs) | C# | 31 | 4 | 6 | 41 |
| [Marketplace.Application/Mappings/NotificationMappingProfile.cs](/Marketplace.Application/Mappings/NotificationMappingProfile.cs) | C# | 21 | 4 | 4 | 29 |
| [Marketplace.Application/Mappings/OrderCouponMappingProfile.cs](/Marketplace.Application/Mappings/OrderCouponMappingProfile.cs) | C# | 16 | 4 | 4 | 24 |
| [Marketplace.Application/Mappings/OrderItemMappingProfile.cs](/Marketplace.Application/Mappings/OrderItemMappingProfile.cs) | C# | 25 | 6 | 6 | 37 |
| [Marketplace.Application/Mappings/OrderMappingProfile.cs](/Marketplace.Application/Mappings/OrderMappingProfile.cs) | C# | 39 | 7 | 5 | 51 |
| [Marketplace.Application/Mappings/OrderNoteMappingProfile.cs](/Marketplace.Application/Mappings/OrderNoteMappingProfile.cs) | C# | 16 | 1 | 3 | 20 |
| [Marketplace.Application/Mappings/PaymentMappingProfile.cs](/Marketplace.Application/Mappings/PaymentMappingProfile.cs) | C# | 24 | 6 | 6 | 36 |
| [Marketplace.Application/Mappings/ProductImageMappingProfile.cs](/Marketplace.Application/Mappings/ProductImageMappingProfile.cs) | C# | 24 | 4 | 5 | 33 |
| [Marketplace.Application/Mappings/ProductMappingProfile.cs](/Marketplace.Application/Mappings/ProductMappingProfile.cs) | C# | 56 | 13 | 9 | 78 |
| [Marketplace.Application/Mappings/RatingMappingProfile.cs](/Marketplace.Application/Mappings/RatingMappingProfile.cs) | C# | 33 | 7 | 5 | 45 |
| [Marketplace.Application/Mappings/ReviewMappingProfile.cs](/Marketplace.Application/Mappings/ReviewMappingProfile.cs) | C# | 31 | 5 | 5 | 41 |
| [Marketplace.Application/Mappings/SellerRequestMappingProfile.cs](/Marketplace.Application/Mappings/SellerRequestMappingProfile.cs) | C# | 47 | 4 | 6 | 57 |
| [Marketplace.Application/Mappings/ShippingMethodMappingProfile.cs](/Marketplace.Application/Mappings/ShippingMethodMappingProfile.cs) | C# | 21 | 1 | 5 | 27 |
| [Marketplace.Application/Mappings/UserMappingProfile.cs](/Marketplace.Application/Mappings/UserMappingProfile.cs) | C# | 92 | 6 | 11 | 109 |
| [Marketplace.Application/Mappings/WishlistItemMappingProfile.cs](/Marketplace.Application/Mappings/WishlistItemMappingProfile.cs) | C# | 22 | 6 | 4 | 32 |
| [Marketplace.Application/Mappings/WishlistMappingProfile.cs](/Marketplace.Application/Mappings/WishlistMappingProfile.cs) | C# | 20 | 5 | 5 | 30 |
| [Marketplace.Application/Marketplace.Application.csproj](/Marketplace.Application/Marketplace.Application.csproj) | XML | 21 | 0 | 6 | 27 |
| [Marketplace.Application/Queries/Address/GetAddressQuery.cs](/Marketplace.Application/Queries/Address/GetAddressQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Address/GetAddressQueryHandler.cs](/Marketplace.Application/Queries/Address/GetAddressQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Address/GetAllAddressQuery.cs](/Marketplace.Application/Queries/Address/GetAllAddressQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/Address/GetAllAddressQueryHandler.cs](/Marketplace.Application/Queries/Address/GetAllAddressQueryHandler.cs) | C# | 58 | 6 | 8 | 72 |
| [Marketplace.Application/Queries/Admin/GetAdminDashboardDataQuery.cs](/Marketplace.Application/Queries/Admin/GetAdminDashboardDataQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Admin/GetAdminDashboardDataQueryHandler.cs](/Marketplace.Application/Queries/Admin/GetAdminDashboardDataQueryHandler.cs) | C# | 237 | 23 | 38 | 298 |
| [Marketplace.Application/Queries/Admin/GetAdminOrdersByStatusQuery.cs](/Marketplace.Application/Queries/Admin/GetAdminOrdersByStatusQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Admin/GetAdminOrdersByStatusQueryHandler.cs](/Marketplace.Application/Queries/Admin/GetAdminOrdersByStatusQueryHandler.cs) | C# | 27 | 2 | 7 | 36 |
| [Marketplace.Application/Queries/Admin/GetAdminPendingSellerRequestsQuery.cs](/Marketplace.Application/Queries/Admin/GetAdminPendingSellerRequestsQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Admin/GetAdminPendingSellerRequestsQueryHandler.cs](/Marketplace.Application/Queries/Admin/GetAdminPendingSellerRequestsQueryHandler.cs) | C# | 48 | 2 | 8 | 58 |
| [Marketplace.Application/Queries/Admin/GetAdminRecentOrdersQuery.cs](/Marketplace.Application/Queries/Admin/GetAdminRecentOrdersQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Admin/GetAdminRecentOrdersQueryHandler.cs](/Marketplace.Application/Queries/Admin/GetAdminRecentOrdersQueryHandler.cs) | C# | 37 | 2 | 7 | 46 |
| [Marketplace.Application/Queries/Admin/GetAdminSalesDataQuery.cs](/Marketplace.Application/Queries/Admin/GetAdminSalesDataQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Admin/GetAdminSalesDataQueryHandler.cs](/Marketplace.Application/Queries/Admin/GetAdminSalesDataQueryHandler.cs) | C# | 72 | 5 | 14 | 91 |
| [Marketplace.Application/Queries/Analytics/GetSellerCompanyAnalyticsQuery.cs](/Marketplace.Application/Queries/Analytics/GetSellerCompanyAnalyticsQuery.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Application/Queries/Analytics/GetSellerCompanyAnalyticsQueryHandler.cs](/Marketplace.Application/Queries/Analytics/GetSellerCompanyAnalyticsQueryHandler.cs) | C# | 269 | 24 | 48 | 341 |
| [Marketplace.Application/Queries/Analytics/GetSellerOrderAnalyticsQuery.cs](/Marketplace.Application/Queries/Analytics/GetSellerOrderAnalyticsQuery.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Application/Queries/Analytics/GetSellerOrderAnalyticsQueryHandler.cs](/Marketplace.Application/Queries/Analytics/GetSellerOrderAnalyticsQueryHandler.cs) | C# | 255 | 19 | 39 | 313 |
| [Marketplace.Application/Queries/Analytics/GetSellerProductAnalyticsQuery.cs](/Marketplace.Application/Queries/Analytics/GetSellerProductAnalyticsQuery.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Application/Queries/Analytics/GetSellerProductAnalyticsQueryHandler.cs](/Marketplace.Application/Queries/Analytics/GetSellerProductAnalyticsQueryHandler.cs) | C# | 153 | 14 | 22 | 189 |
| [Marketplace.Application/Queries/Cart/GetCartQuery.cs](/Marketplace.Application/Queries/Cart/GetCartQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Cart/GetCartQueryHandler.cs](/Marketplace.Application/Queries/Cart/GetCartQueryHandler.cs) | C# | 50 | 5 | 11 | 66 |
| [Marketplace.Application/Queries/Category/GetAllCategoryQuery.cs](/Marketplace.Application/Queries/Category/GetAllCategoryQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Category/GetAllCategoryQueryHandler.cs](/Marketplace.Application/Queries/Category/GetAllCategoryQueryHandler.cs) | C# | 84 | 4 | 12 | 100 |
| [Marketplace.Application/Queries/Category/GetCategoryBySlugQuery.cs](/Marketplace.Application/Queries/Category/GetCategoryBySlugQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Category/GetCategoryBySlugQueryHandler.cs](/Marketplace.Application/Queries/Category/GetCategoryBySlugQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Category/GetCategoryQuery.cs](/Marketplace.Application/Queries/Category/GetCategoryQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Category/GetCategoryQueryHandler.cs](/Marketplace.Application/Queries/Category/GetCategoryQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Category/GetRootCategoriesQuery.cs](/Marketplace.Application/Queries/Category/GetRootCategoriesQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/Category/GetRootCategoriesQueryHandler.cs](/Marketplace.Application/Queries/Category/GetRootCategoriesQueryHandler.cs) | C# | 56 | 6 | 10 | 72 |
| [Marketplace.Application/Queries/Category/GetSubcategoriesQuery.cs](/Marketplace.Application/Queries/Category/GetSubcategoriesQuery.cs) | C# | 12 | 0 | 3 | 15 |
| [Marketplace.Application/Queries/Category/GetSubcategoriesQueryHandler.cs](/Marketplace.Application/Queries/Category/GetSubcategoriesQueryHandler.cs) | C# | 56 | 6 | 11 | 73 |
| [Marketplace.Application/Queries/Chat/GetAllChatQuery.cs](/Marketplace.Application/Queries/Chat/GetAllChatQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Chat/GetAllChatQueryHandler.cs](/Marketplace.Application/Queries/Chat/GetAllChatQueryHandler.cs) | C# | 44 | 3 | 8 | 55 |
| [Marketplace.Application/Queries/Chat/GetChatQuery.cs](/Marketplace.Application/Queries/Chat/GetChatQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Chat/GetChatQueryHandler.cs](/Marketplace.Application/Queries/Chat/GetChatQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Chat/GetChatsByUserIdQuery.cs](/Marketplace.Application/Queries/Chat/GetChatsByUserIdQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/Chat/GetChatsByUserIdQueryHandler.cs](/Marketplace.Application/Queries/Chat/GetChatsByUserIdQueryHandler.cs) | C# | 45 | 6 | 8 | 59 |
| [Marketplace.Application/Queries/Chat/GetUserChatQuery.cs](/Marketplace.Application/Queries/Chat/GetUserChatQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Chat/GetUserChatQueryHandler.cs](/Marketplace.Application/Queries/Chat/GetUserChatQueryHandler.cs) | C# | 30 | 2 | 7 | 39 |
| [Marketplace.Application/Queries/Common/PaginatedQuery.cs](/Marketplace.Application/Queries/Common/PaginatedQuery.cs) | C# | 10 | 4 | 3 | 17 |
| [Marketplace.Application/Queries/Common/PaginatedQueryHandler.cs](/Marketplace.Application/Queries/Common/PaginatedQueryHandler.cs) | C# | 76 | 23 | 10 | 109 |
| [Marketplace.Application/Queries/CompanyFinance/GetSellerCompanyFinanceQuery.cs](/Marketplace.Application/Queries/CompanyFinance/GetSellerCompanyFinanceQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/CompanyFinance/GetSellerCompanyFinanceQueryHandler.cs](/Marketplace.Application/Queries/CompanyFinance/GetSellerCompanyFinanceQueryHandler.cs) | C# | 34 | 2 | 8 | 44 |
| [Marketplace.Application/Queries/CompanySchedule/GetAllCompanyScheduleQuery.cs](/Marketplace.Application/Queries/CompanySchedule/GetAllCompanyScheduleQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/CompanySchedule/GetAllCompanyScheduleQueryHandler.cs](/Marketplace.Application/Queries/CompanySchedule/GetAllCompanyScheduleQueryHandler.cs) | C# | 40 | 3 | 8 | 51 |
| [Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleByCompanyIdQuery.cs](/Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleByCompanyIdQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleByCompanyIdQueryHandler.cs](/Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleByCompanyIdQueryHandler.cs) | C# | 31 | 3 | 7 | 41 |
| [Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleByCompanySlugQuery.cs](/Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleByCompanySlugQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleByCompanySlugQueryHandler.cs](/Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleByCompanySlugQueryHandler.cs) | C# | 39 | 4 | 8 | 51 |
| [Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleQuery.cs](/Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleQueryHandler.cs](/Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/CompanySchedule/GetSellerCompanyScheduleQuery.cs](/Marketplace.Application/Queries/CompanySchedule/GetSellerCompanyScheduleQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/CompanySchedule/GetSellerCompanyScheduleQueryHandler.cs](/Marketplace.Application/Queries/CompanySchedule/GetSellerCompanyScheduleQueryHandler.cs) | C# | 40 | 2 | 8 | 50 |
| [Marketplace.Application/Queries/CompanyUser/GetAllCompanyUserQuery.cs](/Marketplace.Application/Queries/CompanyUser/GetAllCompanyUserQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/CompanyUser/GetAllCompanyUserQueryHandler.cs](/Marketplace.Application/Queries/CompanyUser/GetAllCompanyUserQueryHandler.cs) | C# | 50 | 3 | 7 | 60 |
| [Marketplace.Application/Queries/CompanyUser/GetCompanyUserQuery.cs](/Marketplace.Application/Queries/CompanyUser/GetCompanyUserQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/CompanyUser/GetCompanyUserQueryHandler.cs](/Marketplace.Application/Queries/CompanyUser/GetCompanyUserQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/CompanyUser/GetCompanyUsersByCompanyIdQuery.cs](/Marketplace.Application/Queries/CompanyUser/GetCompanyUsersByCompanyIdQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/CompanyUser/GetCompanyUsersByCompanyIdQueryHandler.cs](/Marketplace.Application/Queries/CompanyUser/GetCompanyUsersByCompanyIdQueryHandler.cs) | C# | 32 | 3 | 7 | 42 |
| [Marketplace.Application/Queries/CompanyUser/GetSellerCompanyUsersQuery.cs](/Marketplace.Application/Queries/CompanyUser/GetSellerCompanyUsersQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/CompanyUser/GetSellerCompanyUsersQueryHandler.cs](/Marketplace.Application/Queries/CompanyUser/GetSellerCompanyUsersQueryHandler.cs) | C# | 51 | 5 | 10 | 66 |
| [Marketplace.Application/Queries/Company/GetAllCompanyQuery.cs](/Marketplace.Application/Queries/Company/GetAllCompanyQuery.cs) | C# | 12 | 0 | 3 | 15 |
| [Marketplace.Application/Queries/Company/GetAllCompanyQueryHandler.cs](/Marketplace.Application/Queries/Company/GetAllCompanyQueryHandler.cs) | C# | 66 | 11 | 8 | 85 |
| [Marketplace.Application/Queries/Company/GetApprovedCompaniesQuery.cs](/Marketplace.Application/Queries/Company/GetApprovedCompaniesQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Company/GetApprovedCompaniesQueryHandler.cs](/Marketplace.Application/Queries/Company/GetApprovedCompaniesQueryHandler.cs) | C# | 54 | 8 | 10 | 72 |
| [Marketplace.Application/Queries/Company/GetCompanyBySlugQuery.cs](/Marketplace.Application/Queries/Company/GetCompanyBySlugQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Company/GetCompanyBySlugQueryHandler.cs](/Marketplace.Application/Queries/Company/GetCompanyBySlugQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Company/GetCompanyQuery.cs](/Marketplace.Application/Queries/Company/GetCompanyQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Company/GetCompanyQueryHandler.cs](/Marketplace.Application/Queries/Company/GetCompanyQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Company/GetDetailedCompanyQuery.cs](/Marketplace.Application/Queries/Company/GetDetailedCompanyQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Company/GetDetailedCompanyQueryHandler.cs](/Marketplace.Application/Queries/Company/GetDetailedCompanyQueryHandler.cs) | C# | 42 | 3 | 10 | 55 |
| [Marketplace.Application/Queries/Company/GetPendingCompaniesQuery.cs](/Marketplace.Application/Queries/Company/GetPendingCompaniesQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Company/GetPendingCompaniesQueryHandler.cs](/Marketplace.Application/Queries/Company/GetPendingCompaniesQueryHandler.cs) | C# | 54 | 8 | 10 | 72 |
| [Marketplace.Application/Queries/Company/GetSellerCompanyQuery.cs](/Marketplace.Application/Queries/Company/GetSellerCompanyQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Company/GetSellerCompanyQueryHandler.cs](/Marketplace.Application/Queries/Company/GetSellerCompanyQueryHandler.cs) | C# | 40 | 2 | 9 | 51 |
| [Marketplace.Application/Queries/Company/GetSellerCompanySettingsQuery.cs](/Marketplace.Application/Queries/Company/GetSellerCompanySettingsQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Company/GetSellerCompanySettingsQueryHandler.cs](/Marketplace.Application/Queries/Company/GetSellerCompanySettingsQueryHandler.cs) | C# | 44 | 4 | 9 | 57 |
| [Marketplace.Application/Queries/Coupon/GetAllCouponQuery.cs](/Marketplace.Application/Queries/Coupon/GetAllCouponQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Coupon/GetAllCouponQueryHandler.cs](/Marketplace.Application/Queries/Coupon/GetAllCouponQueryHandler.cs) | C# | 39 | 3 | 8 | 50 |
| [Marketplace.Application/Queries/Coupon/GetCouponQuery.cs](/Marketplace.Application/Queries/Coupon/GetCouponQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Coupon/GetCouponQueryHandler.cs](/Marketplace.Application/Queries/Coupon/GetCouponQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Coupon/ValidateCouponQuery.cs](/Marketplace.Application/Queries/Coupon/ValidateCouponQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Coupon/ValidateCouponQueryHandler.cs](/Marketplace.Application/Queries/Coupon/ValidateCouponQueryHandler.cs) | C# | 29 | 3 | 8 | 40 |
| [Marketplace.Application/Queries/Favorite/GetAllFavoriteQuery.cs](/Marketplace.Application/Queries/Favorite/GetAllFavoriteQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/Favorite/GetAllFavoriteQueryHandler.cs](/Marketplace.Application/Queries/Favorite/GetAllFavoriteQueryHandler.cs) | C# | 64 | 5 | 8 | 77 |
| [Marketplace.Application/Queries/Favorite/GetFavoriteQuery.cs](/Marketplace.Application/Queries/Favorite/GetFavoriteQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Favorite/GetFavoriteQueryHandler.cs](/Marketplace.Application/Queries/Favorite/GetFavoriteQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Message/GetAllMessageQuery.cs](/Marketplace.Application/Queries/Message/GetAllMessageQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Message/GetAllMessageQueryHandler.cs](/Marketplace.Application/Queries/Message/GetAllMessageQueryHandler.cs) | C# | 42 | 3 | 8 | 53 |
| [Marketplace.Application/Queries/Message/GetMessageQuery.cs](/Marketplace.Application/Queries/Message/GetMessageQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Message/GetMessageQueryHandler.cs](/Marketplace.Application/Queries/Message/GetMessageQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Message/GetMessagesByChatQuery.cs](/Marketplace.Application/Queries/Message/GetMessagesByChatQuery.cs) | C# | 11 | 0 | 2 | 13 |
| [Marketplace.Application/Queries/Message/GetMessagesByChatQueryHandler.cs](/Marketplace.Application/Queries/Message/GetMessagesByChatQueryHandler.cs) | C# | 49 | 5 | 8 | 62 |
| [Marketplace.Application/Queries/Notification/GetAllNotificationQuery.cs](/Marketplace.Application/Queries/Notification/GetAllNotificationQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Notification/GetAllNotificationQueryHandler.cs](/Marketplace.Application/Queries/Notification/GetAllNotificationQueryHandler.cs) | C# | 39 | 3 | 8 | 50 |
| [Marketplace.Application/Queries/Notification/GetNotificationQuery.cs](/Marketplace.Application/Queries/Notification/GetNotificationQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Notification/GetNotificationQueryHandler.cs](/Marketplace.Application/Queries/Notification/GetNotificationQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Notification/GetNotificationsByUserIdQuery.cs](/Marketplace.Application/Queries/Notification/GetNotificationsByUserIdQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/Notification/GetNotificationsByUserIdQueryHandler.cs](/Marketplace.Application/Queries/Notification/GetNotificationsByUserIdQueryHandler.cs) | C# | 41 | 4 | 8 | 53 |
| [Marketplace.Application/Queries/Notification/GetUserNotificationQuery.cs](/Marketplace.Application/Queries/Notification/GetUserNotificationQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Notification/GetUserNotificationQueryHandler.cs](/Marketplace.Application/Queries/Notification/GetUserNotificationQueryHandler.cs) | C# | 22 | 2 | 7 | 31 |
| [Marketplace.Application/Queries/OrderCoupon/GetAllOrderCouponQuery.cs](/Marketplace.Application/Queries/OrderCoupon/GetAllOrderCouponQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/OrderCoupon/GetAllOrderCouponQueryHandler.cs](/Marketplace.Application/Queries/OrderCoupon/GetAllOrderCouponQueryHandler.cs) | C# | 45 | 3 | 8 | 56 |
| [Marketplace.Application/Queries/OrderCoupon/GetOrderCouponQuery.cs](/Marketplace.Application/Queries/OrderCoupon/GetOrderCouponQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/OrderCoupon/GetOrderCouponQueryHandler.cs](/Marketplace.Application/Queries/OrderCoupon/GetOrderCouponQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/OrderCoupon/GetOrderCouponsByOrderIdQuery.cs](/Marketplace.Application/Queries/OrderCoupon/GetOrderCouponsByOrderIdQuery.cs) | C# | 12 | 0 | 3 | 15 |
| [Marketplace.Application/Queries/OrderCoupon/GetOrderCouponsByOrderIdQueryHandler.cs](/Marketplace.Application/Queries/OrderCoupon/GetOrderCouponsByOrderIdQueryHandler.cs) | C# | 50 | 5 | 9 | 64 |
| [Marketplace.Application/Queries/OrderItem/GetAdminOrderItemsQuery.cs](/Marketplace.Application/Queries/OrderItem/GetAdminOrderItemsQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/OrderItem/GetAdminOrderItemsQueryHandler.cs](/Marketplace.Application/Queries/OrderItem/GetAdminOrderItemsQueryHandler.cs) | C# | 64 | 7 | 12 | 83 |
| [Marketplace.Application/Queries/OrderItem/GetAllOrderItemQuery.cs](/Marketplace.Application/Queries/OrderItem/GetAllOrderItemQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/OrderItem/GetAllOrderItemQueryHandler.cs](/Marketplace.Application/Queries/OrderItem/GetAllOrderItemQueryHandler.cs) | C# | 60 | 4 | 8 | 72 |
| [Marketplace.Application/Queries/OrderItem/GetOrderItemQuery.cs](/Marketplace.Application/Queries/OrderItem/GetOrderItemQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/OrderItem/GetOrderItemQueryHandler.cs](/Marketplace.Application/Queries/OrderItem/GetOrderItemQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/OrderItem/GetOrderItemsByOrderIdQuery.cs](/Marketplace.Application/Queries/OrderItem/GetOrderItemsByOrderIdQuery.cs) | C# | 12 | 0 | 3 | 15 |
| [Marketplace.Application/Queries/OrderItem/GetOrderItemsByOrderIdQueryHandler.cs](/Marketplace.Application/Queries/OrderItem/GetOrderItemsByOrderIdQueryHandler.cs) | C# | 52 | 5 | 9 | 66 |
| [Marketplace.Application/Queries/OrderItem/GetSellerOrderItemsQuery.cs](/Marketplace.Application/Queries/OrderItem/GetSellerOrderItemsQuery.cs) | C# | 12 | 0 | 3 | 15 |
| [Marketplace.Application/Queries/OrderItem/GetSellerOrderItemsQueryHandler.cs](/Marketplace.Application/Queries/OrderItem/GetSellerOrderItemsQueryHandler.cs) | C# | 67 | 8 | 14 | 89 |
| [Marketplace.Application/Queries/OrderNote/GetOrderNotesQuery.cs](/Marketplace.Application/Queries/OrderNote/GetOrderNotesQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/OrderNote/GetOrderNotesQueryHandler.cs](/Marketplace.Application/Queries/OrderNote/GetOrderNotesQueryHandler.cs) | C# | 22 | 0 | 5 | 27 |
| [Marketplace.Application/Queries/Order/GetAllOrderQuery.cs](/Marketplace.Application/Queries/Order/GetAllOrderQuery.cs) | C# | 13 | 0 | 3 | 16 |
| [Marketplace.Application/Queries/Order/GetAllOrderQueryHandler.cs](/Marketplace.Application/Queries/Order/GetAllOrderQueryHandler.cs) | C# | 135 | 16 | 21 | 172 |
| [Marketplace.Application/Queries/Order/GetOrderQuery.cs](/Marketplace.Application/Queries/Order/GetOrderQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Order/GetOrderQueryHandler.cs](/Marketplace.Application/Queries/Order/GetOrderQueryHandler.cs) | C# | 42 | 2 | 9 | 53 |
| [Marketplace.Application/Queries/Order/GetOrdersByUserIdQuery.cs](/Marketplace.Application/Queries/Order/GetOrdersByUserIdQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/Order/GetOrdersByUserIdQueryHandler.cs](/Marketplace.Application/Queries/Order/GetOrdersByUserIdQueryHandler.cs) | C# | 45 | 4 | 8 | 57 |
| [Marketplace.Application/Queries/Order/GetSellerOrderQuery.cs](/Marketplace.Application/Queries/Order/GetSellerOrderQuery.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Queries/Order/GetSellerOrderQueryHandler.cs](/Marketplace.Application/Queries/Order/GetSellerOrderQueryHandler.cs) | C# | 61 | 6 | 15 | 82 |
| [Marketplace.Application/Queries/Order/GetSellerOrdersQuery.cs](/Marketplace.Application/Queries/Order/GetSellerOrdersQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/Order/GetSellerOrdersQueryHandler.cs](/Marketplace.Application/Queries/Order/GetSellerOrdersQueryHandler.cs) | C# | 77 | 10 | 17 | 104 |
| [Marketplace.Application/Queries/Order/GetUserOrderQuery.cs](/Marketplace.Application/Queries/Order/GetUserOrderQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Order/GetUserOrderQueryHandler.cs](/Marketplace.Application/Queries/Order/GetUserOrderQueryHandler.cs) | C# | 29 | 2 | 7 | 38 |
| [Marketplace.Application/Queries/Payment/GetAllPaymentQuery.cs](/Marketplace.Application/Queries/Payment/GetAllPaymentQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Payment/GetAllPaymentQueryHandler.cs](/Marketplace.Application/Queries/Payment/GetAllPaymentQueryHandler.cs) | C# | 43 | 3 | 8 | 54 |
| [Marketplace.Application/Queries/Payment/GetPaymentQuery.cs](/Marketplace.Application/Queries/Payment/GetPaymentQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Payment/GetPaymentQueryHandler.cs](/Marketplace.Application/Queries/Payment/GetPaymentQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Payment/GetPaymentsByUserIdQuery.cs](/Marketplace.Application/Queries/Payment/GetPaymentsByUserIdQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/Payment/GetPaymentsByUserIdQueryHandler.cs](/Marketplace.Application/Queries/Payment/GetPaymentsByUserIdQueryHandler.cs) | C# | 60 | 6 | 10 | 76 |
| [Marketplace.Application/Queries/Payment/GetUserPaymentQuery.cs](/Marketplace.Application/Queries/Payment/GetUserPaymentQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Payment/GetUserPaymentQueryHandler.cs](/Marketplace.Application/Queries/Payment/GetUserPaymentQueryHandler.cs) | C# | 36 | 2 | 8 | 46 |
| [Marketplace.Application/Queries/ProductImage/GetAllProductImageQuery.cs](/Marketplace.Application/Queries/ProductImage/GetAllProductImageQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/ProductImage/GetAllProductImageQueryHandler.cs](/Marketplace.Application/Queries/ProductImage/GetAllProductImageQueryHandler.cs) | C# | 45 | 4 | 8 | 57 |
| [Marketplace.Application/Queries/ProductImage/GetProductImageQuery.cs](/Marketplace.Application/Queries/ProductImage/GetProductImageQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/ProductImage/GetProductImageQueryHandler.cs](/Marketplace.Application/Queries/ProductImage/GetProductImageQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/ProductImage/GetProductImagesByProductIdQuery.cs](/Marketplace.Application/Queries/ProductImage/GetProductImagesByProductIdQuery.cs) | C# | 11 | 3 | 3 | 17 |
| [Marketplace.Application/Queries/ProductImage/GetProductImagesQuery.cs](/Marketplace.Application/Queries/ProductImage/GetProductImagesQuery.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Queries/ProductImage/GetProductImagesQueryHandler.cs](/Marketplace.Application/Queries/ProductImage/GetProductImagesQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/ProductImage/GetSellerProductImagesQuery.cs](/Marketplace.Application/Queries/ProductImage/GetSellerProductImagesQuery.cs) | C# | 12 | 0 | 3 | 15 |
| [Marketplace.Application/Queries/ProductImage/GetSellerProductImagesQueryHandler.cs](/Marketplace.Application/Queries/ProductImage/GetSellerProductImagesQueryHandler.cs) | C# | 54 | 6 | 11 | 71 |
| [Marketplace.Application/Queries/Product/GetAllProductQuery.cs](/Marketplace.Application/Queries/Product/GetAllProductQuery.cs) | C# | 14 | 0 | 3 | 17 |
| [Marketplace.Application/Queries/Product/GetAllProductQueryHandler.cs](/Marketplace.Application/Queries/Product/GetAllProductQueryHandler.cs) | C# | 69 | 11 | 12 | 92 |
| [Marketplace.Application/Queries/Product/GetPendingProductsQuery.cs](/Marketplace.Application/Queries/Product/GetPendingProductsQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Product/GetPendingProductsQueryHandler.cs](/Marketplace.Application/Queries/Product/GetPendingProductsQueryHandler.cs) | C# | 49 | 4 | 8 | 61 |
| [Marketplace.Application/Queries/Product/GetProductBySlugQuery.cs](/Marketplace.Application/Queries/Product/GetProductBySlugQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Product/GetProductBySlugQueryHandler.cs](/Marketplace.Application/Queries/Product/GetProductBySlugQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Product/GetProductQuery.cs](/Marketplace.Application/Queries/Product/GetProductQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Product/GetProductQueryHandler.cs](/Marketplace.Application/Queries/Product/GetProductQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Product/GetProductWithImagesQuery.cs](/Marketplace.Application/Queries/Product/GetProductWithImagesQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Product/GetProductWithImagesQueryHandler.cs](/Marketplace.Application/Queries/Product/GetProductWithImagesQueryHandler.cs) | C# | 65 | 1 | 8 | 74 |
| [Marketplace.Application/Queries/Product/GetProductsByCategoryQuery.cs](/Marketplace.Application/Queries/Product/GetProductsByCategoryQuery.cs) | C# | 11 | 0 | 2 | 13 |
| [Marketplace.Application/Queries/Product/GetProductsByCategoryQueryHandler.cs](/Marketplace.Application/Queries/Product/GetProductsByCategoryQueryHandler.cs) | C# | 92 | 8 | 14 | 114 |
| [Marketplace.Application/Queries/Product/GetProductsByCompanyQuery.cs](/Marketplace.Application/Queries/Product/GetProductsByCompanyQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/Product/GetProductsByCompanyQueryHandler.cs](/Marketplace.Application/Queries/Product/GetProductsByCompanyQueryHandler.cs) | C# | 50 | 4 | 9 | 63 |
| [Marketplace.Application/Queries/Product/GetProductsByCompanySlugQuery.cs](/Marketplace.Application/Queries/Product/GetProductsByCompanySlugQuery.cs) | C# | 12 | 0 | 3 | 15 |
| [Marketplace.Application/Queries/Product/GetProductsByCompanySlugQueryHandler.cs](/Marketplace.Application/Queries/Product/GetProductsByCompanySlugQueryHandler.cs) | C# | 65 | 8 | 11 | 84 |
| [Marketplace.Application/Queries/Product/GetSellerProductAttributesQuery.cs](/Marketplace.Application/Queries/Product/GetSellerProductAttributesQuery.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Queries/Product/GetSellerProductAttributesQueryHandler.cs](/Marketplace.Application/Queries/Product/GetSellerProductAttributesQueryHandler.cs) | C# | 29 | 4 | 9 | 42 |
| [Marketplace.Application/Queries/Product/GetSellerProductQuery.cs](/Marketplace.Application/Queries/Product/GetSellerProductQuery.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Queries/Product/GetSellerProductQueryHandler.cs](/Marketplace.Application/Queries/Product/GetSellerProductQueryHandler.cs) | C# | 41 | 4 | 10 | 55 |
| [Marketplace.Application/Queries/Product/GetSellerProductsQuery.cs](/Marketplace.Application/Queries/Product/GetSellerProductsQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/Product/GetSellerProductsQueryHandler.cs](/Marketplace.Application/Queries/Product/GetSellerProductsQueryHandler.cs) | C# | 56 | 6 | 11 | 73 |
| [Marketplace.Application/Queries/Rating/GetAllRatingQuery.cs](/Marketplace.Application/Queries/Rating/GetAllRatingQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Rating/GetAllRatingQueryHandler.cs](/Marketplace.Application/Queries/Rating/GetAllRatingQueryHandler.cs) | C# | 62 | 4 | 8 | 74 |
| [Marketplace.Application/Queries/Rating/GetRatingQuery.cs](/Marketplace.Application/Queries/Rating/GetRatingQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Rating/GetRatingQueryHandler.cs](/Marketplace.Application/Queries/Rating/GetRatingQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Rating/GetUserRatingsQuery.cs](/Marketplace.Application/Queries/Rating/GetUserRatingsQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/Rating/GetUserRatingsQueryHandler.cs](/Marketplace.Application/Queries/Rating/GetUserRatingsQueryHandler.cs) | C# | 46 | 4 | 8 | 58 |
| [Marketplace.Application/Queries/Review/GetAllReviewQuery.cs](/Marketplace.Application/Queries/Review/GetAllReviewQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Review/GetAllReviewQueryHandler.cs](/Marketplace.Application/Queries/Review/GetAllReviewQueryHandler.cs) | C# | 63 | 4 | 7 | 74 |
| [Marketplace.Application/Queries/Review/GetReviewQuery.cs](/Marketplace.Application/Queries/Review/GetReviewQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Review/GetReviewQueryHandler.cs](/Marketplace.Application/Queries/Review/GetReviewQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Review/GetUserReviewsQuery.cs](/Marketplace.Application/Queries/Review/GetUserReviewsQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/Review/GetUserReviewsQueryHandler.cs](/Marketplace.Application/Queries/Review/GetUserReviewsQueryHandler.cs) | C# | 43 | 4 | 8 | 55 |
| [Marketplace.Application/Queries/SellerRequest/GetAllSellerRequestQuery.cs](/Marketplace.Application/Queries/SellerRequest/GetAllSellerRequestQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/SellerRequest/GetAllSellerRequestQueryHandler.cs](/Marketplace.Application/Queries/SellerRequest/GetAllSellerRequestQueryHandler.cs) | C# | 79 | 7 | 15 | 101 |
| [Marketplace.Application/Queries/SellerRequest/GetPendingSellerRequestsQuery.cs](/Marketplace.Application/Queries/SellerRequest/GetPendingSellerRequestsQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/SellerRequest/GetPendingSellerRequestsQueryHandler.cs](/Marketplace.Application/Queries/SellerRequest/GetPendingSellerRequestsQueryHandler.cs) | C# | 46 | 4 | 8 | 58 |
| [Marketplace.Application/Queries/SellerRequest/GetSellerRequestQuery.cs](/Marketplace.Application/Queries/SellerRequest/GetSellerRequestQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/SellerRequest/GetSellerRequestQueryHandler.cs](/Marketplace.Application/Queries/SellerRequest/GetSellerRequestQueryHandler.cs) | C# | 27 | 0 | 6 | 33 |
| [Marketplace.Application/Queries/SellerRequest/GetSellerRequestsByUserIdQuery.cs](/Marketplace.Application/Queries/SellerRequest/GetSellerRequestsByUserIdQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/SellerRequest/GetSellerRequestsByUserIdQueryHandler.cs](/Marketplace.Application/Queries/SellerRequest/GetSellerRequestsByUserIdQueryHandler.cs) | C# | 44 | 4 | 8 | 56 |
| [Marketplace.Application/Queries/SellerRequest/GetUserSellerRequestQuery.cs](/Marketplace.Application/Queries/SellerRequest/GetUserSellerRequestQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/SellerRequest/GetUserSellerRequestQueryHandler.cs](/Marketplace.Application/Queries/SellerRequest/GetUserSellerRequestQueryHandler.cs) | C# | 23 | 2 | 7 | 32 |
| [Marketplace.Application/Queries/ShippingMethod/GetAllShippingMethodQuery.cs](/Marketplace.Application/Queries/ShippingMethod/GetAllShippingMethodQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/ShippingMethod/GetAllShippingMethodQueryHandler.cs](/Marketplace.Application/Queries/ShippingMethod/GetAllShippingMethodQueryHandler.cs) | C# | 40 | 3 | 8 | 51 |
| [Marketplace.Application/Queries/ShippingMethod/GetShippingMethodQuery.cs](/Marketplace.Application/Queries/ShippingMethod/GetShippingMethodQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/ShippingMethod/GetShippingMethodQueryHandler.cs](/Marketplace.Application/Queries/ShippingMethod/GetShippingMethodQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/User/GetAdminUsersQuery.cs](/Marketplace.Application/Queries/User/GetAdminUsersQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/User/GetAdminUsersQueryHandler.cs](/Marketplace.Application/Queries/User/GetAdminUsersQueryHandler.cs) | C# | 57 | 7 | 11 | 75 |
| [Marketplace.Application/Queries/User/GetAllUserQuery.cs](/Marketplace.Application/Queries/User/GetAllUserQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/User/GetAllUserQueryHandler.cs](/Marketplace.Application/Queries/User/GetAllUserQueryHandler.cs) | C# | 40 | 3 | 8 | 51 |
| [Marketplace.Application/Queries/User/GetCurrentUserQuery.cs](/Marketplace.Application/Queries/User/GetCurrentUserQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/User/GetCurrentUserQueryHandler.cs](/Marketplace.Application/Queries/User/GetCurrentUserQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/User/GetDetailedUserByIdQuery.cs](/Marketplace.Application/Queries/User/GetDetailedUserByIdQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/User/GetDetailedUserByIdQueryHandler.cs](/Marketplace.Application/Queries/User/GetDetailedUserByIdQueryHandler.cs) | C# | 342 | 3 | 30 | 375 |
| [Marketplace.Application/Queries/User/GetUserByEmailQuery.cs](/Marketplace.Application/Queries/User/GetUserByEmailQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/User/GetUserByEmailQueryHandler.cs](/Marketplace.Application/Queries/User/GetUserByEmailQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/User/GetUserByIdQuery.cs](/Marketplace.Application/Queries/User/GetUserByIdQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/User/GetUserByIdQueryHandler.cs](/Marketplace.Application/Queries/User/GetUserByIdQueryHandler.cs) | C# | 20 | 0 | 5 | 25 |
| [Marketplace.Application/Queries/User/GetUserByUsernameQuery.cs](/Marketplace.Application/Queries/User/GetUserByUsernameQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/User/GetUserByUsernameQueryHandler.cs](/Marketplace.Application/Queries/User/GetUserByUsernameQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/User/GetUserPublicQuery.cs](/Marketplace.Application/Queries/User/GetUserPublicQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/User/GetUserPublicQueryHandler.cs](/Marketplace.Application/Queries/User/GetUserPublicQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/User/GetUserQuery.cs](/Marketplace.Application/Queries/User/GetUserQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/User/GetUserQueryHandler.cs](/Marketplace.Application/Queries/User/GetUserQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/WishlistItem/GetAllWishlistItemQuery.cs](/Marketplace.Application/Queries/WishlistItem/GetAllWishlistItemQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/WishlistItem/GetAllWishlistItemQueryHandler.cs](/Marketplace.Application/Queries/WishlistItem/GetAllWishlistItemQueryHandler.cs) | C# | 84 | 4 | 7 | 95 |
| [Marketplace.Application/Queries/WishlistItem/GetWishlistItemQuery.cs](/Marketplace.Application/Queries/WishlistItem/GetWishlistItemQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/WishlistItem/GetWishlistItemQueryHandler.cs](/Marketplace.Application/Queries/WishlistItem/GetWishlistItemQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Wishlist/GetAllWishlistQuery.cs](/Marketplace.Application/Queries/Wishlist/GetAllWishlistQuery.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Queries/Wishlist/GetAllWishlistQueryHandler.cs](/Marketplace.Application/Queries/Wishlist/GetAllWishlistQueryHandler.cs) | C# | 57 | 4 | 8 | 69 |
| [Marketplace.Application/Queries/Wishlist/GetWishlistQuery.cs](/Marketplace.Application/Queries/Wishlist/GetWishlistQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Wishlist/GetWishlistQueryHandler.cs](/Marketplace.Application/Queries/Wishlist/GetWishlistQueryHandler.cs) | C# | 33 | 4 | 10 | 47 |
| [Marketplace.Application/Requests/Category/BulkDeleteCategoryRequest.cs](/Marketplace.Application/Requests/Category/BulkDeleteCategoryRequest.cs) | C# | 5 | 0 | 2 | 7 |
| [Marketplace.Application/Requests/Category/MoveCategoryRequest.cs](/Marketplace.Application/Requests/Category/MoveCategoryRequest.cs) | C# | 5 | 0 | 2 | 7 |
| [Marketplace.Application/Requests/Category/PatchCategoryRequest.cs](/Marketplace.Application/Requests/Category/PatchCategoryRequest.cs) | C# | 12 | 0 | 2 | 14 |
| [Marketplace.Application/Requests/Common/PaginationRequest.cs](/Marketplace.Application/Requests/Common/PaginationRequest.cs) | C# | 9 | 18 | 6 | 33 |
| [Marketplace.Application/Requests/Company/BulkDeleteCompanyRequest.cs](/Marketplace.Application/Requests/Company/BulkDeleteCompanyRequest.cs) | C# | 5 | 0 | 2 | 7 |
| [Marketplace.Application/Requests/Company/PatchCompanyRequest.cs](/Marketplace.Application/Requests/Company/PatchCompanyRequest.cs) | C# | 17 | 0 | 2 | 19 |
| [Marketplace.Application/Responses/AddressResponse.cs](/Marketplace.Application/Responses/AddressResponse.cs) | C# | 24 | 0 | 3 | 27 |
| [Marketplace.Application/Responses/Admin/AdminDashboardResponse.cs](/Marketplace.Application/Responses/Admin/AdminDashboardResponse.cs) | C# | 42 | 0 | 7 | 49 |
| [Marketplace.Application/Responses/AuthResponse.cs](/Marketplace.Application/Responses/AuthResponse.cs) | C# | 12 | 0 | 2 | 14 |
| [Marketplace.Application/Responses/CartItemResponse.cs](/Marketplace.Application/Responses/CartItemResponse.cs) | C# | 47 | 0 | 4 | 51 |
| [Marketplace.Application/Responses/CartResponse.cs](/Marketplace.Application/Responses/CartResponse.cs) | C# | 22 | 0 | 4 | 26 |
| [Marketplace.Application/Responses/CategoryResponse.cs](/Marketplace.Application/Responses/CategoryResponse.cs) | C# | 28 | 0 | 5 | 33 |
| [Marketplace.Application/Responses/CategoryTreeNode.cs](/Marketplace.Application/Responses/CategoryTreeNode.cs) | C# | 9 | 0 | 2 | 11 |
| [Marketplace.Application/Responses/ChatResponse.cs](/Marketplace.Application/Responses/ChatResponse.cs) | C# | 32 | 0 | 4 | 36 |
| [Marketplace.Application/Responses/CompanyAnalyticsResponse.cs](/Marketplace.Application/Responses/CompanyAnalyticsResponse.cs) | C# | 24 | 0 | 3 | 27 |
| [Marketplace.Application/Responses/CompanyFinanceResponse.cs](/Marketplace.Application/Responses/CompanyFinanceResponse.cs) | C# | 13 | 0 | 2 | 15 |
| [Marketplace.Application/Responses/CompanyResponse.cs](/Marketplace.Application/Responses/CompanyResponse.cs) | C# | 63 | 0 | 4 | 67 |
| [Marketplace.Application/Responses/CompanyScheduleResponse.cs](/Marketplace.Application/Responses/CompanyScheduleResponse.cs) | C# | 21 | 0 | 3 | 24 |
| [Marketplace.Application/Responses/CompanySettingsResponse.cs](/Marketplace.Application/Responses/CompanySettingsResponse.cs) | C# | 14 | 0 | 2 | 16 |
| [Marketplace.Application/Responses/CompanyUserResponse.cs](/Marketplace.Application/Responses/CompanyUserResponse.cs) | C# | 16 | 0 | 3 | 19 |
| [Marketplace.Application/Responses/CouponResponse.cs](/Marketplace.Application/Responses/CouponResponse.cs) | C# | 21 | 0 | 3 | 24 |
| [Marketplace.Application/Responses/CouponValidationResponse.cs](/Marketplace.Application/Responses/CouponValidationResponse.cs) | C# | 14 | 0 | 4 | 18 |
| [Marketplace.Application/Responses/DetailedCompanyResponse.cs](/Marketplace.Application/Responses/DetailedCompanyResponse.cs) | C# | 28 | 2 | 5 | 35 |
| [Marketplace.Application/Responses/DetailedSellerRequestResponse.cs](/Marketplace.Application/Responses/DetailedSellerRequestResponse.cs) | C# | 55 | 0 | 8 | 63 |
| [Marketplace.Application/Responses/DetailedUserResponse.cs](/Marketplace.Application/Responses/DetailedUserResponse.cs) | C# | 117 | 5 | 15 | 137 |
| [Marketplace.Application/Responses/FavoriteResponse.cs](/Marketplace.Application/Responses/FavoriteResponse.cs) | C# | 32 | 0 | 3 | 35 |
| [Marketplace.Application/Responses/FileUploadResult.cs](/Marketplace.Application/Responses/FileUploadResult.cs) | C# | 14 | 18 | 8 | 40 |
| [Marketplace.Application/Responses/MessageResponse.cs](/Marketplace.Application/Responses/MessageResponse.cs) | C# | 25 | 0 | 3 | 28 |
| [Marketplace.Application/Responses/NotificationResponse.cs](/Marketplace.Application/Responses/NotificationResponse.cs) | C# | 18 | 0 | 2 | 20 |
| [Marketplace.Application/Responses/OrderAnalyticsResponse.cs](/Marketplace.Application/Responses/OrderAnalyticsResponse.cs) | C# | 19 | 0 | 3 | 22 |
| [Marketplace.Application/Responses/OrderCouponResponse.cs](/Marketplace.Application/Responses/OrderCouponResponse.cs) | C# | 14 | 0 | 1 | 15 |
| [Marketplace.Application/Responses/OrderItemResponse.cs](/Marketplace.Application/Responses/OrderItemResponse.cs) | C# | 20 | 0 | 1 | 21 |
| [Marketplace.Application/Responses/OrderNoteResponse.cs](/Marketplace.Application/Responses/OrderNoteResponse.cs) | C# | 9 | 0 | 2 | 11 |
| [Marketplace.Application/Responses/OrderResponse.cs](/Marketplace.Application/Responses/OrderResponse.cs) | C# | 27 | 2 | 5 | 34 |
| [Marketplace.Application/Responses/PaginatedResponse.cs](/Marketplace.Application/Responses/PaginatedResponse.cs) | C# | 16 | 0 | 0 | 16 |
| [Marketplace.Application/Responses/PaymentResponse.cs](/Marketplace.Application/Responses/PaymentResponse.cs) | C# | 19 | 0 | 1 | 20 |
| [Marketplace.Application/Responses/ProductAnalyticsResponse.cs](/Marketplace.Application/Responses/ProductAnalyticsResponse.cs) | C# | 29 | 0 | 4 | 33 |
| [Marketplace.Application/Responses/ProductImageResponse.cs](/Marketplace.Application/Responses/ProductImageResponse.cs) | C# | 29 | 0 | 4 | 33 |
| [Marketplace.Application/Responses/ProductResponse.cs](/Marketplace.Application/Responses/ProductResponse.cs) | C# | 60 | 0 | 1 | 61 |
| [Marketplace.Application/Responses/ProductWithImagesResponse.cs](/Marketplace.Application/Responses/ProductWithImagesResponse.cs) | C# | 27 | 2 | 6 | 35 |
| [Marketplace.Application/Responses/RatingResponse.cs](/Marketplace.Application/Responses/RatingResponse.cs) | C# | 33 | 0 | 3 | 36 |
| [Marketplace.Application/Responses/RegisterUserDto.cs](/Marketplace.Application/Responses/RegisterUserDto.cs) | C# | 11 | 4 | 3 | 18 |
| [Marketplace.Application/Responses/ReviewResponse.cs](/Marketplace.Application/Responses/ReviewResponse.cs) | C# | 29 | 0 | 3 | 32 |
| [Marketplace.Application/Responses/SellerRequestResponse.cs](/Marketplace.Application/Responses/SellerRequestResponse.cs) | C# | 20 | 0 | 4 | 24 |
| [Marketplace.Application/Responses/ShippingMethodResponse.cs](/Marketplace.Application/Responses/ShippingMethodResponse.cs) | C# | 19 | 0 | 1 | 20 |
| [Marketplace.Application/Responses/TopCustomerResponse.cs](/Marketplace.Application/Responses/TopCustomerResponse.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Responses/UserDto.cs](/Marketplace.Application/Responses/UserDto.cs) | C# | 14 | 4 | 3 | 21 |
| [Marketplace.Application/Responses/UserPublicDto.cs](/Marketplace.Application/Responses/UserPublicDto.cs) | C# | 11 | 4 | 3 | 18 |
| [Marketplace.Application/Responses/UserResponse.cs](/Marketplace.Application/Responses/UserResponse.cs) | C# | 35 | 0 | 2 | 37 |
| [Marketplace.Application/Responses/WishlistItemResponse.cs](/Marketplace.Application/Responses/WishlistItemResponse.cs) | C# | 41 | 0 | 4 | 45 |
| [Marketplace.Application/Responses/WishlistResponse.cs](/Marketplace.Application/Responses/WishlistResponse.cs) | C# | 18 | 0 | 3 | 21 |
| [Marketplace.Application/Services/Auth/EmailService.cs](/Marketplace.Application/Services/Auth/EmailService.cs) | C# | 48 | 0 | 9 | 57 |
| [Marketplace.Application/Services/Auth/JwtTokenService.cs](/Marketplace.Application/Services/Auth/JwtTokenService.cs) | C# | 48 | 0 | 9 | 57 |
| [Marketplace.Application/Services/Auth/TokenService.cs](/Marketplace.Application/Services/Auth/TokenService.cs) | C# | 15 | 0 | 3 | 18 |
| [Marketplace.Application/Services/CategoryHierarchyService.cs](/Marketplace.Application/Services/CategoryHierarchyService.cs) | C# | 14 | 0 | 5 | 19 |
| [Marketplace.Application/Services/ICategoryHierarchyService.cs](/Marketplace.Application/Services/ICategoryHierarchyService.cs) | C# | 5 | 0 | 2 | 7 |
| [Marketplace.Application/Services/Payment/PaymentProcessingService.cs](/Marketplace.Application/Services/Payment/PaymentProcessingService.cs) | C# | 26 | 2 | 6 | 34 |
| [Marketplace.Application/Services/UserRole/IUserRoleService.cs](/Marketplace.Application/Services/UserRole/IUserRoleService.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Services/UserRole/UserRoleService.cs](/Marketplace.Application/Services/UserRole/UserRoleService.cs) | C# | 43 | 4 | 8 | 55 |
| [Marketplace.Application/Utils/SlugGenerator.cs](/Marketplace.Application/Utils/SlugGenerator.cs) | C# | 66 | 20 | 15 | 101 |
| [Marketplace.Application/Validators/AddressValidator.cs](/Marketplace.Application/Validators/AddressValidator.cs) | C# | 42 | 1 | 5 | 48 |
| [Marketplace.Application/Validators/Address/BulkDeleteAddressCommandValidator.cs](/Marketplace.Application/Validators/Address/BulkDeleteAddressCommandValidator.cs) | C# | 14 | 0 | 4 | 18 |
| [Marketplace.Application/Validators/CartItemValidator.cs](/Marketplace.Application/Validators/CartItemValidator.cs) | C# | 33 | 0 | 8 | 41 |
| [Marketplace.Application/Validators/CartValidator.cs](/Marketplace.Application/Validators/CartValidator.cs) | C# | 32 | 0 | 7 | 39 |
| [Marketplace.Application/Validators/CategoryValidator.cs](/Marketplace.Application/Validators/CategoryValidator.cs) | C# | 65 | 0 | 16 | 81 |
| [Marketplace.Application/Validators/ChatValidator.cs](/Marketplace.Application/Validators/ChatValidator.cs) | C# | 25 | 0 | 7 | 32 |
| [Marketplace.Application/Validators/CompanyScheduleValidator.cs](/Marketplace.Application/Validators/CompanyScheduleValidator.cs) | C# | 38 | 0 | 11 | 49 |
| [Marketplace.Application/Validators/CompanyUserValidator.cs](/Marketplace.Application/Validators/CompanyUserValidator.cs) | C# | 25 | 0 | 6 | 31 |
| [Marketplace.Application/Validators/CompanyValidator.cs](/Marketplace.Application/Validators/CompanyValidator.cs) | C# | 89 | 0 | 20 | 109 |
| [Marketplace.Application/Validators/Company/ApproveCompanyCommandValidator.cs](/Marketplace.Application/Validators/Company/ApproveCompanyCommandValidator.cs) | C# | 19 | 0 | 6 | 25 |
| [Marketplace.Application/Validators/Company/BulkApproveCompanyCommandValidator.cs](/Marketplace.Application/Validators/Company/BulkApproveCompanyCommandValidator.cs) | C# | 15 | 0 | 4 | 19 |
| [Marketplace.Application/Validators/Company/BulkRejectCompanyCommandValidator.cs](/Marketplace.Application/Validators/Company/BulkRejectCompanyCommandValidator.cs) | C# | 15 | 0 | 4 | 19 |
| [Marketplace.Application/Validators/Company/ModeratorBulkRejectCompaniesCommandValidator.cs](/Marketplace.Application/Validators/Company/ModeratorBulkRejectCompaniesCommandValidator.cs) | C# | 14 | 0 | 4 | 18 |
| [Marketplace.Application/Validators/Company/RejectCompanyCommandValidator.cs](/Marketplace.Application/Validators/Company/RejectCompanyCommandValidator.cs) | C# | 19 | 0 | 6 | 25 |
| [Marketplace.Application/Validators/CouponValidator.cs](/Marketplace.Application/Validators/CouponValidator.cs) | C# | 40 | 6 | 12 | 58 |
| [Marketplace.Application/Validators/FavoriteValidator.cs](/Marketplace.Application/Validators/FavoriteValidator.cs) | C# | 27 | 0 | 7 | 34 |
| [Marketplace.Application/Validators/MessageValidator.cs](/Marketplace.Application/Validators/MessageValidator.cs) | C# | 34 | 0 | 10 | 44 |
| [Marketplace.Application/Validators/Message/ModeratorUpdateMessageCommandValidator.cs](/Marketplace.Application/Validators/Message/ModeratorUpdateMessageCommandValidator.cs) | C# | 22 | 0 | 7 | 29 |
| [Marketplace.Application/Validators/NotificationValidator.cs](/Marketplace.Application/Validators/NotificationValidator.cs) | C# | 29 | 0 | 8 | 37 |
| [Marketplace.Application/Validators/OrderCouponValidator.cs](/Marketplace.Application/Validators/OrderCouponValidator.cs) | C# | 25 | 0 | 7 | 32 |
| [Marketplace.Application/Validators/OrderItemValidator.cs](/Marketplace.Application/Validators/OrderItemValidator.cs) | C# | 36 | 5 | 12 | 53 |
| [Marketplace.Application/Validators/OrderNoteValidator.cs](/Marketplace.Application/Validators/OrderNoteValidator.cs) | C# | 17 | 0 | 4 | 21 |
| [Marketplace.Application/Validators/OrderValidator.cs](/Marketplace.Application/Validators/OrderValidator.cs) | C# | 43 | 0 | 14 | 57 |
| [Marketplace.Application/Validators/PaymentValidator.cs](/Marketplace.Application/Validators/PaymentValidator.cs) | C# | 37 | 0 | 12 | 49 |
| [Marketplace.Application/Validators/ProductImageValidator.cs](/Marketplace.Application/Validators/ProductImageValidator.cs) | C# | 27 | 0 | 6 | 33 |
| [Marketplace.Application/Validators/ProductValidator.cs](/Marketplace.Application/Validators/ProductValidator.cs) | C# | 83 | 5 | 28 | 116 |
| [Marketplace.Application/Validators/Product/ModeratorBulkApproveProductsCommandValidator.cs](/Marketplace.Application/Validators/Product/ModeratorBulkApproveProductsCommandValidator.cs) | C# | 16 | 0 | 5 | 21 |
| [Marketplace.Application/Validators/Product/ModeratorBulkRejectProductsCommandValidator.cs](/Marketplace.Application/Validators/Product/ModeratorBulkRejectProductsCommandValidator.cs) | C# | 17 | 0 | 5 | 22 |
| [Marketplace.Application/Validators/RatingValidator.cs](/Marketplace.Application/Validators/RatingValidator.cs) | C# | 40 | 0 | 13 | 53 |
| [Marketplace.Application/Validators/Rating/ModeratorBulkDeleteRatingsCommandValidator.cs](/Marketplace.Application/Validators/Rating/ModeratorBulkDeleteRatingsCommandValidator.cs) | C# | 14 | 0 | 4 | 18 |
| [Marketplace.Application/Validators/Rating/ModeratorUpdateRatingCommandValidator.cs](/Marketplace.Application/Validators/Rating/ModeratorUpdateRatingCommandValidator.cs) | C# | 27 | 0 | 10 | 37 |
| [Marketplace.Application/Validators/ReviewValidator.cs](/Marketplace.Application/Validators/ReviewValidator.cs) | C# | 32 | 0 | 10 | 42 |
| [Marketplace.Application/Validators/Review/ModeratorBulkDeleteReviewsCommandValidator.cs](/Marketplace.Application/Validators/Review/ModeratorBulkDeleteReviewsCommandValidator.cs) | C# | 14 | 0 | 4 | 18 |
| [Marketplace.Application/Validators/Review/ModeratorUpdateReviewCommandValidator.cs](/Marketplace.Application/Validators/Review/ModeratorUpdateReviewCommandValidator.cs) | C# | 22 | 0 | 7 | 29 |
| [Marketplace.Application/Validators/SellerRequestValidator.cs](/Marketplace.Application/Validators/SellerRequestValidator.cs) | C# | 19 | 0 | 4 | 23 |
| [Marketplace.Application/Validators/ShippingMethodValidator.cs](/Marketplace.Application/Validators/ShippingMethodValidator.cs) | C# | 28 | 5 | 9 | 42 |
| [Marketplace.Application/Validators/UserValidator.cs](/Marketplace.Application/Validators/UserValidator.cs) | C# | 71 | 0 | 17 | 88 |
| [Marketplace.Application/Validators/WishlistItemValidator.cs](/Marketplace.Application/Validators/WishlistItemValidator.cs) | C# | 25 | 0 | 7 | 32 |
| [Marketplace.Application/Validators/WishlistValidator.cs](/Marketplace.Application/Validators/WishlistValidator.cs) | C# | 23 | 0 | 6 | 29 |
| [Marketplace.Domain/Entities/Address.cs](/Marketplace.Domain/Entities/Address.cs) | C# | 46 | 0 | 4 | 50 |
| [Marketplace.Domain/Entities/Cart.cs](/Marketplace.Domain/Entities/Cart.cs) | C# | 27 | 3 | 7 | 37 |
| [Marketplace.Domain/Entities/CartItem.cs](/Marketplace.Domain/Entities/CartItem.cs) | C# | 54 | 3 | 10 | 67 |
| [Marketplace.Domain/Entities/Category.cs](/Marketplace.Domain/Entities/Category.cs) | C# | 128 | 4 | 18 | 150 |
| [Marketplace.Domain/Entities/Chat.cs](/Marketplace.Domain/Entities/Chat.cs) | C# | 48 | 17 | 6 | 71 |
| [Marketplace.Domain/Entities/Company.cs](/Marketplace.Domain/Entities/Company.cs) | C# | 188 | 0 | 18 | 206 |
| [Marketplace.Domain/Entities/CompanyFinance.cs](/Marketplace.Domain/Entities/CompanyFinance.cs) | C# | 130 | 0 | 34 | 164 |
| [Marketplace.Domain/Entities/CompanySchedule.cs](/Marketplace.Domain/Entities/CompanySchedule.cs) | C# | 108 | 11 | 20 | 139 |
| [Marketplace.Domain/Entities/CompanyUser.cs](/Marketplace.Domain/Entities/CompanyUser.cs) | C# | 41 | 0 | 6 | 47 |
| [Marketplace.Domain/Entities/Coupon.cs](/Marketplace.Domain/Entities/Coupon.cs) | C# | 103 | 0 | 12 | 115 |
| [Marketplace.Domain/Entities/Favorite.cs](/Marketplace.Domain/Entities/Favorite.cs) | C# | 45 | 3 | 8 | 56 |
| [Marketplace.Domain/Entities/IEntity.cs](/Marketplace.Domain/Entities/IEntity.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Domain/Entities/Message.cs](/Marketplace.Domain/Entities/Message.cs) | C# | 58 | 0 | 9 | 67 |
| [Marketplace.Domain/Entities/Notification.cs](/Marketplace.Domain/Entities/Notification.cs) | C# | 65 | 0 | 10 | 75 |
| [Marketplace.Domain/Entities/Order.cs](/Marketplace.Domain/Entities/Order.cs) | C# | 105 | 0 | 16 | 121 |
| [Marketplace.Domain/Entities/OrderCoupon.cs](/Marketplace.Domain/Entities/OrderCoupon.cs) | C# | 45 | 0 | 9 | 54 |
| [Marketplace.Domain/Entities/OrderItem.cs](/Marketplace.Domain/Entities/OrderItem.cs) | C# | 74 | 0 | 11 | 85 |
| [Marketplace.Domain/Entities/OrderNote.cs](/Marketplace.Domain/Entities/OrderNote.cs) | C# | 74 | 0 | 16 | 90 |
| [Marketplace.Domain/Entities/Payment.cs](/Marketplace.Domain/Entities/Payment.cs) | C# | 84 | 0 | 13 | 97 |
| [Marketplace.Domain/Entities/Product.cs](/Marketplace.Domain/Entities/Product.cs) | C# | 202 | 2 | 22 | 226 |
| [Marketplace.Domain/Entities/ProductImage.cs](/Marketplace.Domain/Entities/ProductImage.cs) | C# | 58 | 0 | 13 | 71 |
| [Marketplace.Domain/Entities/Rating.cs](/Marketplace.Domain/Entities/Rating.cs) | C# | 115 | 0 | 13 | 128 |
| [Marketplace.Domain/Entities/Review.cs](/Marketplace.Domain/Entities/Review.cs) | C# | 65 | 0 | 8 | 73 |
| [Marketplace.Domain/Entities/SellerRequest.cs](/Marketplace.Domain/Entities/SellerRequest.cs) | C# | 152 | 3 | 28 | 183 |
| [Marketplace.Domain/Entities/ShippingMethod.cs](/Marketplace.Domain/Entities/ShippingMethod.cs) | C# | 76 | 0 | 14 | 90 |
| [Marketplace.Domain/Entities/User.cs](/Marketplace.Domain/Entities/User.cs) | C# | 108 | 0 | 13 | 121 |
| [Marketplace.Domain/Entities/Wishlist.cs](/Marketplace.Domain/Entities/Wishlist.cs) | C# | 25 | 0 | 4 | 29 |
| [Marketplace.Domain/Entities/WishlistItem.cs](/Marketplace.Domain/Entities/WishlistItem.cs) | C# | 46 | 0 | 6 | 52 |
| [Marketplace.Domain/Exceptions/DomainException.cs](/Marketplace.Domain/Exceptions/DomainException.cs) | C# | 15 | 1 | 5 | 21 |
| [Marketplace.Domain/Marketplace.Domain.csproj](/Marketplace.Domain/Marketplace.Domain.csproj) | XML | 10 | 0 | 4 | 14 |
| [Marketplace.Domain/Repositories/IAddressRepository.cs](/Marketplace.Domain/Repositories/IAddressRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/ICartItemRepository.cs](/Marketplace.Domain/Repositories/ICartItemRepository.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Domain/Repositories/ICartRepository.cs](/Marketplace.Domain/Repositories/ICartRepository.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Domain/Repositories/ICategoryRepository.cs](/Marketplace.Domain/Repositories/ICategoryRepository.cs) | C# | 26 | 2 | 5 | 33 |
| [Marketplace.Domain/Repositories/IChatRepository.cs](/Marketplace.Domain/Repositories/IChatRepository.cs) | C# | 5 | 0 | 3 | 8 |
| [Marketplace.Domain/Repositories/ICompanyFinanceRepository.cs](/Marketplace.Domain/Repositories/ICompanyFinanceRepository.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Domain/Repositories/ICompanyRepository.cs](/Marketplace.Domain/Repositories/ICompanyRepository.cs) | C# | 27 | 0 | 9 | 36 |
| [Marketplace.Domain/Repositories/ICompanyScheduleRepository.cs](/Marketplace.Domain/Repositories/ICompanyScheduleRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/ICompanyUserRepository.cs](/Marketplace.Domain/Repositories/ICompanyUserRepository.cs) | C# | 5 | 6 | 3 | 14 |
| [Marketplace.Domain/Repositories/ICouponRepository.cs](/Marketplace.Domain/Repositories/ICouponRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IFavoriteRepository.cs](/Marketplace.Domain/Repositories/IFavoriteRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IMessageRepository.cs](/Marketplace.Domain/Repositories/IMessageRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/INotificationRepository.cs](/Marketplace.Domain/Repositories/INotificationRepository.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Domain/Repositories/IOrderCouponRepository.cs](/Marketplace.Domain/Repositories/IOrderCouponRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IOrderItemRepository.cs](/Marketplace.Domain/Repositories/IOrderItemRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IOrderNoteRepository.cs](/Marketplace.Domain/Repositories/IOrderNoteRepository.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Domain/Repositories/IOrderRepository.cs](/Marketplace.Domain/Repositories/IOrderRepository.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Domain/Repositories/IPaymentRepository.cs](/Marketplace.Domain/Repositories/IPaymentRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IProductImageRepository.cs](/Marketplace.Domain/Repositories/IProductImageRepository.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Domain/Repositories/IProductRepository.cs](/Marketplace.Domain/Repositories/IProductRepository.cs) | C# | 20 | 0 | 5 | 25 |
| [Marketplace.Domain/Repositories/IRatingRepository.cs](/Marketplace.Domain/Repositories/IRatingRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IRepository.cs](/Marketplace.Domain/Repositories/IRepository.cs) | C# | 37 | 0 | 8 | 45 |
| [Marketplace.Domain/Repositories/IReviewRepository.cs](/Marketplace.Domain/Repositories/IReviewRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/ISellerRequestRepository.cs](/Marketplace.Domain/Repositories/ISellerRequestRepository.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Domain/Repositories/IShippingMethodRepository.cs](/Marketplace.Domain/Repositories/IShippingMethodRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IUserRepository.cs](/Marketplace.Domain/Repositories/IUserRepository.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Domain/Repositories/IWishlistItemRepository.cs](/Marketplace.Domain/Repositories/IWishlistItemRepository.cs) | C# | 8 | 0 | 4 | 12 |
| [Marketplace.Domain/Repositories/IWishlistRepository.cs](/Marketplace.Domain/Repositories/IWishlistRepository.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Domain/Services/IFileService.cs](/Marketplace.Domain/Services/IFileService.cs) | C# | 12 | 30 | 6 | 48 |
| [Marketplace.Domain/ValueObjects/AddressVO.cs](/Marketplace.Domain/ValueObjects/AddressVO.cs) | C# | 40 | 1 | 7 | 48 |
| [Marketplace.Domain/ValueObjects/CompanyRequestData.cs](/Marketplace.Domain/ValueObjects/CompanyRequestData.cs) | C# | 120 | 0 | 17 | 137 |
| [Marketplace.Domain/ValueObjects/Email.cs](/Marketplace.Domain/ValueObjects/Email.cs) | C# | 28 | 0 | 8 | 36 |
| [Marketplace.Domain/ValueObjects/FinanceRequestData.cs](/Marketplace.Domain/ValueObjects/FinanceRequestData.cs) | C# | 59 | 0 | 13 | 72 |
| [Marketplace.Domain/ValueObjects/Meta.cs](/Marketplace.Domain/ValueObjects/Meta.cs) | C# | 20 | 0 | 7 | 27 |
| [Marketplace.Domain/ValueObjects/Money.cs](/Marketplace.Domain/ValueObjects/Money.cs) | C# | 33 | 1 | 7 | 41 |
| [Marketplace.Domain/ValueObjects/Password.cs](/Marketplace.Domain/ValueObjects/Password.cs) | C# | 29 | 0 | 6 | 35 |
| [Marketplace.Domain/ValueObjects/Phone.cs](/Marketplace.Domain/ValueObjects/Phone.cs) | C# | 22 | 0 | 7 | 29 |
| [Marketplace.Domain/ValueObjects/ScheduleRequestData.cs](/Marketplace.Domain/ValueObjects/ScheduleRequestData.cs) | C# | 75 | 2 | 21 | 98 |
| [Marketplace.Domain/ValueObjects/Slug.cs](/Marketplace.Domain/ValueObjects/Slug.cs) | C# | 20 | 0 | 7 | 27 |
| [Marketplace.Domain/ValueObjects/Url.cs](/Marketplace.Domain/ValueObjects/Url.cs) | C# | 17 | 0 | 6 | 23 |
| [Marketplace.Infrastructure/DatabaseMigrator.cs](/Marketplace.Infrastructure/DatabaseMigrator.cs) | C# | 55 | 3 | 10 | 68 |
| [Marketplace.Infrastructure/DatabaseSeeder/DatabaseSeeder.cs](/Marketplace.Infrastructure/DatabaseSeeder/DatabaseSeeder.cs) | C# | 1,626 | 290 | 384 | 2,300 |
| [Marketplace.Infrastructure/InfrastructureServiceExtensions.cs](/Marketplace.Infrastructure/InfrastructureServiceExtensions.cs) | C# | 75 | 11 | 14 | 100 |
| [Marketplace.Infrastructure/Marketplace.Infrastructure.csproj](/Marketplace.Infrastructure/Marketplace.Infrastructure.csproj) | XML | 29 | 0 | 7 | 36 |
| [Marketplace.Infrastructure/Migrations/20250623104124\_InitialCreate.Designer.cs](/Marketplace.Infrastructure/Migrations/20250623104124_InitialCreate.Designer.cs) | C# | 1,277 | 2 | 465 | 1,744 |
| [Marketplace.Infrastructure/Migrations/20250623104124\_InitialCreate.cs](/Marketplace.Infrastructure/Migrations/20250623104124_InitialCreate.cs) | C# | 949 | 3 | 103 | 1,055 |
| [Marketplace.Infrastructure/Migrations/MarketplaceDbContextModelSnapshot.cs](/Marketplace.Infrastructure/Migrations/MarketplaceDbContextModelSnapshot.cs) | C# | 1,275 | 1 | 465 | 1,741 |
| [Marketplace.Infrastructure/Payment/LiqPayClient.cs](/Marketplace.Infrastructure/Payment/LiqPayClient.cs) | C# | 63 | 1 | 9 | 73 |
| [Marketplace.Infrastructure/Persistence/Configurations/AddressConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/AddressConfiguration.cs) | C# | 46 | 1 | 10 | 57 |
| [Marketplace.Infrastructure/Persistence/Configurations/CartConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/CartConfiguration.cs) | C# | 21 | 0 | 7 | 28 |
| [Marketplace.Infrastructure/Persistence/Configurations/CartItemConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/CartItemConfiguration.cs) | C# | 29 | 0 | 10 | 39 |
| [Marketplace.Infrastructure/Persistence/Configurations/CategoryConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/CategoryConfiguration.cs) | C# | 56 | 0 | 12 | 68 |
| [Marketplace.Infrastructure/Persistence/Configurations/ChatConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/ChatConfiguration.cs) | C# | 31 | 7 | 9 | 47 |
| [Marketplace.Infrastructure/Persistence/Configurations/CompanyConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/CompanyConfiguration.cs) | C# | 76 | 1 | 18 | 95 |
| [Marketplace.Infrastructure/Persistence/Configurations/CompanyFinanceConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/CompanyFinanceConfiguration.cs) | C# | 35 | 0 | 12 | 47 |
| [Marketplace.Infrastructure/Persistence/Configurations/CompanyScheduleConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/CompanyScheduleConfiguration.cs) | C# | 29 | 0 | 7 | 36 |
| [Marketplace.Infrastructure/Persistence/Configurations/CompanyUserConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/CompanyUserConfiguration.cs) | C# | 28 | 5 | 8 | 41 |
| [Marketplace.Infrastructure/Persistence/Configurations/CouponConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/CouponConfiguration.cs) | C# | 36 | 0 | 11 | 47 |
| [Marketplace.Infrastructure/Persistence/Configurations/FavoriteConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/FavoriteConfiguration.cs) | C# | 27 | 0 | 8 | 35 |
| [Marketplace.Infrastructure/Persistence/Configurations/MessageConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/MessageConfiguration.cs) | C# | 33 | 8 | 9 | 50 |
| [Marketplace.Infrastructure/Persistence/Configurations/NotificationConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/NotificationConfiguration.cs) | C# | 28 | 2 | 7 | 37 |
| [Marketplace.Infrastructure/Persistence/Configurations/OrderConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/OrderConfiguration.cs) | C# | 56 | 4 | 18 | 78 |
| [Marketplace.Infrastructure/Persistence/Configurations/OrderCouponConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/OrderCouponConfiguration.cs) | C# | 29 | 2 | 7 | 38 |
| [Marketplace.Infrastructure/Persistence/Configurations/OrderItemConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/OrderItemConfiguration.cs) | C# | 43 | 2 | 12 | 57 |
| [Marketplace.Infrastructure/Persistence/Configurations/OrderNoteConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/OrderNoteConfiguration.cs) | C# | 25 | 0 | 8 | 33 |
| [Marketplace.Infrastructure/Persistence/Configurations/PaymentConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/PaymentConfiguration.cs) | C# | 41 | 2 | 11 | 54 |
| [Marketplace.Infrastructure/Persistence/Configurations/ProductConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/ProductConfiguration.cs) | C# | 82 | 3 | 23 | 108 |
| [Marketplace.Infrastructure/Persistence/Configurations/ProductImageConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/ProductImageConfiguration.cs) | C# | 38 | 1 | 10 | 49 |
| [Marketplace.Infrastructure/Persistence/Configurations/RatingConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/RatingConfiguration.cs) | C# | 41 | 0 | 12 | 53 |
| [Marketplace.Infrastructure/Persistence/Configurations/ReviewConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/ReviewConfiguration.cs) | C# | 41 | 0 | 12 | 53 |
| [Marketplace.Infrastructure/Persistence/Configurations/SellerRequestConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/SellerRequestConfiguration.cs) | C# | 44 | 3 | 10 | 57 |
| [Marketplace.Infrastructure/Persistence/Configurations/ShippingMethodConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/ShippingMethodConfiguration.cs) | C# | 32 | 0 | 6 | 38 |
| [Marketplace.Infrastructure/Persistence/Configurations/UserConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/UserConfiguration.cs) | C# | 47 | 0 | 16 | 63 |
| [Marketplace.Infrastructure/Persistence/Configurations/WishlistConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/WishlistConfiguration.cs) | C# | 21 | 0 | 6 | 27 |
| [Marketplace.Infrastructure/Persistence/Configurations/WishlistItemConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/WishlistItemConfiguration.cs) | C# | 29 | 0 | 5 | 34 |
| [Marketplace.Infrastructure/Persistence/Implementation/AddressRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/AddressRepository.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Infrastructure/Persistence/Implementation/CartItemRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/CartItemRepository.cs) | C# | 35 | 0 | 8 | 43 |
| [Marketplace.Infrastructure/Persistence/Implementation/CartRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/CartRepository.cs) | C# | 18 | 0 | 5 | 23 |
| [Marketplace.Infrastructure/Persistence/Implementation/CategoryRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/CategoryRepository.cs) | C# | 146 | 25 | 29 | 200 |
| [Marketplace.Infrastructure/Persistence/Implementation/ChatRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/ChatRepository.cs) | C# | 15 | 0 | 3 | 18 |
| [Marketplace.Infrastructure/Persistence/Implementation/CompanyFinanceRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/CompanyFinanceRepository.cs) | C# | 18 | 0 | 5 | 23 |
| [Marketplace.Infrastructure/Persistence/Implementation/CompanyRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/CompanyRepository.cs) | C# | 173 | 20 | 25 | 218 |
| [Marketplace.Infrastructure/Persistence/Implementation/CompanyScheduleRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/CompanyScheduleRepository.cs) | C# | 15 | 0 | 2 | 17 |
| [Marketplace.Infrastructure/Persistence/Implementation/CompanyUserRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/CompanyUserRepository.cs) | C# | 18 | 0 | 4 | 22 |
| [Marketplace.Infrastructure/Persistence/Implementation/CouponRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/CouponRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/FavoriteRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/FavoriteRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/MessageRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/MessageRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/NotificationRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/NotificationRepository.cs) | C# | 29 | 3 | 8 | 40 |
| [Marketplace.Infrastructure/Persistence/Implementation/OrderCouponRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/OrderCouponRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/OrderItemRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/OrderItemRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/OrderNoteRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/OrderNoteRepository.cs) | C# | 20 | 0 | 5 | 25 |
| [Marketplace.Infrastructure/Persistence/Implementation/OrderRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/OrderRepository.cs) | C# | 20 | 0 | 4 | 24 |
| [Marketplace.Infrastructure/Persistence/Implementation/PaymentRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/PaymentRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/ProductImageRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/ProductImageRepository.cs) | C# | 25 | 0 | 4 | 29 |
| [Marketplace.Infrastructure/Persistence/Implementation/ProductRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/ProductRepository.cs) | C# | 158 | 10 | 31 | 199 |
| [Marketplace.Infrastructure/Persistence/Implementation/RatingRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/RatingRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/Repository.cs](/Marketplace.Infrastructure/Persistence/Implementation/Repository.cs) | C# | 142 | 14 | 27 | 183 |
| [Marketplace.Infrastructure/Persistence/Implementation/ReviewRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/ReviewRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/SellerRequestRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/SellerRequestRepository.cs) | C# | 42 | 0 | 5 | 47 |
| [Marketplace.Infrastructure/Persistence/Implementation/ShippingMethodRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/ShippingMethodRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/UserRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/UserRepository.cs) | C# | 49 | 4 | 8 | 61 |
| [Marketplace.Infrastructure/Persistence/Implementation/WishlistItemRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/WishlistItemRepository.cs) | C# | 35 | 0 | 6 | 41 |
| [Marketplace.Infrastructure/Persistence/Implementation/WishlistRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/WishlistRepository.cs) | C# | 18 | 0 | 3 | 21 |
| [Marketplace.Infrastructure/Persistence/MarketplaceDbContext.cs](/Marketplace.Infrastructure/Persistence/MarketplaceDbContext.cs) | C# | 39 | 0 | 3 | 42 |
| [Marketplace.Infrastructure/Persistence/Seeders/CompanySeeder.cs](/Marketplace.Infrastructure/Persistence/Seeders/CompanySeeder.cs) | C# | 175 | 20 | 37 | 232 |
| [Marketplace.Infrastructure/Persistence/Seeders/SellerRequestSeeder.cs](/Marketplace.Infrastructure/Persistence/Seeders/SellerRequestSeeder.cs) | C# | 175 | 13 | 35 | 223 |
| [Marketplace.Infrastructure/Persistence/Seeders/UserSeeder.cs](/Marketplace.Infrastructure/Persistence/Seeders/UserSeeder.cs) | C# | 124 | 10 | 25 | 159 |
| [Marketplace.Infrastructure/Services/Auth/IPasswordHasher.cs](/Marketplace.Infrastructure/Services/Auth/IPasswordHasher.cs) | C# | 6 | 0 | 2 | 8 |
| [Marketplace.Infrastructure/Services/Auth/PasswordHasher.cs](/Marketplace.Infrastructure/Services/Auth/PasswordHasher.cs) | C# | 48 | 0 | 9 | 57 |
| [Marketplace.Infrastructure/Services/FileServiceOptions.cs](/Marketplace.Infrastructure/Services/FileServiceOptions.cs) | C# | 15 | 15 | 5 | 35 |
| [Marketplace.Infrastructure/Services/FileStorageService.cs](/Marketplace.Infrastructure/Services/FileStorageService.cs) | C# | 65 | 8 | 16 | 89 |
| [Marketplace.Infrastructure/Services/Interfaces/IFileStorageService.cs](/Marketplace.Infrastructure/Services/Interfaces/IFileStorageService.cs) | C# | 11 | 26 | 6 | 43 |
| [Marketplace.Infrastructure/Services/LocalFileService.cs](/Marketplace.Infrastructure/Services/LocalFileService.cs) | C# | 156 | 25 | 35 | 216 |
| [Marketplace.Presentation/Controllers/Addresses/UserAddressController.cs](/Marketplace.Presentation/Controllers/Addresses/UserAddressController.cs) | C# | 81 | 6 | 21 | 108 |
| [Marketplace.Presentation/Controllers/Admin/AdminAddressController.cs](/Marketplace.Presentation/Controllers/Admin/AdminAddressController.cs) | C# | 107 | 6 | 19 | 132 |
| [Marketplace.Presentation/Controllers/Admin/AdminCategoryController.cs](/Marketplace.Presentation/Controllers/Admin/AdminCategoryController.cs) | C# | 122 | 11 | 32 | 165 |
| [Marketplace.Presentation/Controllers/Admin/AdminChatController.cs](/Marketplace.Presentation/Controllers/Admin/AdminChatController.cs) | C# | 92 | 0 | 14 | 106 |
| [Marketplace.Presentation/Controllers/Admin/AdminCompanyController.cs](/Marketplace.Presentation/Controllers/Admin/AdminCompanyController.cs) | C# | 239 | 16 | 55 | 310 |
| [Marketplace.Presentation/Controllers/Admin/AdminDashboardController.cs](/Marketplace.Presentation/Controllers/Admin/AdminDashboardController.cs) | C# | 59 | 0 | 14 | 73 |
| [Marketplace.Presentation/Controllers/Admin/AdminOrderController.cs](/Marketplace.Presentation/Controllers/Admin/AdminOrderController.cs) | C# | 146 | 1 | 23 | 170 |
| [Marketplace.Presentation/Controllers/Admin/AdminProductController.cs](/Marketplace.Presentation/Controllers/Admin/AdminProductController.cs) | C# | 294 | 17 | 68 | 379 |
| [Marketplace.Presentation/Controllers/Admin/AdminRatingController.cs](/Marketplace.Presentation/Controllers/Admin/AdminRatingController.cs) | C# | 77 | 0 | 14 | 91 |
| [Marketplace.Presentation/Controllers/Admin/AdminReviewController.cs](/Marketplace.Presentation/Controllers/Admin/AdminReviewController.cs) | C# | 77 | 0 | 14 | 91 |
| [Marketplace.Presentation/Controllers/Admin/AdminSecurityController.cs](/Marketplace.Presentation/Controllers/Admin/AdminSecurityController.cs) | C# | 201 | 7 | 15 | 223 |
| [Marketplace.Presentation/Controllers/Admin/AdminSellerRequestController.cs](/Marketplace.Presentation/Controllers/Admin/AdminSellerRequestController.cs) | C# | 132 | 36 | 26 | 194 |
| [Marketplace.Presentation/Controllers/Admin/AdminSettingsController.cs](/Marketplace.Presentation/Controllers/Admin/AdminSettingsController.cs) | C# | 122 | 11 | 18 | 151 |
| [Marketplace.Presentation/Controllers/Admin/AdminUserController.cs](/Marketplace.Presentation/Controllers/Admin/AdminUserController.cs) | C# | 111 | 4 | 21 | 136 |
| [Marketplace.Presentation/Controllers/AuthController.cs](/Marketplace.Presentation/Controllers/AuthController.cs) | C# | 96 | 11 | 25 | 132 |
| [Marketplace.Presentation/Controllers/BasicApiController.cs](/Marketplace.Presentation/Controllers/BasicApiController.cs) | C# | 35 | 0 | 8 | 43 |
| [Marketplace.Presentation/Controllers/Categories/CategoryController.cs](/Marketplace.Presentation/Controllers/Categories/CategoryController.cs) | C# | 121 | 4 | 24 | 149 |
| [Marketplace.Presentation/Controllers/ChatController.cs](/Marketplace.Presentation/Controllers/ChatController.cs) | C# | 69 | 0 | 17 | 86 |
| [Marketplace.Presentation/Controllers/Companies/CompanyController.cs](/Marketplace.Presentation/Controllers/Companies/CompanyController.cs) | C# | 100 | 3 | 19 | 122 |
| [Marketplace.Presentation/Controllers/Products/ProductController.cs](/Marketplace.Presentation/Controllers/Products/ProductController.cs) | C# | 68 | 1 | 13 | 82 |
| [Marketplace.Presentation/Controllers/Products/SellerProductController.cs](/Marketplace.Presentation/Controllers/Products/SellerProductController.cs) | C# | 99 | 4 | 24 | 127 |
| [Marketplace.Presentation/Controllers/Sellers/SellerAnalyticsController.cs](/Marketplace.Presentation/Controllers/Sellers/SellerAnalyticsController.cs) | C# | 58 | 0 | 13 | 71 |
| [Marketplace.Presentation/Controllers/Sellers/SellerCompanyController.cs](/Marketplace.Presentation/Controllers/Sellers/SellerCompanyController.cs) | C# | 103 | 20 | 32 | 155 |
| [Marketplace.Presentation/Controllers/Sellers/SellerCompanyFinanceController.cs](/Marketplace.Presentation/Controllers/Sellers/SellerCompanyFinanceController.cs) | C# | 39 | 1 | 10 | 50 |
| [Marketplace.Presentation/Controllers/Sellers/SellerCompanyScheduleController.cs](/Marketplace.Presentation/Controllers/Sellers/SellerCompanyScheduleController.cs) | C# | 65 | 2 | 16 | 83 |
| [Marketplace.Presentation/Controllers/Sellers/SellerCompanySettingsController.cs](/Marketplace.Presentation/Controllers/Sellers/SellerCompanySettingsController.cs) | C# | 39 | 1 | 10 | 50 |
| [Marketplace.Presentation/Controllers/Sellers/SellerCompanyUserController.cs](/Marketplace.Presentation/Controllers/Sellers/SellerCompanyUserController.cs) | C# | 78 | 3 | 17 | 98 |
| [Marketplace.Presentation/Controllers/Sellers/SellerOrderController.cs](/Marketplace.Presentation/Controllers/Sellers/SellerOrderController.cs) | C# | 75 | 0 | 13 | 88 |
| [Marketplace.Presentation/Controllers/TestController.cs](/Marketplace.Presentation/Controllers/TestController.cs) | C# | 49 | 0 | 9 | 58 |
| [Marketplace.Presentation/Controllers/UserCarts/UserCartController.cs](/Marketplace.Presentation/Controllers/UserCarts/UserCartController.cs) | C# | 45 | 1 | 14 | 60 |
| [Marketplace.Presentation/Controllers/UserCarts/UserCartItemController.cs](/Marketplace.Presentation/Controllers/UserCarts/UserCartItemController.cs) | C# | 76 | 7 | 19 | 102 |
| [Marketplace.Presentation/Controllers/UserChats/UserChatController.cs](/Marketplace.Presentation/Controllers/UserChats/UserChatController.cs) | C# | 60 | 2 | 13 | 75 |
| [Marketplace.Presentation/Controllers/UserChats/UserChatMessageController.cs](/Marketplace.Presentation/Controllers/UserChats/UserChatMessageController.cs) | C# | 66 | 2 | 13 | 81 |
| [Marketplace.Presentation/Controllers/UserCoupons/UserCouponController.cs](/Marketplace.Presentation/Controllers/UserCoupons/UserCouponController.cs) | C# | 35 | 1 | 10 | 46 |
| [Marketplace.Presentation/Controllers/UserFavorites/UserFavoriteController.cs](/Marketplace.Presentation/Controllers/UserFavorites/UserFavoriteController.cs) | C# | 86 | 7 | 22 | 115 |
| [Marketplace.Presentation/Controllers/UserNotifications/UserNotificationController.cs](/Marketplace.Presentation/Controllers/UserNotifications/UserNotificationController.cs) | C# | 71 | 1 | 16 | 88 |
| [Marketplace.Presentation/Controllers/UserOrders/UserOrderController.cs](/Marketplace.Presentation/Controllers/UserOrders/UserOrderController.cs) | C# | 72 | 2 | 16 | 90 |
| [Marketplace.Presentation/Controllers/UserOrders/UserOrderCouponController.cs](/Marketplace.Presentation/Controllers/UserOrders/UserOrderCouponController.cs) | C# | 44 | 1 | 8 | 53 |
| [Marketplace.Presentation/Controllers/UserOrders/UserOrderItemController.cs](/Marketplace.Presentation/Controllers/UserOrders/UserOrderItemController.cs) | C# | 44 | 1 | 8 | 53 |
| [Marketplace.Presentation/Controllers/UserPayments/UserPaymentController.cs](/Marketplace.Presentation/Controllers/UserPayments/UserPaymentController.cs) | C# | 70 | 2 | 14 | 86 |
| [Marketplace.Presentation/Controllers/UserRatings/UserRatingController.cs](/Marketplace.Presentation/Controllers/UserRatings/UserRatingController.cs) | C# | 72 | 3 | 16 | 91 |
| [Marketplace.Presentation/Controllers/UserReviews/UserReviewController.cs](/Marketplace.Presentation/Controllers/UserReviews/UserReviewController.cs) | C# | 72 | 3 | 16 | 91 |
| [Marketplace.Presentation/Controllers/UserSellerRequests/UserSellerRequestController.cs](/Marketplace.Presentation/Controllers/UserSellerRequests/UserSellerRequestController.cs) | C# | 61 | 2 | 13 | 76 |
| [Marketplace.Presentation/Controllers/UserWishlists/UserWishlistController.cs](/Marketplace.Presentation/Controllers/UserWishlists/UserWishlistController.cs) | C# | 46 | 51 | 22 | 119 |
| [Marketplace.Presentation/Controllers/UserWishlists/UserWishlistItemController.cs](/Marketplace.Presentation/Controllers/UserWishlists/UserWishlistItemController.cs) | C# | 60 | 5 | 10 | 75 |
| [Marketplace.Presentation/Controllers/Users/<USER>/Marketplace.Presentation/Controllers/Users/<USER>
| [Marketplace.Presentation/Controllers/Users/<USER>/Marketplace.Presentation/Controllers/Users/<USER>
| [Marketplace.Presentation/Filters/AddFileUploadParams.cs](/Marketplace.Presentation/Filters/AddFileUploadParams.cs) | C# | 33 | 0 | 2 | 35 |
| [Marketplace.Presentation/Filters/ApiExceptionFilterAttribute.cs](/Marketplace.Presentation/Filters/ApiExceptionFilterAttribute.cs) | C# | 108 | 2 | 26 | 136 |
| [Marketplace.Presentation/Marketplace.Presentation.csproj](/Marketplace.Presentation/Marketplace.Presentation.csproj) | XML | 18 | 0 | 5 | 23 |
| [Marketplace.Presentation/Middleware/FileMiddleware.cs](/Marketplace.Presentation/Middleware/FileMiddleware.cs) | C# | 64 | 13 | 13 | 90 |
| [Marketplace.Presentation/Middleware/GlobalExceptionHandlingMiddleware.cs](/Marketplace.Presentation/Middleware/GlobalExceptionHandlingMiddleware.cs) | C# | 126 | 5 | 18 | 149 |
| [Marketplace.Presentation/Middleware/RequestLoggingMiddleware.cs](/Marketplace.Presentation/Middleware/RequestLoggingMiddleware.cs) | C# | 47 | 4 | 11 | 62 |
| [Marketplace.Presentation/Models/AddOrderNoteRequest.cs](/Marketplace.Presentation/Models/AddOrderNoteRequest.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Presentation/Models/FileUploadRequest.cs](/Marketplace.Presentation/Models/FileUploadRequest.cs) | C# | 6 | 6 | 3 | 15 |
| [Marketplace.Presentation/Models/UpdateOrderRequest.cs](/Marketplace.Presentation/Models/UpdateOrderRequest.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Presentation/Models/UpdateOrderStatusRequest.cs](/Marketplace.Presentation/Models/UpdateOrderStatusRequest.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Presentation/Pages/Error.cshtml](/Marketplace.Presentation/Pages/Error.cshtml) | ASP.NET Razor | 23 | 0 | 4 | 27 |
| [Marketplace.Presentation/Pages/Error.cshtml.cs](/Marketplace.Presentation/Pages/Error.cshtml.cs) | C# | 20 | 0 | 8 | 28 |
| [Marketplace.Presentation/Pages/MockAuthenticationHandler.cs](/Marketplace.Presentation/Pages/MockAuthenticationHandler.cs) | C# | 27 | 2 | 7 | 36 |
| [Marketplace.Presentation/Pages/Privacy.cshtml](/Marketplace.Presentation/Pages/Privacy.cshtml) | ASP.NET Razor | 7 | 0 | 2 | 9 |
| [Marketplace.Presentation/Pages/Privacy.cshtml.cs](/Marketplace.Presentation/Pages/Privacy.cshtml.cs) | C# | 14 | 0 | 6 | 20 |
| [Marketplace.Presentation/Pages/Shared/\_Layout.cshtml](/Marketplace.Presentation/Pages/Shared/_Layout.cshtml) | ASP.NET Razor | 49 | 0 | 4 | 53 |
| [Marketplace.Presentation/Pages/Shared/\_Layout.cshtml.css](/Marketplace.Presentation/Pages/Shared/_Layout.cshtml.css) | PostCSS | 38 | 2 | 9 | 49 |
| [Marketplace.Presentation/Pages/Shared/\_ValidationScriptsPartial.cshtml](/Marketplace.Presentation/Pages/Shared/_ValidationScriptsPartial.cshtml) | ASP.NET Razor | 2 | 0 | 1 | 3 |
| [Marketplace.Presentation/Pages/\_ViewImports.cshtml](/Marketplace.Presentation/Pages/_ViewImports.cshtml) | ASP.NET Razor | 5 | 0 | 2 | 7 |
| [Marketplace.Presentation/Pages/\_ViewStart.cshtml](/Marketplace.Presentation/Pages/_ViewStart.cshtml) | ASP.NET Razor | 3 | 0 | 1 | 4 |
| [Marketplace.Presentation/Program.cs](/Marketplace.Presentation/Program.cs) | C# | 142 | 8 | 30 | 180 |
| [Marketplace.Presentation/Properties/launchSettings.json](/Marketplace.Presentation/Properties/launchSettings.json) | JSON | 23 | 0 | 1 | 24 |
| [Marketplace.Presentation/Responses/ApiResponse.cs](/Marketplace.Presentation/Responses/ApiResponse.cs) | C# | 65 | 59 | 21 | 145 |
| [Marketplace.Presentation/Validators/CartRequestValidator.cs](/Marketplace.Presentation/Validators/CartRequestValidator.cs) | C# | 21 | 0 | 5 | 26 |
| [Marketplace.Presentation/appsettings.Development.json](/Marketplace.Presentation/appsettings.Development.json) | JSON | 8 | 0 | 1 | 9 |
| [Marketplace.Presentation/appsettings.json](/Marketplace.Presentation/appsettings.json) | JSON | 49 | 0 | 1 | 50 |
| [Routes.md](/Routes.md) | Markdown | 348 | 0 | 50 | 398 |
| [frontend/.env](/frontend/.env) | Properties | 1 | 0 | 1 | 2 |
| [frontend/README.md](/frontend/README.md) | Markdown | 36 | 0 | 15 | 51 |
| [frontend/debug-companies.html](/frontend/debug-companies.html) | HTML | 173 | 0 | 36 | 209 |
| [frontend/dist/assets/AddressList-C7QdrGga.js](/frontend/dist/assets/AddressList-C7QdrGga.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/AddressList-DUoQNMeQ.css](/frontend/dist/assets/AddressList-DUoQNMeQ.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/AdminLayout-CBHIPG9S.js](/frontend/dist/assets/AdminLayout-CBHIPG9S.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/AdminLayout-dOIlMIHf.css](/frontend/dist/assets/AdminLayout-dOIlMIHf.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/ApiTest-C0k86QHm.css](/frontend/dist/assets/ApiTest-C0k86QHm.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/ApiTest-DW6hagWN.js](/frontend/dist/assets/ApiTest-DW6hagWN.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/Categories-BpGwVtEa.css](/frontend/dist/assets/Categories-BpGwVtEa.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/Categories-D9zsjlES.js](/frontend/dist/assets/Categories-D9zsjlES.js) | JavaScript | 4 | 0 | 2 | 6 |
| [frontend/dist/assets/CategoryDetail-B4IKCyPy.css](/frontend/dist/assets/CategoryDetail-B4IKCyPy.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/CategoryDetail-BXsk97iL.js](/frontend/dist/assets/CategoryDetail-BXsk97iL.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/CategoryForm-B3RdiLfv.css](/frontend/dist/assets/CategoryForm-B3RdiLfv.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/CategoryForm-D5d5Ygds.js](/frontend/dist/assets/CategoryForm-D5d5Ygds.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/ChatDetail-BKvrnliU.css](/frontend/dist/assets/ChatDetail-BKvrnliU.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/ChatDetail-HTEL-Mxz.js](/frontend/dist/assets/ChatDetail-HTEL-Mxz.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/ChatList-7T4PXaa6.js](/frontend/dist/assets/ChatList-7T4PXaa6.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/ChatList-DtcMA9p3.css](/frontend/dist/assets/ChatList-DtcMA9p3.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/CompanyDetail-B67gj86p.css](/frontend/dist/assets/CompanyDetail-B67gj86p.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/CompanyDetail-CwN6XXMC.js](/frontend/dist/assets/CompanyDetail-CwN6XXMC.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/CompanyEdit-8cgn5VqO.css](/frontend/dist/assets/CompanyEdit-8cgn5VqO.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/CompanyEdit-scb5NTdg.js](/frontend/dist/assets/CompanyEdit-scb5NTdg.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/CompanyList-BdY3K2iA.js](/frontend/dist/assets/CompanyList-BdY3K2iA.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/CompanyList-CGKCizUa.css](/frontend/dist/assets/CompanyList-CGKCizUa.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/ConfirmDialog-Ciq4Lct0.css](/frontend/dist/assets/ConfirmDialog-Ciq4Lct0.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/ConfirmDialog-D-ERnN1w.js](/frontend/dist/assets/ConfirmDialog-D-ERnN1w.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/Dashboard-Bl7jn66j.css](/frontend/dist/assets/Dashboard-Bl7jn66j.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/Dashboard-T74uSeOy.js](/frontend/dist/assets/Dashboard-T74uSeOy.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/NotFoundPage-BQ83\_W1z.js](/frontend/dist/assets/NotFoundPage-BQ83_W1z.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/NotFoundPage-CYYAFLUo.css](/frontend/dist/assets/NotFoundPage-CYYAFLUo.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/OrderDetail-CVFXdiNN.css](/frontend/dist/assets/OrderDetail-CVFXdiNN.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/OrderDetail-D4oSc2Ve.js](/frontend/dist/assets/OrderDetail-D4oSc2Ve.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/OrderList-C3bK\_vtI.css](/frontend/dist/assets/OrderList-C3bK_vtI.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/OrderList-IQHPhA79.js](/frontend/dist/assets/OrderList-IQHPhA79.js) | JavaScript | 96 | 0 | 0 | 96 |
| [frontend/dist/assets/Pagination-CPqEyUKP.js](/frontend/dist/assets/Pagination-CPqEyUKP.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/Pagination-D8AmIHT6.css](/frontend/dist/assets/Pagination-D8AmIHT6.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/Pagination-DRPk5ywh.css](/frontend/dist/assets/Pagination-DRPk5ywh.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/Pagination-edGYNZJ6.js](/frontend/dist/assets/Pagination-edGYNZJ6.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/ProductCreate-CkVbH1Tx.js](/frontend/dist/assets/ProductCreate-CkVbH1Tx.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/ProductEdit-DAzH4Um\_.js](/frontend/dist/assets/ProductEdit-DAzH4Um_.js) | JavaScript | 4 | 0 | 0 | 4 |
| [frontend/dist/assets/ProductEdit-H8vHDEWl.css](/frontend/dist/assets/ProductEdit-H8vHDEWl.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/ProductView-BoUpyhlu.js](/frontend/dist/assets/ProductView-BoUpyhlu.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/ProductView-DQSO-bh3.css](/frontend/dist/assets/ProductView-DQSO-bh3.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/Products-DOEb2zmr.css](/frontend/dist/assets/Products-DOEb2zmr.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/Products-x9aTC5oc.js](/frontend/dist/assets/Products-x9aTC5oc.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/RatingList-1IgW6TpA.css](/frontend/dist/assets/RatingList-1IgW6TpA.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/RatingList-WeDJdqt5.js](/frontend/dist/assets/RatingList-WeDJdqt5.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/Reports-Bxawa4RO.css](/frontend/dist/assets/Reports-Bxawa4RO.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/Reports-CZBRxAD9.js](/frontend/dist/assets/Reports-CZBRxAD9.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/ReviewDetail-B6JCSIst.css](/frontend/dist/assets/ReviewDetail-B6JCSIst.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/ReviewDetail-DFYP6jy3.js](/frontend/dist/assets/ReviewDetail-DFYP6jy3.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/ReviewList-BgzQ0K07.js](/frontend/dist/assets/ReviewList-BgzQ0K07.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/ReviewList-DLdVcqqv.css](/frontend/dist/assets/ReviewList-DLdVcqqv.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/SearchAndFilters-C\_9O2onY.js](/frontend/dist/assets/SearchAndFilters-C_9O2onY.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/SearchAndFilters-Cu63lczy.css](/frontend/dist/assets/SearchAndFilters-Cu63lczy.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/Security-CgfBEWo9.js](/frontend/dist/assets/Security-CgfBEWo9.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/Security-DnZKgZUg.css](/frontend/dist/assets/Security-DnZKgZUg.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/SellerRequestDetail-CglLDR5r.css](/frontend/dist/assets/SellerRequestDetail-CglLDR5r.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/SellerRequestDetail-DGGSLhU8.js](/frontend/dist/assets/SellerRequestDetail-DGGSLhU8.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/SellerRequestList-DkJjL0zB.js](/frontend/dist/assets/SellerRequestList-DkJjL0zB.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/SellerRequestList-VDtJTVtz.css](/frontend/dist/assets/SellerRequestList-VDtJTVtz.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/Settings-BaQGZkOH.css](/frontend/dist/assets/Settings-BaQGZkOH.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/Settings-CBGtY6Ex.js](/frontend/dist/assets/Settings-CBGtY6Ex.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/StatusBadge-DOAc9VY6.css](/frontend/dist/assets/StatusBadge-DOAc9VY6.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/StatusBadge-nLr7h2vF.js](/frontend/dist/assets/StatusBadge-nLr7h2vF.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/UserDetail-DiNFp9Ly.js](/frontend/dist/assets/UserDetail-DiNFp9Ly.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/UserDetail-qS4\_ki7-.css](/frontend/dist/assets/UserDetail-qS4_ki7-.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/UserList-BgyPdX3Z.js](/frontend/dist/assets/UserList-BgyPdX3Z.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/UserList-KfO-dYkO.css](/frontend/dist/assets/UserList-KfO-dYkO.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/auto-Cz6uSJnr.js](/frontend/dist/assets/auto-Cz6uSJnr.js) | JavaScript | 19 | 0 | 0 | 19 |
| [frontend/dist/assets/chats-CZakl\_Qo.js](/frontend/dist/assets/chats-CZakl_Qo.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/companies-br2VMpfN.js](/frontend/dist/assets/companies-br2VMpfN.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/index-C988bJkl.css](/frontend/dist/assets/index-C988bJkl.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/index-DMg5qKr1.js](/frontend/dist/assets/index-DMg5qKr1.js) | JavaScript | 43 | 0 | 3 | 46 |
| [frontend/dist/assets/logo-DSD9Se8B.svg](/frontend/dist/assets/logo-DSD9Se8B.svg) | XML | 24 | 0 | 1 | 25 |
| [frontend/dist/assets/orders-ClCsUPn6.js](/frontend/dist/assets/orders-ClCsUPn6.js) | JavaScript | 4 | 0 | 0 | 4 |
| [frontend/dist/assets/products-BHT6Tynz.js](/frontend/dist/assets/products-BHT6Tynz.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/reviews-SbbYQUtQ.js](/frontend/dist/assets/reviews-SbbYQUtQ.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/seller-requests-BH28X\_RT.js](/frontend/dist/assets/seller-requests-BH28X_RT.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/useAdminSearch-BQKMobin.js](/frontend/dist/assets/useAdminSearch-BQKMobin.js) | JavaScript | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/useAdminSearch-CT-OHu28.css](/frontend/dist/assets/useAdminSearch-CT-OHu28.css) | PostCSS | 1 | 0 | 1 | 2 |
| [frontend/dist/assets/users-BrKe\_L0p.js](/frontend/dist/assets/users-BrKe_L0p.js) | JavaScript | 4 | 0 | 0 | 4 |
| [frontend/dist/index.html](/frontend/dist/index.html) | HTML | 153 | 2 | 27 | 182 |
| [frontend/index.html](/frontend/index.html) | HTML | 152 | 2 | 26 | 180 |
| [frontend/package-lock.json](/frontend/package-lock.json) | JSON | 1,025 | 0 | 1 | 1,026 |
| [frontend/package.json](/frontend/package.json) | JSON | 31 | 0 | 1 | 32 |
| [frontend/src/App.vue](/frontend/src/App.vue) | vue | 344 | 1 | 63 | 408 |
| [frontend/src/TestComponent.vue](/frontend/src/TestComponent.vue) | vue | 17 | 0 | 4 | 21 |
| [frontend/src/admin/README.md](/frontend/src/admin/README.md) | Markdown | 74 | 0 | 17 | 91 |
| [frontend/src/admin/api.js](/frontend/src/admin/api.js) | JavaScript | 2 | 2 | 3 | 7 |
| [frontend/src/admin/components/Header.vue](/frontend/src/admin/components/Header.vue) | vue | 306 | 3 | 54 | 363 |
| [frontend/src/admin/components/Sidebar.vue](/frontend/src/admin/components/Sidebar.vue) | vue | 293 | 8 | 44 | 345 |
| [frontend/src/admin/components/categories/CategoryFormModal.vue](/frontend/src/admin/components/categories/CategoryFormModal.vue) | vue | 209 | 4 | 24 | 237 |
| [frontend/src/admin/components/categories/CategoryHierarchy.vue](/frontend/src/admin/components/categories/CategoryHierarchy.vue) | vue | 41 | 0 | 7 | 48 |
| [frontend/src/admin/components/categories/CategoryHierarchyItem.vue](/frontend/src/admin/components/categories/CategoryHierarchyItem.vue) | vue | 208 | 3 | 20 | 231 |
| [frontend/src/admin/components/categories/CategorySkeleton.vue](/frontend/src/admin/components/categories/CategorySkeleton.vue) | vue | 162 | 2 | 20 | 184 |
| [frontend/src/admin/components/categories/CategoryTable.vue](/frontend/src/admin/components/categories/CategoryTable.vue) | vue | 207 | 6 | 20 | 233 |
| [frontend/src/admin/components/categories/CategoryTreeNode.vue](/frontend/src/admin/components/categories/CategoryTreeNode.vue) | vue | 141 | 0 | 19 | 160 |
| [frontend/src/admin/components/common/AdminStatCard.vue](/frontend/src/admin/components/common/AdminStatCard.vue) | vue | 89 | 0 | 13 | 102 |
| [frontend/src/admin/components/common/CategorySelect.vue](/frontend/src/admin/components/common/CategorySelect.vue) | vue | 273 | 0 | 38 | 311 |
| [frontend/src/admin/components/common/CompanySelect.vue](/frontend/src/admin/components/common/CompanySelect.vue) | vue | 290 | 0 | 39 | 329 |
| [frontend/src/admin/components/common/ConfirmDialog.vue](/frontend/src/admin/components/common/ConfirmDialog.vue) | vue | 102 | 0 | 16 | 118 |
| [frontend/src/admin/components/common/DataTable.vue](/frontend/src/admin/components/common/DataTable.vue) | vue | 403 | 5 | 56 | 464 |
| [frontend/src/admin/components/common/EmptyState.vue](/frontend/src/admin/components/common/EmptyState.vue) | vue | 62 | 0 | 7 | 69 |
| [frontend/src/admin/components/common/FilterPanel.vue](/frontend/src/admin/components/common/FilterPanel.vue) | vue | 98 | 0 | 16 | 114 |
| [frontend/src/admin/components/common/LoadingIndicator.vue](/frontend/src/admin/components/common/LoadingIndicator.vue) | vue | 58 | 0 | 7 | 65 |
| [frontend/src/admin/components/common/Pagination.vue](/frontend/src/admin/components/common/Pagination.vue) | vue | 134 | 5 | 22 | 161 |
| [frontend/src/admin/components/common/SearchAndFilters.vue](/frontend/src/admin/components/common/SearchAndFilters.vue) | vue | 242 | 10 | 28 | 280 |
| [frontend/src/admin/components/common/StatusBadge.vue](/frontend/src/admin/components/common/StatusBadge.vue) | vue | 191 | 0 | 26 | 217 |
| [frontend/src/admin/components/common/Toast.vue](/frontend/src/admin/components/common/Toast.vue) | vue | 144 | 0 | 25 | 169 |
| [frontend/src/admin/components/dashboard/OrdersByStatusChart.vue](/frontend/src/admin/components/dashboard/OrdersByStatusChart.vue) | vue | 205 | 0 | 26 | 231 |
| [frontend/src/admin/components/dashboard/PendingSellerRequests.vue](/frontend/src/admin/components/dashboard/PendingSellerRequests.vue) | vue | 194 | 0 | 30 | 224 |
| [frontend/src/admin/components/dashboard/RecentOrders.vue](/frontend/src/admin/components/dashboard/RecentOrders.vue) | vue | 160 | 0 | 23 | 183 |
| [frontend/src/admin/components/dashboard/SalesChart.vue](/frontend/src/admin/components/dashboard/SalesChart.vue) | vue | 220 | 0 | 23 | 243 |
| [frontend/src/admin/components/orders/OrderDetails.vue](/frontend/src/admin/components/orders/OrderDetails.vue) | vue | 306 | 5 | 20 | 331 |
| [frontend/src/admin/components/orders/OrderDetailsModal.vue](/frontend/src/admin/components/orders/OrderDetailsModal.vue) | vue | 244 | 4 | 22 | 270 |
| [frontend/src/admin/components/orders/OrderItemsTable.vue](/frontend/src/admin/components/orders/OrderItemsTable.vue) | vue | 282 | 8 | 30 | 320 |
| [frontend/src/admin/components/orders/OrderStatusUpdate.vue](/frontend/src/admin/components/orders/OrderStatusUpdate.vue) | vue | 302 | 2 | 37 | 341 |
| [frontend/src/admin/components/orders/OrderTable.vue](/frontend/src/admin/components/orders/OrderTable.vue) | vue | 132 | 0 | 11 | 143 |
| [frontend/src/admin/components/products/ProductCreate.vue](/frontend/src/admin/components/products/ProductCreate.vue) | vue | 27 | 0 | 8 | 35 |
| [frontend/src/admin/components/products/ProductEdit.vue](/frontend/src/admin/components/products/ProductEdit.vue) | vue | 745 | 13 | 85 | 843 |
| [frontend/src/admin/components/products/ProductFormModal.vue](/frontend/src/admin/components/products/ProductFormModal.vue) | vue | 263 | 4 | 23 | 290 |
| [frontend/src/admin/components/products/ProductImageManager.vue](/frontend/src/admin/components/products/ProductImageManager.vue) | vue | 666 | 11 | 102 | 779 |
| [frontend/src/admin/components/products/ProductTable.vue](/frontend/src/admin/components/products/ProductTable.vue) | vue | 247 | 0 | 32 | 279 |
| [frontend/src/admin/components/products/ProductView.vue](/frontend/src/admin/components/products/ProductView.vue) | vue | 645 | 14 | 58 | 717 |
| [frontend/src/admin/components/seller-requests/SellerRequestDetailsModal.vue](/frontend/src/admin/components/seller-requests/SellerRequestDetailsModal.vue) | vue | 194 | 5 | 23 | 222 |
| [frontend/src/admin/components/seller-requests/SellerRequestFilters.vue](/frontend/src/admin/components/seller-requests/SellerRequestFilters.vue) | vue | 113 | 0 | 14 | 127 |
| [frontend/src/admin/components/seller-requests/SellerRequestTable.vue](/frontend/src/admin/components/seller-requests/SellerRequestTable.vue) | vue | 102 | 0 | 7 | 109 |
| [frontend/src/admin/components/users/UserFilters.vue](/frontend/src/admin/components/users/UserFilters.vue) | vue | 113 | 0 | 19 | 132 |
| [frontend/src/admin/components/users/UserFormModal.vue](/frontend/src/admin/components/users/UserFormModal.vue) | vue | 223 | 4 | 20 | 247 |
| [frontend/src/admin/components/users/UserTable.vue](/frontend/src/admin/components/users/UserTable.vue) | vue | 121 | 0 | 14 | 135 |
| [frontend/src/admin/layouts/AdminLayout.vue](/frontend/src/admin/layouts/AdminLayout.vue) | vue | 112 | 3 | 21 | 136 |
| [frontend/src/admin/services/addresses.js](/frontend/src/admin/services/addresses.js) | JavaScript | 66 | 0 | 8 | 74 |
| [frontend/src/admin/services/categories.js](/frontend/src/admin/services/categories.js) | JavaScript | 298 | 40 | 57 | 395 |
| [frontend/src/admin/services/chats.js](/frontend/src/admin/services/chats.js) | JavaScript | 57 | 0 | 7 | 64 |
| [frontend/src/admin/services/companies.js](/frontend/src/admin/services/companies.js) | JavaScript | 133 | 10 | 26 | 169 |
| [frontend/src/admin/services/dashboard.js](/frontend/src/admin/services/dashboard.js) | JavaScript | 88 | 26 | 28 | 142 |
| [frontend/src/admin/services/exportService.js](/frontend/src/admin/services/exportService.js) | JavaScript | 179 | 42 | 38 | 259 |
| [frontend/src/admin/services/orders.js](/frontend/src/admin/services/orders.js) | JavaScript | 555 | 50 | 68 | 673 |
| [frontend/src/admin/services/products.js](/frontend/src/admin/services/products.js) | JavaScript | 262 | 20 | 41 | 323 |
| [frontend/src/admin/services/ratings.js](/frontend/src/admin/services/ratings.js) | JavaScript | 57 | 0 | 7 | 64 |
| [frontend/src/admin/services/reports.js](/frontend/src/admin/services/reports.js) | JavaScript | 210 | 6 | 7 | 223 |
| [frontend/src/admin/services/reviews.js](/frontend/src/admin/services/reviews.js) | JavaScript | 57 | 0 | 7 | 64 |
| [frontend/src/admin/services/roles.js](/frontend/src/admin/services/roles.js) | JavaScript | 121 | 65 | 27 | 213 |
| [frontend/src/admin/services/security.js](/frontend/src/admin/services/security.js) | JavaScript | 123 | 0 | 14 | 137 |
| [frontend/src/admin/services/seller-requests.js](/frontend/src/admin/services/seller-requests.js) | JavaScript | 159 | 9 | 35 | 203 |
| [frontend/src/admin/services/sellerRequests.js](/frontend/src/admin/services/sellerRequests.js) | JavaScript | 168 | 7 | 11 | 186 |
| [frontend/src/admin/services/settings.js](/frontend/src/admin/services/settings.js) | JavaScript | 259 | 10 | 11 | 280 |
| [frontend/src/admin/services/toast.service.js](/frontend/src/admin/services/toast.service.js) | JavaScript | 27 | 5 | 8 | 40 |
| [frontend/src/admin/services/users.js](/frontend/src/admin/services/users.js) | JavaScript | 337 | 26 | 63 | 426 |
| [frontend/src/admin/views/Categories.vue](/frontend/src/admin/views/Categories.vue) | vue | 530 | 10 | 53 | 593 |
| [frontend/src/admin/views/Dashboard.vue](/frontend/src/admin/views/Dashboard.vue) | vue | 410 | 6 | 60 | 476 |
| [frontend/src/admin/views/Orders.vue](/frontend/src/admin/views/Orders.vue) | vue | 279 | 6 | 28 | 313 |
| [frontend/src/admin/views/Products.vue](/frontend/src/admin/views/Products.vue) | vue | 378 | 7 | 50 | 435 |
| [frontend/src/admin/views/Reports.vue](/frontend/src/admin/views/Reports.vue) | vue | 445 | 8 | 47 | 500 |
| [frontend/src/admin/views/Security.vue](/frontend/src/admin/views/Security.vue) | vue | 321 | 4 | 33 | 358 |
| [frontend/src/admin/views/SellerRequests.vue](/frontend/src/admin/views/SellerRequests.vue) | vue | 136 | 4 | 19 | 159 |
| [frontend/src/admin/views/Settings.vue](/frontend/src/admin/views/Settings.vue) | vue | 376 | 7 | 49 | 432 |
| [frontend/src/admin/views/Users.vue](/frontend/src/admin/views/Users.vue) | vue | 192 | 5 | 28 | 225 |
| [frontend/src/admin/views/addresses/AddressList.vue](/frontend/src/admin/views/addresses/AddressList.vue) | vue | 443 | 6 | 60 | 509 |
| [frontend/src/admin/views/categories/CategoryDetail.vue](/frontend/src/admin/views/categories/CategoryDetail.vue) | vue | 535 | 6 | 65 | 606 |
| [frontend/src/admin/views/categories/CategoryForm.vue](/frontend/src/admin/views/categories/CategoryForm.vue) | vue | 454 | 6 | 64 | 524 |
| [frontend/src/admin/views/categories/CategoryList.vue](/frontend/src/admin/views/categories/CategoryList.vue) | vue | 402 | 7 | 67 | 476 |
| [frontend/src/admin/views/chats/ChatDetail.vue](/frontend/src/admin/views/chats/ChatDetail.vue) | vue | 163 | 5 | 20 | 188 |
| [frontend/src/admin/views/chats/ChatList.vue](/frontend/src/admin/views/chats/ChatList.vue) | vue | 423 | 5 | 61 | 489 |
| [frontend/src/admin/views/companies/CompanyDetail.vue](/frontend/src/admin/views/companies/CompanyDetail.vue) | vue | 358 | 10 | 33 | 401 |
| [frontend/src/admin/views/companies/CompanyEdit.vue](/frontend/src/admin/views/companies/CompanyEdit.vue) | vue | 602 | 9 | 55 | 666 |
| [frontend/src/admin/views/companies/CompanyList.vue](/frontend/src/admin/views/companies/CompanyList.vue) | vue | 293 | 7 | 32 | 332 |
| [frontend/src/admin/views/orders/OrderDetail.vue](/frontend/src/admin/views/orders/OrderDetail.vue) | vue | 724 | 6 | 90 | 820 |
| [frontend/src/admin/views/orders/OrderEdit.vue](/frontend/src/admin/views/orders/OrderEdit.vue) | vue | 530 | 13 | 52 | 595 |
| [frontend/src/admin/views/orders/OrderList.vue](/frontend/src/admin/views/orders/OrderList.vue) | vue | 498 | 4 | 60 | 562 |
| [frontend/src/admin/views/orders/OrderView.vue](/frontend/src/admin/views/orders/OrderView.vue) | vue | 287 | 9 | 38 | 334 |
| [frontend/src/admin/views/products/ProductDetail.vue](/frontend/src/admin/views/products/ProductDetail.vue) | vue | 463 | 5 | 52 | 520 |
| [frontend/src/admin/views/products/ProductForm.vue](/frontend/src/admin/views/products/ProductForm.vue) | vue | 661 | 7 | 70 | 738 |
| [frontend/src/admin/views/products/ProductList.vue](/frontend/src/admin/views/products/ProductList.vue) | vue | 444 | 4 | 44 | 492 |
| [frontend/src/admin/views/ratings/RatingList.vue](/frontend/src/admin/views/ratings/RatingList.vue) | vue | 381 | 5 | 40 | 426 |
| [frontend/src/admin/views/reviews/ReviewDetail.vue](/frontend/src/admin/views/reviews/ReviewDetail.vue) | vue | 259 | 8 | 23 | 290 |
| [frontend/src/admin/views/reviews/ReviewList.vue](/frontend/src/admin/views/reviews/ReviewList.vue) | vue | 359 | 5 | 43 | 407 |
| [frontend/src/admin/views/seller-requests/SellerRequestDetail.vue](/frontend/src/admin/views/seller-requests/SellerRequestDetail.vue) | vue | 637 | 8 | 86 | 731 |
| [frontend/src/admin/views/seller-requests/SellerRequestList.vue](/frontend/src/admin/views/seller-requests/SellerRequestList.vue) | vue | 501 | 5 | 73 | 579 |
| [frontend/src/admin/views/test/ApiTest.vue](/frontend/src/admin/views/test/ApiTest.vue) | vue | 72 | 0 | 13 | 85 |
| [frontend/src/admin/views/users/UserDetail.vue](/frontend/src/admin/views/users/UserDetail.vue) | vue | 1,080 | 10 | 121 | 1,211 |
| [frontend/src/admin/views/users/UserList.vue](/frontend/src/admin/views/users/UserList.vue) | vue | 753 | 10 | 147 | 910 |
| [frontend/src/assets/css/admin-components.css](/frontend/src/assets/css/admin-components.css) | PostCSS | 540 | 58 | 87 | 685 |
| [frontend/src/assets/css/admin-form-fixes.css](/frontend/src/assets/css/admin-form-fixes.css) | PostCSS | 125 | 20 | 18 | 163 |
| [frontend/src/assets/css/admin-utilities.css](/frontend/src/assets/css/admin-utilities.css) | PostCSS | 207 | 23 | 37 | 267 |
| [frontend/src/assets/css/admin.css](/frontend/src/assets/css/admin.css) | PostCSS | 185 | 12 | 38 | 235 |
| [frontend/src/assets/css/main-menu.css](/frontend/src/assets/css/main-menu.css) | PostCSS | 39 | 6 | 17 | 62 |
| [frontend/src/assets/css/style.css](/frontend/src/assets/css/style.css) | PostCSS | 46 | 1 | 10 | 57 |
| [frontend/src/assets/css/toast-fixes.css](/frontend/src/assets/css/toast-fixes.css) | PostCSS | 123 | 17 | 24 | 164 |
| [frontend/src/assets/css/toast-notification-fixes.css](/frontend/src/assets/css/toast-notification-fixes.css) | PostCSS | 144 | 18 | 26 | 188 |
| [frontend/src/assets/css/toast.css](/frontend/src/assets/css/toast.css) | PostCSS | 56 | 2 | 11 | 69 |
| [frontend/src/assets/images/icons/apple-icon.svg](/frontend/src/assets/images/icons/apple-icon.svg) | XML | 3 | 0 | 1 | 4 |
| [frontend/src/assets/images/icons/catalog-icon.svg](/frontend/src/assets/images/icons/catalog-icon.svg) | XML | 6 | 0 | 1 | 7 |
| [frontend/src/assets/images/icons/google-icon.svg](/frontend/src/assets/images/icons/google-icon.svg) | XML | 6 | 0 | 1 | 7 |
| [frontend/src/assets/images/icons/placeholder-icon.svg](/frontend/src/assets/images/icons/placeholder-icon.svg) | XML | 2 | 0 | 0 | 2 |
| [frontend/src/assets/images/icons/telegram.svg](/frontend/src/assets/images/icons/telegram.svg) | XML | 3 | 0 | 0 | 3 |
| [frontend/src/assets/images/icons/viber.svg](/frontend/src/assets/images/icons/viber.svg) | XML | 2 | 0 | 0 | 2 |
| [frontend/src/assets/images/icons/youtube.svg](/frontend/src/assets/images/icons/youtube.svg) | XML | 3 | 0 | 0 | 3 |
| [frontend/src/assets/images/logo.svg](/frontend/src/assets/images/logo.svg) | XML | 24 | 0 | 1 | 25 |
| [frontend/src/components/ErrorBoundary.vue](/frontend/src/components/ErrorBoundary.vue) | vue | 173 | 0 | 33 | 206 |
| [frontend/src/components/GlobalLoading.vue](/frontend/src/components/GlobalLoading.vue) | vue | 72 | 0 | 10 | 82 |
| [frontend/src/components/admin/AddressForm.vue](/frontend/src/components/admin/AddressForm.vue) | vue | 180 | 0 | 22 | 202 |
| [frontend/src/components/admin/Pagination.vue](/frontend/src/components/admin/Pagination.vue) | vue | 174 | 9 | 32 | 215 |
| [frontend/src/components/admin/SearchAndFilters.vue](/frontend/src/components/admin/SearchAndFilters.vue) | vue | 208 | 8 | 25 | 241 |
| [frontend/src/components/admin/common/StatusBadge.vue](/frontend/src/components/admin/common/StatusBadge.vue) | vue | 81 | 0 | 9 | 90 |
| [frontend/src/components/catalog/CatalogProducts.vue](/frontend/src/components/catalog/CatalogProducts.vue) | vue | 250 | 0 | 31 | 281 |
| [frontend/src/components/catalog/CategoryReviews.vue](/frontend/src/components/catalog/CategoryReviews.vue) | vue | 89 | 0 | 10 | 99 |
| [frontend/src/components/catalog/FiltersSidebar.vue](/frontend/src/components/catalog/FiltersSidebar.vue) | vue | 200 | 0 | 32 | 232 |
| [frontend/src/components/catalog/Pagination.vue](/frontend/src/components/catalog/Pagination.vue) | vue | 163 | 2 | 16 | 181 |
| [frontend/src/components/common/CategoryMenu.vue](/frontend/src/components/common/CategoryMenu.vue) | vue | 297 | 2 | 51 | 350 |
| [frontend/src/components/common/CategoryMenuButton.vue](/frontend/src/components/common/CategoryMenuButton.vue) | vue | 63 | 0 | 12 | 75 |
| [frontend/src/components/common/Toast.vue](/frontend/src/components/common/Toast.vue) | vue | 134 | 0 | 22 | 156 |
| [frontend/src/components/home/<USER>/frontend/src/components/home/<USER>
| [frontend/src/components/home/<USER>/frontend/src/components/home/<USER>
| [frontend/src/components/home/<USER>/frontend/src/components/home/<USER>
| [frontend/src/components/home/<USER>/frontend/src/components/home/<USER>
| [frontend/src/composables/useAdminSearch.js](/frontend/src/composables/useAdminSearch.js) | JavaScript | 324 | 53 | 41 | 418 |
| [frontend/src/composables/useToast.js](/frontend/src/composables/useToast.js) | JavaScript | 36 | 9 | 11 | 56 |
| [frontend/src/config/google-auth.js](/frontend/src/config/google-auth.js) | JavaScript | 112 | 23 | 17 | 152 |
| [frontend/src/layouts/AdminLayout.vue](/frontend/src/layouts/AdminLayout.vue) | vue | 539 | 3 | 69 | 611 |
| [frontend/src/main.js](/frontend/src/main.js) | JavaScript | 14 | 1 | 6 | 21 |
| [frontend/src/mock/categories.js](/frontend/src/mock/categories.js) | JavaScript | 220 | 6 | 7 | 233 |
| [frontend/src/router/index.js](/frontend/src/router/index.js) | JavaScript | 355 | 37 | 40 | 432 |
| [frontend/src/services/api.js](/frontend/src/services/api.js) | JavaScript | 205 | 47 | 50 | 302 |
| [frontend/src/services/api.service.js](/frontend/src/services/api.service.js) | JavaScript | 49 | 11 | 12 | 72 |
| [frontend/src/services/auth.js](/frontend/src/services/auth.js) | JavaScript | 96 | 45 | 17 | 158 |
| [frontend/src/services/auth.service.js](/frontend/src/services/auth.service.js) | JavaScript | 118 | 26 | 34 | 178 |
| [frontend/src/services/cart.service.js](/frontend/src/services/cart.service.js) | JavaScript | 65 | 2 | 7 | 74 |
| [frontend/src/services/category.service.js](/frontend/src/services/category.service.js) | JavaScript | 149 | 15 | 31 | 195 |
| [frontend/src/services/dashboard.service.js](/frontend/src/services/dashboard.service.js) | JavaScript | 94 | 10 | 7 | 111 |
| [frontend/src/services/order.service.js](/frontend/src/services/order.service.js) | JavaScript | 49 | 8 | 17 | 74 |
| [frontend/src/services/product.service.js](/frontend/src/services/product.service.js) | JavaScript | 35 | 8 | 11 | 54 |
| [frontend/src/services/request-manager.js](/frontend/src/services/request-manager.js) | JavaScript | 47 | 28 | 13 | 88 |
| [frontend/src/services/review.service.js](/frontend/src/services/review.service.js) | JavaScript | 7 | 1 | 3 | 11 |
| [frontend/src/services/seller-request.service.js](/frontend/src/services/seller-request.service.js) | JavaScript | 60 | 17 | 11 | 88 |
| [frontend/src/services/user.service.js](/frontend/src/services/user.service.js) | JavaScript | 25 | 7 | 9 | 41 |
| [frontend/src/services/wishlist.service.js](/frontend/src/services/wishlist.service.js) | JavaScript | 35 | 2 | 4 | 41 |
| [frontend/src/store/index.js](/frontend/src/store/index.js) | JavaScript | 11 | 0 | 2 | 13 |
| [frontend/src/store/modules/auth.js](/frontend/src/store/modules/auth.js) | JavaScript | 163 | 25 | 35 | 223 |
| [frontend/src/store/modules/categories.js](/frontend/src/store/modules/categories.js) | JavaScript | 181 | 7 | 39 | 227 |
| [frontend/src/store/modules/loading.js](/frontend/src/store/modules/loading.js) | JavaScript | 70 | 1 | 17 | 88 |
| [frontend/src/utils/slugify.js](/frontend/src/utils/slugify.js) | JavaScript | 18 | 12 | 4 | 34 |
| [frontend/src/utils/toast.js](/frontend/src/utils/toast.js) | JavaScript | 84 | 19 | 12 | 115 |
| [frontend/src/views/AdminDashboardPage.vue](/frontend/src/views/AdminDashboardPage.vue) | vue | 150 | 0 | 9 | 159 |
| [frontend/src/views/CategoryMenuTest.vue](/frontend/src/views/CategoryMenuTest.vue) | vue | 36 | 0 | 8 | 44 |
| [frontend/src/views/Dashboard.vue](/frontend/src/views/Dashboard.vue) | vue | 839 | 8 | 101 | 948 |
| [frontend/src/views/NotFoundPage.vue](/frontend/src/views/NotFoundPage.vue) | vue | 19 | 0 | 3 | 22 |
| [frontend/src/views/UserDashboardPage.vue](/frontend/src/views/UserDashboardPage.vue) | vue | 71 | 0 | 10 | 81 |
| [frontend/src/views/admin/Orders.vue](/frontend/src/views/admin/Orders.vue) | vue | 567 | 5 | 50 | 622 |
| [frontend/src/views/admin/Products.vue](/frontend/src/views/admin/Products.vue) | vue | 434 | 5 | 53 | 492 |
| [frontend/src/views/admin/SellerRequests.vue](/frontend/src/views/admin/SellerRequests.vue) | vue | 469 | 5 | 49 | 523 |
| [frontend/src/views/admin/Users.vue](/frontend/src/views/admin/Users.vue) | vue | 412 | 5 | 45 | 462 |
| [frontend/src/views/auth/LoginPage.vue](/frontend/src/views/auth/LoginPage.vue) | vue | 651 | 2 | 113 | 766 |
| [frontend/src/views/auth/RegisterPage.vue](/frontend/src/views/auth/RegisterPage.vue) | vue | 704 | 2 | 118 | 824 |
| [frontend/src/views/cart/CartPage.vue](/frontend/src/views/cart/CartPage.vue) | vue | 763 | 0 | 102 | 865 |
| [frontend/src/views/catalog/CatalogPage.vue](/frontend/src/views/catalog/CatalogPage.vue) | vue | 326 | 7 | 43 | 376 |
| [frontend/src/views/home/<USER>/frontend/src/views/home/<USER>
| [frontend/src/views/profile/UserProfile.vue](/frontend/src/views/profile/UserProfile.vue) | vue | 839 | 16 | 82 | 937 |
| [frontend/test-api.html](/frontend/test-api.html) | HTML | 88 | 0 | 14 | 102 |
| [frontend/test-categories-count.html](/frontend/test-categories-count.html) | HTML | 148 | 0 | 32 | 180 |
| [frontend/test-companies-service.html](/frontend/test-companies-service.html) | HTML | 116 | 0 | 28 | 144 |
| [frontend/test-company-select-component.html](/frontend/test-company-select-component.html) | HTML | 131 | 0 | 21 | 152 |
| [frontend/test-company-select-direct.html](/frontend/test-company-select-direct.html) | HTML | 262 | 0 | 31 | 293 |
| [frontend/test-create-product.html](/frontend/test-create-product.html) | HTML | 211 | 0 | 41 | 252 |
| [frontend/test-dropdown-behavior.html](/frontend/test-dropdown-behavior.html) | HTML | 194 | 0 | 24 | 218 |
| [frontend/test-fixes-verification.html](/frontend/test-fixes-verification.html) | HTML | 203 | 0 | 37 | 240 |
| [frontend/vite.config.js](/frontend/vite.config.js) | JavaScript | 24 | 0 | 2 | 26 |
| [package-lock.json](/package-lock.json) | JSON | 297 | 0 | 1 | 298 |
| [package.json](/package.json) | JSON | 6 | 0 | 1 | 7 |
| [start-backend.bat](/start-backend.bat) | Batch | 15 | 0 | 4 | 19 |
| [start-backend.ps1](/start-backend.ps1) | PowerShell | 38 | 5 | 8 | 51 |
| [start-dev.bat](/start-dev.bat) | Batch | 9 | 0 | 4 | 13 |
| [temp\_disabled/ModeratorBulkApproveSellerRequestsCommandHandler.cs](/temp_disabled/ModeratorBulkApproveSellerRequestsCommandHandler.cs) | C# | 47 | 6 | 10 | 63 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)