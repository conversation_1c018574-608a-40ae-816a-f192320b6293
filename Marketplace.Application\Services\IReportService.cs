using Marketplace.Application.Queries.Reports;

namespace Marketplace.Application.Services
{
    /// <summary>
    /// Interface for unified report service operations
    /// Provides abstraction for all report-related functionality
    /// </summary>
    public interface IReportService
    {
        /// <summary>
        /// Gets financial report data
        /// </summary>
        Task<ReportResult> GetFinancialReportAsync(GetFinancialReportQuery query, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets sales report data
        /// </summary>
        Task<ReportResult> GetSalesReportAsync(GetSalesReportQuery query, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets products report data
        /// </summary>
        Task<ReportResult> GetProductsReportAsync(GetProductsReportQuery query, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets users report data
        /// </summary>
        Task<ReportResult> GetUsersReportAsync(GetUsersReportQuery query, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets orders report data
        /// </summary>
        Task<ReportResult> GetOrdersReportAsync(GetOrdersReportQuery query, CancellationToken cancellationToken = default);

        /// <summary>
        /// Exports report data in the specified format
        /// </summary>
        Task<byte[]> ExportReportAsync(ReportResult reportData, string reportType, string format);

        /// <summary>
        /// Validates common report parameters
        /// </summary>
        bool ValidateReportParameters(DateTime? startDate, DateTime? endDate, out string? errorMessage);

        /// <summary>
        /// Gets default date range for reports (last 30 days)
        /// </summary>
        (DateTime startDate, DateTime endDate) GetDefaultDateRange();

        /// <summary>
        /// Normalizes report type string
        /// </summary>
        string NormalizeReportType(string reportType);
    }
}
