using MediatR;
using Microsoft.AspNetCore.Mvc;
using Marketplace.Application.Services;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;

namespace Marketplace.Presentation.Controllers.Admin
{
    /// <summary>
    /// Base controller for all report-related operations
    /// Provides common functionality to avoid code duplication
    /// </summary>
    [ApiController]
    [Route("api/admin/reports")]
    public abstract class BaseReportController : ControllerBase
    {
        protected readonly IMediator _mediator;
        protected readonly IExportService _exportService;
        protected readonly ILogger _logger;

        protected BaseReportController(
            IMediator mediator,
            IExportService exportService,
            ILogger logger)
        {
            _mediator = mediator;
            _exportService = exportService;
            _logger = logger;
        }

        /// <summary>
        /// Generic method to handle report requests
        /// </summary>
        /// <typeparam name="TQuery">Query type</typeparam>
        /// <typeparam name="TResult">Result type</typeparam>
        /// <param name="query">Query object</param>
        /// <param name="reportType">Report type for logging</param>
        /// <returns>Report result</returns>
        protected async Task<IActionResult> HandleReportRequest<TQuery, TResult>(
            TQuery query, 
            string reportType) 
            where TQuery : IRequest<TResult>
        {
            try
            {
                _logger.LogInformation("Processing {ReportType} report request", reportType);
                
                var result = await _mediator.Send(query);
                
                _logger.LogInformation("{ReportType} report processed successfully", reportType);
                return Ok(result);
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning("Validation error in {ReportType} report: {Message}", reportType, ex.Message);
                return BadRequest(new { message = ex.Message, errors = ex.Data });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Invalid argument in {ReportType} report: {Message}", reportType, ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning("Unauthorized access to {ReportType} report: {Message}", reportType, ex.Message);
                return Forbid();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing {ReportType} report", reportType);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Generic method to handle export requests
        /// </summary>
        /// <typeparam name="TQuery">Query type</typeparam>
        /// <typeparam name="TResult">Result type</typeparam>
        /// <param name="query">Query object</param>
        /// <param name="format">Export format (csv, pdf, excel)</param>
        /// <param name="reportType">Report type for logging</param>
        /// <param name="filename">Optional custom filename</param>
        /// <returns>File result</returns>
        protected async Task<IActionResult> HandleExportRequest<TQuery, TResult>(
            TQuery query,
            string format,
            string reportType,
            string filename = null)
            where TQuery : IRequest<TResult>
        {
            try
            {
                _logger.LogInformation("Processing {ReportType} export request in {Format} format", reportType, format);

                // Validate export format
                if (!IsValidExportFormat(format))
                {
                    return BadRequest(new { message = $"Unsupported export format: {format}" });
                }

                // Get report data
                var reportData = await _mediator.Send(query);

                // Generate export file
                var fileResult = await GenerateExportFile(reportData, format, reportType, filename);

                _logger.LogInformation("{ReportType} export in {Format} format completed successfully", reportType, format);
                return fileResult;
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning("Validation error in {ReportType} export: {Message}", reportType, ex.Message);
                return BadRequest(new { message = ex.Message, errors = ex.Data });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Invalid argument in {ReportType} export: {Message}", reportType, ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting {ReportType} report in {Format} format", reportType, format);
                return StatusCode(500, new { message = "Export failed" });
            }
        }

        /// <summary>
        /// Validate date range parameters
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Validation result</returns>
        protected IActionResult ValidateDateRange(DateTime? startDate, DateTime? endDate)
        {
            if (startDate.HasValue && endDate.HasValue)
            {
                if (startDate.Value > endDate.Value)
                {
                    return BadRequest(new { message = "Start date must be before end date" });
                }

                if (startDate.Value > DateTime.Now)
                {
                    return BadRequest(new { message = "Start date cannot be in the future" });
                }

                if (endDate.Value > DateTime.Now)
                {
                    return BadRequest(new { message = "End date cannot be in the future" });
                }

                // Check if date range is too large (more than 1 year)
                var daysDifference = (endDate.Value - startDate.Value).TotalDays;
                if (daysDifference > 365)
                {
                    return BadRequest(new { message = "Date range cannot exceed 1 year" });
                }
            }

            return null; // Validation passed
        }

        /// <summary>
        /// Check if export format is valid
        /// </summary>
        /// <param name="format">Export format</param>
        /// <returns>True if valid</returns>
        private bool IsValidExportFormat(string format)
        {
            var validFormats = new[] { "csv", "pdf", "excel", "xlsx" };
            return validFormats.Contains(format.ToLowerInvariant());
        }

        /// <summary>
        /// Generate export file based on format
        /// </summary>
        /// <typeparam name="TResult">Result type</typeparam>
        /// <param name="data">Report data</param>
        /// <param name="format">Export format</param>
        /// <param name="reportType">Report type</param>
        /// <param name="customFilename">Custom filename</param>
        /// <returns>File result</returns>
        private async Task<IActionResult> GenerateExportFile<TResult>(
            TResult data,
            string format,
            string reportType,
            string customFilename = null)
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var filename = customFilename ?? $"{reportType}_report_{timestamp}";

            switch (format.ToLowerInvariant())
            {
                case "csv":
                    var csvData = await _exportService.ExportToCsvAsync(data);
                    return File(csvData, "text/csv", $"{filename}.csv");

                case "pdf":
                    var pdfData = await _exportService.ExportToPdfAsync(data, reportType);
                    return File(pdfData, "application/pdf", $"{filename}.pdf");

                case "excel":
                case "xlsx":
                    var excelData = await _exportService.ExportToExcelAsync(data);
                    return File(excelData, 
                        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                        $"{filename}.xlsx");

                default:
                    throw new ArgumentException($"Unsupported export format: {format}");
            }
        }

        /// <summary>
        /// Health check endpoint for reports system
        /// </summary>
        /// <returns>Health status</returns>
        [HttpGet("health")]
        public IActionResult HealthCheck()
        {
            try
            {
                return Ok(new
                {
                    status = "healthy",
                    timestamp = DateTime.UtcNow,
                    service = "Reports API",
                    version = "1.0.0"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed");
                return StatusCode(500, new
                {
                    status = "unhealthy",
                    timestamp = DateTime.UtcNow,
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Get available export formats
        /// </summary>
        /// <returns>List of supported export formats</returns>
        [HttpGet("export-formats")]
        public IActionResult GetExportFormats()
        {
            var formats = new[]
            {
                new { type = "csv", label = "CSV", description = "Comma-separated values", mimeType = "text/csv" },
                new { type = "pdf", label = "PDF", description = "Portable Document Format", mimeType = "application/pdf" },
                new { type = "excel", label = "Excel", description = "Microsoft Excel", mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" }
            };

            return Ok(formats);
        }

        /// <summary>
        /// Validate common query parameters
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="limit">Result limit</param>
        /// <param name="offset">Result offset</param>
        /// <returns>Validation result</returns>
        protected IActionResult ValidateCommonParameters(
            DateTime? startDate = null,
            DateTime? endDate = null,
            int? limit = null,
            int? offset = null)
        {
            // Validate date range
            var dateValidation = ValidateDateRange(startDate, endDate);
            if (dateValidation != null)
            {
                return dateValidation;
            }

            // Validate pagination parameters
            if (limit.HasValue && limit.Value <= 0)
            {
                return BadRequest(new { message = "Limit must be greater than 0" });
            }

            if (limit.HasValue && limit.Value > 1000)
            {
                return BadRequest(new { message = "Limit cannot exceed 1000" });
            }

            if (offset.HasValue && offset.Value < 0)
            {
                return BadRequest(new { message = "Offset cannot be negative" });
            }

            return null; // Validation passed
        }

        /// <summary>
        /// Create standardized error response
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="details">Additional error details</param>
        /// <returns>Error response</returns>
        protected IActionResult CreateErrorResponse(string message, object details = null)
        {
            var response = new
            {
                message,
                timestamp = DateTime.UtcNow,
                details
            };

            return BadRequest(response);
        }
    }
}
