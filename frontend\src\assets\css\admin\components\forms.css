/* ===== ADMIN FORMS SYSTEM ===== */
/* Based on Reports page form patterns */

/* ===== BASE FORM STYLES ===== */
.admin-form {
  width: 100%;
}

.admin-form-group {
  margin-bottom: var(--admin-space-lg);
}

.admin-form-group:last-child {
  margin-bottom: 0;
}

/* ===== LABELS ===== */
.admin-form-label {
  display: block;
  font-weight: var(--admin-font-medium);
  color: var(--admin-gray-700);
  margin-bottom: var(--admin-space-sm);
  font-size: var(--admin-text-sm);
}

.admin-form-label.required::after {
  content: ' *';
  color: var(--admin-danger);
}

/* ===== INPUT FIELDS ===== */
.admin-form-input,
.admin-form-select,
.admin-form-textarea {
  width: 100%;
  padding: var(--admin-input-padding);
  border: 1px solid var(--admin-input-border);
  border-radius: var(--admin-input-radius);
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-700);
  background: var(--admin-white);
  transition: all var(--admin-transition-normal);
}

.admin-form-input:focus,
.admin-form-select:focus,
.admin-form-textarea:focus {
  outline: none;
  border-color: var(--admin-input-border-focus);
  box-shadow: var(--admin-input-shadow-focus);
}

.admin-form-input:disabled,
.admin-form-select:disabled,
.admin-form-textarea:disabled {
  background: var(--admin-gray-100);
  color: var(--admin-gray-500);
  cursor: not-allowed;
}

.admin-form-input.error,
.admin-form-select.error,
.admin-form-textarea.error {
  border-color: var(--admin-danger);
}

.admin-form-input.success,
.admin-form-select.success,
.admin-form-textarea.success {
  border-color: var(--admin-success);
}

/* ===== INPUT SIZES ===== */
.admin-form-input-sm,
.admin-form-select-sm {
  padding: var(--admin-space-sm) var(--admin-space-md);
  font-size: var(--admin-text-xs);
}

.admin-form-input-lg,
.admin-form-select-lg {
  padding: var(--admin-space-lg) var(--admin-space-xl);
  font-size: var(--admin-text-base);
}

/* ===== TEXTAREA ===== */
.admin-form-textarea {
  resize: vertical;
  min-height: 100px;
  line-height: var(--admin-leading-normal);
}

.admin-form-textarea-sm {
  min-height: 80px;
}

.admin-form-textarea-lg {
  min-height: 150px;
}

/* ===== SELECT DROPDOWN ===== */
.admin-form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--admin-space-md) center;
  background-repeat: no-repeat;
  background-size: 16px 16px;
  padding-right: var(--admin-space-4xl);
  appearance: none;
  color: var(--admin-gray-900);
  background-color: var(--admin-white);
}

/* ===== SELECT OPTIONS STYLING ===== */
.admin-form-select option {
  color: var(--admin-gray-900);
  background-color: var(--admin-white);
  padding: var(--admin-space-sm);
}

.admin-form-select option:hover,
.admin-form-select option:focus {
  background-color: var(--admin-primary-bg);
  color: var(--admin-primary-dark);
}

.admin-form-select option:checked {
  background-color: var(--admin-primary);
  color: white;
}

/* ===== BULMA SELECT OVERRIDE ===== */
.select select {
  color: var(--admin-gray-900) !important;
  background-color: var(--admin-white) !important;
  border: 1px solid var(--admin-border-color) !important;
  border-radius: var(--admin-radius-md) !important;
  padding: var(--admin-space-md) !important;
  font-size: var(--admin-text-sm) !important;
}

.select select option {
  color: var(--admin-gray-900) !important;
  background-color: var(--admin-white) !important;
  padding: var(--admin-space-sm) !important;
}

.select select:focus {
  border-color: var(--admin-primary) !important;
  box-shadow: 0 0 0 3px var(--admin-primary-bg) !important;
  outline: none !important;
}

/* ===== DROPDOWN MENU STYLING ===== */
.dropdown-menu {
  background-color: var(--admin-white) !important;
  border: 1px solid var(--admin-border-color) !important;
  border-radius: var(--admin-radius-lg) !important;
  box-shadow: var(--admin-shadow-lg) !important;
  padding: var(--admin-space-sm) !important;
}

.dropdown-item {
  color: var(--admin-gray-900) !important;
  background-color: transparent !important;
  padding: var(--admin-space-sm) var(--admin-space-md) !important;
  border-radius: var(--admin-radius-md) !important;
  transition: all var(--admin-transition-fast) !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
  background-color: var(--admin-primary-bg) !important;
  color: var(--admin-primary-dark) !important;
}

.dropdown-item.is-active {
  background-color: var(--admin-primary) !important;
  color: white !important;
}

/* ===== CHECKBOX AND RADIO ===== */
.admin-form-checkbox,
.admin-form-radio {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  margin-bottom: var(--admin-space-sm);
}

.admin-form-checkbox input[type="checkbox"],
.admin-form-radio input[type="radio"] {
  width: 16px;
  height: 16px;
  border: 1px solid var(--admin-gray-300);
  border-radius: var(--admin-radius-sm);
  background: var(--admin-white);
  cursor: pointer;
}

.admin-form-radio input[type="radio"] {
  border-radius: 50%;
}

.admin-form-checkbox input[type="checkbox"]:checked,
.admin-form-radio input[type="radio"]:checked {
  background: var(--admin-primary);
  border-color: var(--admin-primary);
}

.admin-form-checkbox label,
.admin-form-radio label {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-700);
  cursor: pointer;
  margin: 0;
}

/* ===== INPUT GROUPS ===== */
.admin-input-group {
  display: flex;
  align-items: stretch;
}

.admin-input-group .admin-form-input {
  border-radius: 0;
  border-right: none;
}

.admin-input-group .admin-form-input:first-child {
  border-top-left-radius: var(--admin-input-radius);
  border-bottom-left-radius: var(--admin-input-radius);
}

.admin-input-group .admin-form-input:last-child {
  border-top-right-radius: var(--admin-input-radius);
  border-bottom-right-radius: var(--admin-input-radius);
  border-right: 1px solid var(--admin-input-border);
}

.admin-input-group-addon {
  display: flex;
  align-items: center;
  padding: var(--admin-input-padding);
  background: var(--admin-gray-100);
  border: 1px solid var(--admin-input-border);
  color: var(--admin-gray-600);
  font-size: var(--admin-text-sm);
  white-space: nowrap;
}

.admin-input-group-addon:first-child {
  border-top-left-radius: var(--admin-input-radius);
  border-bottom-left-radius: var(--admin-input-radius);
  border-right: none;
}

.admin-input-group-addon:last-child {
  border-top-right-radius: var(--admin-input-radius);
  border-bottom-right-radius: var(--admin-input-radius);
  border-left: none;
}

/* ===== SEARCH INPUT ===== */
.admin-search-input {
  position: relative;
}

.admin-search-input .admin-form-input {
  padding-left: var(--admin-space-4xl);
}

.admin-search-input::before {
  content: '\f002'; /* FontAwesome search icon */
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  left: var(--admin-space-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--admin-gray-400);
  z-index: 1;
}

/* ===== DATE INPUTS ===== */
.admin-date-range {
  display: flex;
  align-items: center;
  gap: var(--admin-space-md);
}

.admin-date-input {
  flex: 1;
}

.admin-date-separator {
  color: var(--admin-gray-500);
  font-weight: var(--admin-font-medium);
}

/* ===== FORM VALIDATION ===== */
.admin-form-error {
  color: var(--admin-danger);
  font-size: var(--admin-text-xs);
  margin-top: var(--admin-space-xs);
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-form-success {
  color: var(--admin-success);
  font-size: var(--admin-text-xs);
  margin-top: var(--admin-space-xs);
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-form-help {
  color: var(--admin-gray-500);
  font-size: var(--admin-text-xs);
  margin-top: var(--admin-space-xs);
}

/* ===== FORM LAYOUTS ===== */
.admin-form-row {
  display: flex;
  gap: var(--admin-space-lg);
  margin-bottom: var(--admin-space-lg);
}

.admin-form-col {
  flex: 1;
}

.admin-form-col-auto {
  flex: none;
}

.admin-form-col-2 {
  flex: 0 0 50%;
}

.admin-form-col-3 {
  flex: 0 0 33.333333%;
}

.admin-form-col-4 {
  flex: 0 0 25%;
}

/* ===== FILTER FORMS ===== */
.admin-filters-form {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  margin-bottom: var(--admin-space-2xl);
}

.admin-filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-space-lg);
  padding-bottom: var(--admin-space-lg);
  border-bottom: 1px solid var(--admin-border-color);
}

.admin-filters-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
}

.admin-filters-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--admin-space-lg);
}

.admin-filters-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--admin-space-lg);
  border-top: 1px solid var(--admin-border-color);
  margin-top: var(--admin-space-lg);
}

/* ===== PRESET BUTTONS ===== */
.admin-preset-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admin-space-sm);
  margin-bottom: var(--admin-space-lg);
}

.admin-preset-btn {
  background: var(--admin-white);
  border: 1px solid var(--admin-border-color);
  padding: var(--admin-space-sm) var(--admin-space-md);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-xs);
  cursor: pointer;
  transition: all var(--admin-transition-normal);
}

.admin-preset-btn:hover {
  border-color: var(--admin-primary);
  background: var(--admin-primary-bg);
}

.admin-preset-btn.active {
  background: var(--admin-primary);
  color: white;
  border-color: var(--admin-primary);
}

/* ===== RESPONSIVE FORMS ===== */
@media (max-width: 768px) {
  .admin-form-row {
    flex-direction: column;
    gap: var(--admin-space-md);
  }
  
  .admin-filters-content {
    grid-template-columns: 1fr;
    gap: var(--admin-space-md);
  }
  
  .admin-filters-actions {
    flex-direction: column;
    gap: var(--admin-space-md);
    align-items: stretch;
  }
  
  .admin-date-range {
    flex-direction: column;
    align-items: stretch;
  }
  
  .admin-input-group {
    flex-direction: column;
  }
  
  .admin-input-group .admin-form-input,
  .admin-input-group-addon {
    border-radius: var(--admin-input-radius);
    border: 1px solid var(--admin-input-border);
  }
}
