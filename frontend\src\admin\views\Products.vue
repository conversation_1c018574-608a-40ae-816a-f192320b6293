<template>
  <div class="admin-products">
    <div class="admin-page-header">
      <div class="admin-header-content">
        <div class="admin-header-left">
          <h1 class="admin-page-title">
            <i class="fas fa-box"></i>
            Products
          </h1>
        </div>
        <div class="admin-header-right">
          <button class="admin-btn admin-btn-primary" @click="createProduct">
            <i class="fas fa-plus"></i>
            <span>Create Product</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="admin-product-filters">
      <SearchAndFilters
        :filters="filters"
        :filter-fields="filterFields"
        search-label="Search Products"
        search-placeholder="Search by name, description, or category..."
        search-column-class="is-4"
        :total-items="totalItems"
        item-name="products"
        :loading="loading"
        @search-changed="handleSearchChange"
        @filter-changed="handleFilterChange"
        @reset-filters="handleResetFilters"
      />
    </div>

    <!-- Loading -->
    <div v-if="loading && isFirstLoad" class="admin-loading-state">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading products...</p>
    </div>

    <!-- Error -->
    <div v-else-if="error" class="admin-alert admin-alert-danger">
      <div class="admin-alert-content">
        <p>{{ error }}</p>
        <button class="admin-btn admin-btn-secondary admin-btn-sm" @click="fetchData">
          <i class="fas fa-redo"></i>
          <span>Retry</span>
        </button>
      </div>
    </div>

    <!-- No Results -->
    <div v-else-if="!loading && items.length === 0" class="admin-empty-state">
      <div class="admin-empty-icon">
        <i class="fas fa-box"></i>
      </div>
      <h3 class="admin-empty-title">No products found</h3>
      <p class="admin-empty-text">There are no products matching your search criteria.</p>
    </div>

    <!-- Products Table -->
    <div v-else class="admin-product-table">
      <div class="admin-table-container" :class="{ 'admin-table-loading': loading && !isFirstLoad }">
        <product-table
          :products="items"
          :categories="categories"
          :loading="loading"
          @view="viewProduct"
          @edit="editProduct"
          @delete="confirmDeleteProduct" />
      </div>
    </div>

    <!-- Pagination -->
    <div class="pagination-wrapper" v-if="totalPages > 1 || totalItems > 0">
      <pagination
        :current-page="currentPage"
        :total-pages="totalPages"
        @page-changed="handlePageChange" />
    </div>

    <!-- Add/Edit Product Modal -->
    <product-form-modal
      :is-open="isProductModalOpen"
      :product="selectedProduct"
      @close="closeProductModal"
      @save="saveProduct" />

    <!-- Delete Confirmation Modal -->
    <confirm-dialog
      :is-open="isDeleteModalOpen"
      :title="'Delete Product'"
      :message="'Are you sure you want to delete this product? This action cannot be undone.'"
      @confirm="deleteProduct"
      @cancel="isDeleteModalOpen = false" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SearchAndFilters from '@/admin/components/common/SearchAndFilters.vue';
import ProductTable from '@/admin/components/products/ProductTable.vue';
import ProductFormModal from '@/admin/components/products/ProductFormModal.vue';
import Pagination from '@/admin/components/common/Pagination.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
import { productsService } from '@/admin/services/products';
import { categoriesService } from '@/admin/services/categories';
import { useAdminSearch } from '@/composables/useAdminSearch';

// Router
const router = useRouter();

// Filter configuration
const filterFields = ref([
  {
    key: 'categoryId',
    label: 'Category',
    type: 'select',
    columnClass: 'is-3',
    allOption: 'All Categories',
    options: [] // Will be populated dynamically
  },
  {
    key: 'status',
    label: 'Status',
    type: 'select',
    columnClass: 'is-3',
    allOption: 'All Statuses',
    options: [
      { value: '0', label: 'Pending' },
      { value: '1', label: 'Approved' },
      { value: '2', label: 'Rejected' }
    ]
  },
  {
    key: 'stock',
    label: 'Stock Level',
    type: 'select',
    columnClass: 'is-3',
    allOption: 'All Stock Levels',
    options: [
      { value: 'in-stock', label: 'In Stock (>10)' },
      { value: 'low-stock', label: 'Low Stock (1-10)' },
      { value: 'out-of-stock', label: 'Out of Stock (0)' }
    ]
  },
  {
    key: 'sortBy',
    label: 'Sort By',
    type: 'select',
    columnClass: 'is-3',
    allOption: false,
    options: [
      { value: 'Name', label: 'Name' },
      { value: 'CreatedAt', label: 'Created Date' },
      { value: 'Stock', label: 'Stock' },
      { value: 'Status', label: 'Status' }
    ]
  },
  {
    key: 'sortOrder',
    label: 'Order',
    type: 'select',
    columnClass: 'is-3',
    options: [
      { value: 'desc', label: 'Descending' },
      { value: 'asc', label: 'Ascending' }
    ]
  }
]);

// Use the admin search composable
const {
  items,
  loading,
  error,
  isFirstLoad,
  currentPage,
  totalPages,
  totalItems,
  filters,
  fetchData,
  handlePageChange
} = useAdminSearch({
  fetchFunction: productsService.getProducts,
  defaultFilters: {
    search: '',
    categoryId: '',
    status: '',
    stock: '',
    sortBy: 'CreatedAt',
    sortOrder: 'desc'
  },
  debounceTime: 300,
  defaultPageSize: 20, // Збільшуємо розмір сторінки для кращого відображення
  clientSideSearch: false
});

// Modal state
const isProductModalOpen = ref(false);
const isDeleteModalOpen = ref(false);
const selectedProduct = ref(null);

// Categories state
const categories = ref([]);

// Load categories for filter
const loadCategories = async () => {
  try {
    console.log('🗂️ Loading categories...');
    const response = await categoriesService.getCategories();
    console.log('📋 Categories service response:', response);

    const categoriesData = response.data || response.categories || [];
    console.log('📊 Categories data:', categoriesData);

    // Store categories for the table component
    categories.value = categoriesData;
    console.log('✅ Categories stored for table:', categories.value.length, 'categories');

    // Flatten categories and subcategories for the filter
    const flattenCategories = (cats, prefix = '') => {
      let result = [];
      cats.forEach(category => {
        const label = prefix ? `${prefix} > ${category.name}` : category.name;
        result.push({
          value: category.id,
          label: label
        });

        // Add subcategories if they exist
        if (category.children && category.children.length > 0) {
          result = result.concat(flattenCategories(category.children, label));
        }
      });
      return result;
    };

    const flattenedCategories = flattenCategories(categoriesData);
    console.log('🔄 Flattened categories for filter:', flattenedCategories);

    // Remove duplicates by name and id
    const uniqueCategories = [];
    const seenNames = new Set();
    const seenIds = new Set();

    flattenedCategories.forEach(category => {
      const nameKey = category.label.toLowerCase();
      if (!seenNames.has(nameKey) && !seenIds.has(category.value)) {
        uniqueCategories.push(category);
        seenNames.add(nameKey);
        seenIds.add(category.value);
      } else {
        console.log('🔄 Removing duplicate category:', category.label);
      }
    });

    console.log('✅ Unique categories after deduplication:', uniqueCategories.length);

    // Update the category filter options
    const categoryFilter = filterFields.value.find(field => field.key === 'categoryId');
    if (categoryFilter) {
      categoryFilter.options = uniqueCategories;
      console.log('✅ Category filter updated with', uniqueCategories.length, 'options');
      console.log('📋 Sample categories:', uniqueCategories.slice(0, 3));

      // Force reactivity update
      filterFields.value = [...filterFields.value];
    } else {
      console.error('❌ Category filter field not found');
    }
  } catch (error) {
    console.error('❌ Error loading categories:', error);
  }
};

// Subcategories removed - not available in current data structure

// Event handlers
const handleSearchChange = (searchValue) => {
  filters.search = searchValue;
};

const handleFilterChange = (filterKey, filterValue) => {
  console.log(`🔄 Filter changed: ${filterKey} = "${filterValue}"`);

  // Special handling for category filter to include subcategories
  if (filterKey === 'categoryId' && filterValue) {
    const selectedCategoryIds = getCategoryAndSubcategoryIds(filterValue);
    console.log(`📂 Selected category includes subcategories:`, selectedCategoryIds);

    // Send all category IDs to backend
    filters.categoryIds = selectedCategoryIds;
    filters[filterKey] = filterValue; // Keep original for UI
  } else if (filterKey === 'categoryId' && !filterValue) {
    // Clear category filter
    delete filters.categoryIds;
    filters[filterKey] = filterValue;
  } else {
    filters[filterKey] = filterValue;
  }

  console.log('📊 Current filters state:', { ...filters });
};

// Helper function to get category and all its subcategory IDs
const getCategoryAndSubcategoryIds = (categoryId) => {
  const ids = [categoryId];

  const collectSubcategoryIds = (cats, targetId) => {
    for (const category of cats) {
      if (category.id === targetId) {
        // Found the category, now collect all subcategory IDs
        const collectAllChildren = (cat) => {
          if (cat.children && cat.children.length > 0) {
            cat.children.forEach(child => {
              ids.push(child.id);
              collectAllChildren(child); // Recursive for nested subcategories
            });
          }
        };
        collectAllChildren(category);
        return true;
      }

      if (category.children && category.children.length > 0) {
        if (collectSubcategoryIds(category.children, targetId)) {
          return true;
        }
      }
    }
    return false;
  };

  collectSubcategoryIds(categories.value, categoryId);
  return ids;
};

const handleResetFilters = () => {
  Object.keys(filters).forEach(key => {
    if (key === 'search') {
      filters[key] = '';
    } else if (key === 'sortBy') {
      filters[key] = 'CreatedAt';
    } else if (key === 'sortOrder') {
      filters[key] = 'desc';
    } else {
      filters[key] = '';
    }
  });

  // Clear categoryIds as well
  delete filters.categoryIds;

  // Викликаємо fetchData після скидання фільтрів
  fetchData(1);
};

// Open add product modal
const openAddProductModal = () => {
  selectedProduct.value = null; // Reset selected product for adding new
  isProductModalOpen.value = true;
};

// Create product
const createProduct = () => {
  // Navigate to product create page
  router.push('/admin/products/create');
};

// View product
const viewProduct = (product) => {
  // Navigate to product view page
  router.push(`/admin/products/${product.id}/view`);
};

// Edit product
const editProduct = (product) => {
  // Navigate to product edit page
  router.push(`/admin/products/${product.id}/edit`);
};

// Close product modal
const closeProductModal = () => {
  isProductModalOpen.value = false;
  selectedProduct.value = null;
};

// Save product (create or update)
const saveProduct = async (productData) => {
  try {
    if (productData.id) {
      await productsService.updateProduct(productData.id, productData);
    } else {
      await productsService.createProduct(productData);
    }
    closeProductModal();
    fetchData(currentPage.value);
  } catch (error) {
    console.error('Error saving product:', error);
  }
};

// Confirm delete product
const confirmDeleteProduct = (product) => {
  selectedProduct.value = product;
  isDeleteModalOpen.value = true;
};

// Delete product
const deleteProduct = async () => {
  if (!selectedProduct.value) return;

  try {
    await productsService.deleteProduct(selectedProduct.value.id);
    isDeleteModalOpen.value = false;
    fetchData(currentPage.value);
  } catch (error) {
    console.error('Error deleting product:', error);
  }
};

// Initialize component
onMounted(async () => {
  await loadCategories();
  // Fetch initial data
  fetchData(1);
});
</script>

<style scoped>
.admin-products {
  padding: 1rem;
}

.pagination-wrapper {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}
</style>
