/* ===== ADMIN COMPANIES PAGE STYLES ===== */
/* Based on Reports page design patterns */

/* ===== COMPANIES PAGE LAYOUT ===== */
.admin-companies {
  padding: var(--admin-space-2xl);
  background-color: var(--admin-bg-primary);
  min-height: 100vh;
}

.admin-companies .admin-page-header {
  background: var(--admin-gradient-secondary);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-3xl);
  margin-bottom: var(--admin-space-2xl);
  color: white;
  box-shadow: var(--admin-shadow-lg);
}

.admin-companies .admin-page-title {
  font-size: var(--admin-text-4xl);
  font-weight: var(--admin-font-bold);
  color: white;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-lg);
}

/* ===== COMPANY FILTERS ===== */
.admin-company-filters {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  margin-bottom: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
}

.admin-company-filters .field {
  margin-bottom: var(--admin-space-lg);
}

.admin-company-filters .label {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-700);
  margin-bottom: var(--admin-space-sm);
  font-size: var(--admin-text-sm);
}

.admin-company-filters .input,
.admin-company-filters .select select {
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-md);
  font-size: var(--admin-text-sm);
  transition: all var(--admin-transition-fast);
}

.admin-company-filters .input:focus,
.admin-company-filters .select select:focus {
  border-color: var(--admin-secondary);
  box-shadow: 0 0 0 3px var(--admin-secondary-bg);
  outline: none;
}

.admin-company-filters .control.has-icons-left .icon {
  color: var(--admin-gray-400);
}

.admin-company-filters .buttons {
  margin-top: var(--admin-space-xl);
  display: flex;
  justify-content: center;
  gap: var(--admin-space-md);
}

/* ===== COMPANY TABLE ===== */
.admin-company-table {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  margin-bottom: var(--admin-space-2xl);
}

.admin-company-table .table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.admin-company-table .table th {
  background: var(--admin-gray-50);
  color: var(--admin-gray-700);
  font-weight: var(--admin-font-semibold);
  padding: var(--admin-space-lg);
  text-align: left;
  border-bottom: 2px solid var(--admin-border-color);
  font-size: var(--admin-text-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-company-table .table td {
  padding: var(--admin-space-lg);
  border-bottom: 1px solid var(--admin-border-light);
  vertical-align: middle;
  font-size: var(--admin-text-sm);
}

.admin-company-table .table tr:hover {
  background: var(--admin-gray-25);
}

.admin-company-table .table tr:last-child td {
  border-bottom: none;
}

/* ===== COMPANY INFO CELL ===== */
.admin-company-info {
  display: flex;
  align-items: center;
  gap: var(--admin-space-md);
}

.admin-company-logo {
  width: 48px;
  height: 48px;
  border-radius: var(--admin-radius-lg);
  object-fit: cover;
  border: 2px solid var(--admin-border-color);
  flex-shrink: 0;
}

.admin-company-details {
  flex: 1;
  min-width: 0;
}

.admin-company-name {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin-bottom: var(--admin-space-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.admin-company-slug {
  font-size: var(--admin-text-xs);
  color: var(--admin-gray-500);
  font-family: var(--admin-font-mono);
}

/* ===== COMPANY CONTACT INFO ===== */
.admin-company-contact {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-company-email {
  color: var(--admin-gray-900);
  font-size: var(--admin-text-sm);
}

.admin-company-phone {
  color: var(--admin-gray-500);
  font-size: var(--admin-text-xs);
}

/* ===== COMPANY ADDRESS ===== */
.admin-company-address {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-company-city {
  color: var(--admin-gray-900);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
}

.admin-company-street {
  color: var(--admin-gray-500);
  font-size: var(--admin-text-xs);
}

/* ===== COMPANY STATUS ===== */
.admin-company-status {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-full);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-company-status.featured {
  background: var(--admin-info-bg);
  color: var(--admin-info-dark);
  border: 1px solid var(--admin-info-light);
}

.admin-company-status.regular {
  background: var(--admin-gray-100);
  color: var(--admin-gray-600);
  border: 1px solid var(--admin-gray-200);
}

.admin-company-status.approved {
  background: var(--admin-success-bg);
  color: var(--admin-success-dark);
  border: 1px solid var(--admin-success-light);
}

.admin-company-status.pending {
  background: var(--admin-warning-bg);
  color: var(--admin-warning-dark);
  border: 1px solid var(--admin-warning-light);
}

.admin-company-status.rejected {
  background: var(--admin-danger-bg);
  color: var(--admin-danger-dark);
  border: 1px solid var(--admin-danger-light);
}

/* ===== COMPANY ACTION BUTTONS ===== */
.admin-company-actions {
  display: flex;
  gap: var(--admin-space-xs);
  align-items: center;
}

.admin-company-actions .button {
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-md);
  border: 1px solid transparent;
  font-size: var(--admin-text-xs);
  transition: all var(--admin-transition-fast);
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-company-actions .button.is-info {
  background: var(--admin-info);
  color: white;
  border-color: var(--admin-info);
}

.admin-company-actions .button.is-info:hover {
  background: var(--admin-info-dark);
  border-color: var(--admin-info-dark);
  transform: translateY(-1px);
}

.admin-company-actions .button.is-warning {
  background: var(--admin-warning);
  color: white;
  border-color: var(--admin-warning);
}

.admin-company-actions .button.is-warning:hover {
  background: var(--admin-warning-dark);
  border-color: var(--admin-warning-dark);
  transform: translateY(-1px);
}

.admin-company-actions .button.is-danger {
  background: var(--admin-danger);
  color: white;
  border-color: var(--admin-danger);
}

.admin-company-actions .button.is-danger:hover {
  background: var(--admin-danger-dark);
  border-color: var(--admin-danger-dark);
  transform: translateY(-1px);
}

/* ===== LOADING STATES ===== */
.admin-company-table .admin-loading {
  text-align: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.admin-company-table .admin-loading i {
  font-size: 2rem;
  color: var(--admin-secondary);
  margin-bottom: var(--admin-space-lg);
  animation: spin 1s linear infinite;
}

.admin-company-table .admin-empty {
  text-align: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.admin-company-table .admin-empty i {
  font-size: 3rem;
  color: var(--admin-gray-300);
  margin-bottom: var(--admin-space-lg);
}

.admin-company-table .admin-empty-text {
  font-size: var(--admin-text-lg);
  margin-bottom: var(--admin-space-sm);
}

.admin-company-table .admin-empty-subtext {
  color: var(--admin-gray-400);
  font-size: var(--admin-text-sm);
}

/* ===== PAGINATION WRAPPER ===== */
.admin-companies .pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: var(--admin-space-2xl);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .admin-company-table .table {
    font-size: var(--admin-text-xs);
  }
  
  .admin-company-table .table th,
  .admin-company-table .table td {
    padding: var(--admin-space-md);
  }
  
  .admin-company-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--admin-space-sm);
  }
  
  .admin-company-logo {
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 768px) {
  .admin-companies {
    padding: var(--admin-space-lg);
  }
  
  .admin-companies .admin-page-header {
    padding: var(--admin-space-2xl);
  }
  
  .admin-companies .admin-page-title {
    font-size: var(--admin-text-2xl);
  }
  
  .admin-company-filters {
    padding: var(--admin-space-lg);
  }
  
  .admin-company-table {
    padding: var(--admin-space-lg);
    overflow-x: auto;
  }
  
  .admin-company-table .table {
    min-width: 800px;
  }
  
  .admin-company-actions {
    flex-direction: column;
    gap: var(--admin-space-xs);
  }
  
  .admin-company-actions .button {
    width: 100%;
    min-width: auto;
  }
}
