<template>
  <div>
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Seller Applications</h1>
        </div>
      </div>
    </div>
    
    <!-- Search and Filter -->
    <div class="box">
      <div class="field has-addons">
        <div class="control is-expanded">
          <input class="input" type="text" placeholder="Search by username, email, or company info..." v-model="searchQuery">
        </div>
        <div class="control">
          <button class="button is-info" @click="searchApplications">
            <span class="icon"><i class="fas fa-search"></i></span>
          </button>
        </div>
      </div>
      
      <div class="columns">
        <div class="column">
          <div class="field">
            <label class="label">Status</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="filters.status">
                  <option value="">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        
        <div class="column">
          <div class="field">
            <label class="label">Date Range</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="filters.dateRange">
                  <option value="">All Time</option>
                  <option value="today">Today</option>
                  <option value="yesterday">Yesterday</option>
                  <option value="last7days">Last 7 Days</option>
                  <option value="last30days">Last 30 Days</option>
                  <option value="thisMonth">This Month</option>
                  <option value="lastMonth">Last Month</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        
        <div class="column">
          <div class="field">
            <label class="label">Sort By</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="filters.sortBy">
                  <option value="createdAt">Date</option>
                  <option value="applicantName">Applicant Name</option>
                  <option value="status">Status</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Applications Table -->
    <div class="box">
      <table class="table is-fullwidth is-striped">
        <thead>
          <tr>
            <th>ID</th>
            <th>Applicant</th>
            <th>Company Name</th>
            <th>Date Applied</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading">
            <td colspan="6" class="has-text-centered">
              <div class="is-loading">Loading applications...</div>
            </td>
          </tr>
          <tr v-else-if="error">
            <td colspan="6" class="has-text-centered has-text-danger">
              {{ error }}
              <br>
              <button class="button is-small is-primary mt-2" @click="fetchApplications">
                Retry
              </button>
            </td>
          </tr>
          <tr v-else-if="applications.length === 0">
            <td colspan="6" class="has-text-centered">
              No applications found
            </td>
          </tr>
          <tr v-else v-for="application in applications" :key="application.id">
            <td>{{ application.id.substring(0, 8) }}...</td>
            <td>{{ application.userName || 'N/A' }}</td>
            <td>{{ application.companyName || 'N/A' }}</td>
            <td>{{ formatDate(application.createdAt) }}</td>
            <td>
              <span class="tag" :class="getStatusTagClass(application.status)">
                {{ application.status }}
              </span>
            </td>
            <td>
              <div class="buttons are-small">
                <button class="button is-info" @click="viewApplication(application)" :disabled="loading">
                  <span class="icon"><i class="fas fa-eye"></i></span>
                </button>
                <button
                  v-if="application.status?.toLowerCase() === 'pending'"
                  class="button is-success"
                  @click="approveApplication(application)"
                  :disabled="loading"
                >
                  <span class="icon"><i class="fas fa-check"></i></span>
                </button>
                <button
                  v-if="application.status?.toLowerCase() === 'pending'"
                  class="button is-danger"
                  @click="rejectApplication(application)"
                  :disabled="loading"
                >
                  <span class="icon"><i class="fas fa-times"></i></span>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      
      <!-- Pagination -->
      <nav class="pagination is-centered" role="navigation" aria-label="pagination" v-if="totalPages > 1">
        <a
          class="pagination-previous"
          :class="{ 'is-disabled': currentPage === 1 || loading }"
          @click="changePage(currentPage - 1)"
        >
          Previous
        </a>
        <a
          class="pagination-next"
          :class="{ 'is-disabled': currentPage === totalPages || loading }"
          @click="changePage(currentPage + 1)"
        >
          Next
        </a>
        <ul class="pagination-list">
          <li v-for="page in paginationItems" :key="page.value || page.type">
            <a
              v-if="page.type === 'page'"
              class="pagination-link"
              :class="{ 'is-current': page.value === currentPage }"
              @click="changePage(page.value)"
            >
              {{ page.value }}
            </a>
            <span v-else class="pagination-ellipsis">&hellip;</span>
          </li>
        </ul>
      </nav>

      <!-- Pagination Info -->
      <div class="has-text-centered mt-3" v-if="totalItems > 0">
        <p class="is-size-7 has-text-grey">
          Showing {{ ((currentPage - 1) * itemsPerPage) + 1 }} to
          {{ Math.min(currentPage * itemsPerPage, totalItems) }} of
          {{ totalItems }} applications
        </p>
      </div>
    </div>
    
    <!-- View Application Modal -->
    <div class="modal" :class="{ 'is-active': showViewModal }">
      <div class="modal-background" @click="showViewModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Application Details - #{{ selectedApplication?.id }}</p>
          <button class="delete" aria-label="close" @click="showViewModal = false"></button>
        </header>
        <section class="modal-card-body" v-if="selectedApplication">
          <div class="columns">
            <div class="column">
              <h3 class="title is-5">Applicant Information</h3>
              <p><strong>Name:</strong> {{ selectedApplication.userName || 'N/A' }}</p>
              <p><strong>Email:</strong> {{ selectedApplication.userEmail || 'N/A' }}</p>
              <p><strong>Date Applied:</strong> {{ formatDate(selectedApplication.createdAt) }}</p>
            </div>
            <div class="column">
              <h3 class="title is-5">Company Information</h3>
              <p><strong>Company Name:</strong> {{ selectedApplication.companyName || 'N/A' }}</p>
              <p><strong>Additional Information:</strong> {{ selectedApplication.additionalInformation || 'N/A' }}</p>
            </div>
          </div>

          <hr>

          <h3 class="title is-5">Application Status</h3>
          <div class="field">
            <div class="control">
              <span class="tag is-large" :class="getStatusTagClass(selectedApplication.status)">
                {{ selectedApplication.status }}
              </span>
            </div>
          </div>

          <div class="field" v-if="selectedApplication.status?.toLowerCase() !== 'pending'">
            <label class="label">Decision Date</label>
            <div class="control">
              <input class="input" type="text" :value="formatDate(selectedApplication.updatedAt)" disabled>
            </div>
          </div>

          <div class="field" v-if="selectedApplication.status?.toLowerCase() === 'rejected' && selectedApplication.rejectionReason">
            <label class="label">Rejection Reason</label>
            <div class="control">
              <textarea class="textarea" v-model="selectedApplication.rejectionReason" :disabled="true"></textarea>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <div class="buttons" v-if="selectedApplication && selectedApplication.status?.toLowerCase() === 'pending'">
            <button class="button is-success" @click="approveApplication(selectedApplication)" :disabled="loading">
              <span v-if="loading" class="icon">
                <i class="fas fa-spinner fa-spin"></i>
              </span>
              <span>Approve</span>
            </button>
            <button class="button is-danger" @click="showRejectModal = true; showViewModal = false" :disabled="loading">
              Reject
            </button>
          </div>
          <button class="button" @click="showViewModal = false">Close</button>
        </footer>
      </div>
    </div>
    
    <!-- Reject Application Modal -->
    <div class="modal" :class="{ 'is-active': showRejectModal }">
      <div class="modal-background" @click="showRejectModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Reject Application - #{{ selectedApplication?.id }}</p>
          <button class="delete" aria-label="close" @click="showRejectModal = false"></button>
        </header>
        <section class="modal-card-body" v-if="selectedApplication">
          <div class="field">
            <label class="label">Rejection Reason</label>
            <div class="control">
              <textarea class="textarea" placeholder="Please provide a reason for rejection" v-model="rejectionReason"></textarea>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button class="button is-danger" @click="confirmReject" :disabled="loading || !rejectionReason.trim()">
            <span v-if="loading" class="icon">
              <i class="fas fa-spinner fa-spin"></i>
            </span>
            <span>Confirm Rejection</span>
          </button>
          <button class="button" @click="showRejectModal = false" :disabled="loading">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { sellerRequestService } from '@/services/seller-request.service';

// Data
const applications = ref([]);
const loading = ref(false);
const error = ref(null);

// Pagination
const currentPage = ref(1);
const itemsPerPage = ref(15);
const totalItems = ref(0);

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage.value));

const paginationItems = computed(() => {
  const items = [];
  const maxVisiblePages = 5;

  if (totalPages.value <= maxVisiblePages) {
    // Show all pages
    for (let i = 1; i <= totalPages.value; i++) {
      items.push({ type: 'page', value: i });
    }
  } else {
    // Always show first page
    items.push({ type: 'page', value: 1 });

    // Calculate start and end of visible pages
    let startPage = Math.max(2, currentPage.value - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages.value - 1, startPage + maxVisiblePages - 3);

    // Adjust if we're near the beginning
    if (startPage > 2) {
      items.push({ type: 'ellipsis' });
    }

    // Add visible pages
    for (let i = startPage; i <= endPage; i++) {
      items.push({ type: 'page', value: i });
    }

    // Adjust if we're near the end
    if (endPage < totalPages.value - 1) {
      items.push({ type: 'ellipsis' });
    }

    // Always show last page
    items.push({ type: 'page', value: totalPages.value });
  }

  return items;
});

// Search and filters
const searchQuery = ref('');
const filters = ref({
  status: '',
  dateRange: '',
  sortBy: 'createdAt'
});

// Computed for API parameters
const apiParams = computed(() => ({
  filter: searchQuery.value || undefined,
  status: filters.value.status || undefined,
  orderBy: filters.value.sortBy || 'createdAt',
  descending: true,
  page: currentPage.value,
  pageSize: itemsPerPage.value
}));

// Modals
const showViewModal = ref(false);
const showRejectModal = ref(false);
const selectedApplication = ref(null);
const rejectionReason = ref('');

// API Methods
const fetchApplications = async () => {
  try {
    loading.value = true;
    error.value = null;

    const result = await sellerRequestService.getAllSellerRequests(apiParams.value);

    applications.value = result.requests;
    totalItems.value = result.pagination.total;

  } catch (err) {
    console.error('Error fetching applications:', err);
    error.value = 'Failed to load applications. Please try again.';
    applications.value = [];
    totalItems.value = 0;
  } finally {
    loading.value = false;
  }
};

// Methods
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';

  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

const getStatusTagClass = (status) => {
  switch (status?.toLowerCase()) {
    case 'pending':
      return 'is-warning';
    case 'approved':
      return 'is-success';
    case 'rejected':
      return 'is-danger';
    default:
      return 'is-light';
  }
};

const searchApplications = () => {
  currentPage.value = 1; // Reset to first page when searching
  fetchApplications();
};

const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

const viewApplication = async (application) => {
  try {
    // Fetch detailed application data
    const detailedApplication = await sellerRequestService.getSellerRequestById(application.id);
    selectedApplication.value = detailedApplication;
    showViewModal.value = true;
  } catch (err) {
    console.error('Error fetching application details:', err);
    // Fallback to basic data
    selectedApplication.value = { ...application };
    showViewModal.value = true;
  }
};

const approveApplication = async (application) => {
  try {
    loading.value = true;
    await sellerRequestService.approveSellerRequest(application.id);

    // Refresh the list
    await fetchApplications();
    showViewModal.value = false;

    // Show success message (you can add a toast notification here)
    console.log('Application approved successfully');
  } catch (err) {
    console.error('Error approving application:', err);
    error.value = 'Failed to approve application. Please try again.';
  } finally {
    loading.value = false;
  }
};

const rejectApplication = (application) => {
  selectedApplication.value = { ...application };
  rejectionReason.value = '';
  showRejectModal.value = true;
};

const confirmReject = async () => {
  if (!rejectionReason.value.trim()) {
    alert('Please provide a reason for rejection');
    return;
  }

  try {
    loading.value = true;
    await sellerRequestService.rejectSellerRequest(
      selectedApplication.value.id,
      rejectionReason.value
    );

    // Refresh the list
    await fetchApplications();
    showRejectModal.value = false;
    rejectionReason.value = '';

    // Show success message (you can add a toast notification here)
    console.log('Application rejected successfully');
  } catch (err) {
    console.error('Error rejecting application:', err);
    error.value = 'Failed to reject application. Please try again.';
  } finally {
    loading.value = false;
  }
};

// Watchers
watch([currentPage, filters], () => {
  fetchApplications();
}, { deep: true });

onMounted(() => {
  fetchApplications();
});
</script>

<style scoped>
.level {
  margin-bottom: 1.5rem;
}

.pagination {
  margin-top: 1.5rem;
}

.is-loading {
  padding: 2rem;
  font-style: italic;
  color: #999;
}

.pagination-previous.is-disabled,
.pagination-next.is-disabled {
  pointer-events: none;
  opacity: 0.5;
}

.fa-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
