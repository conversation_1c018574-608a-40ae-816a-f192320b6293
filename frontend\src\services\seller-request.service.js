import apiService from './api';

class SellerRequestService {
  /**
   * Отримує заявки на отримання статусу продавця для поточного користувача
   * @param {Object} params - Параметри запиту
   * @returns {Promise<Object>} - Об'єкт з заявками та інформацією про пагінацію
   */
  async getUserSellerRequests(params = {}) {
    try {
      const response = await apiService.get('/api/users/me/seller-requests', { params });

      if (response.data && response.data.success && response.data.data) {
        return {
          requests: response.data.data.items || [],
          pagination: {
            total: response.data.data.totalItems || 0,
            page: response.data.data.currentPage || 1,
            limit: response.data.data.pageSize || 10,
            totalPages: response.data.data.totalPages || 1
          }
        };
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error('Error fetching user seller requests:', error);
      // Return empty data if API fails
      return {
        requests: [],
        pagination: {
          total: 0,
          page: 1,
          limit: 10,
          totalPages: 0
        }
      };
    }
  }

  /**
   * Отримує заявку на отримання статусу продавця за ID
   * @param {string} id - ID заявки
   * @returns {Promise<Object>} - Об'єкт з даними заявки
   */
  async getUserSellerRequestById(id) {
    try {
      const response = await apiService.get(`/api/users/me/seller-requests/${id}`);

      if (response.data && response.data.success && response.data.data) {
        return response.data.data;
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error(`Error fetching user seller request ${id}:`, error);
      // Return null if API fails
      return null;
    }
  }

  /**
   * Створює нову заявку на отримання статусу продавця
   * @param {Object} data - Дані заявки
   * @returns {Promise<Object>} - Об'єкт з даними створеної заявки
   */
  async createSellerRequest(data) {
    try {
      const response = await apiService.post('/api/users/me/seller-requests', data);

      if (response.data && response.data.success && response.data.data) {
        return response.data.data;
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error('Error creating seller request:', error);
      throw error;
    }
  }

  /**
   * Отримує всі заявки на отримання статусу продавця (для адміністраторів)
   * @param {Object} params - Параметри запиту (filter, status, orderBy, descending, page, pageSize)
   * @returns {Promise<Object>} - Об'єкт з заявками та інформацією про пагінацію
   */
  async getAllSellerRequests(params = {}) {
    try {
      const response = await apiService.get('/api/admin/seller-requests', { params });

      if (response.data && response.data.success && response.data.data) {
        return {
          requests: response.data.data.items || [],
          pagination: {
            total: response.data.data.totalItems || 0,
            page: response.data.data.currentPage || 1,
            limit: response.data.data.pageSize || 15,
            totalPages: response.data.data.totalPages || 1
          }
        };
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error('Error fetching all seller requests:', error);
      throw error;
    }
  }

  /**
   * Отримує заявку на отримання статусу продавця за ID (для адміністраторів)
   * @param {string} id - ID заявки
   * @returns {Promise<Object>} - Об'єкт з даними заявки
   */
  async getSellerRequestById(id) {
    try {
      const response = await apiService.get(`/api/admin/seller-requests/${id}`);

      if (response.data && response.data.success && response.data.data) {
        return response.data.data;
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error(`Error fetching seller request ${id}:`, error);
      throw error;
    }
  }

  /**
   * Схвалює заявку на отримання статусу продавця
   * @param {string} id - ID заявки
   * @returns {Promise<boolean>} - Результат операції
   */
  async approveSellerRequest(id) {
    try {
      const response = await apiService.post(`/api/admin/seller-requests/${id}/approve`);
      return response.data && response.data.success;
    } catch (error) {
      console.error(`Error approving seller request ${id}:`, error);
      throw error;
    }
  }

  /**
   * Відхиляє заявку на отримання статусу продавця
   * @param {string} id - ID заявки
   * @param {string} reason - Причина відхилення
   * @returns {Promise<boolean>} - Результат операції
   */
  async rejectSellerRequest(id, reason) {
    try {
      const response = await apiService.post(`/api/admin/seller-requests/${id}/reject`, { reason });
      return response.data && response.data.success;
    } catch (error) {
      console.error(`Error rejecting seller request ${id}:`, error);
      throw error;
    }
  }
}

export const sellerRequestService = new SellerRequestService();
export default sellerRequestService;
