<template>
  <div class="dashboard">
    <h1 class="mb-4">User Dashboard</h1>
    
    <div class="row">
      <div class="col-md-4">
        <div class="dashboard-card">
          <h2 class="dashboard-card-title">My Profile</h2>
          <p>Manage your personal information and account settings.</p>
          <router-link to="/profile" class="btn btn-primary">View Profile</router-link>
        </div>
      </div>
      
      <div class="col-md-4">
        <div class="dashboard-card">
          <h2 class="dashboard-card-title">My Orders</h2>
          <p>View your order history and track current orders.</p>
          <a href="#" class="btn btn-primary">View Orders</a>
        </div>
      </div>
      
      <div class="col-md-4">
        <div class="dashboard-card">
          <h2 class="dashboard-card-title">My Wishlist</h2>
          <p>View and manage your saved items.</p>
          <a href="#" class="btn btn-primary">View Wishlist</a>
        </div>
      </div>
    </div>
    
    <div class="row mt-4">
      <div class="col-md-6">
        <div class="dashboard-card">
          <h2 class="dashboard-card-title">Recent Activity</h2>
          <ul class="list-group list-group-flush">
            <li class="list-group-item">You logged in on {{ formatDate(new Date()) }}</li>
            <li class="list-group-item">Profile updated on {{ formatDate(new Date(Date.now() - ********)) }}</li>
          </ul>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="dashboard-card">
          <h2 class="dashboard-card-title">Account Summary</h2>
          <div class="card-body">
            <p><strong>Username:</strong> {{ user.username }}</p>
            <p><strong>Email:</strong> {{ user.email }}</p>
            <p><strong>Member Since:</strong> {{ formatDate(new Date()) }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue';
import { useStore } from 'vuex';

export default {
  name: 'Dashboard',
  setup() {
    const store = useStore();
    const user = computed(() => store.getters['auth/user']);
    
    const formatDate = (date) => {
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }).format(date);
    };
    
    return {
      user,
      formatDate
    };
  }
};
</script>
