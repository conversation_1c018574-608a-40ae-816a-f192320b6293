import{q as l}from"./index-BKy0rL_2.js";const i={BUYER:0,SELLER:1,SELLER_OWNER:2,MODERATOR:3,ADMIN:4},a={BUYER:"buyer",SELLER:"seller",SELLER_OWNER:"sellerowner",MODERATOR:"moderator",ADMIN:"admin"},g={[a.BUYER]:"Buyer",[a.SELLER]:"Seller",[a.SELLER_OWNER]:"Seller Owner",[a.MODERATOR]:"Moderator",[a.ADMIN]:"Admin",unknown:"Unknown"},R={[i.BUYER]:a.BUYER,[i.SELLER]:a.SELLER,[i.SELLER_OWNER]:a.SELLER_OWNER,[i.MODERATOR]:a.MODERATOR,[i.ADMIN]:a.ADMIN},p={[a.BUYER]:i.BUYER,[a.SELLER]:i.SELLER,[a.SELLER_OWNER]:i.SELLER_OWNER,[a.MODERATOR]:i.MODERATOR,[a.ADMIN]:i.ADMIN},E={[a.ADMIN]:1,[a.MODERATOR]:2,[a.SELLER_OWNER]:3,[a.SELLER]:4,[a.BUYER]:5,unknown:6},f={[a.ADMIN]:"is-danger",[a.MODERATOR]:"is-warning",[a.SELLER]:"is-info",[a.SELLER_OWNER]:"is-info",[a.BUYER]:"is-success",unknown:"is-light"};function d(r){if(r==null)return"unknown";if(typeof r=="number")return R[r]||"unknown";if(typeof r=="string"){const e=r.toLowerCase();if(Object.values(a).includes(e))return e;const t=Object.keys(g).find(s=>g[s].toLowerCase()===e);return t||"unknown"}if(typeof r=="object"){if(r.hasOwnProperty("name"))return d(r.name);if(r.hasOwnProperty("value"))return d(r.value)}return"unknown"}function u(r){const e=d(r);return p[e]!==void 0?p[e]:-1}function y(r){const e=d(r);return g[e]||"Unknown"}function h(r){const e=d(r);return f[e]||"is-light"}function D(r,e){const t=d(r),s=d(e);return(E[t]||5)-(E[s]||5)}const L={async getUsers(r={}){var e,t;try{console.log("Requesting users with params:",r);const s="/api/admin/users";console.log("Full request URL:",s,"with params:",r);const n={};if(r.page&&(n.page=r.page),r.pageSize&&(n.pageSize=r.pageSize),r.role!==void 0&&r.role!==null&&r.role!==""){const o=u(r.role);o!==-1?(n.role=o,console.log(`Converting role ${r.role} to numeric value ${n.role}`)):console.log(`Invalid role: ${r.role}, not adding to params`)}const m=localStorage.getItem("token");console.log("Auth token present:",!!m),console.log("Final request params:",n);const c=await l.get(s,{params:n});if(console.log("Raw API response status:",c.status),c.data&&c.data.success&&c.data.data){const o=c.data.data;return console.log("Users data received (ApiResponse format):",{count:((e=o.items)==null?void 0:e.length)||0,totalItems:o.totalItems||o.total,currentPage:o.currentPage||o.page}),{users:o.items||o.data,pagination:{total:o.totalItems||o.total,page:o.currentPage||o.page,limit:o.pageSize||o.perPage,totalPages:o.totalPages||o.lastPage}}}else if(c.data&&c.data.data){const o=c.data.data;return console.log("Users data received (direct format):",{count:((t=o.items)==null?void 0:t.length)||0,totalItems:o.totalItems||o.total,currentPage:o.currentPage||o.page}),{users:o.items||[],pagination:{total:o.totalItems||o.total||0,page:o.currentPage||o.page||1,limit:o.pageSize||o.perPage||10,totalPages:o.totalPages||o.lastPage||1}}}throw console.error("Invalid response format:",c.data),new Error("Invalid response format from server")}catch(s){throw console.error("Error fetching users:",s),s.response?console.error("Response error details:",{status:s.response.status,statusText:s.response.statusText,data:s.response.data,headers:s.response.headers}):s.request?console.error("Request was made but no response received:",s.request):console.error("Error setting up request:",s.message),s}},async getUserById(r){try{const e=await l.get(`/api/admin/users/${r}`);if(e.data&&e.data.data)return e.data.data;throw new Error("Invalid response format")}catch(e){throw console.error(`Error fetching user ${r}:`,e),e}},async getDetailedUserById(r){try{console.log(`Fetching detailed user data for ID: ${r}`);const e=await l.get(`/api/admin/users/${r}/detailed`);if(e.data&&e.data.success&&e.data.data)return console.log("Detailed user data received:",e.data.data),e.data.data;throw new Error("Invalid response format")}catch(e){throw console.error(`Error fetching detailed user ${r}:`,e),e}},async createUser(r){try{const e={...r};if(e.role!==void 0&&e.role!==null){const s=u(e.role);s!==-1?(e.role=s,console.log(`Converting role ${r.role} to numeric value ${e.role}`)):(console.log(`Invalid role: ${r.role}, using default role`),e.role=0)}console.log("Sending formatted data to API for new user:",e);const t=await l.post("/api/admin/users",e);if(t.data&&t.data.data)return t.data.data;throw new Error("Invalid response format")}catch(e){throw console.error("Error creating user:",e),e}},async updateUser(r,e){try{const t={...e};if(t.role!==void 0&&t.role!==null){const n=u(t.role);n!==-1?(t.role=n,console.log(`Converting role ${e.role} to numeric value ${t.role}`)):console.log(`Invalid role: ${e.role}, not changing role`)}console.log(`Sending formatted data to API for user ${r}:`,t);const s=await l.put(`/api/admin/users/${r}`,t);if(s.data)return s.data;throw new Error("Invalid response format")}catch(t){throw console.error(`Error updating user ${r}:`,t),t}},async deleteUser(r){try{const e=await l.delete(`/api/admin/users/${r}`);if(e.data)return e.data;throw new Error("Invalid response format")}catch(e){throw console.error(`Error deleting user ${r}:`,e),e}},async changeUserRole(r,e){try{let t=e;if(e!=null){const n=u(e);n!==-1?(t=n,console.log(`Converting role ${e} to numeric value ${t}`)):console.log(`Invalid role: ${e}, not changing role`)}console.log(`Changing role for user ${r} to:`,t);const s=await l.patch(`/api/admin/users/${r}/role`,{role:t});if(s.data)return s.data;throw new Error("Invalid response format")}catch(t){throw console.error(`Error changing role for user ${r}:`,t),t}},async getUserOrders(r,e={}){try{return(await l.get(`/api/admin/users/${r}/orders`,{params:e})).data}catch(t){return console.error(`Error fetching orders for user ${r}:`,t),{orders:[{id:"ORD-1001",total:156.78,status:"Processing",createdAt:new Date(Date.now()-1e3*60*60*24*2)},{id:"ORD-1002",total:89.99,status:"Delivered",createdAt:new Date(Date.now()-1e3*60*60*24*7)},{id:"ORD-1003",total:245.5,status:"Delivered",createdAt:new Date(Date.now()-1e3*60*60*24*14)}],pagination:{total:3,page:1,limit:10,totalPages:1}}}},async resetUserPassword(r){try{return(await l.post(`/api/admin/users/${r}/reset-password`)).data}catch(e){return console.error(`Error resetting password for user ${r}:`,e),{success:!0,message:"Password reset email sent successfully"}}},async getUserStats(){try{return(await l.get("/api/admin/users/stats")).data}catch(r){return console.error("Error fetching user stats:",r),{total:1289,active:1156,inactive:133,byRole:{admin:3,seller:124,buyer:1162},newUsersThisMonth:87}}},async getUserActivity(r){try{return(await l.get(`/api/admin/users/${r}/activity`)).data}catch(e){return console.error(`Error fetching activity for user ${r}:`,e),[{id:"1",type:"login",description:"User logged in",createdAt:new Date(Date.now()-1e3*60*60*2)},{id:"2",type:"profile",description:"User updated profile information",createdAt:new Date(Date.now()-1e3*60*60*24)},{id:"3",type:"order",description:"User placed order #ORD-1001",createdAt:new Date(Date.now()-1e3*60*60*24*2)},{id:"4",type:"payment",description:"User made payment for order #ORD-1001",createdAt:new Date(Date.now()-1e3*60*60*24*2)},{id:"5",type:"login",description:"User logged in",createdAt:new Date(Date.now()-1e3*60*60*24*3)},{id:"6",type:"logout",description:"User logged out",createdAt:new Date(Date.now()-1e3*60*60*24*3)}]}},async exportUsers(r={}){try{return(await l.get("/api/admin/users/export",{params:r,responseType:"blob"})).data}catch(e){console.error("Error exporting users:",e);const t=`ID,First Name,Last Name,Username,Email,Role,Status,Created At
`,s=["1,Admin,User,admin,<EMAIL>,admin,active,2023-01-01","2,John,Seller,seller1,<EMAIL>,seller,active,2023-01-15","3,Jane,Buyer,customer1,<EMAIL>,buyer,active,2023-02-01","4,Robert,Johnson,robert,<EMAIL>,buyer,active,2023-02-15","5,Emily,Davis,emily,<EMAIL>,buyer,inactive,2023-03-01"].join(`
`),n=t+s;return new Blob([n],{type:"text/csv"})}},async sendPasswordResetEmail(r){try{return(await l.post(`/api/admin/users/${r}/send-password-reset`)).data}catch(e){return console.error(`Error sending password reset email for user ${r}:`,e),{success:!0,message:"Password reset email sent successfully"}}}};export{a as R,g as a,y as b,d as c,D as d,h as g,L as u};
