using ClosedXML.Excel;
using Marketplace.Application.Queries.Reports;
using Microsoft.Extensions.Logging;
using System.Text;

namespace Marketplace.Application.Services
{
    public interface IExportService
    {
        Task<byte[]> ExportToExcelAsync(ReportResult reportData, string reportType);
        Task<byte[]> ExportToCsvAsync(ReportResult reportData, string reportType);
    }

    public class ExportService : IExportService
    {
        private readonly ILogger<ExportService> _logger;

        public ExportService(ILogger<ExportService> logger)
        {
            _logger = logger;
        }

        public async Task<byte[]> ExportToExcelAsync(ReportResult reportData, string reportType)
        {
            try
            {
                _logger.LogInformation("Starting Excel export for {ReportType}", reportType);

                using var workbook = new XLWorkbook();
                
                // Create main worksheet
                var worksheet = workbook.Worksheets.Add($"{reportType.ToUpper()} Report");
                
                // Add header information
                AddReportHeader(worksheet, reportData, reportType);
                
                // Add metrics section
                var currentRow = AddMetricsSection(worksheet, reportData.Metrics, 5);
                
                // Add table data
                currentRow = AddTableSection(worksheet, reportData.Table, currentRow + 2);
                
                // Add insights section
                if (reportData.Insights?.Any() == true)
                {
                    AddInsightsSection(worksheet, reportData.Insights, currentRow + 2);
                }
                
                // Auto-fit columns
                worksheet.Columns().AdjustToContents();
                
                // Apply styling
                ApplyWorksheetStyling(worksheet);
                
                // Convert to byte array
                using var stream = new MemoryStream();
                workbook.SaveAs(stream);
                
                _logger.LogInformation("Excel export completed successfully for {ReportType}", reportType);
                return stream.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting to Excel for {ReportType}", reportType);
                throw;
            }
        }

        public async Task<byte[]> ExportToCsvAsync(ReportResult reportData, string reportType)
        {
            try
            {
                _logger.LogInformation("Starting CSV export for {ReportType}", reportType);

                var csv = new StringBuilder();
                
                // Add header
                csv.AppendLine($"{reportType.ToUpper()} Report");
                csv.AppendLine($"Generated: {reportData.Summary?.GeneratedAt:yyyy-MM-dd HH:mm:ss}");
                csv.AppendLine($"Period: {reportData.Summary?.Description}");
                csv.AppendLine();
                
                // Add metrics
                if (reportData.Metrics?.Items?.Any() == true)
                {
                    csv.AppendLine("KEY METRICS");
                    csv.AppendLine("Metric,Value,Type");
                    
                    foreach (var metric in reportData.Metrics.Items)
                    {
                        var value = FormatMetricValue(metric);
                        csv.AppendLine($"\"{metric.Label}\",\"{value}\",\"{metric.Type}\"");
                    }
                    csv.AppendLine();
                }
                
                // Add table data
                if (reportData.Table?.Data?.Any() == true)
                {
                    csv.AppendLine("DETAILED DATA");
                    
                    // Headers
                    if (reportData.Table.Columns?.Any() == true)
                    {
                        var headers = string.Join(",", reportData.Table.Columns.Select(c => $"\"{c.Label}\""));
                        csv.AppendLine(headers);
                    }
                    
                    // Data rows
                    foreach (var row in reportData.Table.Data)
                    {
                        var values = new List<string>();
                        
                        // Convert row object to CSV values based on report type
                        switch (reportType.ToLower())
                        {
                            case "sales":
                                if (row is IDictionary<string, object> dict)
                                {
                                    values.Add($"\"{(dict.ContainsKey("productName") ? dict["productName"] : "")}\"");
                                    values.Add($"\"{(dict.ContainsKey("category") ? dict["category"] : "")}\"");
                                    values.Add($"\"{(dict.ContainsKey("salesCount") ? dict["salesCount"] : 0)}\"");
                                    values.Add($"\"{(dict.ContainsKey("revenue") ? dict["revenue"] : 0):C}\"");
                                    values.Add($"\"{(dict.ContainsKey("averagePrice") ? dict["averagePrice"] : 0):C}\"");
                                }
                                break;

                            case "products":
                                if (row is IDictionary<string, object> dict2)
                                {
                                    values.Add($"\"{(dict2.ContainsKey("name") ? dict2["name"] : "")}\"");
                                    values.Add($"\"{(dict2.ContainsKey("category") ? dict2["category"] : "")}\"");
                                    values.Add($"\"{(dict2.ContainsKey("price") ? dict2["price"] : 0):C}\"");
                                    values.Add($"\"{(dict2.ContainsKey("stock") ? dict2["stock"] : 0)}\"");
                                    values.Add($"\"{(dict2.ContainsKey("status") ? dict2["status"] : "")}\"");
                                }
                                break;
                            
                            default:
                                // Generic handling for other report types
                                if (row is IDictionary<string, object> genericDict)
                                {
                                    foreach (var kvp in genericDict)
                                    {
                                        values.Add($"\"{kvp.Value}\"");
                                    }
                                }
                                break;
                        }
                        
                        if (values.Any())
                        {
                            csv.AppendLine(string.Join(",", values));
                        }
                    }
                }
                
                _logger.LogInformation("CSV export completed successfully for {ReportType}", reportType);
                return Encoding.UTF8.GetBytes(csv.ToString());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting to CSV for {ReportType}", reportType);
                throw;
            }
        }

        private void AddReportHeader(IXLWorksheet worksheet, ReportResult reportData, string reportType)
        {
            // Title
            worksheet.Cell(1, 1).Value = $"{reportType.ToUpper()} REPORT";
            worksheet.Cell(1, 1).Style.Font.FontSize = 16;
            worksheet.Cell(1, 1).Style.Font.Bold = true;
            worksheet.Range(1, 1, 1, 6).Merge();
            
            // Report info
            worksheet.Cell(2, 1).Value = "Generated:";
            worksheet.Cell(2, 2).Value = reportData.Summary?.GeneratedAt.ToString("yyyy-MM-dd HH:mm:ss") ?? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            worksheet.Cell(3, 1).Value = "Period:";
            worksheet.Cell(3, 2).Value = reportData.Summary?.Description ?? "N/A";
            
            if (reportData.Summary?.Metadata != null)
            {
                worksheet.Cell(3, 4).Value = "Total Records:";
                var totalRecords = reportData.Summary.Metadata.ContainsKey("totalRecords") ? reportData.Summary.Metadata["totalRecords"] : 0;
                worksheet.Cell(3, 5).Value = Convert.ToInt32(totalRecords);
            }
        }

        private int AddMetricsSection(IXLWorksheet worksheet, ReportMetrics? metrics, int startRow)
        {
            if (metrics?.Items?.Any() != true)
                return startRow;

            var currentRow = startRow;
            
            // Section title
            worksheet.Cell(currentRow, 1).Value = "KEY METRICS";
            worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
            worksheet.Cell(currentRow, 1).Style.Font.FontSize = 14;
            currentRow++;
            
            // Headers
            worksheet.Cell(currentRow, 1).Value = "Metric";
            worksheet.Cell(currentRow, 2).Value = "Value";
            worksheet.Cell(currentRow, 3).Value = "Type";
            worksheet.Cell(currentRow, 4).Value = "Change %";
            
            // Style headers
            var headerRange = worksheet.Range(currentRow, 1, currentRow, 4);
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.BackgroundColor = XLColor.LightGray;
            headerRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            
            currentRow++;
            
            // Data
            foreach (var metric in metrics.Items)
            {
                worksheet.Cell(currentRow, 1).Value = metric.Label;
                worksheet.Cell(currentRow, 2).Value = FormatMetricValue(metric);
                worksheet.Cell(currentRow, 3).Value = metric.Type;
                
                if (metric.ChangePercentage.HasValue)
                {
                    worksheet.Cell(currentRow, 4).Value = $"{metric.ChangePercentage:F1}%";
                    
                    // Color code the change
                    if (metric.ChangePercentage > 0)
                        worksheet.Cell(currentRow, 4).Style.Font.FontColor = XLColor.Green;
                    else if (metric.ChangePercentage < 0)
                        worksheet.Cell(currentRow, 4).Style.Font.FontColor = XLColor.Red;
                }
                
                currentRow++;
            }
            
            // Add border to metrics table
            var metricsRange = worksheet.Range(startRow + 1, 1, currentRow - 1, 4);
            metricsRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            metricsRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
            
            return currentRow;
        }

        private int AddTableSection(IXLWorksheet worksheet, ReportTable? table, int startRow)
        {
            if (table?.Data?.Any() != true)
                return startRow;

            var currentRow = startRow;
            
            // Section title
            worksheet.Cell(currentRow, 1).Value = table.Title ?? "DETAILED DATA";
            worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
            worksheet.Cell(currentRow, 1).Style.Font.FontSize = 14;
            currentRow++;
            
            // Headers
            if (table.Columns?.Any() == true)
            {
                for (int i = 0; i < table.Columns.Count; i++)
                {
                    worksheet.Cell(currentRow, i + 1).Value = table.Columns[i].Label;
                }

                // Style headers
                var headerRange = worksheet.Range(currentRow, 1, currentRow, table.Columns.Count);
                headerRange.Style.Font.Bold = true;
                headerRange.Style.Fill.BackgroundColor = XLColor.LightBlue;
                headerRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;

                currentRow++;
            }
            
            // Data rows (limit to first 1000 rows for performance)
            var dataToShow = table.Data.Take(1000);
            foreach (var row in dataToShow)
            {
                if (row is IDictionary<string, object> dict)
                {
                    int col = 1;
                    foreach (var kvp in dict)
                    {
                        var cellValue = kvp.Value?.ToString() ?? "";
                        
                        // Try to parse numbers for better formatting
                        if (decimal.TryParse(cellValue, out decimal numValue))
                        {
                            worksheet.Cell(currentRow, col).Value = numValue;
                            
                            // Format currency if it looks like money
                            if (kvp.Key.ToLower().Contains("price") || 
                                kvp.Key.ToLower().Contains("revenue") || 
                                kvp.Key.ToLower().Contains("total"))
                            {
                                worksheet.Cell(currentRow, col).Style.NumberFormat.Format = "$#,##0.00";
                            }
                        }
                        else
                        {
                            worksheet.Cell(currentRow, col).Value = cellValue;
                        }
                        
                        col++;
                    }
                }
                currentRow++;
            }
            
            // Add border to table
            if (table.Columns?.Any() == true)
            {
                var tableRange = worksheet.Range(startRow + 1, 1, currentRow - 1, table.Columns.Count);
                tableRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                tableRange.Style.Border.InsideBorder = XLBorderStyleValues.Hair;
            }
            
            return currentRow;
        }

        private int AddInsightsSection(IXLWorksheet worksheet, List<ReportInsight> insights, int startRow)
        {
            var currentRow = startRow;
            
            // Section title
            worksheet.Cell(currentRow, 1).Value = "INSIGHTS & RECOMMENDATIONS";
            worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
            worksheet.Cell(currentRow, 1).Style.Font.FontSize = 14;
            currentRow++;
            
            foreach (var insight in insights)
            {
                // Insight title
                worksheet.Cell(currentRow, 1).Value = insight.Title;
                worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
                currentRow++;
                
                // Insight description
                worksheet.Cell(currentRow, 1).Value = insight.Description;
                worksheet.Range(currentRow, 1, currentRow, 6).Merge();
                currentRow++;
                
                // Add insight type and priority info
                worksheet.Cell(currentRow, 1).Value = $"Type: {insight.Type} | Priority: {insight.Priority}";
                worksheet.Cell(currentRow, 1).Style.Font.Italic = true;
                worksheet.Range(currentRow, 1, currentRow, 6).Merge();
                currentRow++;
                
                currentRow++; // Add space between insights
            }
            
            return currentRow;
        }

        private void ApplyWorksheetStyling(IXLWorksheet worksheet)
        {
            // Set default font
            worksheet.Style.Font.FontName = "Calibri";
            worksheet.Style.Font.FontSize = 11;
            
            // Auto-fit columns with max width
            foreach (var column in worksheet.Columns())
            {
                column.AdjustToContents();
                if (column.Width > 50)
                    column.Width = 50;
            }
            
            // Freeze header rows
            worksheet.SheetView.FreezeRows(4);
        }

        private string FormatMetricValue(MetricItem metric)
        {
            if (metric.Value == null) return "N/A";
            
            return metric.Type?.ToLower() switch
            {
                "currency" => $"${metric.Value:N2}",
                "percentage" => $"{metric.Value:F1}%",
                "number" => Convert.ToDecimal(metric.Value) >= 1000 ? $"{metric.Value:N0}" : metric.Value.ToString(),
                _ => metric.Value.ToString()
            };
        }
    }
}
