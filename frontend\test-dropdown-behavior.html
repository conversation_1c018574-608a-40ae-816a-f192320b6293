<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dropdown Behavior</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div id="app" class="container mt-4">
        <h1 class="title">Test Dropdown Behavior</h1>
        
        <div class="columns">
            <div class="column">
                <h2 class="subtitle">Companies Test</h2>
                <div class="field">
                    <label class="label">Search Query: "{{ companySearchQuery }}"</label>
                    <label class="label">Selected Company: {{ selectedCompanyId }}</label>
                    <label class="label">Dropdown Open: {{ isCompanyDropdownOpen }}</label>
                    <label class="label">Total Companies: {{ companies.length }}</label>
                    <label class="label">Filtered Companies: {{ filteredCompanies.length }}</label>
                </div>
                
                <div class="field">
                    <div class="control">
                        <div class="dropdown" :class="{ 'is-active': isCompanyDropdownOpen }">
                            <div class="dropdown-trigger">
                                <div class="field has-addons">
                                    <div class="control is-expanded">
                                        <input 
                                            class="input" 
                                            type="text" 
                                            placeholder="Search companies..."
                                            v-model="companySearchQuery"
                                            @focus="isCompanyDropdownOpen = true"
                                            @input="isCompanyDropdownOpen = true"
                                        >
                                    </div>
                                    <div class="control">
                                        <button 
                                            class="button" 
                                            type="button"
                                            @click="toggleCompanyDropdown"
                                        >
                                            <span class="icon">
                                                <i class="fas fa-chevron-down" :class="{ 'fa-rotate-180': isCompanyDropdownOpen }"></i>
                                            </span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="dropdown-menu" v-show="isCompanyDropdownOpen">
                                <div class="dropdown-content">
                                    <div v-if="loading" class="dropdown-item">
                                        <span class="icon">
                                            <i class="fas fa-spinner fa-spin"></i>
                                        </span>
                                        Loading companies...
                                    </div>
                                    <div v-else-if="filteredCompanies.length === 0" class="dropdown-item">
                                        No companies found
                                    </div>
                                    <a 
                                        v-else
                                        v-for="company in filteredCompanies" 
                                        :key="company.id"
                                        class="dropdown-item"
                                        @click="selectCompany(company)"
                                    >
                                        <div class="company-item">
                                            <div class="company-name">{{ company.name }}</div>
                                            <div class="company-details is-size-7 has-text-grey">
                                                {{ company.contactEmail }} • {{ company.addressCity }}
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="field">
                    <button @click="clearCompanySearch" class="button is-warning">Clear Search</button>
                    <button @click="loadCompanies" class="button is-info">Reload Companies</button>
                </div>
            </div>
            
            <div class="column">
                <h2 class="subtitle">Debug Info</h2>
                <div v-for="log in debugLogs" :key="log.id" class="notification is-small" :class="log.type">
                    <strong>{{ log.timestamp }}:</strong> {{ log.message }}
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, computed } = Vue;

        createApp({
            setup() {
                const companies = ref([]);
                const companySearchQuery = ref('');
                const selectedCompanyId = ref('');
                const isCompanyDropdownOpen = ref(false);
                const loading = ref(false);
                const debugLogs = ref([]);
                let logId = 0;

                const addLog = (message, type = 'is-info') => {
                    debugLogs.value.push({
                        id: ++logId,
                        message,
                        type,
                        timestamp: new Date().toLocaleTimeString()
                    });
                    
                    // Keep only last 10 logs
                    if (debugLogs.value.length > 10) {
                        debugLogs.value.shift();
                    }
                };

                const filteredCompanies = computed(() => {
                    const companiesArray = Array.isArray(companies.value) ? companies.value : [];
                    
                    addLog(`Filtering: query="${companySearchQuery.value}", total=${companiesArray.length}`);
                    
                    if (!companySearchQuery.value.trim()) {
                        addLog(`No search query, showing all ${companiesArray.length} companies`);
                        return companiesArray.slice(0, 50);
                    }

                    const query = companySearchQuery.value.toLowerCase().trim();
                    const filtered = companiesArray.filter(company => {
                        if (!company || typeof company !== 'object') return false;
                        
                        return (
                            (company.name && company.name.toLowerCase().includes(query)) ||
                            (company.contactEmail && company.contactEmail.toLowerCase().includes(query)) ||
                            (company.addressCity && company.addressCity.toLowerCase().includes(query)) ||
                            (company.slug && company.slug.toLowerCase().includes(query))
                        );
                    }).slice(0, 50);
                    
                    addLog(`Filtered to ${filtered.length} companies`);
                    return filtered;
                });

                const loadCompanies = async () => {
                    try {
                        loading.value = true;
                        addLog('Loading companies...', 'is-info');
                        
                        const response = await axios.get('http://localhost:5296/api/companies?pageSize=200');
                        addLog(`API response received: ${response.data.data.length} companies`, 'is-success');
                        
                        companies.value = response.data.data || [];
                        addLog(`Companies loaded: ${companies.value.length}`, 'is-success');
                        
                    } catch (error) {
                        addLog(`Error loading companies: ${error.message}`, 'is-danger');
                        companies.value = [];
                    } finally {
                        loading.value = false;
                    }
                };

                const selectCompany = (company) => {
                    selectedCompanyId.value = company.id;
                    companySearchQuery.value = company.name;
                    isCompanyDropdownOpen.value = false;
                    addLog(`Company selected: ${company.name}`, 'is-success');
                };

                const toggleCompanyDropdown = () => {
                    if (!isCompanyDropdownOpen.value) {
                        // Clear search when opening dropdown to show all companies
                        companySearchQuery.value = '';
                        addLog('Dropdown opened, search cleared', 'is-info');
                    }
                    isCompanyDropdownOpen.value = !isCompanyDropdownOpen.value;
                    addLog(`Dropdown ${isCompanyDropdownOpen.value ? 'opened' : 'closed'}`, 'is-info');
                };

                const clearCompanySearch = () => {
                    companySearchQuery.value = '';
                    addLog('Search cleared manually', 'is-warning');
                };

                // Load companies on mount
                Vue.onMounted(() => {
                    loadCompanies();
                });

                return {
                    companies,
                    companySearchQuery,
                    selectedCompanyId,
                    isCompanyDropdownOpen,
                    loading,
                    debugLogs,
                    filteredCompanies,
                    loadCompanies,
                    selectCompany,
                    toggleCompanyDropdown,
                    clearCompanySearch
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
