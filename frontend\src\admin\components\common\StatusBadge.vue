<template>
  <span class="tag" :class="statusClass">
    {{ formattedStatus }}
  </span>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  status: {
    type: [String, Number],
    required: true
  },
  label: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'order', 'payment', 'user', 'product'].includes(value)
  }
});

// Format status text
const formattedStatus = computed(() => {
  if (props.status === null || props.status === undefined) return 'Unknown';

  if (props.label) return props.label;

  // Handle order status (matching backend enums)
  if (props.type === 'order') {
    const statusMap = {
      // Numeric values (backend enum values)
      0: 'Processing',  // OrderStatus.Processing = 0
      1: 'Pending',     // OrderStatus.Pending = 1
      2: 'Shipped',     // OrderStatus.Shipped = 2
      3: 'Delivered',   // OrderStatus.Delivered = 3
      4: 'Cancelled',   // OrderStatus.Cancelled = 4
      // String values (for backward compatibility)
      'Processing': 'Processing',
      'Pending': 'Pending',
      'Shipped': 'Shipped',
      'Delivered': 'Delivered',
      'Cancelled': 'Cancelled'
    };
    return statusMap[props.status] || props.status.toString();
  }

  // Handle payment status (matching backend enums)
  if (props.type === 'payment') {
    const statusMap = {
      // Numeric values (backend enum values)
      0: 'Pending',     // PaymentStatus.Pending = 0
      1: 'Completed',   // PaymentStatus.Completed = 1
      2: 'Refunded',    // PaymentStatus.Refunded = 2
      3: 'Failed',      // PaymentStatus.Failed = 3
      // String values (for backward compatibility)
      'Pending': 'Pending',
      'Completed': 'Completed',
      'Refunded': 'Refunded',
      'Failed': 'Failed'
    };
    return statusMap[props.status] || props.status.toString();
  }

  // Handle product status
  if (props.type === 'product') {
    const statusMap = {
      0: 'Pending',
      1: 'Approved',
      2: 'Rejected',
      'pending': 'Pending',
      'approved': 'Approved',
      'rejected': 'Rejected'
    };
    return statusMap[props.status] || props.status.toString();
  }

  // Handle other types
  return props.status.toString();
});

// Determine status class based on status and type
const statusClass = computed(() => {
  if (props.status === null || props.status === undefined) return 'is-light';

  // Order status classes (matching backend enums)
  if (props.type === 'order') {
    const classMap = {
      // Numeric values (backend enum values)
      0: 'is-info',     // Processing
      1: 'is-warning',  // Pending
      2: 'is-primary',  // Shipped
      3: 'is-success',  // Delivered
      4: 'is-danger',   // Cancelled
      // String values (for backward compatibility)
      'Processing': 'is-info',
      'Pending': 'is-warning',
      'Shipped': 'is-primary',
      'Delivered': 'is-success',
      'Cancelled': 'is-danger'
    };
    return classMap[props.status] || 'is-light';
  }

  // Payment status classes (matching backend enums)
  if (props.type === 'payment') {
    const classMap = {
      // Numeric values (backend enum values)
      0: 'is-warning',  // Pending
      1: 'is-success',  // Completed
      2: 'is-info',     // Refunded
      3: 'is-danger',   // Failed
      // String values (for backward compatibility)
      'Pending': 'is-warning',
      'Completed': 'is-success',
      'Refunded': 'is-info',
      'Failed': 'is-danger'
    };
    return classMap[props.status] || 'is-light';
  }

  // Product status classes
  if (props.type === 'product') {
    const classMap = {
      0: 'is-warning',  // Pending
      1: 'is-success', // Approved
      2: 'is-danger',  // Rejected
      'pending': 'is-warning',
      'approved': 'is-success',
      'rejected': 'is-danger'
    };
    return classMap[props.status] || 'is-light';
  }

  // Default status classes
  const status = props.status.toString().toLowerCase();
  switch (status) {
    case 'active':
    case 'approved':
    case 'completed':
    case '1':
      return 'is-success';
    case 'inactive':
    case 'pending':
    case '0':
      return 'is-warning';
    case 'rejected':
    case 'cancelled':
    case '2':
      return 'is-danger';
    default:
      return 'is-light';
  }
});
</script>

<style scoped>
.tag {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-full);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all var(--admin-transition-fast);
}

.tag.is-success {
  background: var(--admin-success-bg);
  color: var(--admin-success-dark);
  border: 1px solid var(--admin-success-light);
}

.tag.is-warning {
  background: var(--admin-warning-bg);
  color: var(--admin-warning-dark);
  border: 1px solid var(--admin-warning-light);
}

.tag.is-danger {
  background: var(--admin-danger-bg);
  color: var(--admin-danger-dark);
  border: 1px solid var(--admin-danger-light);
}

.tag.is-info {
  background: var(--admin-info-bg);
  color: var(--admin-info-dark);
  border: 1px solid var(--admin-info-light);
}

.tag.is-primary {
  background: var(--admin-primary-bg);
  color: var(--admin-primary-dark);
  border: 1px solid var(--admin-primary-light);
}

.tag.is-light {
  background: var(--admin-gray-100);
  color: var(--admin-gray-600);
  border: 1px solid var(--admin-gray-200);
}

.tag.is-success.is-light {
  background-color: #effaf3;
  color: #257942;
}

.tag.is-warning.is-light {
  background-color: #fffbeb;
  color: #947600;
}

.tag.is-danger.is-light {
  background-color: #feecf0;
  color: #cc0f35;
}

.tag.is-info.is-light {
  background-color: #eef6fc;
  color: #1d72aa;
}

.tag.is-primary.is-light {
  background-color: #fff0e6;
  color: #cc5f00;
}
</style>
