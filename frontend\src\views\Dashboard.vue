<template>
  <div class="dashboard-container">
    <div class="notification is-info is-light" v-if="loading">
      <p>Loading dashboard data...</p>
    </div>

    <div class="notification is-danger is-light" v-if="error">
      <p>{{ error }}</p>
      <button class="button is-small is-danger mt-2" @click="fetchDashboardData">Retry</button>
    </div>

    <!-- Top Section -->
    <div class="dashboard-top-section">
      <!-- Welcome Card -->
      <div class="welcome-card">
        <div class="columns is-vcentered">
          <div class="column is-8">
            <div class="user-avatar-large">{{ currentUser.username.charAt(0).toUpperCase() }}</div>
            <div class="welcome-text">
              <h3 class="welcome-title">Welcome</h3>
              <p class="welcome-name">{{ currentUser.username }}</p>
            </div>
          </div>
          <div class="column is-4 has-text-right">
            <div class="brand-info">
              <h3 class="brand-name">Marketplace</h3>
              <p class="brand-version">v1.0.0</p>
            </div>
            <div class="action-buttons">
              <a href="#" class="button is-small mr-2">
                <span class="icon"><i class="fas fa-book"></i></span>
                <span>Documentation</span>
              </a>
              <a href="#" class="button is-small">
                <span class="icon"><i class="fab fa-github"></i></span>
                <span>GitHub</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Filter Controls -->
      <div class="filter-controls">
        <div class="columns is-vcentered">
          <div class="column is-4">
            <label class="filter-label">Business customers only</label>
            <div class="select is-fullwidth">
              <select>
                <option value="">-</option>
                <option value="yes">Yes</option>
                <option value="no">No</option>
              </select>
            </div>
          </div>
          <div class="column is-4">
            <label class="filter-label">Start date</label>
            <input type="date" class="date-picker" value="2023-01-01">
          </div>
          <div class="column is-4">
            <label class="filter-label">End date</label>
            <input type="date" class="date-picker" value="2023-04-30">
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="columns is-multiline">
      <div class="column is-4-desktop is-6-tablet">
        <div class="card stat-card">
          <div class="stat-title">Revenue</div>
          <div class="stat-value">${{ formatCurrency(192.10) }}k</div>
          <div class="stat-trend trend-up">
            <span class="icon"><i class="fas fa-arrow-up"></i></span>
            <span>3% increase</span>
          </div>
          <div class="chart-container">
            <canvas ref="revenueChart"></canvas>
          </div>
        </div>
      </div>

      <div class="column is-4-desktop is-6-tablet">
        <div class="card stat-card">
          <div class="stat-title">New customers</div>
          <div class="stat-value">1.34k</div>
          <div class="stat-trend trend-down">
            <span class="icon"><i class="fas fa-arrow-down"></i></span>
            <span>3% decrease</span>
          </div>
          <div class="chart-container">
            <canvas ref="customersChart"></canvas>
          </div>
        </div>
      </div>

      <div class="column is-4-desktop is-6-tablet">
        <div class="card stat-card">
          <div class="stat-title">New orders</div>
          <div class="stat-value">3.54k</div>
          <div class="stat-trend trend-up">
            <span class="icon"><i class="fas fa-arrow-up"></i></span>
            <span>7% increase</span>
          </div>
          <div class="chart-container">
            <canvas ref="ordersChart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts -->
    <div class="columns is-multiline">
      <div class="column is-6-desktop is-12-tablet">
        <div class="card chart-card">
          <div class="chart-header">
            <h3 class="chart-title">Orders per month</h3>
          </div>
          <div class="chart-body">
            <canvas ref="ordersMonthChart" height="250"></canvas>
          </div>
        </div>
      </div>

      <div class="column is-6-desktop is-12-tablet">
        <div class="card chart-card">
          <div class="chart-header">
            <h3 class="chart-title">Total customers</h3>
          </div>
          <div class="chart-body">
            <canvas ref="totalCustomersChart" height="250"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Data Tables Section -->
    <div class="columns is-multiline">
      <!-- Recent Users -->
      <div class="column is-6-desktop is-12-tablet">
        <div class="card data-card">
          <div class="card-header">
            <div class="card-header-title">
              <h3 class="title is-5">Recent Users</h3>
            </div>
            <div class="card-header-actions">
              <router-link to="/admin/users" class="button is-small">
                <span class="icon"><i class="fas fa-external-link-alt"></i></span>
                <span>View All</span>
              </router-link>
            </div>
          </div>
          <div class="card-content">
            <div class="table-container">
              <table class="table is-fullwidth">
                <thead>
                  <tr>
                    <th>Username</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Registered</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(user, index) in recentUsers.slice(0, 3)" :key="index">
                    <td>{{ user.username }}</td>
                    <td>{{ user.email }}</td>
                    <td>
                      <span class="tag" :class="getRoleClass(user.role)">{{ user.role }}</span>
                    </td>
                    <td>{{ formatDate(user.createdAt) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Orders -->
      <div class="column is-6-desktop is-12-tablet">
        <div class="card data-card">
          <div class="card-header">
            <div class="card-header-title">
              <h3 class="title is-5">Recent Orders</h3>
            </div>
            <div class="card-header-actions">
              <router-link to="/admin/orders" class="button is-small">
                <span class="icon"><i class="fas fa-external-link-alt"></i></span>
                <span>View All</span>
              </router-link>
            </div>
          </div>
          <div class="card-content">
            <div class="table-container">
              <table class="table is-fullwidth">
                <thead>
                  <tr>
                    <th>Order ID</th>
                    <th>Customer</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Date</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(order, index) in recentOrders.slice(0, 3)" :key="index">
                    <td>{{ order.id }}</td>
                    <td>{{ order.customer }}</td>
                    <td>${{ order.total.toFixed(2) }}</td>
                    <td>
                      <span class="tag" :class="getStatusClass(order.status)">{{ order.status }}</span>
                    </td>
                    <td>{{ formatDate(order.createdAt) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useStore } from 'vuex';
import { Chart, Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, Filler } from 'chart.js';
import dashboardService from '../services/dashboard.service';
import userService from '../services/user.service';
import productService from '../services/product.service';
import categoryService from '../services/category.service';
import orderService from '../services/order.service';

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, Filler);

// Store and user
const store = useStore();
const currentUser = computed(() => store.getters['auth/user']);

// Chart references
const revenueChart = ref(null);
const customersChart = ref(null);
const ordersChart = ref(null);
const ordersMonthChart = ref(null);
const totalCustomersChart = ref(null);

// State
const loading = ref(false);
const error = ref(null);
const stats = ref({
  products: 0,
  categories: 0,
  orders: 0,
  users: 0
});
const recentUsers = ref([]);
const recentOrders = ref([]);
const recentActivity = ref([
  { date: new Date(2023, 10, 15, 14, 30), user: 'John Doe', action: 'Added new product' },
  { date: new Date(2023, 10, 15, 13, 45), user: 'Jane Smith', action: 'Updated category' },
  { date: new Date(2023, 10, 15, 12, 20), user: 'Mike Johnson', action: 'Processed order #1234' },
  { date: new Date(2023, 10, 15, 11, 10), user: 'Sarah Williams', action: 'Approved seller application' },
  { date: new Date(2023, 10, 15, 10, 5), user: 'David Brown', action: 'Added new user' }
]);

// Fetch dashboard data
const fetchDashboardData = async () => {
  loading.value = true;
  error.value = null;

  try {
    // Get dashboard stats - our modified service will return mock data if API fails
    const statsResponse = await dashboardService.getStats();
    stats.value = statsResponse.data;

    // Get recent users - our modified service will return mock data if API fails
    const usersResponse = await dashboardService.getRecentUsers();
    recentUsers.value = usersResponse.data;

    // Get recent orders - our modified service will return mock data if API fails
    const ordersResponse = await dashboardService.getRecentOrders();
    recentOrders.value = ordersResponse.data;

    // Get recent activities - our modified service will return mock data if API fails
    const activitiesResponse = await dashboardService.getRecentActivities();
    recentActivity.value = activitiesResponse.data;

    // Get sales stats for charts
    const salesResponse = await dashboardService.getSalesStats();
    initCharts(salesResponse.data);

  } catch (err) {
    console.error('Error fetching dashboard data:', err);
    error.value = 'Failed to load dashboard data. Please try again.';
  } finally {
    loading.value = false;
  }
};

// Initialize charts
const initCharts = (salesData) => {
  // Wait for DOM to be ready
  setTimeout(() => {
    try {
      // Small line charts for stat cards
      if (revenueChart.value) {
        initSmallChart(revenueChart.value, [65, 59, 80, 81, 56, 55, 40, 45, 60, 70, 85, 90], '#10b981');
      }

      if (customersChart.value) {
        initSmallChart(customersChart.value, [28, 48, 40, 19, 86, 27, 90, 35, 42, 50, 60, 65], '#3b82f6');
      }

      if (ordersChart.value) {
        initSmallChart(ordersChart.value, [35, 45, 55, 50, 60, 65, 70, 75, 80, 85, 90, 95], '#f59e0b');
      }

      // Orders per month chart
      if (ordersMonthChart.value) {
        new Chart(ordersMonthChart.value, {
          type: 'line',
          data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
              label: 'Orders',
              data: [2500, 3200, 2800, 4500, 3800, 4200, 3500, 4800, 5200, 6500, 5800, 6200],
              borderColor: '#f59e0b',
              backgroundColor: 'rgba(245, 158, 11, 0.1)',
              borderWidth: 2,
              tension: 0.4,
              fill: true
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                grid: {
                  color: 'rgba(255, 255, 255, 0.05)'
                },
                ticks: {
                  color: 'rgba(255, 255, 255, 0.7)'
                }
              },
              x: {
                grid: {
                  color: 'rgba(255, 255, 255, 0.05)'
                },
                ticks: {
                  color: 'rgba(255, 255, 255, 0.7)'
                }
              }
            }
          }
        });
      }

      // Total customers chart
      if (totalCustomersChart.value) {
        new Chart(totalCustomersChart.value, {
          type: 'line',
          data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
              label: 'Customers',
              data: [4000, 4500, 5000, 5500, 6000, 6500, 7000, 7500, 8000, 9000, 10000, 11000],
              borderColor: '#3b82f6',
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              borderWidth: 2,
              tension: 0.4,
              fill: true
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                grid: {
                  color: 'rgba(255, 255, 255, 0.05)'
                },
                ticks: {
                  color: 'rgba(255, 255, 255, 0.7)'
                }
              },
              x: {
                grid: {
                  color: 'rgba(255, 255, 255, 0.05)'
                },
                ticks: {
                  color: 'rgba(255, 255, 255, 0.7)'
                }
              }
            }
          }
        });
      }
    } catch (error) {
      console.error('Error initializing charts:', error);
    }
  }, 100); // Small delay to ensure DOM elements are ready
};

// Initialize small chart for stat cards
const initSmallChart = (canvas, data, color) => {
  if (!canvas) return;

  try {
    // Clear any existing chart
    const existingChart = Chart.getChart(canvas);
    if (existingChart) {
      existingChart.destroy();
    }

    // Create new chart
    new Chart(canvas, {
      type: 'line',
      data: {
        labels: Array(data.length).fill(''),
        datasets: [{
          data: data,
          borderColor: color,
          backgroundColor: 'transparent',
          borderWidth: 2,
          pointRadius: 0,
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            enabled: false
          }
        },
        scales: {
          x: {
            display: false
          },
          y: {
            display: false
          }
        },
        elements: {
          line: {
            borderWidth: 2
          },
          point: {
            radius: 0
          }
        }
      }
    });
  } catch (error) {
    console.error('Error creating small chart:', error);
  }
};

// Format date
const formatDate = (date) => {
  if (!date) return 'N/A';

  const dateObj = date instanceof Date ? date : new Date(date);

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(dateObj);
};

// Format currency
const formatCurrency = (value) => {
  return value.toFixed(2);
};

// Get role class
const getRoleClass = (role) => {
  switch (role?.toLowerCase()) {
    case 'admin':
      return 'is-danger';
    case 'moderator':
      return 'is-warning';
    case 'seller':
      return 'is-success';
    case 'buyer':
    default:
      return 'is-info';
  }
};

// Get status class
const getStatusClass = (status) => {
  switch (status?.toLowerCase()) {
    case 'completed':
      return 'is-success';
    case 'processing':
      return 'is-info';
    case 'pending':
      return 'is-warning';
    case 'cancelled':
      return 'is-danger';
    default:
      return 'is-light';
  }
};

// Fetch data when component is mounted
onMounted(() => {
  fetchDashboardData();
});
</script>

<style scoped>
/* Dashboard Container */
.dashboard-container {
  width: 100%;
  max-width: 100%;
  padding: 0;
}

/* Top Section */
.dashboard-top-section {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

/* General styles */
.mb-4 {
  margin-bottom: 1.5rem !important;
}

.mt-4 {
  margin-top: 1.5rem !important;
}

.mr-2 {
  margin-right: 0.5rem !important;
}

/* Filter controls */
.filter-controls {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
}

.select {
  width: 100%;
}

.select select {
  width: 100%;
  background-color: var(--darker-bg);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 0.5rem 1rem;
  height: 2.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select select:hover {
  border-color: var(--accent-color);
}

.date-picker {
  width: 100%;
  background-color: var(--darker-bg);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 0.5rem 1rem;
  height: 2.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.date-picker:hover {
  border-color: var(--accent-color);
}

/* Welcome card */
.welcome-card {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-avatar-large {
  width: 48px;
  height: 48px;
  background-color: var(--accent-color);
  color: white;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.25rem;
  margin-right: 1rem;
  float: left;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.welcome-text {
  display: inline-block;
}

.welcome-title {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.welcome-name {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

.brand-info {
  margin-bottom: 1rem;
}

.brand-name {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.brand-version {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
}

/* Stat cards */
.card {
  background-color: var(--card-bg);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.stat-title {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.stat-value {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.stat-trend {
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-weight: 500;
}

.trend-up {
  color: var(--success-color);
}

.trend-down {
  color: var(--danger-color);
}

.chart-container {
  width: 100%;
  height: 100px;
  margin-top: auto;
}

/* Chart cards */
.chart-card {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chart-header {
  margin-bottom: 1rem;
}

.chart-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.chart-body {
  flex: 1;
  min-height: 250px;
}

/* Data cards */
.data-card {
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  background-color: var(--card-bg);
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
}

.card-header-title {
  margin: 0;
}

.card-header-title h3 {
  margin: 0;
  color: var(--text-primary);
}

.card-header-actions {
  display: flex;
  align-items: center;
}

.card-content {
  padding: 1.5rem;
  background-color: var(--card-bg);
  flex: 1;
  overflow: auto;
}

/* Table container */
.table-container {
  width: 100%;
  overflow-x: auto;
}

/* Table styles */
.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background-color: transparent;
}

.table th {
  font-weight: 600;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 0.75rem;
  font-size: 0.875rem;
  text-align: left;
  position: sticky;
  top: 0;
  background-color: var(--card-bg);
  z-index: 1;
}

.table td {
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.03);
}

/* Notification styles */
.notification {
  border-radius: 8px;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.notification.is-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--text-primary);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.notification.is-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--text-primary);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Button styles */
.button {
  background-color: var(--card-bg);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.button:hover {
  background-color: var(--darker-bg);
  transform: translateY(-1px);
}

.button.is-small {
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
}

/* Tag styles */
.tag {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
}

/* Columns adjustments */
.columns {
  margin-left: -0.75rem;
  margin-right: -0.75rem;
  margin-top: -0.75rem;
  margin-bottom: 1.5rem;
}

.columns:last-child {
  margin-bottom: -0.75rem;
}

.column {
  padding: 0.75rem;
}

/* Responsive adjustments */
@media screen and (min-width: 1408px) {
  .container {
    max-width: 100%;
  }
}

@media screen and (max-width: 1023px) {
  .stat-value {
    font-size: 1.5rem;
  }
}

@media screen and (max-width: 768px) {
  .dashboard-top-section {
    grid-template-columns: 1fr;
  }

  .welcome-card .columns {
    flex-direction: column;
  }

  .welcome-card .column.is-4 {
    margin-top: 1rem;
    text-align: left;
  }

  .action-buttons {
    justify-content: flex-start;
  }

  .columns {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
  }

  .column {
    padding: 0.5rem;
  }
}
</style>
