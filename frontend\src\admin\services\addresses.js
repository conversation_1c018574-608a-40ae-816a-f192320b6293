import api from '@/services/api';

export const addressesService = {
  async getAddresses(params = {}) {
    try {
      const response = await api.get('/api/admin/addresses', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching addresses:', error);
      throw new Error(error.response?.data?.message || 'Failed to load addresses');
    }
  },

  async getAddressById(id) {
    try {
      const response = await api.get(`/api/admin/addresses/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching address:', error);
      throw new Error(error.response?.data?.message || 'Failed to load address details');
    }
  },

  async getAddressesByUserId(userId, params = {}) {
    try {
      const response = await api.get(`/api/admin/addresses/user/${userId}`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching user addresses:', error);
      throw new Error(error.response?.data?.message || 'Failed to load user addresses');
    }
  },

  async createAddress(data) {
    try {
      const response = await api.post('/api/admin/addresses', data);
      return response.data;
    } catch (error) {
      console.error('Error creating address:', error);
      throw new Error(error.response?.data?.message || 'Failed to create address');
    }
  },

  async updateAddress(id, data) {
    try {
      const response = await api.put(`/api/admin/addresses/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating address:', error);
      throw new Error(error.response?.data?.message || 'Failed to update address');
    }
  },

  async deleteAddress(id) {
    try {
      const response = await api.delete(`/api/admin/addresses/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting address:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete address');
    }
  },

  async getAddressStats() {
    try {
      const response = await api.get('/api/admin/addresses/stats');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching address stats:', error);
      throw new Error(error.response?.data?.message || 'Failed to load address statistics');
    }
  }
};
