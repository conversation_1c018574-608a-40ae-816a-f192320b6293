/**
 * Centralized Mock Data Service
 * Single source of truth for all development and testing data
 */

class MockDataService {
  constructor() {
    this.baseData = this.initializeBaseData()
  }

  /**
   * Initialize base mock data structure
   */
  initializeBaseData() {
    return {
      financial: {
        type: 'financial',
        metrics: {
          items: [
            { key: 'revenue', label: 'Total Revenue', value: 125000, type: 'currency', icon: 'fas fa-dollar-sign', trend: 'up', changePercentage: 12.5 },
            { key: 'expenses', label: 'Total Expenses', value: 85000, type: 'currency', icon: 'fas fa-credit-card', trend: 'down', changePercentage: -5.2 },
            { key: 'profit', label: 'Net Profit', value: 40000, type: 'currency', icon: 'fas fa-chart-line', trend: 'up', changePercentage: 18.7 },
            { key: 'margin', label: 'Profit Margin', value: 32, type: 'percentage', icon: 'fas fa-percentage', trend: 'up', changePercentage: 3.2 }
          ]
        },
        charts: {
          revenue: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            data: [10000, 15000, 20000, 18000, 22000, 25000]
          },
          expenses: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            data: [8000, 12000, 15000, 14000, 16000, 18000]
          },
          paymentMethods: {
            data: [
              { label: 'Credit Card', value: 45000, percentage: 45 },
              { label: 'PayPal', value: 30000, percentage: 30 },
              { label: 'Bank Transfer', value: 25000, percentage: 25 }
            ]
          }
        },
        insights: [
          { id: 1, type: 'success', icon: 'fas fa-arrow-up', title: 'Revenue Growth', description: 'Revenue increased by 12.5% compared to last period' },
          { id: 2, type: 'warning', icon: 'fas fa-exclamation-triangle', title: 'High Expenses', description: 'Expenses are higher than expected this month' }
        ],
        table: {
          data: [
            { category: 'Sales', revenue: 75000, expenses: 45000, profit: 30000, margin: 40 },
            { category: 'Marketing', revenue: 25000, expenses: 20000, profit: 5000, margin: 20 },
            { category: 'Operations', revenue: 25000, expenses: 20000, profit: 5000, margin: 20 }
          ],
          columns: [
            { key: 'category', label: 'Category', sortable: true },
            { key: 'revenue', label: 'Revenue', type: 'currency', sortable: true },
            { key: 'expenses', label: 'Expenses', type: 'currency', sortable: true },
            { key: 'profit', label: 'Profit', type: 'currency', sortable: true },
            { key: 'margin', label: 'Margin', type: 'percentage', sortable: true }
          ]
        }
      },

      sales: {
        type: 'sales',
        metrics: {
          items: [
            { key: 'totalSales', label: 'Total Sales', value: 28456.78, type: 'currency', icon: 'fas fa-chart-line', trend: 'up', changePercentage: 15.3 },
            { key: 'totalOrders', label: 'Total Orders', value: 356, type: 'number', icon: 'fas fa-shopping-cart', trend: 'up', changePercentage: 8.7 },
            { key: 'averageOrder', label: 'Average Order Value', value: 79.93, type: 'currency', icon: 'fas fa-receipt', trend: 'up', changePercentage: 6.2 },
            { key: 'conversionRate', label: 'Conversion Rate', value: 3.2, type: 'percentage', icon: 'fas fa-percentage', trend: 'neutral', changePercentage: 0.1 }
          ]
        },
        charts: {
          sales: {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            data: [8000, 12000, 8456.78, 10000]
          },
          topProducts: {
            data: [
              { label: 'Product A', value: 5000, percentage: 25 },
              { label: 'Product B', value: 4000, percentage: 20 },
              { label: 'Product C', value: 3000, percentage: 15 },
              { label: 'Product D', value: 2500, percentage: 12.5 },
              { label: 'Others', value: 5500, percentage: 27.5 }
            ]
          },
          categories: {
            data: [
              { label: 'Electronics', value: 12000, orders: 120, growth: 15.5 },
              { label: 'Clothing', value: 8000, orders: 200, growth: -2.3 },
              { label: 'Books', value: 5000, orders: 150, growth: 8.7 },
              { label: 'Home & Garden', value: 3456.78, orders: 86, growth: 22.1 }
            ]
          }
        },
        insights: [
          { id: 1, type: 'success', icon: 'fas fa-arrow-up', title: 'Sales Growth', description: 'Sales increased by 15.3% this period' },
          { id: 2, type: 'info', icon: 'fas fa-info-circle', title: 'Top Category', description: 'Electronics category is performing best with 42% of total sales' }
        ],
        table: {
          data: [
            { product: 'Product A', category: 'Electronics', sales: 5000, quantity: 50, revenue: 5000, profit: 1500, margin: 30 },
            { product: 'Product B', category: 'Clothing', sales: 4000, quantity: 80, revenue: 4000, profit: 800, margin: 20 },
            { product: 'Product C', category: 'Books', sales: 3000, quantity: 100, revenue: 3000, profit: 900, margin: 30 }
          ],
          columns: [
            { key: 'product', label: 'Product', sortable: true },
            { key: 'category', label: 'Category', sortable: true },
            { key: 'sales', label: 'Sales', type: 'currency', sortable: true },
            { key: 'quantity', label: 'Quantity', type: 'number', sortable: true },
            { key: 'revenue', label: 'Revenue', type: 'currency', sortable: true },
            { key: 'profit', label: 'Profit', type: 'currency', sortable: true },
            { key: 'margin', label: 'Margin', type: 'percentage', sortable: true }
          ]
        }
      },

      products: {
        type: 'products',
        metrics: {
          items: [
            { key: 'totalProducts', label: 'Total Products', value: 1250, type: 'number', icon: 'fas fa-box', trend: 'up', changePercentage: 5.2 },
            { key: 'activeProducts', label: 'Active Products', value: 1180, type: 'number', icon: 'fas fa-check-circle', trend: 'up', changePercentage: 3.1 },
            { key: 'outOfStock', label: 'Out of Stock', value: 70, type: 'number', icon: 'fas fa-exclamation-triangle', trend: 'down', changePercentage: -12.5 },
            { key: 'lowStock', label: 'Low Stock', value: 45, type: 'number', icon: 'fas fa-exclamation-circle', trend: 'neutral', changePercentage: 2.3 }
          ]
        },
        charts: {
          performance: {
            labels: ['Electronics', 'Clothing', 'Books', 'Home & Garden', 'Sports'],
            data: [350, 280, 220, 180, 150]
          },
          categories: {
            data: [
              { label: 'Electronics', value: 350, percentage: 28 },
              { label: 'Clothing', value: 280, percentage: 22.4 },
              { label: 'Books', value: 220, percentage: 17.6 },
              { label: 'Home & Garden', value: 180, percentage: 14.4 },
              { label: 'Sports', value: 150, percentage: 12 }
            ]
          }
        },
        insights: [
          { id: 1, type: 'warning', icon: 'fas fa-exclamation-triangle', title: 'Stock Alert', description: '70 products are out of stock and need restocking' },
          { id: 2, type: 'success', icon: 'fas fa-arrow-up', title: 'New Products', description: '25 new products added this week' }
        ],
        table: {
          data: [
            { name: 'Product A', category: 'Electronics', stock: 150, sales: 89, revenue: 4450, status: 'active' },
            { name: 'Product B', category: 'Clothing', stock: 0, sales: 45, revenue: 2250, status: 'out_of_stock' },
            { name: 'Product C', category: 'Books', stock: 25, sales: 67, revenue: 1340, status: 'low_stock' }
          ],
          columns: [
            { key: 'name', label: 'Product Name', sortable: true },
            { key: 'category', label: 'Category', sortable: true },
            { key: 'stock', label: 'Stock', type: 'number', sortable: true },
            { key: 'sales', label: 'Sales', type: 'number', sortable: true },
            { key: 'revenue', label: 'Revenue', type: 'currency', sortable: true },
            { key: 'status', label: 'Status', type: 'status', sortable: true }
          ]
        }
      },

      users: {
        type: 'users',
        metrics: {
          items: [
            { key: 'totalUsers', label: 'Total Users', value: 5420, type: 'number', icon: 'fas fa-users', trend: 'up', changePercentage: 8.3 },
            { key: 'activeUsers', label: 'Active Users', value: 4890, type: 'number', icon: 'fas fa-user-check', trend: 'up', changePercentage: 5.7 },
            { key: 'newUsers', label: 'New Users', value: 230, type: 'number', icon: 'fas fa-user-plus', trend: 'up', changePercentage: 15.2 },
            { key: 'engagementRate', label: 'Engagement Rate', value: 78.5, type: 'percentage', icon: 'fas fa-chart-bar', trend: 'up', changePercentage: 3.1 }
          ]
        },
        charts: {
          registrations: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            data: [100, 150, 230, 180, 200, 250]
          },
          activity: {
            data: Array.from({ length: 24 }, (_, hour) => 
              Array.from({ length: 7 }, (_, day) => ({
                hour,
                day,
                value: Math.floor(Math.random() * 100)
              }))
            ).flat()
          },
          userTypes: {
            data: [
              { label: 'Regular', value: 3200, percentage: 59 },
              { label: 'Premium', value: 1500, percentage: 28 },
              { label: 'VIP', value: 720, percentage: 13 }
            ]
          }
        },
        insights: [
          { id: 1, type: 'success', icon: 'fas fa-arrow-up', title: 'User Growth', description: 'New user registrations increased by 15.2% this month' },
          { id: 2, type: 'info', icon: 'fas fa-clock', title: 'Peak Hours', description: 'Most users are active between 2-4 PM and 8-10 PM' }
        ],
        table: {
          data: [
            { name: 'John Doe', email: '<EMAIL>', type: 'Premium', registeredAt: '2024-01-15', lastActive: '2024-01-20', orders: 15 },
            { name: 'Jane Smith', email: '<EMAIL>', type: 'Regular', registeredAt: '2024-01-10', lastActive: '2024-01-19', orders: 8 },
            { name: 'Bob Johnson', email: '<EMAIL>', type: 'VIP', registeredAt: '2024-01-05', lastActive: '2024-01-21', orders: 25 }
          ],
          columns: [
            { key: 'name', label: 'Name', sortable: true },
            { key: 'email', label: 'Email', sortable: true },
            { key: 'type', label: 'Type', type: 'status', sortable: true },
            { key: 'registeredAt', label: 'Registered', type: 'date', sortable: true },
            { key: 'lastActive', label: 'Last Active', type: 'date', sortable: true },
            { key: 'orders', label: 'Orders', type: 'number', sortable: true }
          ]
        }
      },

      orders: {
        type: 'orders',
        metrics: {
          items: [
            { key: 'totalOrders', label: 'Total Orders', value: 1250, type: 'number', icon: 'fas fa-shopping-cart', trend: 'up', changePercentage: 12.3 },
            { key: 'pendingOrders', label: 'Pending Orders', value: 45, type: 'number', icon: 'fas fa-clock', trend: 'down', changePercentage: -8.5 },
            { key: 'completedOrders', label: 'Completed Orders', value: 1180, type: 'number', icon: 'fas fa-check-circle', trend: 'up', changePercentage: 15.2 },
            { key: 'averageOrderValue', label: 'Average Order Value', value: 89.50, type: 'currency', icon: 'fas fa-receipt', trend: 'up', changePercentage: 5.7 }
          ]
        },
        charts: {
          orders: {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            data: [280, 320, 350, 300]
          },
          status: {
            data: [
              { label: 'Pending', value: 45, percentage: 3.6 },
              { label: 'Processing', value: 25, percentage: 2 },
              { label: 'Shipped', value: 80, percentage: 6.4 },
              { label: 'Delivered', value: 1050, percentage: 84 },
              { label: 'Cancelled', value: 35, percentage: 2.8 },
              { label: 'Returned', value: 15, percentage: 1.2 }
            ]
          },
          fulfillment: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            processing: [2.5, 2.2, 2.8, 2.1, 2.6, 3.2, 2.9],
            shipping: [3.2, 3.5, 3.1, 3.8, 3.4, 4.1, 3.6]
          }
        },
        insights: [
          { id: 1, type: 'success', icon: 'fas fa-arrow-up', title: 'Order Growth', description: 'Orders increased by 12.3% compared to last period' },
          { id: 2, type: 'warning', icon: 'fas fa-exclamation-triangle', title: 'Processing Delay', description: '5 orders are taking longer than usual to process' }
        ],
        table: {
          data: [
            { id: 'ORD-001', customer: 'John Doe', status: 'delivered', total: 125.50, items: 3, createdAt: '2024-01-15', updatedAt: '2024-01-18' },
            { id: 'ORD-002', customer: 'Jane Smith', status: 'shipped', total: 89.99, items: 2, createdAt: '2024-01-16', updatedAt: '2024-01-19' },
            { id: 'ORD-003', customer: 'Bob Johnson', status: 'processing', total: 199.99, items: 5, createdAt: '2024-01-17', updatedAt: '2024-01-20' }
          ],
          columns: [
            { key: 'id', label: 'Order ID', sortable: true },
            { key: 'customer', label: 'Customer', sortable: true },
            { key: 'status', label: 'Status', type: 'status', sortable: true },
            { key: 'total', label: 'Total', type: 'currency', sortable: true },
            { key: 'items', label: 'Items', type: 'number', sortable: true },
            { key: 'createdAt', label: 'Created', type: 'date', sortable: true },
            { key: 'updatedAt', label: 'Updated', type: 'date', sortable: true }
          ]
        }
      }
    }
  }

  /**
   * Get report data for specific type
   * @param {string} reportType - Type of report
   * @param {Object} filters - Applied filters
   * @returns {Object} Mock report data
   */
  getReportData(reportType, filters = {}) {
    const baseData = this.baseData[reportType]
    if (!baseData) {
      throw new Error(`Unknown report type: ${reportType}`)
    }

    // Apply filters to modify data (simplified simulation)
    return this.applyFilters(JSON.parse(JSON.stringify(baseData)), filters)
  }

  /**
   * Get dashboard summary with all report types
   * @param {Object} filters - Global filters
   * @returns {Object} Dashboard summary
   */
  getDashboardSummary(filters = {}) {
    const summary = {}
    
    Object.keys(this.baseData).forEach(reportType => {
      const data = this.getReportData(reportType, filters)
      summary[reportType] = {
        metrics: data.metrics,
        summary: this.extractSummary(data)
      }
    })

    return {
      summary,
      generatedAt: new Date().toISOString(),
      filters: filters
    }
  }

  /**
   * Generate mock export file
   * @param {string} reportType - Report type
   * @param {string} format - Export format
   * @param {Object} filters - Applied filters
   * @returns {Blob} Mock file blob
   */
  generateExportFile(reportType, format, filters = {}) {
    const data = this.getReportData(reportType, filters)
    let content = ''
    
    switch (format) {
      case 'csv':
        content = this.generateCSV(data)
        return new Blob([content], { type: 'text/csv' })
      case 'pdf':
        content = `Mock PDF content for ${reportType} report`
        return new Blob([content], { type: 'application/pdf' })
      case 'excel':
        content = `Mock Excel content for ${reportType} report`
        return new Blob([content], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      default:
        content = JSON.stringify(data, null, 2)
        return new Blob([content], { type: 'application/json' })
    }
  }

  /**
   * Apply filters to data (simplified simulation)
   * @param {Object} data - Base data
   * @param {Object} filters - Filters to apply
   * @returns {Object} Filtered data
   */
  applyFilters(data, filters) {
    // Simulate filter effects on data
    if (filters.startDate || filters.endDate) {
      // Modify metrics slightly to simulate date filtering
      data.metrics.items.forEach(item => {
        item.value = Math.floor(item.value * (0.8 + Math.random() * 0.4))
      })
    }

    if (filters.category) {
      // Filter table data by category if applicable
      if (data.table && data.table.data) {
        data.table.data = data.table.data.filter(row => 
          !row.category || row.category.toLowerCase().includes(filters.category.toLowerCase())
        )
      }
    }

    return data
  }

  /**
   * Extract summary from report data
   * @param {Object} data - Report data
   * @returns {Object} Summary data
   */
  extractSummary(data) {
    const summary = {}
    
    if (data.metrics && data.metrics.items) {
      data.metrics.items.forEach(item => {
        summary[item.key] = item.value
      })
    }

    return summary
  }

  /**
   * Generate CSV content from data
   * @param {Object} data - Report data
   * @returns {string} CSV content
   */
  generateCSV(data) {
    if (!data.table || !data.table.data || !data.table.columns) {
      return 'No table data available'
    }

    const headers = data.table.columns.map(col => col.label).join(',')
    const rows = data.table.data.map(row => 
      data.table.columns.map(col => row[col.key] || '').join(',')
    ).join('\n')

    return `${headers}\n${rows}`
  }
}

// Create and export singleton instance
export const mockData = new MockDataService()
export default mockData
