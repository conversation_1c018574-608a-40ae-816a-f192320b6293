<template>
  <div class="user-table">
    <div class="table-container">
      <table class="table is-fullwidth is-striped">
        <thead>
          <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Email</th>
            <th>Role</th>
            <th>Registered</th>

            <th>Actions</th>
          </tr>
        </thead>
        <tbody v-if="!loading && users.length > 0">
          <tr v-for="user in users" :key="user.id">
            <td>{{ truncateId(user.id) }}</td>
            <td>{{ user.username || 'N/A' }}</td>
            <td>{{ user.email }}</td>
            <td>
              <div class="select is-small">
                <select
                  :value="user.role"
                  @change="roleChanged($event, user.id)"
                  :disabled="user.role === 'Admin' && currentUserRole !== 'Admin'">
                  <option value="Admin">Admin</option>
                  <option value="Moderator">Moderator</option>
                  <option value="Seller">Seller</option>
                  <option value="Buyer">Buyer</option>
                </select>
              </div>
            </td>
            <td>{{ formatDate(user.createdAt || user.registeredAt) }}</td>

            <td>
              <div class="buttons are-small">
                <button
                  class="button is-primary"
                  @click="$emit('edit', user)">
                  <span class="icon"><i class="fas fa-edit"></i></span>
                </button>
                <button
                  class="button is-danger"
                  @click="$emit('delete', user)"
                  :disabled="user.role === 'Admin' && currentUserRole !== 'Admin'">
                  <span class="icon"><i class="fas fa-trash"></i></span>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else-if="loading">
          <tr>
            <td colspan="6" class="has-text-centered">
              <div class="loader-wrapper">
                <div class="loader is-loading"></div>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else>
          <tr>
            <td colspan="6" class="has-text-centered">
              No users found.
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useStore } from 'vuex';

const props = defineProps({
  users: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['edit', 'delete', 'change-role']);

const store = useStore();
const currentUserRole = computed(() => store.getters['auth/user']?.role || '');

// Format date
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

// Truncate ID for display
const truncateId = (id) => {
  if (!id) return 'N/A';
  if (typeof id === 'string' && id.length > 8) {
    return id.substring(0, 8) + '...';
  }
  return id;
};



// Handle role change
const roleChanged = (event, userId) => {
  const newRole = event.target.value;
  emit('change-role', userId, newRole);
};
</script>

<style scoped>
.loader-wrapper {
  padding: 2rem;
  display: flex;
  justify-content: center;
}

.loader {
  height: 80px;
  width: 80px;
}
</style>
