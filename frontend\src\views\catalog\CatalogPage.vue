<template>
  <div class="electronics-page">
    <!-- Breadcrumbs -->
    <div class="breadcrumbs">
      <a v-if="description" href="/">{{ description }}</a> / <span v-if="categoryName">{{ categoryName }}</span>
    </div>
    
    <!-- Page Header -->
    <h1 v-if="categoryName" class="page-title">{{ categoryName }}</h1>
    <div class="page-stats">
      <span v-if="totalCount" >Знайдено {{ totalCount }} товарів</span>
      <div class="sort-container">
        <span>За популярністю</span>
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M6 9l6 6 6-6"></path>
        </svg>
      </div>
    </div>
    
    <!-- Filters and Products -->
    <div class="content-container">
        <FiltersSidebar
        :filterList="filters"/>
      
      <!-- Products Grid -->
      <div class="products-grid">
        <CatalogProducts 
        :products="products"
        />
        
        <!-- More rows of products -->
        <!-- Include all products from both images -->
        
          <Pagination
          :currentPage="currentPage"
          :total-items="totalCount"
          :items-per-page="itemsPerPage"
          :max-visible-pages="10"
          @page-changed="handlePageChange"
          />
        
      </div>
    </div>

    <section class="recommended-products-section">
      <div class="container">
        <RecommendedProducts 
        :products="recommendedProducts"
        />
      </div>
    </section>
    
    <!-- Add Category Reviews Section -->
    <section class="category-reviews-section">
      <div class="container">
        <CategoryReviews 
        :reviews="categoryReviews"
        />
      </div>
    </section>
 </div>
</template>

<script setup>
import CatalogProducts from '../../components/catalog/CatalogProducts.vue';
import FiltersSidebar from '../../components/catalog/FiltersSidebar.vue';
import Pagination from '../../components/catalog/Pagination.vue';
import RecommendedProducts from '../../components/home/<USER>';
import CategoryReviews from '../../components/catalog/CategoryReviews.vue';
</script>

<script>
import categoryService from '@/services/category.service';
import productService from '@/services/product.service';
import reviewService from '@/services/review.service';

export default 
{
  data() 
  {
    return {
      filters: [],
      products: [],
      recommendedProducts: [],
      categoryReviews: [],
      currentPage: 1,
      itemsPerPage: 24,
      totalCount: 0,
      categorySlug: this.$route.params.value,
      categoryName: '',
      description: '',
      error: null,
    };
  },
  async mounted()
  {
    await this.fetchProducts();
    await this.fetchAllCount();
    await this.fetchFilters();
    await this.fetchCategoryInfo();
    await this.fetchRecommendedProducts();
    await this.fetchCategoryReviews();
    // if(this.error != null)
    // {
    //   this.$router.push("/");
    // }
  },
  methods: {
    async fetchAllCount() 
    {
      try {
        const response = await categoryService.getProducts(this.categorySlug);
        this.totalCount = response.data.data.filter(product => product.status === 1).length;
        this.error = null;
      } catch (error) {
        this.error = 'Failed to load product count. Please try again.';
        console.error(error);
      }
    },
    async fetchCategoryInfo(params) {
      try {
        const response = await categoryService.getBySlug(this.categorySlug, params);
        this.categoryName = response.data.name;
        this.description = response.data.description;
        this.error = null;
      } catch(error) {
        this.error = 'Failed to load category info. Please try again.';
        console.error(error);
      }
    },
    async fetchProducts(params = {page: 1, pageSize: this.itemsPerPage}) {
      try {
        const response = await categoryService.getProducts(this.categorySlug, params);
        //console.log(response);
        this.products = response.data.data || [];
        this.currentPage = response.data.currentPage;
        this.error = null;
      } catch (error) {
        this.error = 'Failed to load products. Please try again.';
        console.error(error);
      }
    },
    async fetchFilters() {
      try {
        const response = await productService.getFilters(this.categorySlug);
        console.log(response);
        this.filters = response.data.data || [];
        console.log(filters);
        this.error = null;
      } catch(error) {
        this.error = 'Failed to load filters. Please try again.';
        console.error(error);
      }
    },
    async fetchRecommendedProducts(params = {page: 1, pageSize: 4}) {
      try {
        const response = await categoryService.getProducts(this.categorySlug, params);
        this.recommendedProducts = response.data.data;
        this.error = null;
      } catch (error) {
        this.error = 'Failed to load products. Please try again.';
        console.error(error);
      }
    },
    handlePageChange(newPage) {
      this.fetchProducts({ page: newPage, pageSize: this.itemsPerPage });
    },
    async fetchCategoryReviews(params = {page: 1, pageSize: 5}) {
      try {
        const response = await reviewService.getCategoryReviews(this.categorySlug, params);
        this.categoryReviews = response.data.data || [];
        this.error = null;
      } catch (error) {
        this.error = 'Failed to load category reviews. Please try again.';
        console.error(error);
      }
    },
  }
};
</script>

<style scoped>

.recommended-products-section {
  margin-top: 48px;
  margin-bottom: 48px;
}

.category-reviews-section {
  margin-top: 48px;
  margin-bottom: 48px;
}

.products-grid {
  border-left: solid #ABAAAA 2px;
  padding: 15px;
}

.electronics-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.breadcrumbs {
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
}

.breadcrumbs a {
  color: #666;
  text-decoration: none;
}

.page-title {
  font-size: 28px;
  margin-bottom: 10px;
}

.page-stats {
  display: flex;
  border-bottom: solid #ABAAAA 2px;
  justify-content: space-between;
  margin-bottom: 20px;
  font-size: 14px;
}

.sort-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.content-container {
  display: flex;
  gap: 20px;
}

.filters-sidebar {
  width: 250px;
  flex-shrink: 0;
}

.filter-group {
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.filter-title {
  font-size: 16px;
  margin-bottom: 10px;
  font-weight: bold;
}

.filter-dropdown select {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
}

.filter-search input {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.price-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.price-inputs input {
  width: 70px;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.ok-btn {
  padding: 6px 10px;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.price-slider {
  position: relative;
  height: 20px;
  margin-bottom: 10px;
}

.price-range {
  font-size: 14px;
  color: #666;
}

.new-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #ff7700;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}


.product-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  font-size: 12px;
}

.stars {
  color: #ff7700;
}

.reviews-count {
  color: #666;
}

.pagination {
  grid-column: 1 / -1;
  border-top: solid #ABAAAA 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px;
  gap: 8px;
  margin-top: 30px;
}

.page-btn, .prev-btn, .next-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
}

.page-btn.active {
  background: #ff7700;
  color: white;
  border-color: #ff7700;
}
</style>
