﻿﻿using Marketplace.Application.Responses.Admin;
using Marketplace.Domain.Entities;
using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Queries.Admin;

public class GetAdminSalesDataQueryHandler : IRequestHandler<GetAdminSalesDataQuery, List<AdminSalesDataItem>>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IOrderItemRepository _orderItemRepository;

    public GetAdminSalesDataQueryHandler(
        IOrderRepository orderRepository,
        IOrderItemRepository orderItemRepository)
    {
        _orderRepository = orderRepository;
        _orderItemRepository = orderItemRepository;
    }

    public async Task<List<AdminSalesDataItem>> Handle(GetAdminSalesDataQuery request, CancellationToken cancellationToken)
    {
        // Get all orders
        var orders = await _orderRepository.GetAllAsync(
            filter: null,
            cancellationToken: cancellationToken);

        // Determine date range based on period
        DateTime startDate;
        string format;
        Func<DateTime, string> groupByFunc;

        switch (request.Period.ToLower())
        {
            case "year":
                startDate = DateTime.UtcNow.AddYears(-1);
                format = "MMM yyyy";
                groupByFunc = d => d.ToString("MMM yyyy");
                break;
            case "month":
                startDate = DateTime.UtcNow.AddMonths(-1);
                format = "dd MMM";
                groupByFunc = d => d.ToString("dd MMM");
                break;
            case "week":
            default:
                startDate = DateTime.UtcNow.AddDays(-7);
                format = "dd MMM";
                groupByFunc = d => d.ToString("dd MMM");
                break;
        }

        // Filter orders by date range
        var filteredOrders = orders.Where(o => o.CreatedAt >= startDate).ToList();

        // Get order items for filtered orders
        var orderIds = filteredOrders.Select(o => o.Id).ToList();
        var orderItems = await _orderItemRepository.GetAllAsync(
            filter: oi => orderIds.Contains(oi.OrderId),
            cancellationToken: cancellationToken);

        // Group by date and calculate total sales
        var salesByDate = filteredOrders
            .GroupBy(o => groupByFunc(o.CreatedAt))
            .ToDictionary(g => g.Key, g => g.ToList());

        var result = new List<AdminSalesDataItem>();

        // Generate all dates in the period to ensure complete data
        var currentDate = startDate;
        var endDate = DateTime.UtcNow.Date; // Use current date as end date

        while (currentDate <= endDate)
        {
            var dateKey = groupByFunc(currentDate);
            var ordersForDate = salesByDate.ContainsKey(dateKey) ? salesByDate[dateKey] : new List<Domain.Entities.Order>();

            var groupOrderIds = ordersForDate.Select(o => o.Id).ToList();
            var groupOrderItems = orderItems.Where(oi => groupOrderIds.Contains(oi.OrderId)).ToList();
            var totalSales = groupOrderItems.Sum(oi => oi.Price.Amount * oi.Quantity);

            result.Add(new AdminSalesDataItem
            {
                Label = dateKey,
                Value = totalSales,
                Date = currentDate // Add date for proper sorting
            });

            // Increment date based on period
            currentDate = request.Period.ToLower() switch
            {
                "week" => currentDate.AddDays(1),
                "month" => currentDate.AddDays(1),
                "year" => currentDate.AddMonths(1),
                _ => currentDate.AddDays(1)
            };
        }

        // Sort by date instead of label to maintain chronological order
        return result.OrderBy(r => r.Date).ToList();
    }
}
