import{_ as B,c as u,o as g,a as e,F as G,p as W,t as M,n as E,b as A,B as ae,g as k,x as L,i as H,m as ie,z as v,C as h,A as T,k as ne,D as c,I as re,e as de}from"./index-BKy0rL_2.js";import{u as ce,S as ue}from"./useAdminSearch-CW-fG_RO.js";import{S as ge}from"./StatusBadge-DZXrI7cG.js";import{p as $}from"./products-A8mTMjnr.js";import{P as me}from"./Pagination-DcbqxmDq.js";import{C as pe}from"./ConfirmDialog-BQ115uZp.js";/* empty css                                                                    */const ve={class:"product-table"},Ie={class:"table-container"},fe={class:"table is-fullwidth is-striped"},be={key:0},ye={class:"image is-64x64"},De=["src","alt"],he={class:"has-text-grey"},Me={style:{width:"140px"}},Ce={class:"buttons are-small has-addons"},ke=["onClick"],Se=["onClick"],Pe=["onClick"],Ae={key:1},_e={key:2},Ne={__name:"ProductTable",props:{products:{type:Array,required:!0},categories:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["view","edit","delete"],setup(I){const C=a=>!a||isNaN(a)?"₴0.00":new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH",minimumFractionDigits:2}).format(a),f=a=>{const i=parseInt(a)||0;return i<=0?"is-danger":i<=10?"is-warning":"is-success"},_=a=>{let i=null;return a.imageUrl&&a.imageUrl.trim()!==""?i=a.imageUrl:a.mainImageUrl&&a.mainImageUrl.trim()!==""?i=a.mainImageUrl:a.image&&a.image.trim()!==""&&(i=a.image),i||"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjhGOUZBIiByeD0iOCIvPgo8cGF0aCBkPSJNNDAgNTZDNDkuOTQxMSA1NiA1OCA0Ny45NDExIDU4IDM4QzU4IDI4LjA1ODkgNDkuOTQxMSAyMCA0MCAyMEMzMC4wNTg5IDIwIDIyIDI4LjA1ODkgMjIgMzhDMjIgNDcuOTQxMSAzMC4wNTg5IDU2IDQwIDU2WiIgc3Ryb2tlPSIjRDFEMUQxIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPHBhdGggZD0iTTMwIDMySDMyVjM0SDMwVjMyWiIgZmlsbD0iI0QxRDFEMSIvPgo8cGF0aCBkPSJNNDggMzJINTBWMzRINDhWMzJaIiBmaWxsPSIjRDFEMUQxIi8+CjxwYXRoIGQ9Ik0zMiA0NEMzMiA0Ni4yMDkxIDMzLjc5MDkgNDggMzYgNDhDMzguMjA5MSA0OCA0MCA0Ni4yMDkxIDQwIDQ0SDMyWiIgZmlsbD0iI0QxRDFEMSIvPgo8L3N2Zz4K"},t=a=>{a.target.dataset.errorHandled||(a.target.dataset.errorHandled="true",a.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjhGOUZBIiByeD0iOCIvPgo8cGF0aCBkPSJNNDAgNTZDNDkuOTQxMSA1NiA1OCA0Ny45NDExIDU4IDM4QzU4IDI4LjA1ODkgNDkuOTQxMSAyMCA0MCAyMEMzMC4wNTg5IDIwIDIyIDI4LjA1ODkgMjIgMzhDMjIgNDcuOTQxMSAzMC4wNTg5IDU2IDQwIDU2WiIgc3Ryb2tlPSIjRDFEMUQxIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPHBhdGggZD0iTTMwIDMySDMyVjM0SDMwVjMyWiIgZmlsbD0iI0QxRDFEMSIvPgo8cGF0aCBkPSJNNDggMzJINTBWMzRINDhWMzJaIiBmaWxsPSIjRDFEMUQxIi8+CjxwYXRoIGQ9Ik0zMiA0NEMzMiA0Ni4yMDkxIDMzLjc5MDkgNDggMzYgNDhDMzguMjA5MSA0OCA0MCA0Ni4yMDkxIDQwIDQ0SDMyWiIgZmlsbD0iI0QxRDFEMSIvPgo8L3N2Zz4K",a.target.classList.add("image-error"))};return(a,i)=>(g(),u("div",ve,[e("div",Ie,[e("table",fe,[i[6]||(i[6]=e("thead",null,[e("tr",null,[e("th",{style:{width:"100px"}},"Image"),e("th",null,"Name"),e("th",null,"Category"),e("th",null,"Price"),e("th",null,"Stock"),e("th",null,"Status"),e("th",{style:{width:"140px"}},"Actions")])],-1)),!I.loading&&I.products.length>0?(g(),u("tbody",be,[(g(!0),u(G,null,W(I.products,d=>(g(),u("tr",{key:d.id},[e("td",null,[e("figure",ye,[e("img",{src:_(d),alt:d.name,class:"product-thumbnail",onError:t},null,40,De)])]),e("td",null,[e("div",null,[e("strong",null,M(d.name),1),i[0]||(i[0]=e("br",null,null,-1)),e("small",he,M(d.slug),1)])]),e("td",null,M(d.categoryName||"Unknown"),1),e("td",null,M(C(d.priceAmount||d.price||d.priceValue)),1),e("td",null,[e("span",{class:E(["tag",f(d.stock||d.stockQuantity||0)])},M(d.stock||d.stockQuantity||0),3)]),e("td",null,[A(ge,{status:d.status,type:"product"},null,8,["status"])]),e("td",Me,[e("div",Ce,[e("button",{class:"button is-info is-small",onClick:b=>a.$emit("view",d),title:"View Product"},i[1]||(i[1]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-eye"})],-1)]),8,ke),e("button",{class:"button is-primary is-small",onClick:b=>a.$emit("edit",d),title:"Edit Product"},i[2]||(i[2]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-edit"})],-1)]),8,Se),e("button",{class:"button is-danger is-small",onClick:b=>a.$emit("delete",d),title:"Delete Product"},i[3]||(i[3]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-trash"})],-1)]),8,Pe)])])]))),128))])):I.loading?(g(),u("tbody",Ae,i[4]||(i[4]=[e("tr",null,[e("td",{colspan:"7",class:"has-text-centered"},[e("div",{class:"loader-wrapper"},[e("div",{class:"loader is-loading"})])])],-1)]))):(g(),u("tbody",_e,i[5]||(i[5]=[e("tr",null,[e("td",{colspan:"7",class:"has-text-centered"}," No products found. ")],-1)])))])])]))}},we=B(Ne,[["__scopeId","data-v-8b7b180f"]]),Ue={class:"modal-card"},xe={class:"modal-card-head"},$e={class:"modal-card-title"},je={class:"modal-card-body"},Oe={class:"field"},ze={class:"control"},Fe={class:"field"},Be={class:"control"},Ee={class:"columns"},Qe={class:"column is-6"},Ze={class:"field"},Re={class:"control"},Le={class:"select is-fullwidth"},Te=["value"],Ge={class:"column is-6"},We={class:"field"},He={class:"control"},Ve={class:"select is-fullwidth"},Je={class:"columns"},qe={class:"column is-4"},Ye={class:"field"},Ke={class:"control"},Xe={class:"column is-4"},es={class:"field"},ss={class:"control"},ts={class:"column is-4"},ls={class:"field"},os={class:"control"},as={class:"field"},is={class:"control"},ns={class:"columns"},rs={class:"column is-6"},ds={class:"field"},cs={class:"control"},us={class:"column is-6"},gs={class:"field"},ms={class:"control"},ps={class:"modal-card-foot"},vs=["disabled"],Is={key:0},fs={key:1},bs={__name:"ProductFormModal",props:{isOpen:{type:Boolean,required:!0},product:{type:Object,default:null}},emits:["close","save"],setup(I,{emit:C}){const f=I,_=C,t=ae({id:null,name:"",description:"",categoryId:"",price:0,salePrice:null,stock:0,status:"active",imageUrl:"",sku:"",slug:""}),a=k(!1),i=k([]),d=async()=>{try{const l=await $.getCategories();i.value=l}catch(l){console.error("Error fetching categories:",l)}},b=async()=>{a.value=!0;try{const l={...t};l.price=parseFloat(l.price),l.stock=parseInt(l.stock),l.salePrice&&(l.salePrice=parseFloat(l.salePrice)),_("save",l)}catch(l){console.error("Error submitting form:",l)}finally{a.value=!1}},N=()=>{t.id=null,t.name="",t.description="",t.categoryId="",t.price=0,t.salePrice=null,t.stock=0,t.status="active",t.imageUrl="",t.sku="",t.slug=""};return L(()=>f.product,l=>{l?Object.keys(t).forEach(s=>{s in l&&(t[s]=l[s])}):N()},{immediate:!0}),L(()=>f.isOpen,l=>{l||N()}),H(()=>{d()}),(l,s)=>(g(),u("div",{class:E(["modal",{"is-active":I.isOpen}])},[e("div",{class:"modal-background",onClick:s[0]||(s[0]=r=>l.$emit("close"))}),e("div",Ue,[e("header",xe,[e("p",$e,M(I.product?"Edit Product":"Add Product"),1),e("button",{class:"delete","aria-label":"close",onClick:s[1]||(s[1]=r=>l.$emit("close"))})]),e("section",je,[e("form",{onSubmit:ie(b,["prevent"])},[e("div",Oe,[s[13]||(s[13]=e("label",{class:"label"},"Name",-1)),e("div",ze,[v(e("input",{class:"input",type:"text",placeholder:"Product name","onUpdate:modelValue":s[2]||(s[2]=r=>t.name=r),required:""},null,512),[[h,t.name]])])]),e("div",Fe,[s[14]||(s[14]=e("label",{class:"label"},"Description",-1)),e("div",Be,[v(e("textarea",{class:"textarea",placeholder:"Product description","onUpdate:modelValue":s[3]||(s[3]=r=>t.description=r),rows:"3"},null,512),[[h,t.description]])])]),e("div",Ee,[e("div",Qe,[e("div",Ze,[s[16]||(s[16]=e("label",{class:"label"},"Category",-1)),e("div",Re,[e("div",Le,[v(e("select",{"onUpdate:modelValue":s[4]||(s[4]=r=>t.categoryId=r),required:""},[s[15]||(s[15]=e("option",{value:"",disabled:""},"Select category",-1)),(g(!0),u(G,null,W(i.value,r=>(g(),u("option",{key:r.id,value:r.id},M(r.name),9,Te))),128))],512),[[T,t.categoryId]])])])])]),e("div",Ge,[e("div",We,[s[18]||(s[18]=e("label",{class:"label"},"Status",-1)),e("div",He,[e("div",Ve,[v(e("select",{"onUpdate:modelValue":s[5]||(s[5]=r=>t.status=r),required:""},s[17]||(s[17]=[e("option",{value:"active"},"Active",-1),e("option",{value:"inactive"},"Inactive",-1),e("option",{value:"out_of_stock"},"Out of Stock",-1)]),512),[[T,t.status]])])])])])]),e("div",Je,[e("div",qe,[e("div",Ye,[s[19]||(s[19]=e("label",{class:"label"},"Price ($)",-1)),e("div",Ke,[v(e("input",{class:"input",type:"number",step:"0.01",min:"0",placeholder:"0.00","onUpdate:modelValue":s[6]||(s[6]=r=>t.price=r),required:""},null,512),[[h,t.price]])])])]),e("div",Xe,[e("div",es,[s[20]||(s[20]=e("label",{class:"label"},"Sale Price ($)",-1)),e("div",ss,[v(e("input",{class:"input",type:"number",step:"0.01",min:"0",placeholder:"0.00","onUpdate:modelValue":s[7]||(s[7]=r=>t.salePrice=r)},null,512),[[h,t.salePrice]])])])]),e("div",ts,[e("div",ls,[s[21]||(s[21]=e("label",{class:"label"},"Stock",-1)),e("div",os,[v(e("input",{class:"input",type:"number",min:"0",placeholder:"0","onUpdate:modelValue":s[8]||(s[8]=r=>t.stock=r),required:""},null,512),[[h,t.stock]])])])])]),e("div",as,[s[22]||(s[22]=e("label",{class:"label"},"Image URL",-1)),e("div",is,[v(e("input",{class:"input",type:"url",placeholder:"https://example.com/image.jpg","onUpdate:modelValue":s[9]||(s[9]=r=>t.imageUrl=r)},null,512),[[h,t.imageUrl]])])]),e("div",ns,[e("div",rs,[e("div",ds,[s[23]||(s[23]=e("label",{class:"label"},"SKU",-1)),e("div",cs,[v(e("input",{class:"input",type:"text",placeholder:"SKU-123","onUpdate:modelValue":s[10]||(s[10]=r=>t.sku=r)},null,512),[[h,t.sku]])])])]),e("div",us,[e("div",gs,[s[24]||(s[24]=e("label",{class:"label"},"Slug",-1)),e("div",ms,[v(e("input",{class:"input",type:"text",placeholder:"product-slug","onUpdate:modelValue":s[11]||(s[11]=r=>t.slug=r)},null,512),[[h,t.slug]])])])])])],32)]),e("footer",ps,[e("button",{class:"button is-primary",onClick:b,disabled:a.value},[a.value?(g(),u("span",Is,s[25]||(s[25]=[e("span",{class:"icon"},[e("i",{class:"fas fa-spinner fa-spin"})],-1),e("span",null,"Saving...",-1)]))):(g(),u("span",fs,"Save"))],8,vs),e("button",{class:"button",onClick:s[12]||(s[12]=r=>l.$emit("close"))},"Cancel")])])],2))}},ys=B(bs,[["__scopeId","data-v-fd05cff4"]]),Ds={class:"admin-products"},hs={key:0,class:"has-text-centered py-6"},Ms={key:1,class:"notification is-danger"},Cs={key:2,class:"card"},ks={class:"card-content"},Ss={key:3,class:"pagination-wrapper"},Ps={__name:"Products",setup(I){const C=de(),f=k([{key:"categoryId",label:"Category",type:"select",columnClass:"is-3",allOption:"All Categories",options:[]},{key:"status",label:"Status",type:"select",columnClass:"is-3",allOption:"All Statuses",options:[{value:"0",label:"Pending"},{value:"1",label:"Approved"},{value:"2",label:"Rejected"}]},{key:"stock",label:"Stock Level",type:"select",columnClass:"is-3",allOption:"All Stock Levels",options:[{value:"in-stock",label:"In Stock (>10)"},{value:"low-stock",label:"Low Stock (1-10)"},{value:"out-of-stock",label:"Out of Stock (0)"}]},{key:"sortBy",label:"Sort By",type:"select",columnClass:"is-3",allOption:!1,options:[{value:"Name",label:"Name"},{value:"CreatedAt",label:"Created Date"},{value:"Stock",label:"Stock"},{value:"Status",label:"Status"}]},{key:"sortOrder",label:"Order",type:"select",columnClass:"is-3",options:[{value:"desc",label:"Descending"},{value:"asc",label:"Ascending"}]}]),{items:_,loading:t,error:a,isFirstLoad:i,currentPage:d,totalPages:b,totalItems:N,filters:l,fetchData:s,handlePageChange:r}=ce({fetchFunction:$.getProducts,defaultFilters:{search:"",categoryId:"",status:"",stock:"",sortBy:"CreatedAt",sortOrder:"desc"},debounceTime:300,defaultPageSize:20,clientSideSearch:!1}),Q=k(!1),j=k(!1),w=k(null),O=k([]),V=async()=>{try{console.log("🗂️ Loading categories...");const o=await re.getCategories();console.log("📋 Categories service response:",o);const n=o.data||o.categories||[];console.log("📊 Categories data:",n),O.value=n,console.log("✅ Categories stored for table:",O.value.length,"categories");const p=(m,x="")=>{let F=[];return m.forEach(P=>{const R=x?`${x} > ${P.name}`:P.name;F.push({value:P.id,label:R}),P.children&&P.children.length>0&&(F=F.concat(p(P.children,R)))}),F},z=p(n);console.log("🔄 Flattened categories for filter:",z);const y=[],D=new Set,U=new Set;z.forEach(m=>{const x=m.label.toLowerCase();!D.has(x)&&!U.has(m.value)?(y.push(m),D.add(x),U.add(m.value)):console.log("🔄 Removing duplicate category:",m.label)}),console.log("✅ Unique categories after deduplication:",y.length);const S=f.value.find(m=>m.key==="categoryId");S?(S.options=y,console.log("✅ Category filter updated with",y.length,"options"),console.log("📋 Sample categories:",y.slice(0,3)),f.value=[...f.value]):console.error("❌ Category filter field not found")}catch(o){console.error("❌ Error loading categories:",o)}},J=o=>{l.search=o},q=(o,n)=>{if(console.log(`🔄 Filter changed: ${o} = "${n}"`),o==="categoryId"&&n){const p=Y(n);console.log("📂 Selected category includes subcategories:",p),l.categoryIds=p,l[o]=n}else o==="categoryId"&&!n&&delete l.categoryIds,l[o]=n;console.log("📊 Current filters state:",{...l})},Y=o=>{const n=[o],p=(z,y)=>{for(const D of z){if(D.id===y){const U=S=>{S.children&&S.children.length>0&&S.children.forEach(m=>{n.push(m.id),U(m)})};return U(D),!0}if(D.children&&D.children.length>0&&p(D.children,y))return!0}return!1};return p(O.value,o),n},K=()=>{Object.keys(l).forEach(o=>{o==="search"?l[o]="":o==="sortBy"?l[o]="CreatedAt":o==="sortOrder"?l[o]="desc":l[o]=""}),delete l.categoryIds,s(1)},X=()=>{C.push("/admin/products/create")},ee=o=>{C.push(`/admin/products/${o.id}/view`)},se=o=>{C.push(`/admin/products/${o.id}/edit`)},Z=()=>{Q.value=!1,w.value=null},te=async o=>{try{o.id?await $.updateProduct(o.id,o):await $.createProduct(o),Z(),s(d.value)}catch(n){console.error("Error saving product:",n)}},le=o=>{w.value=o,j.value=!0},oe=async()=>{if(w.value)try{await $.deleteProduct(w.value.id),j.value=!1,s(d.value)}catch(o){console.error("Error deleting product:",o)}};return H(async()=>{await V(),s(1)}),(o,n)=>(g(),u("div",Ds,[e("div",{class:"level"},[n[3]||(n[3]=e("div",{class:"level-left"},[e("div",{class:"level-item"},[e("h1",{class:"title"},"Products")])],-1)),e("div",{class:"level-right"},[e("div",{class:"level-item"},[e("button",{class:"button is-primary",onClick:X},n[2]||(n[2]=[e("span",{class:"icon"},[e("i",{class:"fas fa-plus"})],-1),e("span",null,"Create Product",-1)]))])])]),A(ue,{filters:c(l),"filter-fields":f.value,"search-label":"Search Products","search-placeholder":"Search by name, description, or category...","search-column-class":"is-4","total-items":c(N),"item-name":"products",loading:c(t),onSearchChanged:J,onFilterChanged:q,onResetFilters:K},null,8,["filters","filter-fields","total-items","loading"]),c(t)&&c(i)?(g(),u("div",hs,n[4]||(n[4]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading products...",-1)]))):c(a)?(g(),u("div",Ms,[e("p",null,M(c(a)),1),e("button",{class:"button is-light mt-2",onClick:n[0]||(n[0]=(...p)=>c(s)&&c(s)(...p))},n[5]||(n[5]=[e("span",{class:"icon"},[e("i",{class:"fas fa-redo"})],-1),e("span",null,"Retry",-1)]))])):(g(),u("div",Cs,[e("div",ks,[e("div",{class:E(["table-container",{"is-loading":c(t)&&!c(i)}])},[A(we,{products:c(_),categories:O.value,loading:c(t),onView:ee,onEdit:se,onDelete:le},null,8,["products","categories","loading"])],2)])])),c(b)>1||c(N)>0?(g(),u("div",Ss,[A(me,{"current-page":c(d),"total-pages":c(b),onPageChanged:c(r)},null,8,["current-page","total-pages","onPageChanged"])])):ne("",!0),A(ys,{"is-open":Q.value,product:w.value,onClose:Z,onSave:te},null,8,["is-open","product"]),A(pe,{"is-open":j.value,title:"Delete Product",message:"Are you sure you want to delete this product? This action cannot be undone.",onConfirm:oe,onCancel:n[1]||(n[1]=p=>j.value=!1)},null,8,["is-open"])]))}},js=B(Ps,[["__scopeId","data-v-8a56306f"]]);export{js as default};
