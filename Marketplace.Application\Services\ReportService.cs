using MediatR;
using Microsoft.Extensions.Logging;
using Marketplace.Application.Queries.Reports;

namespace Marketplace.Application.Services
{
    /// <summary>
    /// Unified service for all report operations
    /// Centralizes report logic and reduces controller complexity
    /// </summary>
    public class ReportService : IReportService
    {
        private readonly IMediator _mediator;
        private readonly IExportService _exportService;
        private readonly ILogger<ReportService> _logger;

        public ReportService(
            IMediator mediator,
            IExportService exportService,
            ILogger<ReportService> logger)
        {
            _mediator = mediator;
            _exportService = exportService;
            _logger = logger;
        }

        public async Task<ReportResult> GetFinancialReportAsync(GetFinancialReportQuery query, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Processing financial report request for period {StartDate} to {EndDate}", 
                query.StartDate, query.EndDate);
            
            return await _mediator.Send(query, cancellationToken);
        }

        public async Task<ReportResult> GetSalesReportAsync(GetSalesReportQuery query, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Processing sales report request for period {StartDate} to {EndDate}", 
                query.StartDate, query.EndDate);
            
            return await _mediator.Send(query, cancellationToken);
        }

        public async Task<ReportResult> GetProductsReportAsync(GetProductsReportQuery query, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Processing products report request for period {StartDate} to {EndDate}", 
                query.StartDate, query.EndDate);
            
            return await _mediator.Send(query, cancellationToken);
        }

        public async Task<ReportResult> GetUsersReportAsync(GetUsersReportQuery query, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Processing users report request for period {StartDate} to {EndDate}", 
                query.StartDate, query.EndDate);
            
            return await _mediator.Send(query, cancellationToken);
        }

        public async Task<ReportResult> GetOrdersReportAsync(GetOrdersReportQuery query, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Processing orders report request for period {StartDate} to {EndDate}", 
                query.StartDate, query.EndDate);
            
            return await _mediator.Send(query, cancellationToken);
        }

        public async Task<byte[]> ExportReportAsync(ReportResult reportData, string reportType, string format)
        {
            _logger.LogInformation("Exporting {ReportType} report in {Format} format", reportType, format);

            return format.ToLowerInvariant() switch
            {
                "excel" or "xlsx" => await _exportService.ExportToExcelAsync(reportData, reportType),
                "csv" => await _exportService.ExportToCsvAsync(reportData, reportType),
                _ => throw new ArgumentException($"Unsupported export format: {format}")
            };
        }

        public bool ValidateReportParameters(DateTime? startDate, DateTime? endDate, out string? errorMessage)
        {
            errorMessage = null;

            if (startDate.HasValue && endDate.HasValue)
            {
                if (startDate.Value > endDate.Value)
                {
                    errorMessage = "Start date cannot be greater than end date";
                    return false;
                }

                if (startDate.Value > DateTime.Now)
                {
                    errorMessage = "Start date cannot be in the future";
                    return false;
                }

                var daysDifference = (endDate.Value - startDate.Value).TotalDays;
                if (daysDifference > 365)
                {
                    errorMessage = "Date range cannot exceed 365 days";
                    return false;
                }
            }

            return true;
        }

        public (DateTime startDate, DateTime endDate) GetDefaultDateRange()
        {
            var endDate = DateTime.Now;
            var startDate = endDate.AddDays(-30);
            return (startDate, endDate);
        }

        public string NormalizeReportType(string reportType)
        {
            return reportType?.ToLowerInvariant().Trim() ?? string.Empty;
        }
    }
}
