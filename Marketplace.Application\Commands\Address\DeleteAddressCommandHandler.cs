﻿using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.Address;

public class DeleteAddressCommandHandler : IRequestHandler<DeleteAddressCommand, bool>
{
    private readonly IAddressRepository _repository;

    public DeleteAddressCommandHandler(IAddressRepository repository)
    {
        _repository = repository;
    }

    public async Task<bool> Handle(DeleteAddressCommand request, CancellationToken cancellationToken)
    {
        var item = await _repository.GetByIdAsync(request.Id, cancellationToken);
        if (item == null)
            return false;

        // Перевіряємо, чи адреса належить користувачу, якщо UserId вказано
        if (request.UserId.HasValue && item.UserId.HasValue && item.UserId.Value != request.UserId.Value)
        {
            throw new UnauthorizedAccessException($"User with ID {request.UserId} is not authorized to delete this address.");
        }

        await _repository.DeleteAsync(item.Id, cancellationToken);
        return true;
    }
}


