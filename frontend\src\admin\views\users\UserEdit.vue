<template>
  <div class="admin-page">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <button @click="goBack" class="admin-btn admin-btn-ghost admin-btn-sm">
          <i class="fas fa-arrow-left"></i>
          Back to Users
        </button>
        <h1 class="admin-page-title">
          <i class="fas fa-user-edit admin-page-icon"></i>
          {{ isEditMode ? 'Edit User' : 'Add User' }}
        </h1>
        <p class="admin-page-subtitle">{{ isEditMode ? 'Update user information and settings' : 'Create a new user account' }}</p>
      </div>
      <div class="admin-page-actions">
        <button @click="resetForm" class="admin-btn admin-btn-secondary">
          <i class="fas fa-undo"></i>
          Reset
        </button>
        <button @click="saveUser" :disabled="loading" class="admin-btn admin-btn-primary">
          <i class="fas fa-save" :class="{ 'fa-spinner fa-pulse': loading }"></i>
          {{ loading ? 'Saving...' : (isEditMode ? 'Save Changes' : 'Create User') }}
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="initialLoading" class="admin-loading-state">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading user data...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="admin-alert admin-alert-danger">
      <i class="fas fa-exclamation-triangle"></i>
      <div>
        <strong>Error loading user data</strong>
        <p>{{ error }}</p>
      </div>
      <button @click="loadUser" class="admin-btn admin-btn-sm admin-btn-danger">
        <i class="fas fa-retry"></i>
        Retry
      </button>
    </div>

    <div v-else class="admin-user-edit-content">
      <div class="admin-card">
        <div class="admin-card-header">
          <h3 class="admin-card-title">
            <i class="fas fa-user-circle"></i>
            User Information
          </h3>
        </div>
        <div class="admin-card-content">
          <form @submit.prevent="saveUser" class="admin-user-form">
            <!-- Basic Info -->
            <div class="admin-form-section">
              <h4 class="admin-form-section-title">Basic Information</h4>
              <div class="admin-form-row">
                <div class="admin-form-group">
                  <label class="admin-form-label">First Name</label>
                  <input 
                    class="admin-form-input" 
                    type="text" 
                    placeholder="First name" 
                    v-model="form.firstName"
                    required>
                </div>
                <div class="admin-form-group">
                  <label class="admin-form-label">Last Name</label>
                  <input 
                    class="admin-form-input" 
                    type="text" 
                    placeholder="Last name" 
                    v-model="form.lastName"
                    required>
                </div>
              </div>
              
              <div class="admin-form-row">
                <div class="admin-form-group">
                  <label class="admin-form-label">Email</label>
                  <input 
                    class="admin-form-input" 
                    type="email" 
                    placeholder="Email address" 
                    v-model="form.email"
                    required>
                </div>
                <div class="admin-form-group">
                  <label class="admin-form-label">Username</label>
                  <input 
                    class="admin-form-input" 
                    type="text" 
                    placeholder="Username" 
                    v-model="form.username"
                    required>
                </div>
              </div>
            </div>

            <!-- Password Section (only for new users) -->
            <div class="admin-form-section" v-if="!isEditMode">
              <h4 class="admin-form-section-title">Password</h4>
              <div class="admin-form-group">
                <label class="admin-form-label">Password</label>
                <input 
                  class="admin-form-input" 
                  type="password" 
                  placeholder="Password" 
                  v-model="form.password"
                  required>
              </div>
            </div>

            <!-- Role and Status -->
            <div class="admin-form-section">
              <h4 class="admin-form-section-title">Role & Status</h4>
              <div class="admin-form-row">
                <div class="admin-form-group">
                  <label class="admin-form-label">Role</label>
                  <select class="admin-form-select" v-model="form.role" required>
                    <option value="Admin">Admin</option>
                    <option value="Seller">Seller</option>
                    <option value="Customer">Customer</option>
                  </select>
                </div>
                <div class="admin-form-group">
                  <label class="admin-form-label">Status</label>
                  <select class="admin-form-select" v-model="form.status" required>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="pending">Pending</option>
                    <option value="banned">Banned</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Contact Info -->
            <div class="admin-form-section">
              <h4 class="admin-form-section-title">Contact Information</h4>
              <div class="admin-form-group">
                <label class="admin-form-label">Phone</label>
                <input 
                  class="admin-form-input" 
                  type="tel" 
                  placeholder="Phone number" 
                  v-model="form.phone">
              </div>
              
              <div class="admin-form-group">
                <label class="admin-form-label">Address</label>
                <textarea 
                  class="admin-form-textarea" 
                  placeholder="Address" 
                  v-model="form.address"
                  rows="3"></textarea>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="admin-form-actions">
              <button type="submit" class="admin-btn admin-btn-primary" :disabled="saving">
                <span v-if="saving">
                  <i class="fas fa-spinner fa-spin"></i>
                  <span>Saving...</span>
                </span>
                <span v-else>
                  <i class="fas fa-save"></i>
                  <span>{{ isEditMode ? 'Update User' : 'Create User' }}</span>
                </span>
              </button>
              <router-link to="/admin/users" class="admin-btn admin-btn-secondary">
                <i class="fas fa-times"></i>
                <span>Cancel</span>
              </router-link>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { usersService } from '@/admin/services/users';

const route = useRoute();
const router = useRouter();

// State
const loading = ref(false);
const saving = ref(false);
const error = ref(null);

// Form data
const form = reactive({
  id: null,
  firstName: '',
  lastName: '',
  email: '',
  username: '',
  password: '',
  role: 'Customer',
  status: 'active',
  phone: '',
  address: ''
});

// Computed
const isEditMode = computed(() => !!route.params.id);
const userId = computed(() => route.params.id);

// Methods
const loadUser = async () => {
  if (!isEditMode.value) return;
  
  loading.value = true;
  error.value = null;
  
  try {
    const user = await usersService.getUserById(userId.value);
    
    // Populate form
    Object.keys(form).forEach(key => {
      if (key in user && key !== 'password') {
        form[key] = user[key];
      }
    });
  } catch (err) {
    console.error('Error loading user:', err);
    error.value = 'Failed to load user data. Please try again.';
  } finally {
    loading.value = false;
  }
};

const saveUser = async () => {
  saving.value = true;
  error.value = null;

  try {
    const userData = { ...form };

    // Remove password if editing and not provided
    if (isEditMode.value && !userData.password) {
      delete userData.password;
    }

    if (isEditMode.value) {
      await usersService.updateUser(userId.value, userData);
    } else {
      await usersService.createUser(userData);
    }

    // Redirect to users list
    router.push('/admin/users');
  } catch (err) {
    console.error('Error saving user:', err);
    error.value = 'Failed to save user. Please try again.';
  } finally {
    saving.value = false;
  }
};

const goBack = () => {
  router.push('/admin/users');
};

const resetForm = () => {
  if (isEditMode.value) {
    // Reload user data
    loadUser();
  } else {
    // Reset to empty form
    Object.keys(form).forEach(key => {
      if (key === 'role') {
        form[key] = 'Customer';
      } else if (key === 'status') {
        form[key] = 'active';
      } else {
        form[key] = key === 'id' ? null : '';
      }
    });
  }
};

// Lifecycle
onMounted(() => {
  loadUser();
});
</script>

<style scoped>
.admin-user-edit-content {
  max-width: 800px;
  margin: 0 auto;
}

.admin-user-form {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-2xl);
}

.admin-form-section {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-lg);
}

.admin-form-section-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0;
  padding-bottom: var(--admin-space-sm);
  border-bottom: 2px solid var(--admin-border-light);
}

.admin-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--admin-space-lg);
}

.admin-form-actions {
  display: flex;
  gap: var(--admin-space-md);
  padding-top: var(--admin-space-lg);
  border-top: 1px solid var(--admin-border-light);
}

@media (max-width: 768px) {
  .admin-form-row {
    grid-template-columns: 1fr;
  }
  
  .admin-form-actions {
    flex-direction: column;
  }
}
</style>
