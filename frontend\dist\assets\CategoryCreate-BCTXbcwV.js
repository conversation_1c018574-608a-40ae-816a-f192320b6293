import{_ as A,g as i,B as G,h as j,i as H,f as J,c as n,a as e,k as y,b as k,w as I,d as _,r as Q,t as v,m as S,H as W,z as m,C as f,n as c,F as K,p as X,I as M,e as Y,o as r}from"./index-BKy0rL_2.js";const Z={class:"category-create"},ee={class:"level mb-5"},se={class:"level-left"},te={class:"level-item"},ae={class:"breadcrumb","aria-label":"breadcrumbs"},le={class:"level-right"},oe={class:"level-item"},ie={key:0,class:"notification is-danger"},ne={key:1,class:"notification is-success"},re={class:"card"},de={class:"card-content"},ue={class:"field"},ce={class:"control"},pe={class:"field"},ve={class:"control"},me={class:"field"},fe={class:"control"},ge={class:"field"},ye={class:"control"},be={class:"dropdown-trigger"},_e={class:"field has-addons"},he={class:"control is-expanded"},Ce={class:"control"},we={class:"icon"},xe={class:"dropdown-menu",role:"menu"},ke={class:"dropdown-content"},Ie=["onMousedown"],Ue={class:"category-item"},De={class:"category-name"},Se={class:"category-slug has-text-grey is-size-7"},Pe={key:0,class:"help"},Te={class:"field"},Ve={key:0,class:"mb-3"},Me={class:"image is-128x128"},Fe=["src","alt"],Oe={class:"tabs is-small"},Be={key:1,class:"field"},Le={class:"file has-name is-fullwidth"},Ne={class:"file-label"},ze={key:0,class:"file-name"},Re={key:2,class:"field"},Ee={class:"control"},$e={class:"field"},qe={class:"control"},Ae={class:"field"},Ge={class:"control"},je={class:"help"},He={class:"field"},Je={class:"control"},Qe={class:"help"},We={class:"card-footer"},Ke={class:"card-footer-item"},Xe={class:"buttons is-fullwidth"},Ye=["disabled"],Ze={__name:"CategoryCreate",setup(es){const F=J(),O=Y(),g=i(""),h=i(""),w=i(!1),C=i([]),t=G({name:"",slug:"",description:"",parentId:"",imageUrl:"",displayOrder:0,metaTitle:"",metaDescription:""}),b=i("file"),x=i(null),U=i(null),d=i(!1),o=i(""),p=i(null),B=j(()=>{if(!o.value.trim())return C.value;const l=o.value.toLowerCase().trim();return C.value.filter(s=>s.name.toLowerCase().includes(l)||s.slug.toLowerCase().includes(l))}),L=async()=>{try{const l=await M.getAll({pageSize:1e3});C.value=l.data||[]}catch(l){console.error("Error fetching categories:",l)}},N=()=>{t.name&&(!t.slug||t.slug===P(t.name))&&(t.slug=P(t.name))},P=l=>l.toString().toLowerCase().trim().replace(/\s+/g,"-").replace(/&/g,"-and-").replace(/[^\w\-]+/g,"").replace(/\-\-+/g,"-").replace(/^-+/,"").replace(/-+$/,""),z=l=>{const s=l.target.files[0];if(!s)return;if(!s.type.startsWith("image/")){g.value="Please select an image file";return}if(s.size>5*1024*1024){g.value="File size must be less than 5MB";return}x.value=s;const u=new FileReader;u.onload=a=>{t.imageUrl=a.target.result},u.readAsDataURL(s)},R=()=>{t.imageUrl="",x.value=null,U.value&&(U.value.value="")},D=l=>{l?(p.value=l,t.parentId=l.id,o.value=l.name):(p.value=null,t.parentId="",o.value=""),d.value=!1},T=()=>{d.value=!0},E=()=>{d.value?d.value=!1:(p.value&&o.value===p.value.name&&(o.value=""),T())},$=()=>{d.value||(d.value=!0)},q=()=>{setTimeout(()=>{d.value=!1,p.value?o.value=p.value.name:o.value=""},200)},V=async()=>{g.value="",h.value="",w.value=!0;try{const l={...t};l.displayOrder=parseInt(l.displayOrder)||0,l.parentId===""&&(l.parentId=null),await M.createCategory(l),h.value="Category created successfully!",setTimeout(()=>{O.push("/admin/categories")},1500)}catch(l){console.error("Error creating category:",l),g.value="Failed to create category. Please try again."}finally{w.value=!1}};return H(async()=>{await L();const l=F.query.parentId;if(l&&C.value.length>0){const s=C.value.find(u=>u.id===l);s&&D(s)}}),(l,s)=>{const u=Q("router-link");return r(),n("div",Z,[e("div",ee,[e("div",se,[e("div",te,[e("div",null,[e("nav",ae,[e("ul",null,[e("li",null,[k(u,{to:"/admin"},{default:I(()=>s[13]||(s[13]=[_("Dashboard")])),_:1})]),e("li",null,[k(u,{to:"/admin/categories"},{default:I(()=>s[14]||(s[14]=[_("Categories")])),_:1})]),s[15]||(s[15]=e("li",{class:"is-active"},[e("a",{href:"#","aria-current":"page"},"Create Category")],-1))])]),s[16]||(s[16]=e("h1",{class:"title is-4"},"Create New Category",-1)),s[17]||(s[17]=e("p",{class:"subtitle is-6"},"Add a new category to your marketplace",-1))])])]),e("div",le,[e("div",oe,[k(u,{to:"/admin/categories",class:"button is-light"},{default:I(()=>s[18]||(s[18]=[e("span",{class:"icon"},[e("i",{class:"fas fa-arrow-left"})],-1),e("span",null,"Back to Categories",-1)])),_:1})])])]),g.value?(r(),n("div",ie,[e("button",{class:"delete",onClick:s[0]||(s[0]=a=>g.value="")}),_(" "+v(g.value),1)])):y("",!0),h.value?(r(),n("div",ne,[e("button",{class:"delete",onClick:s[1]||(s[1]=a=>h.value="")}),_(" "+v(h.value),1)])):y("",!0),e("div",re,[s[37]||(s[37]=e("div",{class:"card-header"},[e("p",{class:"card-header-title"},[e("span",{class:"icon"},[e("i",{class:"fas fa-plus"})]),e("span",null,"Category Information")])],-1)),e("div",de,[e("form",{onSubmit:S(V,["prevent"])},[e("div",ue,[s[19]||(s[19]=e("label",{class:"label"},[_("Name "),e("span",{class:"has-text-danger"},"*")],-1)),e("div",ce,[m(e("input",{class:"input",type:"text",placeholder:"Category name","onUpdate:modelValue":s[2]||(s[2]=a=>t.name=a),onInput:N,required:""},null,544),[[f,t.name]])])]),e("div",pe,[s[20]||(s[20]=e("label",{class:"label"},[_("Slug "),e("span",{class:"has-text-danger"},"*")],-1)),e("div",ve,[m(e("input",{class:"input",type:"text",placeholder:"category-slug","onUpdate:modelValue":s[3]||(s[3]=a=>t.slug=a),required:""},null,512),[[f,t.slug]])]),s[21]||(s[21]=e("p",{class:"help"},"URL-friendly version of the name. Auto-generated but can be edited.",-1))]),e("div",me,[s[22]||(s[22]=e("label",{class:"label"},"Description",-1)),e("div",fe,[m(e("textarea",{class:"textarea",placeholder:"Category description","onUpdate:modelValue":s[4]||(s[4]=a=>t.description=a),rows:"3"},null,512),[[f,t.description]])])]),e("div",ge,[s[24]||(s[24]=e("label",{class:"label"},"Parent Category",-1)),e("div",ye,[e("div",{class:c(["dropdown",{"is-active":d.value}])},[e("div",be,[e("div",_e,[e("div",he,[m(e("input",{class:"input",type:"text",placeholder:"Search for parent category (leave empty for top level)...","onUpdate:modelValue":s[5]||(s[5]=a=>o.value=a),onInput:$,onFocus:T,onBlur:q},null,544),[[f,o.value]])]),e("div",Ce,[e("button",{class:"button",type:"button",onClick:E},[e("span",we,[e("i",{class:c(["fas fa-chevron-down",{"fa-rotate-180":d.value}])},null,2)])])])])]),e("div",xe,[e("div",ke,[e("a",{class:c(["dropdown-item",{"is-active":!t.parentId}]),onMousedown:s[6]||(s[6]=S(a=>D(null),["prevent"]))},s[23]||(s[23]=[e("div",{class:"category-item"},[e("div",{class:"category-name"},"None (Top Level)"),e("div",{class:"category-slug has-text-grey is-size-7"},"Root category")],-1)]),34),(r(!0),n(K,null,X(B.value,a=>(r(),n("a",{key:a.id,class:c(["dropdown-item",{"is-active":t.parentId===a.id}]),onMousedown:S(ss=>D(a),["prevent"])},[e("div",Ue,[e("div",De,v(a.name),1),e("div",Se,v(a.slug),1)])],42,Ie))),128))])])],2)]),p.value?(r(),n("p",Pe," Selected: "+v(p.value.name),1)):y("",!0)]),e("div",Te,[s[29]||(s[29]=e("label",{class:"label"},"Category Image",-1)),t.imageUrl?(r(),n("div",Ve,[e("figure",Me,[e("img",{src:t.imageUrl,alt:t.name,class:"is-rounded"},null,8,Fe)]),e("button",{type:"button",class:"button is-small is-danger mt-2",onClick:R},s[25]||(s[25]=[e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1),e("span",null,"Remove Image",-1)]))])):y("",!0),e("div",Oe,[e("ul",null,[e("li",{class:c({"is-active":b.value==="file"})},[e("a",{onClick:s[7]||(s[7]=a=>b.value="file")},"Upload File")],2),e("li",{class:c({"is-active":b.value==="url"})},[e("a",{onClick:s[8]||(s[8]=a=>b.value="url")},"Image URL")],2)])]),b.value==="file"?(r(),n("div",Be,[e("div",Le,[e("label",Ne,[e("input",{class:"file-input",type:"file",accept:"image/*",onChange:z,ref_key:"fileInput",ref:U},null,544),s[26]||(s[26]=e("span",{class:"file-cta"},[e("span",{class:"icon"},[e("i",{class:"fas fa-upload"})]),e("span",{class:"file-label"},"Choose image...")],-1)),x.value?(r(),n("span",ze,v(x.value.name),1)):y("",!0)])]),s[27]||(s[27]=e("p",{class:"help"},"Supported formats: JPG, PNG, GIF. Max size: 5MB",-1))])):y("",!0),b.value==="url"?(r(),n("div",Re,[e("div",Ee,[m(e("input",{class:"input",type:"url",placeholder:"https://example.com/image.jpg","onUpdate:modelValue":s[9]||(s[9]=a=>t.imageUrl=a)},null,512),[[f,t.imageUrl]])]),s[28]||(s[28]=e("p",{class:"help"},"Enter a direct link to an image",-1))])):y("",!0)]),e("div",$e,[s[30]||(s[30]=e("label",{class:"label"},"Display Order",-1)),e("div",qe,[m(e("input",{class:"input",type:"number",min:"0",placeholder:"0","onUpdate:modelValue":s[10]||(s[10]=a=>t.displayOrder=a)},null,512),[[f,t.displayOrder,void 0,{number:!0}]])]),s[31]||(s[31]=e("p",{class:"help"},"Categories with lower numbers will be displayed first.",-1))]),s[34]||(s[34]=W('<div class="field" data-v-8f265a39><label class="label" data-v-8f265a39><span class="icon-text" data-v-8f265a39><span class="icon" data-v-8f265a39><i class="fas fa-search" data-v-8f265a39></i></span><span data-v-8f265a39>SEO Settings</span></span></label></div>',1)),e("div",Ae,[s[32]||(s[32]=e("label",{class:"label"},"Meta Title",-1)),e("div",Ge,[m(e("input",{class:"input",type:"text",placeholder:"SEO title for search engines","onUpdate:modelValue":s[11]||(s[11]=a=>t.metaTitle=a),maxlength:"60"},null,512),[[f,t.metaTitle]])]),e("p",je,[e("span",{class:c({"has-text-danger":t.metaTitle&&t.metaTitle.length>60})},v(t.metaTitle?t.metaTitle.length:0)+"/60 characters ",3)])]),e("div",He,[s[33]||(s[33]=e("label",{class:"label"},"Meta Description",-1)),e("div",Je,[m(e("textarea",{class:"textarea",placeholder:"SEO description for search engines","onUpdate:modelValue":s[12]||(s[12]=a=>t.metaDescription=a),maxlength:"160",rows:"3"},null,512),[[f,t.metaDescription]])]),e("p",Qe,[e("span",{class:c({"has-text-danger":t.metaDescription&&t.metaDescription.length>160})},v(t.metaDescription?t.metaDescription.length:0)+"/160 characters ",3)])])],32)]),e("footer",We,[e("div",Ke,[e("div",Xe,[e("button",{class:c(["button is-primary is-fullwidth",{"is-loading":w.value}]),onClick:V,disabled:w.value},s[35]||(s[35]=[e("span",{class:"icon"},[e("i",{class:"fas fa-save"})],-1),e("span",null,"Create Category",-1)]),10,Ye),k(u,{to:"/admin/categories",class:"button is-light is-fullwidth"},{default:I(()=>s[36]||(s[36]=[e("span",{class:"icon"},[e("i",{class:"fas fa-times"})],-1),e("span",null,"Cancel",-1)])),_:1})])])])])])}}},as=A(Ze,[["__scopeId","data-v-8f265a39"]]);export{as as default};
