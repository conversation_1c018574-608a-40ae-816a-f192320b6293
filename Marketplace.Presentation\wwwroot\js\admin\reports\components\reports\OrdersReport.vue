<template>
  <div class="orders-report">
    <!-- Header -->
    <div class="report-header">
      <div class="header-content">
        <h1 class="report-title">
          <i class="fas fa-shopping-cart"></i>
          Orders Report
        </h1>
        <p class="report-description">
          Comprehensive analysis of order trends, status distribution, and fulfillment metrics
        </p>
      </div>
      
      <!-- Export Actions -->
      <div class="header-actions">
        <ExportButtons 
          :report-type="'orders'"
          :filters="filters"
          :disabled="isLoading"
        />
      </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
      <ReportFilters 
        :show-report-type="false"
        :report-type="'orders'"
        @filters-changed="handleFiltersChanged"
      />
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading orders data...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <div class="error-content">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Failed to Load Orders Data</h3>
        <p>{{ error }}</p>
        <button @click="refreshData" class="retry-btn">
          <i class="fas fa-redo"></i>
          Try Again
        </button>
      </div>
    </div>

    <!-- Report Content -->
    <div v-else-if="hasData" class="report-content">
      <!-- Key Metrics -->
      <div class="metrics-section">
        <h3 class="section-title">
          <i class="fas fa-chart-line"></i>
          Order Performance Metrics
        </h3>
        
        <div class="metrics-grid">
          <MetricCard
            v-for="metric in orderMetrics"
            :key="metric.key"
            :metric="metric"
            :loading="isLoading"
          />
        </div>
      </div>

      <!-- Orders Trend Chart -->
      <div class="chart-section">
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">Orders Trend</h3>
            <div class="chart-controls">
              <select v-model="chartPeriod" @change="updateChart" class="chart-select">
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
              <select v-model="chartMetric" @change="updateChart" class="chart-select">
                <option value="count">Order Count</option>
                <option value="value">Order Value</option>
                <option value="average">Average Order Value</option>
              </select>
            </div>
          </div>
          
          <div class="chart-content">
            <canvas ref="ordersChartCanvas" id="orders-chart"></canvas>
          </div>
        </div>
      </div>

      <!-- Order Status Distribution -->
      <div class="distribution-section">
        <div class="distribution-container">
          <div class="distribution-header">
            <h3 class="distribution-title">Orders by Status</h3>
          </div>
          
          <div class="distribution-content">
            <div class="chart-wrapper">
              <canvas ref="statusChartCanvas" id="status-chart"></canvas>
            </div>
            
            <div class="distribution-legend">
              <div
                v-for="(item, index) in statusData"
                :key="item.status"
                class="legend-item"
              >
                <div
                  class="legend-color"
                  :style="{ backgroundColor: getStatusColor(item.status) }"
                ></div>
                <div class="legend-info">
                  <div class="legend-label">{{ item.status }}</div>
                  <div class="legend-value">
                    {{ formatNumber(item.count) }} orders ({{ item.percentage.toFixed(1) }}%)
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Fulfillment Performance -->
      <div class="fulfillment-section">
        <div class="fulfillment-container">
          <div class="fulfillment-header">
            <h3 class="fulfillment-title">
              <i class="fas fa-truck"></i>
              Fulfillment Performance
            </h3>
          </div>
          
          <div class="fulfillment-content">
            <div class="fulfillment-stats">
              <div class="stat-item">
                <div class="stat-value">{{ fulfillmentStats.averageProcessingTime }}</div>
                <div class="stat-label">Avg Processing Time</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ fulfillmentStats.averageShippingTime }}</div>
                <div class="stat-label">Avg Shipping Time</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ fulfillmentStats.onTimeDeliveryRate }}%</div>
                <div class="stat-label">On-Time Delivery</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ fulfillmentStats.returnRate }}%</div>
                <div class="stat-label">Return Rate</div>
              </div>
            </div>
            
            <div class="fulfillment-chart">
              <canvas ref="fulfillmentChartCanvas" id="fulfillment-chart"></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- Order Insights -->
      <div class="insights-section">
        <h3 class="section-title">
          <i class="fas fa-lightbulb"></i>
          Order Insights
        </h3>
        
        <div class="insights-grid">
          <div
            v-for="insight in orderInsights"
            :key="insight.id"
            class="insight-card"
            :class="insight.type"
          >
            <div class="insight-icon">
              <i :class="insight.icon"></i>
            </div>
            <div class="insight-content">
              <h4 class="insight-title">{{ insight.title }}</h4>
              <p class="insight-description">{{ insight.description }}</p>
              <div v-if="insight.action" class="insight-action">
                <button @click="handleInsightAction(insight)" class="action-btn">
                  {{ insight.action.label }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Orders Table -->
      <div class="table-section">
        <ReportTable
          :data="tableData"
          :columns="tableColumns"
          :loading="isLoading"
          title="Recent Orders"
          :exportable="true"
          @row-click="viewOrderDetails"
        />
      </div>

      <!-- Problem Orders Alert -->
      <div v-if="problemOrders.length" class="alert-section">
        <div class="alert-container danger">
          <div class="alert-header">
            <i class="fas fa-exclamation-circle"></i>
            <h3>Problem Orders Alert</h3>
          </div>
          <div class="alert-content">
            <p>{{ problemOrders.length }} orders require immediate attention:</p>
            <div class="problem-orders-list">
              <div
                v-for="order in problemOrders.slice(0, 5)"
                :key="order.id"
                class="problem-order-item"
              >
                <span class="order-id">#{{ order.id }}</span>
                <span class="problem-type">{{ order.problem }}</span>
                <span class="order-value">{{ formatCurrency(order.value) }}</span>
              </div>
              <div v-if="problemOrders.length > 5" class="more-items">
                +{{ problemOrders.length - 5 }} more orders
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="empty-state">
      <div class="empty-content">
        <i class="fas fa-shopping-cart"></i>
        <h3>No Orders Data Available</h3>
        <p>Try adjusting your filters or date range to see orders data.</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { reportsService, formatters } from '../../../../../src/services/reports'
import { Chart } from 'chart.js'
import ReportFilters from '../shared/ReportFilters.vue'
import MetricCard from '../shared/MetricCard.vue'
import ExportButtons from '../shared/ExportButtons.vue'
import ReportTable from '../shared/ReportTable.vue'

export default {
  name: 'OrdersReport',
  components: {
    ReportFilters,
    MetricCard,
    ExportButtons,
    ReportTable
  },
  setup() {

    // Reactive references
    const ordersChartCanvas = ref(null)
    const statusChartCanvas = ref(null)
    const fulfillmentChartCanvas = ref(null)
    const chartPeriod = ref('monthly')
    const chartMetric = ref('count')
    const ordersChart = ref(null)
    const statusChart = ref(null)
    const fulfillmentChart = ref(null)

    // Local state
    const isLoading = ref(false)
    const error = ref(null)
    const reportData = ref(null)
    const filters = ref({
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0],
      reportType: 'orders'
    })

    // Computed properties
    const hasData = computed(() => reportData.value && reportData.value.type === 'orders')

    const orderMetrics = computed(() => {
      const data = reportData.value
      if (!data?.metrics?.items) return []

      return data.metrics.items.map(item => ({
        key: item.key,
        label: item.label,
        value: item.value,
        type: item.type || 'number',
        icon: item.icon || 'fas fa-shopping-cart',
        trend: item.trend || 'neutral',
        changePercentage: item.changePercentage || 0,
        previousValue: item.previousValue
      }))
    })

    const statusData = computed(() => {
      const data = reportData.value
      if (!data?.charts?.status?.data) return []

      return data.charts.status.data.map(item => ({
        status: item.label,
        count: item.value,
        percentage: item.percentage || 0
      }))
    })

    const fulfillmentStats = computed(() => {
      const data = reportData.value
      return data?.fulfillment || {
        averageProcessingTime: '2.5 days',
        averageShippingTime: '3.2 days',
        onTimeDeliveryRate: 94.5,
        returnRate: 2.1
      }
    })

    const orderInsights = computed(() => {
      const data = reportData.value
      return data?.insights || []
    })

    const tableData = computed(() => {
      const data = reportData.value
      return data?.table?.data || []
    })

    const tableColumns = computed(() => {
      const data = reportData.value
      return data?.table?.columns || [
        { key: 'id', label: 'Order ID', sortable: true },
        { key: 'customer', label: 'Customer', sortable: true },
        { key: 'status', label: 'Status', type: 'status', sortable: true },
        { key: 'total', label: 'Total', type: 'currency', sortable: true },
        { key: 'items', label: 'Items', type: 'number', sortable: true },
        { key: 'createdAt', label: 'Created', type: 'date', sortable: true },
        { key: 'updatedAt', label: 'Updated', type: 'date', sortable: true }
      ]
    })

    const problemOrders = computed(() => {
      const data = reportData.value
      return data?.problemOrders || []
    })

    // Methods
    const fetchOrdersData = async (newFilters = null) => {
      try {
        isLoading.value = true
        error.value = null

        const currentFilters = newFilters || filters.value
        reportData.value = await reportsService.getOrdersReport(currentFilters)

        if (newFilters) {
          filters.value = { ...filters.value, ...newFilters }
        }
      } catch (err) {
        error.value = err.message
        console.error('Error fetching orders data:', err)
      } finally {
        isLoading.value = false
      }
    }

    const handleFiltersChanged = async (newFilters) => {
      await fetchOrdersData(newFilters)
      await nextTick()
      updateCharts()
    }

    const refreshData = async () => {
      await fetchOrdersData()
      await nextTick()
      updateCharts()
    }

    const updateChart = async () => {
      await nextTick()
      updateCharts()
    }

    const updateCharts = () => {
      updateOrdersChart()
      updateStatusChart()
      updateFulfillmentChart()
    }

    const updateOrdersChart = () => {
      if (!ordersChartCanvas.value) return

      const data = reportData.value
      if (!data?.charts?.orders) return

      const ctx = ordersChartCanvas.value.getContext('2d')

      if (ordersChart.value) {
        ordersChart.value.destroy()
      }

      ordersChart.value = new Chart(ctx, {
        type: 'line',
        data: {
          labels: data.charts.orders.labels || [],
          datasets: [{
            label: chartMetric.value === 'count' ? 'Orders Count' :
                   chartMetric.value === 'value' ? 'Orders Value' : 'Average Order Value',
            data: data.charts.orders.data || [],
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return chartMetric.value === 'value' || chartMetric.value === 'average'
                    ? formatCurrency(value) : formatNumber(value)
                }
              }
            }
          }
        }
      })
    }

    const updateStatusChart = () => {
      if (!statusChartCanvas.value || !statusData.value.length) return

      const ctx = statusChartCanvas.value.getContext('2d')

      if (statusChart.value) {
        statusChart.value.destroy()
      }

      statusChart.value = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: statusData.value.map(item => item.status),
          datasets: [{
            data: statusData.value.map(item => item.count),
            backgroundColor: statusData.value.map(item => getStatusColor(item.status))
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          }
        }
      })
    }

    const updateFulfillmentChart = () => {
      if (!fulfillmentChartCanvas.value) return

      const data = reportData.value
      if (!data?.charts?.fulfillment) return

      const ctx = fulfillmentChartCanvas.value.getContext('2d')

      if (fulfillmentChart.value) {
        fulfillmentChart.value.destroy()
      }

      fulfillmentChart.value = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: data.charts.fulfillment.labels || [],
          datasets: [
            {
              label: 'Processing Time',
              data: data.charts.fulfillment.processing || [],
              backgroundColor: 'rgba(59, 130, 246, 0.8)',
              borderColor: '#3b82f6',
              borderWidth: 1
            },
            {
              label: 'Shipping Time',
              data: data.charts.fulfillment.shipping || [],
              backgroundColor: 'rgba(16, 185, 129, 0.8)',
              borderColor: '#10b981',
              borderWidth: 1
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return value + ' days'
                }
              }
            }
          }
        }
      })
    }

    const getStatusColor = (status) => {
      const statusColors = {
        'pending': '#f59e0b',
        'processing': '#3b82f6',
        'shipped': '#06b6d4',
        'delivered': '#10b981',
        'cancelled': '#ef4444',
        'returned': '#8b5cf6'
      }
      return statusColors[status.toLowerCase()] || '#9ca3af'
    }

    const formatCurrency = (value) => {
      return formatters.formatCurrency(value)
    }

    const formatNumber = (value) => {
      return formatters.formatNumber(value)
    }

    const handleInsightAction = (insight) => {
      if (insight.action?.callback) {
        insight.action.callback()
      }
    }

    const viewOrderDetails = (order) => {
      // Navigate to order details or show modal
      console.log('View order details:', order)
    }

    // Lifecycle
    onMounted(async () => {
      await fetchOrdersData()
      await nextTick()
      updateCharts()
    })

    // Watch for data changes
    watch(() => reportData.value, async () => {
      await nextTick()
      updateCharts()
    }, { deep: true })

    return {
      ordersChartCanvas,
      statusChartCanvas,
      fulfillmentChartCanvas,
      chartPeriod,
      chartMetric,
      isLoading,
      error,
      hasData,
      filters,
      orderMetrics,
      statusData,
      fulfillmentStats,
      orderInsights,
      tableData,
      tableColumns,
      problemOrders,
      fetchOrdersData,
      handleFiltersChanged,
      refreshData,
      updateChart,
      getStatusColor,
      formatCurrency,
      formatNumber,
      handleInsightAction,
      viewOrderDetails
    }
  }
}
</script>

<style scoped>
.orders-report {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 0;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.report-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.report-title i {
  color: #3b82f6;
}

.report-description {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  flex-shrink: 0;
}

.filters-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-container,
.error-container,
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-spinner,
.error-content,
.empty-content {
  text-align: center;
  color: #6b7280;
}

.loading-spinner i {
  font-size: 2rem;
  color: #3b82f6;
  margin-bottom: 1rem;
}

.error-content i,
.empty-content i {
  font-size: 3rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.empty-content i {
  color: #9ca3af;
}

.retry-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 1rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #2563eb;
}

.report-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.metrics-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.chart-section,
.distribution-section,
.fulfillment-section,
.insights-section,
.table-section,
.alert-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-container,
.distribution-container,
.fulfillment-container {
  width: 100%;
}

.chart-header,
.distribution-header,
.fulfillment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.chart-title,
.distribution-title,
.fulfillment-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chart-controls {
  display: flex;
  gap: 1rem;
}

.chart-select {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 0.875rem;
  cursor: pointer;
}

.chart-content {
  position: relative;
  height: 400px;
}

.distribution-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  align-items: center;
}

.chart-wrapper {
  position: relative;
  height: 300px;
}

.distribution-legend {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.legend-info {
  flex: 1;
}

.legend-label {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.legend-value {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Fulfillment Styles */
.fulfillment-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  align-items: start;
}

.fulfillment-stats {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.fulfillment-chart {
  position: relative;
  height: 300px;
}

/* Insights Styles */
.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.insight-card {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  background: #f8fafc;
}

.insight-card.warning {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.insight-card.success {
  border-left-color: #10b981;
  background: #f0fdf4;
}

.insight-card.danger {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.insight-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.insight-card.warning .insight-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.insight-card.success .insight-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.insight-card.danger .insight-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.insight-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.insight-action {
  margin-top: 1rem;
}

.action-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background: #2563eb;
}

/* Alert Styles */
.alert-container {
  border-radius: 8px;
  padding: 1.5rem;
}

.alert-container.danger {
  background: #fef2f2;
  border: 1px solid #fecaca;
}

.alert-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.alert-header i {
  color: #ef4444;
  font-size: 1.25rem;
}

.alert-header h3 {
  color: #dc2626;
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.alert-content {
  color: #dc2626;
}

.alert-content p {
  margin: 0 0 1rem 0;
}

.problem-orders-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.problem-order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 4px;
  gap: 1rem;
}

.order-id {
  font-weight: 600;
  color: #dc2626;
}

.problem-type {
  flex: 1;
  font-size: 0.875rem;
  color: #b91c1c;
}

.order-value {
  font-weight: 500;
  color: #dc2626;
}

.more-items {
  font-size: 0.875rem;
  color: #dc2626;
  font-style: italic;
  text-align: center;
  padding: 0.5rem;
}

@media (max-width: 768px) {
  .report-header {
    flex-direction: column;
    gap: 1rem;
  }

  .distribution-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .fulfillment-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .fulfillment-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .chart-wrapper,
  .fulfillment-chart {
    height: 250px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }

  .chart-controls {
    flex-direction: column;
    gap: 0.5rem;
  }

  .problem-order-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
</style>
