﻿using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.Address;

public class BulkDeleteAddressCommandHandler : IRequestHandler<BulkDeleteAddressCommand, int>
{
    private readonly IAddressRepository _repository;

    public BulkDeleteAddressCommandHandler(IAddressRepository repository)
    {
        _repository = repository;
    }

    public async Task<int> Handle(BulkDeleteAddressCommand request, CancellationToken cancellationToken)
    {
        int deletedCount = 0;

        foreach (Guid id in request.Ids)
        {
            try
            {
                var address = await _repository.GetByIdAsync(id, cancellationToken);
                if (address != null)
                {
                    await _repository.DeleteAsync(id, cancellationToken);
                    deletedCount++;
                }
            }
            catch (Exception)
            {
                // Продовжуємо видаляти інші адреси, навіть якщо одна з них не може бути видалена
            }
        }

        return deletedCount;
    }
}
