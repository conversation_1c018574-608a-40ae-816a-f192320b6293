using Marketplace.Application.Queries.Reports;
using Marketplace.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Handlers.Reports;

public class GetFinancialReportQueryHandler : BaseReportResultHandler<GetFinancialReportQuery>
{
    public GetFinancialReportQueryHandler(MarketplaceDbContext context, ILogger<GetFinancialReportQueryHandler> logger)
        : base(context, logger)
    {
    }

    protected override async Task<ReportResult> GenerateReportAsync(GetFinancialReportQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Get orders data from database
            var startDateUtc = DateTime.SpecifyKind(request.StartDate, DateTimeKind.Utc);
            var endDateUtc = DateTime.SpecifyKind(request.EndDate, DateTimeKind.Utc);

            var orders = await _context.Orders
                .Where(o => o.CreatedAt >= startDateUtc && o.CreatedAt <= endDateUtc)
                .ToListAsync(cancellationToken);

            // Calculate metrics
            var totalRevenue = orders.Sum(o => o.TotalPrice.Amount);
            var totalOrders = orders.Count;
            var averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

            // Calculate profit (assuming 15% profit margin)
            var totalProfit = totalRevenue * 0.15m;

            // Get previous period data for comparison
            var previousPeriodStart = request.StartDate.AddDays(-(request.EndDate - request.StartDate).Days);
            var previousPeriodEnd = request.StartDate;
            
            var previousOrders = await _context.Orders
                .Where(o => o.CreatedAt >= previousPeriodStart && o.CreatedAt < previousPeriodEnd)
                .ToListAsync(cancellationToken);

            var previousRevenue = previousOrders.Sum(o => o.TotalPrice.Amount);
            var revenueChange = previousRevenue > 0 ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0;

            // Create metrics
            var metrics = new ReportMetrics
            {
                Items = new List<MetricItem>
                {
                    new MetricItem
                    {
                        Key = "totalRevenue",
                        Label = "Total Revenue",
                        Value = totalRevenue,
                        Type = "currency",
                        Icon = "fas fa-dollar-sign",
                        PreviousValue = null,
                        ChangePercentage = revenueChange
                    },
                    new MetricItem
                    {
                        Key = "totalProfit",
                        Label = "Total Profit",
                        Value = totalProfit,
                        Type = "currency",
                        Icon = "fas fa-chart-line",
                        PreviousValue = null,
                        ChangePercentage = revenueChange
                    },
                    new MetricItem
                    {
                        Key = "totalOrders",
                        Label = "Total Orders",
                        Value = totalOrders,
                        Type = "number",
                        Icon = "fas fa-shopping-cart",
                        PreviousValue = null,
                        ChangePercentage = 0m
                    },
                    new MetricItem
                    {
                        Key = "averageOrderValue",
                        Label = "Average Order Value",
                        Value = averageOrderValue,
                        Type = "currency",
                        Icon = "fas fa-calculator",
                        PreviousValue = null,
                        ChangePercentage = 0m
                    }
                }
            };

            // Generate daily revenue data for chart
            var chartLabels = new List<string>();
            var revenueData = new List<decimal>();
            var profitData = new List<decimal>();

            for (var date = request.StartDate; date <= request.EndDate; date = date.AddDays(1))
            {
                var dayRevenue = orders
                    .Where(o => o.CreatedAt.Date == date.Date)
                    .Sum(o => o.TotalPrice.Amount);

                chartLabels.Add(date.ToString("MMM dd"));
                revenueData.Add(dayRevenue);
                profitData.Add(dayRevenue * 0.15m);
            }

            // Payment method distribution
            var paymentMethodGroups = orders
                .GroupBy(o => "Card Payment") // Simplified for now
                .Select(g => new { Method = g.Key, Amount = g.Sum(o => o.TotalPrice.Amount) })
                .ToList();

            var charts = new ReportCharts
            {
                Primary = new ChartData
                {
                    Title = "Revenue & Profit Trends",
                    Type = "line",
                    Data = new ChartDataset
                    {
                        Labels = chartLabels,
                        Datasets = new List<ChartSeries>
                        {
                            new ChartSeries
                            {
                                Label = "Revenue",
                                Data = revenueData,
                                BackgroundColor = "rgba(54, 162, 235, 0.2)",
                                BorderColor = "rgba(54, 162, 235, 1)",
                                BorderWidth = 2,
                                Fill = false
                            },
                            new ChartSeries
                            {
                                Label = "Profit",
                                Data = profitData,
                                BackgroundColor = "rgba(75, 192, 192, 0.2)",
                                BorderColor = "rgba(75, 192, 192, 1)",
                                BorderWidth = 2,
                                Fill = false
                            }
                        }
                    }
                },
                Secondary = new ChartData
                {
                    Title = "Payment Methods Distribution",
                    Type = "doughnut",
                    Data = new ChartDataset
                    {
                        Labels = paymentMethodGroups.Select(p => p.Method).ToList(),
                        Datasets = new List<ChartSeries>
                        {
                            new ChartSeries
                            {
                                Label = "Payment Methods",
                                Data = paymentMethodGroups.Select(p => p.Amount).ToList(),
                                BackgroundColors = new List<string>
                                {
                                    "#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF", "#FF9F40"
                                },
                                BorderColors = new List<string>
                                {
                                    "#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF", "#FF9F40"
                                },
                                BorderWidth = 1
                            }
                        }
                    }
                }
            };

            // Generate table data
            var table = new ReportTable
            {
                Title = "Financial Transactions",
                Columns = new List<TableColumn>
                {
                    new TableColumn { Key = "id", Label = "Order ID", Type = "text" },
                    new TableColumn { Key = "date", Label = "Date", Type = "date" },
                    new TableColumn { Key = "amount", Label = "Amount", Type = "currency" },
                    new TableColumn { Key = "profit", Label = "Profit", Type = "currency" },
                    new TableColumn { Key = "status", Label = "Status", Type = "status" }
                },
                Data = orders.Select(o => new Dictionary<string, object>
                {
                    ["id"] = o.Id,
                    ["date"] = o.CreatedAt,
                    ["amount"] = o.TotalPrice.Amount,
                    ["profit"] = o.TotalPrice.Amount * 0.15m,
                    ["status"] = o.Status.ToString()
                }).ToList(),
                TotalCount = orders.Count,
                Page = request.Page,
                PageSize = request.PageSize
            };

            // Generate insights
            var insights = new List<ReportInsight>();

            if (revenueChange > 0)
            {
                insights.Add(new ReportInsight
                {
                    Title = "Revenue Growth",
                    Description = $"Revenue has increased by {Math.Abs(revenueChange):F1}% compared to the previous period",
                    Type = "positive",
                    Icon = "fas fa-arrow-up",
                    Priority = 1
                });
            }
            else if (revenueChange < -5)
            {
                insights.Add(new ReportInsight
                {
                    Title = "Revenue Decline",
                    Description = $"Revenue has decreased by {Math.Abs(revenueChange):F1}% compared to the previous period",
                    Type = "negative",
                    Icon = "fas fa-arrow-down",
                    Priority = 1
                });
            }

            if (averageOrderValue > 1000)
            {
                insights.Add(new ReportInsight
                {
                    Title = "High Order Value",
                    Description = $"Average order value of {averageOrderValue:C} indicates strong customer purchasing power",
                    Type = "positive",
                    Icon = "fas fa-chart-line",
                    Priority = 2
                });
            }

            var summary = new ReportSummary
            {
                Title = "Financial Report Summary",
                Description = $"Financial performance from {request.StartDate:yyyy-MM-dd} to {request.EndDate:yyyy-MM-dd}",
                GeneratedAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["totalRevenue"] = totalRevenue,
                    ["totalProfit"] = totalProfit,
                    ["totalOrders"] = totalOrders,
                    ["averageOrderValue"] = averageOrderValue,
                    ["period"] = new { start = request.StartDate, end = request.EndDate }
                }
            };

            return new ReportResult
            {
                Metrics = metrics,
                Charts = charts,
                Table = table,
                Insights = insights,
                Summary = summary
            };
        }
        catch (Exception ex)
        {
            return new ReportResult
            {
                Summary = new ReportSummary
                {
                    Title = "Error",
                    Description = $"Failed to generate financial report: {ex.Message}",
                    GeneratedAt = DateTime.UtcNow
                }
            };
        }
    }
}
