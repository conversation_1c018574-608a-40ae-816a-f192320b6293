﻿using AutoMapper;
using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.Category;

public class StoreCategoryCommandHandler : IRequestHandler<StoreCategoryCommand, Guid>
{
    private readonly ICategoryRepository _repository;
    private readonly IMapper _mapper;

    public StoreCategoryCommandHandler(ICategoryRepository repository, IMapper mapper)
    {
        _repository = repository;
        _mapper = mapper;
    }

    public async Task<Guid> Handle(StoreCategoryCommand request, CancellationToken cancellationToken)
    {
        var item = _mapper.Map<Domain.Entities.Category>(request);
        await _repository.AddAsync(item, cancellationToken);
        return item.Id;
    }
}

