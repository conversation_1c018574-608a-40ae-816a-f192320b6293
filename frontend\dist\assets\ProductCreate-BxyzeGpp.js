import{s as a,e as n,o as u}from"./index-BKy0rL_2.js";import m from"./ProductEdit-CnMCQgdp.js";import"./products-A8mTMjnr.js";import"./companies-DFFZKRwv.js";const f={__name:"ProductCreate",emits:["save","cancel"],setup(i,{emit:c}){const t=n(),o=c,r=e=>{console.log("Product created:",e),o("save",e),t.push("/admin/products")},s=()=>{o("cancel"),t.push("/admin/products")};return(e,p)=>(u(),a(m,{"is-create":!0,onSave:r,onCancel:s}))}};export{f as default};
