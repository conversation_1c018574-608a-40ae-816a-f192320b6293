<template>
  <div class="admin-search-and-filters">
    <!-- Search and Filters Panel -->
    <div class="admin-filters-panel">
      <div class="admin-filters-content">
        <div class="admin-filters-row">
          <!-- Search Input -->
          <div class="admin-filter-group" :class="searchColumnClass">
            <label class="admin-filter-label">{{ searchLabel }}</label>
            <div class="admin-search-wrapper">
              <input
                class="admin-search-input"
                type="text"
                :placeholder="searchPlaceholder"
                v-model="localFilters.search"
                @input="handleSearchInput"
              />
              <div class="admin-search-icon">
                <i class="fas fa-search"></i>
              </div>
            </div>
          </div>

          <!-- Dynamic Filter Fields -->
          <div
            v-for="filter in filterFields"
            :key="filter.key"
            class="admin-filter-group"
            :class="filter.columnClass || 'admin-filter-group-default'"
          >
            <label class="admin-filter-label">{{ filter.label }}</label>
            <div class="admin-filter-control">
              <!-- Select Filter -->
              <div v-if="filter.type === 'select'" class="admin-select-wrapper">
                <select
                  class="admin-select"
                  v-model="localFilters[filter.key]"
                  @change="handleFilterChange(filter.key, localFilters[filter.key])"
                >
                  <!-- Add "All" option if specified -->
                  <option
                    v-if="filter.allOption"
                    value=""
                  >
                    {{ filter.allOption }}
                  </option>
                  <option
                    v-for="option in filter.options"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </option>
                  </select>
                  <div class="admin-select-arrow">
                    <i class="fas fa-chevron-down"></i>
                  </div>
                </div>

                <!-- Text Input Filter -->
                <input
                  v-else-if="filter.type === 'text'"
                  class="admin-input"
                  type="text"
                  :placeholder="filter.placeholder"
                  v-model="localFilters[filter.key]"
                  @input="handleFilterChange(filter.key, localFilters[filter.key])"
                />

                <!-- Date Input Filter -->
                <input
                  v-else-if="filter.type === 'date'"
                  class="admin-input"
                  type="date"
                  v-model="localFilters[filter.key]"
                  @change="handleFilterChange(filter.key, localFilters[filter.key])"
                />

                <!-- Number Input Filter -->
                <input
                  v-else-if="filter.type === 'number'"
                  class="admin-input"
                  type="number"
                  :placeholder="filter.placeholder"
                  v-model="localFilters[filter.key]"
                  @input="handleFilterChange(filter.key, localFilters[filter.key])"
                />
              </div>
            </div>
          </div>

          <!-- Reset Button -->
          <div class="admin-filter-group admin-filter-actions" v-if="showResetButton">
            <button
              class="admin-btn admin-btn-secondary"
              @click="resetFilters"
              :class="{ 'admin-btn-loading': loading }"
            >
              <i class="fas fa-undo"></i>
              {{ resetButtonText }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Filter Status Bar -->
    <div class="admin-status-bar" v-if="showStatusBar && (totalItems > 0 || hasActiveFilters)">
      <div class="admin-status-content">
        <div class="admin-status-info">
          <span class="admin-status-count">{{ totalItems }}</span>
          <span class="admin-status-text">{{ itemName }} found</span>
          <div v-if="hasActiveFilters" class="admin-active-filters">
            <span class="admin-filters-label">with filters:</span>
            <span
              v-for="(value, key) in activeFilters"
              :key="key"
              class="admin-filter-tag"
            >
              {{ getFilterDisplayName(key) }}: {{ getFilterDisplayValue(key, value) }}
              <button class="admin-filter-tag-remove" @click="removeFilter(key)">
                <i class="fas fa-times"></i>
              </button>
            </span>
          </div>
        </div>
      </div>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

// Props
const props = defineProps({
  filters: {
    type: Object,
    required: true
  },
  filterFields: {
    type: Array,
    default: () => []
  },
  searchLabel: {
    type: String,
    default: 'Search'
  },
  searchPlaceholder: {
    type: String,
    default: 'Search...'
  },
  searchColumnClass: {
    type: String,
    default: 'is-5'
  },
  showResetButton: {
    type: Boolean,
    default: true
  },
  resetButtonText: {
    type: String,
    default: 'Reset Filters'
  },
  showStatusBar: {
    type: Boolean,
    default: true
  },
  totalItems: {
    type: Number,
    default: 0
  },
  itemName: {
    type: String,
    default: 'items'
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits([
  'search-changed',
  'filter-changed',
  'reset-filters'
]);

// Local reactive copy of filters
const localFilters = ref({ ...props.filters });

// Computed properties
const hasActiveFilters = computed(() => {
  return Object.values(localFilters.value).some(value => 
    value !== '' && value !== null && value !== undefined
  );
});

const activeFilters = computed(() => {
  return Object.entries(localFilters.value)
    .filter(([_, value]) => value !== '' && value !== null && value !== undefined)
    .reduce((acc, [key, value]) => {
      acc[key] = value;
      return acc;
    }, {});
});

// Methods
const handleSearchInput = () => {
  emit('search-changed', localFilters.value.search);
};

const handleFilterChange = (filterKey, filterValue) => {
  emit('filter-changed', filterKey, filterValue);
};

const resetFilters = () => {
  // Reset local filters to empty values
  Object.keys(localFilters.value).forEach(key => {
    localFilters.value[key] = '';
  });

  // Emit reset event to parent
  emit('reset-filters');
};

const removeFilter = (key) => {
  localFilters.value[key] = '';
  emit('filter-changed', key, '');
};

const getFilterDisplayName = (key) => {
  if (key === 'search') return 'Search';
  
  const field = props.filterFields.find(f => f.key === key);
  return field ? field.label : key;
};

const getFilterDisplayValue = (key, value) => {
  const field = props.filterFields.find(f => f.key === key);
  
  if (field && field.type === 'select' && field.options) {
    const option = field.options.find(opt => opt.value === value);
    return option ? option.label : value;
  }
  
  return value;
};

// Watch for external filter changes and sync immediately
watch(() => props.filters, (newFilters) => {
  localFilters.value = { ...newFilters };
}, { deep: true, immediate: true });

// Watch for local filter changes and emit them
watch(localFilters, (newLocalFilters) => {
  // Sync search changes
  if (newLocalFilters.search !== props.filters.search) {
    emit('search-changed', newLocalFilters.search);
  }

  // Sync other filter changes
  Object.keys(newLocalFilters).forEach(key => {
    if (key !== 'search' && newLocalFilters[key] !== props.filters[key]) {
      emit('filter-changed', key, newLocalFilters[key]);
    }
  });
}, { deep: true });
</script>

<style scoped>
.search-and-filters {
  margin-bottom: 1rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}
</style>
