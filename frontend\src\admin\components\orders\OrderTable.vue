<template>
  <div class="order-table">
    <div class="table-container">
      <table class="table is-fullwidth is-striped">
        <thead>
          <tr>
            <th>Order ID</th>
            <th>Customer</th>
            <th>Date</th>
            <th>Total</th>
            <th>Status</th>
            <th>Payment</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody v-if="!loading && orders.length > 0">
          <tr v-for="order in orders" :key="order.id">
            <td>{{ order.id }}</td>
            <td>{{ order.customerName }}</td>
            <td>{{ formatDate(order.createdAt) }}</td>
            <td>{{ formatCurrency(order.total) }}</td>
            <td>
              <status-badge :status="order.status" type="order" />
            </td>
            <td>
              <span class="tag" :class="getPaymentStatusClass(order.paymentStatusText)">
                {{ order.paymentStatusText || 'Unknown' }}
              </span>
            </td>
            <td>
              <div class="buttons are-small">
                <button
                  class="button is-info"
                  @click="$emit('view', order.id)"
                  title="View Order">
                  <span class="icon"><i class="fas fa-eye"></i></span>
                </button>
                <button
                  class="button is-primary"
                  @click="$emit('edit', order.id)"
                  title="Edit Order">
                  <span class="icon"><i class="fas fa-edit"></i></span>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else-if="loading">
          <tr>
            <td colspan="7" class="has-text-centered">
              <div class="loader-wrapper">
                <div class="loader is-loading"></div>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else>
          <tr>
            <td colspan="7" class="has-text-centered">
              No orders found.
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import StatusBadge from '@/admin/components/common/StatusBadge.vue';

defineProps({
  orders: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['view', 'edit', 'update-status']);

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

const formatCurrency = (value) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
};

const getPaymentStatusClass = (status) => {
  if (!status || status === 'Unknown') return 'is-light';

  const classMap = {
    // Numeric values (backend enum values)
    0: 'is-warning',  // Pending
    1: 'is-success',  // Completed
    2: 'is-info',     // Refunded
    3: 'is-danger',   // Failed
    // String values (for backward compatibility)
    'Pending': 'is-warning',
    'Completed': 'is-success',
    'Refunded': 'is-info',
    'Failed': 'is-danger',
    // Legacy string values
    'paid': 'is-success',
    'completed': 'is-success',
    'pending': 'is-warning',
    'failed': 'is-danger',
    'cancelled': 'is-danger',
    'refunded': 'is-info'
  };

  return classMap[status] || classMap[status.toLowerCase()] || 'is-light';
};
</script>

<style scoped>
.loader-wrapper {
  padding: 2rem;
  display: flex;
  justify-content: center;
}

.loader {
  height: 80px;
  width: 80px;
}
</style>
