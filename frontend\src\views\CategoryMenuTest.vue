<template>
  <div class="category-menu-test">
    <h1>Category Menu Test</h1>
    <p>Click the "Каталог" button below to open the category menu.</p>
    
    <div class="demo-container">
      <category-menu-demo />
    </div>
  </div>
</template>

<script>
import CategoryMenuDemo from '@/components/common/CategoryMenuDemo.vue';

export default {
  name: 'CategoryMenuTest',
  components: {
    CategoryMenuDemo
  }
};
</script>

<style scoped>
.category-menu-test {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

h1 {
  font-size: 24px;
  margin-bottom: 20px;
}

p {
  margin-bottom: 30px;
  color: #666;
}

.demo-container {
  margin-top: 20px;
}
</style>
