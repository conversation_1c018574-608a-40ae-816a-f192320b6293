<template>
  <div class="card">
    <div class="card-header">
      <p class="card-header-title orders-title">Recent Orders</p>
      <div class="card-header-icon">
        <router-link to="/admin/orders" class="button is-small is-primary">
          <span>View All</span>
          <span class="icon is-small">
            <i class="fas fa-arrow-right"></i>
          </span>
        </router-link>
      </div>
    </div>
    <div class="card-content">
      <div v-if="loading" class="has-text-centered py-4">
        <span class="icon is-large">
          <i class="fas fa-spinner fa-pulse fa-2x"></i>
        </span>
        <p class="mt-2">Loading orders...</p>
      </div>
      <div v-else-if="!orders || orders.length === 0" class="has-text-centered py-4">
        <span class="icon is-large">
          <i class="fas fa-shopping-cart fa-2x"></i>
        </span>
        <p class="mt-2">No recent orders found</p>
      </div>
      <div v-else class="table-container">
        <table class="table is-fullwidth is-hoverable">
          <thead>
            <tr>
              <th>Order ID</th>
              <th>Customer</th>
              <th>Total</th>
              <th>Status</th>
              <th>Date</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="order in orders" :key="order.id">
              <td>{{ order.id }}</td>
              <td>{{ order.customerName }}</td>
              <td>{{ formatCurrency(order.total) }}</td>
              <td>
                <status-badge :status="order.status" type="order" />
              </td>
              <td>{{ formatDate(order.createdAt) }}</td>
              <td class="has-text-right">
                <router-link :to="`/admin/orders/${order.id}`" class="button is-small">
                  <span class="icon is-small">
                    <i class="fas fa-eye"></i>
                  </span>
                </router-link>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';

const props = defineProps({
  orders: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// Format currency
const formatCurrency = (value) => {
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH'
  }).format(value);
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';

  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.round(diffMs / 1000);
  const diffMin = Math.round(diffSec / 60);
  const diffHour = Math.round(diffMin / 60);
  const diffDay = Math.round(diffHour / 24);

  // If less than a minute ago
  if (diffSec < 60) {
    return 'Just now';
  }

  // If less than an hour ago
  if (diffMin < 60) {
    return `${diffMin} minute${diffMin !== 1 ? 's' : ''} ago`;
  }

  // If less than a day ago
  if (diffHour < 24) {
    return `${diffHour} hour${diffHour !== 1 ? 's' : ''} ago`;
  }

  // If less than a week ago
  if (diffDay < 7) {
    return `${diffDay} day${diffDay !== 1 ? 's' : ''} ago`;
  }

  // Otherwise, return formatted date
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};
</script>

<style scoped>
.card {
  height: 100%;
  transition: box-shadow 0.3s;
}

.card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-header-title {
  font-weight: 600;
}

.card-header-icon {
  padding: 0.75rem;
}

.table-container {
  overflow-x: auto;
}

.table {
  margin-bottom: 0;
}

.table th {
  font-weight: 600;
  color: #363636;
  background-color: #f9f9f9;
}

.table td {
  vertical-align: middle;
}

.button.is-primary {
  background-color: transparent;
  border-color: #ff7700;
  color: #ff7700;
}

.button.is-primary:hover {
  background-color: #ff7700;
  color: white;
}

.py-4 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.mt-2 {
  margin-top: 0.5rem;
}
</style>
