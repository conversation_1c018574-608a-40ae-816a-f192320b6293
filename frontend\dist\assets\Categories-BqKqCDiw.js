import{_ as G,c as n,o as i,a as e,F as O,p as U,k as S,t as y,n as z,g as P,h as M,r as le,y as se,s as ae,x as Q,i as he,b as q,w as ye,z as L,A as H,C as Ce,D as te,d as be,I as N,e as _e}from"./index-BKy0rL_2.js";import{C as $e}from"./ConfirmDialog-BQ115uZp.js";const we={class:"category-table"},ke={class:"table-container"},Pe={class:"table is-fullwidth is-striped"},Ie={key:0},De={key:0,class:"image is-48x48"},Se=["src","alt"],xe={key:1,class:"icon is-large has-text-grey-light"},Ae={class:"content"},Te={class:"title is-6"},Ve={class:"has-text-grey"},Fe={key:0,class:"is-size-7 has-text-grey mt-1"},Ee={class:"tags"},Le={key:0,class:"tag is-primary is-small"},ze={key:1,class:"tag is-info is-small"},He={class:"level is-mobile"},Ne={class:"level-left"},Re={class:"level-item"},Be={key:0,class:"level-right"},Me={class:"level-item"},Oe=["onClick"],Ue={class:"tags"},qe={key:0,class:"tag is-success is-small",title:"Has Meta Title"},We={key:1,class:"tag is-info is-small",title:"Has Meta Description"},je={key:2,class:"tag is-warning is-small",title:"Has Meta Image"},Qe={key:3,class:"tag is-light is-small"},Ge={class:"buttons are-small"},Je=["onClick"],Ke=["onClick","disabled","title"],Xe={key:1},Ye={key:2},Ze={__name:"CategoryTable",props:{categories:{type:Array,required:!0},allCategories:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["edit","delete","view-products"],setup(r){const F=r,I=(d,o)=>d?d.length>o?d.substring(0,o)+"...":d:"",_=d=>!d||d===0?"is-light":d<5?"is-warning":d<20?"is-info":"is-success",h=d=>F.allCategories.some(o=>o.parentId===d),m=d=>{const o=(d.productCount||0)>0,t=h(d.id);return!o&&!t},b=d=>{const o=(d.productCount||0)>0,t=h(d.id);return o&&t?"Cannot delete: category has products and subcategories":o?"Cannot delete: category has products":t?"Cannot delete: category has subcategories":"Delete category"};return(d,o)=>(i(),n("div",we,[e("div",ke,[e("table",Pe,[o[14]||(o[14]=e("thead",null,[e("tr",null,[e("th",{width:"60"},"Image"),e("th",null,"Category"),e("th",null,"Hierarchy"),e("th",null,"Products"),e("th",null,"SEO"),e("th",{width:"120"},"Actions")])],-1)),!r.loading&&r.categories.length>0?(i(),n("tbody",Ie,[(i(!0),n(O,null,U(r.categories,t=>(i(),n("tr",{key:t.id,class:"category-row"},[e("td",null,[t.image?(i(),n("figure",De,[e("img",{src:t.image,alt:t.name,class:"is-rounded"},null,8,Se)])):(i(),n("span",xe,o[0]||(o[0]=[e("i",{class:"fas fa-folder fa-2x"},null,-1)])))]),e("td",null,[e("div",Ae,[e("div",null,[e("strong",Te,y(t.name),1),o[1]||(o[1]=e("br",null,null,-1)),e("small",Ve,y(t.slug),1),o[2]||(o[2]=e("br",null,null,-1)),t.description?(i(),n("p",Fe,y(I(t.description,80)),1)):S("",!0)])])]),e("td",null,[e("div",Ee,[t.parentId?(i(),n("span",ze,o[4]||(o[4]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-sitemap"})],-1),e("span",null,"Child",-1)]))):(i(),n("span",Le,o[3]||(o[3]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-layer-group"})],-1),e("span",null,"Root",-1)])))])]),e("td",null,[e("div",He,[e("div",Ne,[e("div",Re,[e("span",{class:z(["tag is-medium",_(t.productCount)])},[o[5]||(o[5]=e("span",{class:"icon is-small"},[e("i",{class:"fas fa-cube"})],-1)),e("span",null,y(t.productCount||0),1)],2)])]),t.productCount>0?(i(),n("div",Be,[e("div",Me,[e("button",{class:"button is-small is-text",onClick:l=>d.$emit("view-products",t),title:"View products"},o[6]||(o[6]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-external-link-alt"})],-1)]),8,Oe)])])):S("",!0)])]),e("td",null,[e("div",Ue,[t.metaTitle?(i(),n("span",qe,o[7]||(o[7]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-heading"})],-1)]))):S("",!0),t.metaDescription?(i(),n("span",We,o[8]||(o[8]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-align-left"})],-1)]))):S("",!0),t.metaImage?(i(),n("span",je,o[9]||(o[9]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-image"})],-1)]))):S("",!0),!t.metaTitle&&!t.metaDescription&&!t.metaImage?(i(),n("span",Qe," No SEO ")):S("",!0)])]),e("td",null,[e("div",Ge,[e("button",{class:"button is-primary is-small",onClick:l=>d.$emit("edit",t),title:"Edit category"},o[10]||(o[10]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-edit"})],-1)]),8,Je),e("button",{class:"button is-danger is-small",onClick:l=>d.$emit("delete",t),disabled:!m(t),title:b(t)},o[11]||(o[11]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-trash"})],-1)]),8,Ke)])])]))),128))])):r.loading?(i(),n("tbody",Xe,o[12]||(o[12]=[e("tr",null,[e("td",{colspan:"6",class:"has-text-centered"},[e("div",{class:"loader-wrapper"},[e("div",{class:"loader is-loading"})])])],-1)]))):(i(),n("tbody",Ye,o[13]||(o[13]=[e("tr",null,[e("td",{colspan:"6",class:"has-text-centered"}," No categories found. ")],-1)])))])])]))}},es=G(Ze,[["__scopeId","data-v-68045b92"]]),ss={class:"card-content py-3"},ts={class:"level is-mobile"},ls={class:"level-left"},as={class:"level-item"},os={class:"media"},is={key:0,class:"media-left"},ns={class:"image is-48x48"},rs=["src","alt"],ds={key:1,class:"media-left"},cs={class:"media-content"},us={class:"content"},vs={class:"level is-mobile"},gs={class:"level-left"},ps={class:"level-item"},ms={class:"title is-6"},fs={class:"has-text-grey"},hs={class:"level-right"},ys={class:"level-item"},Cs={class:"tags"},bs={key:0,class:"tag is-primary is-small"},_s={key:1,class:"tag is-info is-small"},$s={key:2,class:"tag is-success is-small"},ws={key:0,class:"is-size-7 has-text-grey mt-2"},ks={class:"level-right"},Ps={class:"level-item"},Is={class:"buttons are-small"},Ds=["disabled"],Ss=["title","disabled"],xs={key:0,class:"children"},As={key:1,class:"has-text-centered mb-3"},Ts={class:"icon is-small"},Vs={__name:"CategoryHierarchyItem",props:{category:{type:Object,required:!0},level:{type:Number,default:0}},emits:["edit","delete","add-child","view-products"],setup(r){const F=r,I=P(!1),_=M(()=>F.category.children&&F.category.children.length>0),h=()=>{I.value=!I.value},m=t=>!t||t===0?"is-light":t<5?"is-warning":t<20?"is-info":"is-success",b=(t,l)=>t?t.length>l?t.substring(0,l)+"...":t:"",d=t=>{const l=(t.productCount||0)>0,v=_.value;return!l&&!v},o=t=>{const l=(t.productCount||0)>0,v=_.value;return l&&v?"Cannot delete: category has products and subcategories":l?"Cannot delete: category has products":v?"Cannot delete: category has subcategories":"Delete category"};return(t,l)=>{const v=le("CategoryHierarchyItem",!0);return i(),n("div",{class:z(["hierarchy-item",{"has-children":_.value}])},[e("div",{class:"card mb-3",style:se({marginLeft:`${r.level*2}rem`})},[e("div",ss,[e("div",ts,[e("div",ls,[e("div",as,[e("div",os,[r.category.image?(i(),n("div",is,[e("figure",ns,[e("img",{src:r.category.image,alt:r.category.name,class:"is-rounded"},null,8,rs)])])):(i(),n("div",ds,l[8]||(l[8]=[e("span",{class:"icon is-large has-text-grey-light"},[e("i",{class:"fas fa-folder fa-2x"})],-1)]))),e("div",cs,[e("div",us,[e("div",vs,[e("div",gs,[e("div",ps,[e("div",null,[e("strong",ms,y(r.category.name),1),l[10]||(l[10]=e("br",null,null,-1)),e("small",fs,y(r.category.slug),1),l[11]||(l[11]=e("br",null,null,-1)),e("span",{class:z(["tag is-small",m(r.category.productCount)])},[l[9]||(l[9]=e("span",{class:"icon is-small"},[e("i",{class:"fas fa-cube"})],-1)),e("span",null,y(r.category.productCount||0)+" products",1)],2)])])]),e("div",hs,[e("div",ys,[e("div",Cs,[r.level===0?(i(),n("span",bs,"Root")):(i(),n("span",_s,"Level "+y(r.level+1),1)),_.value?(i(),n("span",$s,y(r.category.children.length)+" children ",1)):S("",!0)])])])]),r.category.description?(i(),n("p",ws,y(b(r.category.description,100)),1)):S("",!0)])])])])]),e("div",ks,[e("div",Ps,[e("div",Is,[e("button",{class:"button is-primary is-small",onClick:l[0]||(l[0]=C=>t.$emit("edit",r.category)),title:"Edit category"},l[12]||(l[12]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-edit"})],-1)])),e("button",{class:"button is-info is-small",onClick:l[1]||(l[1]=C=>t.$emit("view-products",r.category)),title:"View products",disabled:!r.category.productCount},l[13]||(l[13]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-box"})],-1)]),8,Ds),e("button",{class:"button is-success is-small",onClick:l[2]||(l[2]=C=>t.$emit("add-child",r.category)),title:"Add subcategory"},l[14]||(l[14]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-plus"})],-1)])),e("button",{class:"button is-danger is-small",onClick:l[3]||(l[3]=C=>t.$emit("delete",r.category)),title:o(r.category),disabled:!d(r.category)},l[15]||(l[15]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-trash"})],-1)]),8,Ss)])])])])])],4),_.value&&I.value?(i(),n("div",xs,[(i(!0),n(O,null,U(r.category.children,C=>(i(),ae(v,{key:C.id,category:C,level:r.level+1,onEdit:l[4]||(l[4]=$=>t.$emit("edit",$)),onDelete:l[5]||(l[5]=$=>t.$emit("delete",$)),onAddChild:l[6]||(l[6]=$=>t.$emit("add-child",$)),onViewProducts:l[7]||(l[7]=$=>t.$emit("view-products",$))},null,8,["category","level"]))),128))])):S("",!0),_.value?(i(),n("div",As,[e("button",{class:"button is-small is-light",onClick:h,style:se({marginLeft:`${r.level*2}rem`})},[e("span",Ts,[e("i",{class:z(["fas",I.value?"fa-chevron-up":"fa-chevron-down"])},null,2)]),e("span",null,y(I.value?"Collapse":"Expand")+" ("+y(r.category.children.length)+")",1)],4)])):S("",!0)],2)}}},Fs=G(Vs,[["__scopeId","data-v-288d3fb5"]]),Es={class:"category-hierarchy"},Ls={key:0,class:"has-text-centered py-6"},zs={key:1,class:"hierarchy-container"},Hs={__name:"CategoryHierarchy",props:{categories:{type:Array,default:()=>[]}},emits:["edit","delete","add-child","view-products"],setup(r){const F=r,I=(h,m=null)=>h.filter(b=>b.parentId===m).map(b=>({...b,children:I(h,b.id)})),_=M(()=>I(F.categories));return(h,m)=>(i(),n("div",Es,[r.categories.length===0?(i(),n("div",Ls,m[4]||(m[4]=[e("span",{class:"icon is-large has-text-grey-light"},[e("i",{class:"fas fa-folder-open fa-3x"})],-1),e("p",{class:"title is-5 has-text-grey"},"No categories found",-1),e("p",{class:"subtitle is-6 has-text-grey"},"Create your first category to get started",-1)]))):(i(),n("div",zs,[(i(!0),n(O,null,U(_.value,b=>(i(),ae(Fs,{key:b.id,category:b,level:0,onEdit:m[0]||(m[0]=d=>h.$emit("edit",d)),onDelete:m[1]||(m[1]=d=>h.$emit("delete",d)),onAddChild:m[2]||(m[2]=d=>h.$emit("add-child",d)),onViewProducts:m[3]||(m[3]=d=>h.$emit("view-products",d))},null,8,["category"]))),128))]))]))}},Ns=G(Hs,[["__scopeId","data-v-c2a2acaa"]]),Rs={class:"categories-page"},Bs={class:"admin-page-header"},Ms={class:"level"},Os={class:"level-right"},Us={class:"level-item"},qs={class:"buttons"},Ws=["disabled"],js={class:"icon"},Qs={class:"columns is-multiline mb-5"},Gs={class:"column is-3"},Js={class:"card"},Ks={class:"card-content"},Xs={class:"level"},Ys={class:"level-left"},Zs={class:"level-item"},et={class:"title is-4"},st={class:"column is-3"},tt={class:"card"},lt={class:"card-content"},at={class:"level"},ot={class:"level-left"},it={class:"level-item"},nt={class:"title is-4"},rt={class:"column is-3"},dt={class:"card"},ct={class:"card-content"},ut={class:"level"},vt={class:"level-left"},gt={class:"level-item"},pt={class:"title is-4"},mt={class:"column is-3"},ft={class:"card"},ht={class:"card-content"},yt={class:"level"},Ct={class:"level-left"},bt={class:"level-item"},_t={class:"title is-4"},$t={class:"card mb-5"},wt={class:"card-content"},kt={class:"columns"},Pt={class:"column is-4"},It={class:"field"},Dt={class:"field has-addons"},St={class:"control"},xt={class:"select"},At={class:"control is-expanded has-icons-left"},Tt=["placeholder"],Vt={key:0,class:"control"},Ft={class:"column is-3"},Et={class:"field"},Lt={class:"control"},zt={class:"select is-fullwidth"},Ht=["value"],Nt={class:"column is-2"},Rt={class:"field"},Bt={class:"control"},Mt={class:"select is-fullwidth"},Ot={class:"columns"},Ut={class:"column is-3"},qt={class:"field"},Wt={class:"control"},jt={class:"select is-fullwidth"},Qt={class:"column is-3"},Gt={class:"field"},Jt={class:"control"},Kt={class:"select is-fullwidth"},Xt={class:"column is-3"},Yt={class:"field"},Zt={class:"control"},el={class:"select is-fullwidth"},sl={key:0,class:"has-text-centered py-6"},tl={key:1,class:"notification is-danger"},ll={key:2},al={key:0},ol={key:1},il={key:0,class:"pagination is-centered mt-5"},nl=["disabled"],rl=["disabled"],dl={class:"pagination-list"},cl=["onClick"],ul={key:1,class:"pagination-ellipsis"},pl={__name:"Categories",setup(r){const F=(a,s)=>{let c;return function(...u){const R=()=>{clearTimeout(c),a(...u)};clearTimeout(c),c=setTimeout(R,s)}},I=_e(),_=P(!1),h=P(""),m=P([]);P(null);const b=P(null),d=P(!1),o=P(!1),t=P(""),l=P("all"),v=P({parentId:"",productCount:""}),C=P("name"),$=P("asc"),g=P({currentPage:1,totalPages:1,totalItems:0,perPage:20}),V=P({total:0,rootCategories:0,withProducts:0,totalProducts:0}),oe=M(()=>E.value.filter(a=>!a.parentId)),ie=M(()=>oe.value),ne=M(()=>{const a=g.value.currentPage,s=g.value.totalPages,c=[];if(s<=7)for(let x=1;x<=s;x++)c.push(x);else a<=4?c.push(1,2,3,4,5,"...",s):a>=s-3?c.push(1,"...",s-4,s-3,s-2,s-1,s):c.push(1,"...",a-1,a,a+1,"...",s);return c}),J=F(()=>{g.value.currentPage=1,T()},300),E=P([]),K=async()=>{try{const a=await N.getAll({pageSize:1e4});E.value=a.data||[],console.log("📊 Fetched all categories:",E.value.length),console.log("📊 Category hierarchy structure:",E.value.map(s=>({id:s.id,name:s.name,parentId:s.parentId})))}catch(a){console.error("Error fetching all categories:",a)}},T=async()=>{_.value=!0,h.value="";try{await K();const a={page:g.value.currentPage,pageSize:g.value.perPage,orderBy:C.value==="productCount"?"name":C.value,descending:$.value==="desc"};t.value&&l.value==="all"&&(a.filter=t.value),v.value.parentId==="root"?a.parentId=null:v.value.parentId;const s=await N.getAll(a);let c=s.data||[];if(t.value&&l.value!=="all")try{c=(await N.getAll({pageSize:1e3,orderBy:C.value==="productCount"?"name":C.value,descending:$.value==="desc"})).data||[]}catch(p){console.warn("Failed to get all categories for search, using current page:",p),c=s.data||[]}if(v.value.parentId&&v.value.parentId!=="root"){const p=v.value.parentId,w=new Set;console.log("🔍 Filtering by root category:",p),console.log("📊 All categories available:",E.value.length),console.log("📊 Categories before filter:",c.length);const k=f=>{const A=E.value.filter(D=>D.parentId===f);console.log(`🔍 Found ${A.length} children for parent ${f}`),A.forEach(D=>{w.has(D.id)||(w.add(D.id),console.log(`➕ Added descendant: ${D.name} (${D.id})`),k(D.id))})};w.add(p),console.log(`➕ Added root category: ${p}`),k(p),console.log("📊 Total descendant IDs:",w.size),console.log("📊 Descendant IDs:",Array.from(w)),c=c.filter(f=>w.has(f.id)),console.log("📊 Categories after filter:",c.length),console.log("📊 Filtered categories:",c.map(f=>`${f.name} (${f.id})`))}if(v.value.productCount&&(c=c.filter(p=>{switch(v.value.productCount){case"empty":return(p.productCount||0)===0;case"hasProducts":return(p.productCount||0)>0;case"many":return(p.productCount||0)>10;default:return!0}})),t.value&&l.value!=="all"){const p=t.value.toLowerCase();c=c.filter(w=>{var k,f,A;switch(l.value){case"name":return(k=w.name)==null?void 0:k.toLowerCase().includes(p);case"slug":return(f=w.slug)==null?void 0:f.toLowerCase().includes(p);case"description":return(A=w.description)==null?void 0:A.toLowerCase().includes(p);default:return!0}})}if(t.value&&l.value==="all"&&c.length===0)try{const w=(await N.getAll({pageSize:1e3,orderBy:C.value==="productCount"?"name":C.value,descending:$.value==="desc"})).data||[],k=t.value.toLowerCase();c=w.filter(f=>{var A,D,B;return((A=f.name)==null?void 0:A.toLowerCase().includes(k))||((D=f.slug)==null?void 0:D.toLowerCase().includes(k))||((B=f.description)==null?void 0:B.toLowerCase().includes(k))})}catch(p){console.warn("Fallback search also failed:",p)}const x=v.value.productCount||t.value&&l.value!=="all"||v.value.parentId&&v.value.parentId!=="root"||t.value;(x||C.value==="productCount")&&c.length>0&&c.sort((p,w)=>{var A,D,B,ee;let k,f;switch(C.value){case"name":k=((A=p.name)==null?void 0:A.toLowerCase())||"",f=((D=w.name)==null?void 0:D.toLowerCase())||"";break;case"productCount":k=p.productCount||0,f=w.productCount||0;break;case"createdAt":k=new Date(p.createdAt||0),f=new Date(w.createdAt||0);break;default:k=((B=p.name)==null?void 0:B.toLowerCase())||"",f=((ee=w.name)==null?void 0:ee.toLowerCase())||""}return k<f?$.value==="asc"?-1:1:k>f?$.value==="asc"?1:-1:0}),m.value=c;const R=s.total||0,fe=c.length;g.value={currentPage:s.currentPage||1,totalPages:x?1:s.totalPages||Math.ceil(R/(s.pageSize||20)),totalItems:x?fe:R,perPage:s.pageSize||20},await re()}catch(a){console.error("Error fetching categories:",a),h.value="Failed to load categories. Please try again."}finally{_.value=!1}},re=async()=>{try{const a=await N.getStats();V.value={total:a.totalCategories||0,rootCategories:a.rootCategories||0,withProducts:a.categoriesWithProducts||0,totalProducts:a.totalProducts||0}}catch(a){console.warn("Failed to fetch stats from backend, using local calculation:",a),V.value.total=g.value.totalItems,V.value.rootCategories=m.value.filter(s=>!s.parentId).length,V.value.withProducts=m.value.filter(s=>s.productCount>0).length,V.value.totalProducts=m.value.reduce((s,c)=>s+(c.productCount||0),0)}},de=()=>{const a={all:"Search by name and slug (backend + frontend)...",name:"Search by category name (frontend only)...",slug:"Search by category slug (frontend only)...",description:"Search by description (frontend only)..."};return a[l.value]||a.all},ce=()=>{t.value="",l.value="all",g.value.currentPage=1,T()},ue=()=>{t.value="",l.value="all",v.value.parentId="",v.value.productCount="",C.value="name",$.value="asc",g.value.currentPage=1,T()},ve=()=>{g.value.currentPage=1,T()},ge=async()=>{o.value=!o.value},W=a=>{a>=1&&a<=g.value.totalPages&&(g.value.currentPage=a,T())},X=a=>{I.push(`/admin/categories/${a.id}/edit`)},pe=a=>{I.push({path:"/admin/categories/create",query:{parentId:a.id}})},Y=a=>{b.value=a,d.value=!0},j=()=>{d.value=!1,b.value=null},me=async()=>{if(b.value)try{console.log("Deleting category:",b.value),await N.delete(b.value.id),console.log("Category deleted successfully"),j(),await T(),await K(),console.log("Categories refreshed after delete")}catch(a){console.error("Error deleting category:",a),j(),a.response&&a.response.data&&a.response.data.message?h.value=a.response.data.message:h.value=`Failed to delete category: ${a.message||"Please try again."}`}},Z=a=>{I.push(`/admin/categories/${a.id}`)};return Q([C,$],()=>{g.value.currentPage=1,T()}),Q(()=>v.value.parentId,()=>{g.value.currentPage=1,T()}),Q(()=>v.value.productCount,()=>{g.value.currentPage=1,T()}),he(()=>{T()}),(a,s)=>{var x;const c=le("router-link");return i(),n("div",Rs,[e("div",Bs,[e("div",Ms,[s[12]||(s[12]=e("div",{class:"level-left"},[e("div",{class:"level-item"},[e("div",null,[e("h1",{class:"admin-page-title"},"Categories Management"),e("p",{class:"admin-page-subtitle"},"Manage category hierarchy and product organization")])])],-1)),e("div",Os,[e("div",Us,[e("div",qs,[q(c,{to:"/admin/categories/create",class:z(["button is-primary",{"is-disabled":_.value}])},{default:ye(()=>s[11]||(s[11]=[e("span",{class:"icon"},[e("i",{class:"fas fa-plus"})],-1),e("span",null,"Add Category",-1)])),_:1},8,["class"]),e("button",{class:"button is-info",onClick:ge,disabled:_.value},[e("span",js,[e("i",{class:z(o.value?"fas fa-table":"fas fa-sitemap")},null,2)]),e("span",null,y(o.value?"Table View":"Hierarchy View"),1)],8,Ws)])])])])]),e("div",Qs,[e("div",Gs,[e("div",Js,[e("div",Ks,[e("div",Xs,[e("div",Ys,[e("div",Zs,[e("div",null,[s[13]||(s[13]=e("p",{class:"heading"},"Total Categories",-1)),e("p",et,y(V.value.total),1)])])]),s[14]||(s[14]=e("div",{class:"level-right"},[e("div",{class:"level-item"},[e("span",{class:"icon is-large has-text-primary"},[e("i",{class:"fas fa-folder fa-2x"})])])],-1))])])])]),e("div",st,[e("div",tt,[e("div",lt,[e("div",at,[e("div",ot,[e("div",it,[e("div",null,[s[15]||(s[15]=e("p",{class:"heading"},"Root Categories",-1)),e("p",nt,y(V.value.rootCategories),1)])])]),s[16]||(s[16]=e("div",{class:"level-right"},[e("div",{class:"level-item"},[e("span",{class:"icon is-large has-text-info"},[e("i",{class:"fas fa-sitemap fa-2x"})])])],-1))])])])]),e("div",rt,[e("div",dt,[e("div",ct,[e("div",ut,[e("div",vt,[e("div",gt,[e("div",null,[s[17]||(s[17]=e("p",{class:"heading"},"With Products",-1)),e("p",pt,y(V.value.withProducts),1)])])]),s[18]||(s[18]=e("div",{class:"level-right"},[e("div",{class:"level-item"},[e("span",{class:"icon is-large has-text-success"},[e("i",{class:"fas fa-box fa-2x"})])])],-1))])])])]),e("div",mt,[e("div",ft,[e("div",ht,[e("div",yt,[e("div",Ct,[e("div",bt,[e("div",null,[s[19]||(s[19]=e("p",{class:"heading"},"Total Products",-1)),e("p",_t,y(V.value.totalProducts),1)])])]),s[20]||(s[20]=e("div",{class:"level-right"},[e("div",{class:"level-item"},[e("span",{class:"icon is-large has-text-warning"},[e("i",{class:"fas fa-shopping-bag fa-2x"})])])],-1))])])])])]),e("div",$t,[e("div",wt,[e("div",kt,[e("div",Pt,[e("div",It,[s[24]||(s[24]=e("label",{class:"label"},"Search Categories",-1)),e("div",Dt,[e("div",St,[e("div",xt,[L(e("select",{"onUpdate:modelValue":s[0]||(s[0]=u=>l.value=u)},s[21]||(s[21]=[e("option",{value:"all"},"Name & Slug",-1),e("option",{value:"name"},"Name Only",-1),e("option",{value:"slug"},"Slug Only",-1),e("option",{value:"description"},"Description Only",-1)]),512),[[H,l.value]])])]),e("div",At,[L(e("input",{class:"input",type:"text",placeholder:de(),"onUpdate:modelValue":s[1]||(s[1]=u=>t.value=u),onInput:s[2]||(s[2]=(...u)=>te(J)&&te(J)(...u))},null,40,Tt),[[Ce,t.value]]),s[22]||(s[22]=e("span",{class:"icon is-small is-left"},[e("i",{class:"fas fa-search"})],-1))]),t.value?(i(),n("div",Vt,[e("button",{class:"button is-light",onClick:ce,title:"Clear search"},s[23]||(s[23]=[e("span",{class:"icon"},[e("i",{class:"fas fa-times"})],-1)]))])):S("",!0)])])]),e("div",Ft,[e("div",Et,[s[27]||(s[27]=e("label",{class:"label"},"Parent Category",-1)),e("div",Lt,[e("div",zt,[L(e("select",{"onUpdate:modelValue":s[3]||(s[3]=u=>v.value.parentId=u)},[s[25]||(s[25]=e("option",{value:""},"All Categories",-1)),s[26]||(s[26]=e("option",{value:"root"},"Root Categories Only",-1)),(i(!0),n(O,null,U(ie.value,u=>(i(),n("option",{key:u.id,value:u.id},y(u.name),9,Ht))),128))],512),[[H,v.value.parentId]])])])])]),e("div",Nt,[e("div",Rt,[s[29]||(s[29]=e("label",{class:"label"},"Product Count",-1)),e("div",Bt,[e("div",Mt,[L(e("select",{"onUpdate:modelValue":s[4]||(s[4]=u=>v.value.productCount=u)},s[28]||(s[28]=[e("option",{value:""},"Any",-1),e("option",{value:"empty"},"Empty (0 products)",-1),e("option",{value:"hasProducts"},"Has Products (>0)",-1),e("option",{value:"many"},"Many Products (>10)",-1)]),512),[[H,v.value.productCount]])])])])])]),e("div",Ot,[e("div",Ut,[e("div",qt,[s[31]||(s[31]=e("label",{class:"label"},"Sort By",-1)),e("div",Wt,[e("div",jt,[L(e("select",{"onUpdate:modelValue":s[5]||(s[5]=u=>C.value=u)},s[30]||(s[30]=[e("option",{value:"name"},"Name",-1),e("option",{value:"productCount"},"Product Count",-1)]),512),[[H,C.value]])])])])]),e("div",Qt,[e("div",Gt,[s[33]||(s[33]=e("label",{class:"label"},"Order",-1)),e("div",Jt,[e("div",Kt,[L(e("select",{"onUpdate:modelValue":s[6]||(s[6]=u=>$.value=u)},s[32]||(s[32]=[e("option",{value:"asc"},"Ascending",-1),e("option",{value:"desc"},"Descending",-1)]),512),[[H,$.value]])])])])]),e("div",Xt,[e("div",Yt,[s[35]||(s[35]=e("label",{class:"label"},"Items per page",-1)),e("div",Zt,[e("div",el,[L(e("select",{"onUpdate:modelValue":s[7]||(s[7]=u=>g.value.perPage=u),onChange:ve},s[34]||(s[34]=[e("option",{value:"10"},"10 per page",-1),e("option",{value:"20"},"20 per page",-1),e("option",{value:"50"},"50 per page",-1),e("option",{value:"100"},"100 per page",-1)]),544),[[H,g.value.perPage]])])])])]),e("div",{class:"column is-3"},[e("div",{class:"field"},[s[37]||(s[37]=e("label",{class:"label"}," ",-1)),e("div",{class:"control"},[e("div",{class:"buttons"},[e("button",{class:"button is-light",onClick:ue,title:"Reset Filters"},s[36]||(s[36]=[e("span",{class:"icon"},[e("i",{class:"fas fa-undo"})],-1),e("span",null,"Reset",-1)]))])])])])])])]),_.value?(i(),n("div",sl,s[38]||(s[38]=[e("div",{class:"loader-wrapper"},[e("div",{class:"loader is-loading"}),e("p",{class:"mt-3"},"Loading categories...")],-1)]))):h.value?(i(),n("div",tl,[e("button",{class:"delete",onClick:s[8]||(s[8]=u=>h.value="")}),be(" "+y(h.value),1)])):(i(),n("div",ll,[o.value?(i(),n("div",al,[q(Ns,{categories:E.value,onEdit:X,onDelete:Y,onAddChild:pe,onViewProducts:Z},null,8,["categories"])])):(i(),n("div",ol,[q(es,{categories:m.value,"all-categories":E.value,loading:_.value,onEdit:X,onDelete:Y,onViewProducts:Z},null,8,["categories","all-categories","loading"]),g.value.totalPages>1?(i(),n("nav",il,[e("button",{class:"pagination-previous",onClick:s[9]||(s[9]=u=>W(g.value.currentPage-1)),disabled:g.value.currentPage<=1}," Previous ",8,nl),e("button",{class:"pagination-next",onClick:s[10]||(s[10]=u=>W(g.value.currentPage+1)),disabled:g.value.currentPage>=g.value.totalPages}," Next ",8,rl),e("ul",dl,[(i(!0),n(O,null,U(ne.value,u=>(i(),n("li",{key:u},[u!=="..."?(i(),n("button",{key:0,class:z(["pagination-link",{"is-current":u===g.value.currentPage}]),onClick:R=>W(u)},y(u),11,cl)):(i(),n("span",ul,"…"))]))),128))])])):S("",!0)]))])),q($e,{"is-open":d.value,title:"Delete Category",message:`Are you sure you want to delete '${(x=b.value)==null?void 0:x.name}'? This action cannot be undone.`,"confirm-text":"Delete","confirm-button-class":"is-danger",onConfirm:me,onCancel:j},null,8,["is-open","message"])])}}};export{pl as default};
