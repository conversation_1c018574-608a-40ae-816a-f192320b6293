<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test CompanySelect Component</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
</head>
<body>
    <div id="app" class="container mt-4">
        <h1 class="title">Test CompanySelect Component Logic</h1>
        
        <div class="box">
            <h2 class="subtitle">Test Results:</h2>
            <div v-for="result in testResults" :key="result.id" class="notification" :class="result.success ? 'is-success' : 'is-danger'">
                {{ result.message }}
            </div>
        </div>
        
        <div class="box">
            <h2 class="subtitle">Companies Data:</h2>
            <div v-if="loading" class="notification is-info">Loading companies...</div>
            <div v-else-if="companies.length > 0">
                <p><strong>Total companies loaded:</strong> {{ companies.length }}</p>
                <ul>
                    <li v-for="company in companies.slice(0, 5)" :key="company.id">
                        {{ company.name }} ({{ company.id }})
                    </li>
                </ul>
                <p v-if="companies.length > 5">... and {{ companies.length - 5 }} more</p>
            </div>
            <div v-else class="notification is-warning">No companies loaded</div>
        </div>
        
        <div class="box">
            <h2 class="subtitle">Test Controls:</h2>
            <button @click="runTests" class="button is-primary" :disabled="loading">
                {{ loading ? 'Loading...' : 'Run Tests' }}
            </button>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;

        createApp({
            setup() {
                const testResults = ref([]);
                const companies = ref([]);
                const loading = ref(false);
                let testId = 0;

                const addResult = (message, success = true) => {
                    testResults.value.push({
                        id: ++testId,
                        message,
                        success
                    });
                };

                // Simulate the companies service logic
                const fetchCompanies = async () => {
                    try {
                        loading.value = true;
                        addResult('🏢 Starting companies fetch...');
                        
                        const response = await axios.get('http://localhost:5296/api/companies?pageSize=200');
                        console.log('📦 Raw companies response:', response.data);
                        
                        // Simulate the defensive programming logic from CompanySelect
                        let companiesData = [];
                        if (response.data && response.data.data && Array.isArray(response.data.data)) {
                            companiesData = response.data.data;
                            addResult(`✅ Found companies in response.data: ${companiesData.length} companies`);
                        } else if (response.data && response.data.companies && Array.isArray(response.data.companies)) {
                            companiesData = response.data.companies;
                            addResult(`✅ Found companies in response.companies: ${companiesData.length} companies`);
                        } else if (Array.isArray(response.data)) {
                            companiesData = response.data;
                            addResult(`✅ Response is direct array: ${companiesData.length} companies`);
                        } else {
                            addResult('⚠️ Unexpected API response structure', false);
                            companiesData = [];
                        }
                        
                        companies.value = companiesData;
                        addResult(`✅ Companies loaded successfully: ${companies.value.length} total`);
                        
                        // Test filtering logic
                        const testCompany = companies.value.find(c => c.id === 'da9dcbee-6752-4121-82b6-e6c8e7106eaf');
                        if (testCompany) {
                            addResult(`✅ Test company found: ${testCompany.name}`);
                        } else {
                            addResult('❌ Test company not found', false);
                        }
                        
                    } catch (error) {
                        console.error('❌ Error fetching companies:', error);
                        addResult(`❌ Error fetching companies: ${error.message}`, false);
                        companies.value = [];
                    } finally {
                        loading.value = false;
                    }
                };

                const testFilterLogic = () => {
                    addResult('🔍 Testing filter logic...');
                    
                    // Test with empty array
                    const emptyArray = [];
                    const safeEmptyArray = Array.isArray(emptyArray) ? emptyArray : [];
                    addResult(`✅ Empty array test: ${safeEmptyArray.length} items`);
                    
                    // Test with null
                    const nullValue = null;
                    const safeNullArray = Array.isArray(nullValue) ? nullValue : [];
                    addResult(`✅ Null value test: ${safeNullArray.length} items`);
                    
                    // Test with companies array
                    if (companies.value.length > 0) {
                        const filteredCompanies = companies.value.filter(company => {
                            if (!company || typeof company !== 'object') return false;
                            return company.name && company.name.toLowerCase().includes('effertz');
                        });
                        addResult(`✅ Filter test: found ${filteredCompanies.length} companies with 'effertz'`);
                    }
                };

                const runTests = async () => {
                    testResults.value = [];
                    addResult('🚀 Starting CompanySelect component tests...');
                    
                    await fetchCompanies();
                    testFilterLogic();
                    
                    addResult('🏁 Tests completed!');
                };

                return {
                    testResults,
                    companies,
                    loading,
                    runTests
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
