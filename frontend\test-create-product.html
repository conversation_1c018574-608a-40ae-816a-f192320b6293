<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Create Product</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 400px; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <h1>Test Create Product API</h1>
    
    <div class="test-section">
        <h2>Product Creation Test</h2>
        <button class="btn-primary" onclick="testCreateProduct()">Test Create Product</button>
        <button class="btn-primary" onclick="testMinimalProduct()">Test Minimal Product</button>
        <button class="btn-secondary" onclick="getCompaniesAndCategories()">Get Companies & Categories</button>
        <button class="btn-secondary" onclick="checkAuthStatus()">Check Auth Status</button>
        <button class="btn-secondary" onclick="loginAsAdmin()">Login as Admin</button>
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5296';
        
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            resultsDiv.innerHTML += `<div class="${className}">${message}</div>`;
        }
        
        function logPre(data, title = '') {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML += `<div class="info">${title}</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
        }
        
        async function getCompaniesAndCategories() {
            document.getElementById('results').innerHTML = '';
            
            try {
                log('🔍 Fetching companies and categories...', 'info');
                
                // Get companies
                const companiesResponse = await axios.get(`${API_BASE}/api/companies?pageSize=10`);
                const companies = companiesResponse.data.data || [];
                log(`✅ Found ${companies.length} companies`, 'success');
                
                // Get categories
                const categoriesResponse = await axios.get(`${API_BASE}/api/categories/all?pageSize=10`);
                const categories = categoriesResponse.data.data || [];
                log(`✅ Found ${categories.length} categories`, 'success');
                
                if (companies.length > 0 && categories.length > 0) {
                    log(`📋 First company: ${companies[0].name} (ID: ${companies[0].id})`, 'info');
                    log(`📋 First category: ${categories[0].name} (ID: ${categories[0].id})`, 'info');
                    
                    // Store for later use
                    window.testCompany = companies[0];
                    window.testCategory = categories[0];
                } else {
                    log('❌ No companies or categories found', 'error');
                }
                
            } catch (error) {
                log(`❌ Error fetching data: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }
        
        async function testCreateProduct() {
            document.getElementById('results').innerHTML = '';
            
            try {
                log('🚀 Testing product creation...', 'info');
                
                // First get companies and categories if not already loaded
                if (!window.testCompany || !window.testCategory) {
                    await getCompaniesAndCategories();
                }
                
                if (!window.testCompany || !window.testCategory) {
                    log('❌ Cannot test without company and category data', 'error');
                    return;
                }
                
                // Prepare test product data
                const timestamp = Date.now();
                const productName = "Test Product " + timestamp;
                const productSlug = "test-product-" + timestamp;

                log(`🔧 Generated name: "${productName}"`, 'info');
                log(`🔧 Generated slug: "${productSlug}"`, 'info');

                const productData = {
                    companyId: window.testCompany.id,
                    name: productName,
                    slug: productSlug,
                    description: "This is a test product created via API",
                    priceCurrency: 0, // 0 = UAH, 1 = USD, 2 = EUR (enum as number)
                    priceAmount: 99.99,
                    stock: 10,
                    categoryId: window.testCategory.id,
                    status: 0, // 0 = Pending
                    attributes: null,
                    metaTitle: "Test Product Meta Title",
                    metaDescription: "Test Product Meta Description",
                    metaImage: "https://via.placeholder.com/300x200.png?text=Test+Product" // Valid URL
                };

                // Validate critical fields before sending
                log('🔍 Validating critical fields:', 'info');
                log(`  - companyId: "${productData.companyId}" (${typeof productData.companyId})`, 'info');
                log(`  - name: "${productData.name}" (length: ${productData.name.length})`, 'info');
                log(`  - slug: "${productData.slug}" (length: ${productData.slug.length})`, 'info');
                log(`  - description: "${productData.description}" (length: ${productData.description.length})`, 'info');
                log(`  - priceCurrency: ${productData.priceCurrency} (${typeof productData.priceCurrency})`, 'info');
                log(`  - priceAmount: ${productData.priceAmount} (${typeof productData.priceAmount})`, 'info');
                log(`  - categoryId: "${productData.categoryId}" (${typeof productData.categoryId})`, 'info');
                log(`  - metaImage: "${productData.metaImage}" (length: ${productData.metaImage.length})`, 'info');
                
                log('📤 Sending product data:', 'info');
                logPre(productData, 'Product Data:');

                // Log the JSON that will be sent
                const jsonData = JSON.stringify(productData, null, 2);
                log('📋 JSON being sent:', 'info');
                logPre(JSON.parse(jsonData), 'Actual JSON:');

                // Get auth token
                const token = localStorage.getItem('token');
                if (!token) {
                    log('❌ No auth token found. Please login first.', 'error');
                    return;
                }

                log(`🔑 Using auth token: ${token.substring(0, 20)}...`, 'info');

                // Make the API call with auth token
                const response = await axios.post(`${API_BASE}/api/admin/products`, productData, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                log('✅ Product created successfully!', 'success');
                logPre(response.data, 'Response:');
                
            } catch (error) {
                log(`❌ Error creating product: ${error.message}`, 'error');
                
                if (error.response) {
                    log(`📋 Status: ${error.response.status}`, 'error');
                    logPre(error.response.data, 'Error Response:');
                    
                    if (error.response.data && error.response.data.errors) {
                        log('📋 Validation Errors:', 'error');
                        Object.entries(error.response.data.errors).forEach(([field, messages]) => {
                            log(`  - ${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`, 'error');
                        });
                    }
                } else {
                    console.error('Full error:', error);
                }
            }
        }
        
        async function testMinimalProduct() {
            document.getElementById('results').innerHTML = '';

            try {
                log('🚀 Testing minimal product creation...', 'info');

                // First get companies and categories if not already loaded
                if (!window.testCompany || !window.testCategory) {
                    await getCompaniesAndCategories();
                }

                if (!window.testCompany || !window.testCategory) {
                    log('❌ Cannot test without company and category data', 'error');
                    return;
                }

                // Minimal product data with only required fields
                const productData = {
                    companyId: window.testCompany.id,
                    name: "Minimal Test Product",
                    slug: "minimal-test-product",
                    description: "Minimal test description",
                    priceCurrency: 0, // 0 = UAH
                    priceAmount: 10.0,
                    stock: 1,
                    categoryId: window.testCategory.id,
                    status: 0,
                    attributes: {},
                    metaTitle: "Minimal Meta Title",
                    metaDescription: "Minimal Meta Description",
                    metaImage: "https://example.com/image.jpg"
                };

                log('📤 Sending minimal product data:', 'info');
                logPre(productData, 'Minimal Product Data:');

                // Get auth token
                const token = localStorage.getItem('token');
                if (!token) {
                    log('❌ No auth token found. Please login first.', 'error');
                    return;
                }

                // Make the API call with auth token
                const response = await axios.post(`${API_BASE}/api/admin/products`, productData, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });

                log('✅ Minimal product created successfully!', 'success');
                logPre(response.data, 'Response:');

            } catch (error) {
                log(`❌ Error creating minimal product: ${error.message}`, 'error');

                if (error.response) {
                    log(`📋 Status: ${error.response.status}`, 'error');
                    logPre(error.response.data, 'Error Response:');
                } else {
                    console.error('Full error:', error);
                }
            }
        }

        // Auto-load companies and categories on page load
        window.onload = () => {
            getCompaniesAndCategories();
        };
    </script>
</body>
</html>
