<template>
  <div class="product-view">
    <!-- Loading State -->
    <div v-if="loading" class="has-text-centered py-6">
      <div class="loader is-loading"></div>
      <p class="mt-4">Loading product details...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="notification is-danger">
      <strong>Error:</strong> {{ error }}
    </div>

    <!-- Product Details -->
    <div v-else-if="product" class="product-details">
      <!-- Header -->
      <div class="hero is-light is-bold mb-6">
        <div class="hero-body py-4">
          <div class="level">
            <div class="level-left">
              <div class="level-item">
                <button class="button is-white" @click="goBack">
                  <span class="icon">
                    <i class="fas fa-arrow-left"></i>
                  </span>
                  <span>Back to Products</span>
                </button>
              </div>
              <div class="level-item">
                <div>
                  <h1 class="title is-2 has-text-dark">{{ product.name }}</h1>
                  <p class="subtitle is-5 has-text-grey">
                    <span class="icon-text">
                      <span class="icon">
                        <i class="fas fa-link"></i>
                      </span>
                      <span>{{ product.slug }}</span>
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div class="level-right">
              <div class="level-item">
                <span class="tag is-large" :class="getStatusClass(product.status)">
                  <span class="icon">
                    <i class="fas" :class="{
                      'fa-check-circle': product.status === 1 || product.status === 'approved',
                      'fa-clock': product.status === 0 || product.status === 'pending',
                      'fa-times-circle': product.status === 2 || product.status === 'rejected'
                    }"></i>
                  </span>
                  <span>{{ getStatusLabel(product.status) }}</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="columns is-multiline">
        <!-- Basic Information Card -->
        <div class="column is-12">
          <div class="card">
            <header class="card-header">
              <p class="card-header-title">
                <span class="icon-text">
                  <span class="icon has-text-primary">
                    <i class="fas fa-info-circle"></i>
                  </span>
                  <span>Basic Information</span>
                </span>
              </p>
            </header>
            <div class="card-content">
              <div class="columns is-multiline">
                <div class="column is-4">
                  <div class="field">
                    <label class="label has-text-grey">Product ID</label>
                    <div class="content">
                      <code class="has-background-light px-2 py-1">{{ product.id }}</code>
                    </div>
                  </div>
                </div>
                <div class="column is-4">
                  <div class="field">
                    <label class="label has-text-grey">Company</label>
                    <div class="content">
                      <span class="icon-text">
                        <span class="icon">
                          <i class="fas fa-building"></i>
                        </span>
                        <span>{{ companyName }}</span>
                        <button
                          v-if="currentCompany"
                          class="button is-small is-text ml-2"
                          @click="viewCompany"
                          title="View company details"
                        >
                          <span class="icon is-small">
                            <i class="fas fa-external-link-alt"></i>
                          </span>
                        </button>
                      </span>
                    </div>
                  </div>
                </div>
                <div class="column is-4">
                  <div class="field">
                    <label class="label has-text-grey">Category</label>
                    <div class="content">
                      <span class="icon-text">
                        <span class="icon">
                          <i class="fas fa-folder"></i>
                        </span>
                        <span>{{ categoryName }}</span>
                      </span>
                    </div>
                  </div>
                </div>
                <div class="column is-12">
                  <div class="field">
                    <label class="label has-text-grey">Description</label>
                    <div class="content">
                      <p class="has-text-dark">{{ product.description || 'No description provided' }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Pricing & Inventory Card -->
        <div class="column is-6">
          <div class="card">
            <header class="card-header">
              <p class="card-header-title">
                <span class="icon-text">
                  <span class="icon has-text-success">
                    <i class="fas fa-dollar-sign"></i>
                  </span>
                  <span>Pricing & Inventory</span>
                </span>
              </p>
            </header>
            <div class="card-content">
              <div class="columns">
                <div class="column is-6">
                  <div class="field">
                    <label class="label has-text-grey">Price</label>
                    <div class="content">
                      <span class="tag is-large is-success">
                        <span class="icon">
                          <i class="fas fa-hryvnia-sign"></i>
                        </span>
                        <span>{{ product.priceAmount }} {{ product.priceCurrency }}</span>
                      </span>
                    </div>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label has-text-grey">Stock</label>
                    <div class="content">
                      <span class="tag is-large" :class="product.stock > 0 ? 'is-info' : 'is-danger'">
                        <span class="icon">
                          <i class="fas fa-boxes"></i>
                        </span>
                        <span>{{ product.stock }} units</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Status Card -->
        <div class="column is-6">
          <div class="card">
            <header class="card-header">
              <p class="card-header-title">
                <span class="icon-text">
                  <span class="icon has-text-info">
                    <i class="fas fa-chart-line"></i>
                  </span>
                  <span>Status</span>
                </span>
              </p>
            </header>
            <div class="card-content">
              <div class="field">
                <label class="label has-text-grey">Product Status</label>
                <div class="content">
                  <span class="tag is-large" :class="getStatusClass(product.status)">
                    <span class="icon">
                      <i class="fas" :class="{
                        'fa-check-circle': product.status === 1 || product.status === 'approved',
                        'fa-clock': product.status === 0 || product.status === 'pending',
                        'fa-times-circle': product.status === 2 || product.status === 'rejected'
                      }"></i>
                    </span>
                    <span>{{ getStatusLabel(product.status) }}</span>
                  </span>
                </div>
              </div>
              <div v-if="product.approvedAt" class="field">
                <label class="label has-text-grey">Approved At</label>
                <div class="content">
                  <span class="icon-text">
                    <span class="icon">
                      <i class="fas fa-calendar-check"></i>
                    </span>
                    <span>{{ formatDate(product.approvedAt) }}</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Attributes Card -->
        <div v-if="parsedAttributes && Object.keys(parsedAttributes).length > 0" class="column is-12">
          <div class="card">
            <header class="card-header">
              <p class="card-header-title">
                <span class="icon-text">
                  <span class="icon has-text-warning">
                    <i class="fas fa-tags"></i>
                  </span>
                  <span>Attributes</span>
                </span>
              </p>
            </header>
            <div class="card-content">
              <div class="table-container">
                <table class="table is-fullwidth is-striped is-hoverable">
                  <thead>
                    <tr>
                      <th>Attribute</th>
                      <th>Values</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(values, key) in parsedAttributes" :key="key">
                      <td><strong>{{ key }}</strong></td>
                      <td>
                        <span v-if="Array.isArray(values)" class="tags">
                          <span v-for="value in values" :key="value" class="tag is-light">{{ value }}</span>
                        </span>
                        <span v-else class="tag is-light">{{ values }}</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- Product Images Card -->
        <div class="column is-12">
          <div class="card">
            <header class="card-header">
              <p class="card-header-title">
                <span class="icon-text">
                  <span class="icon has-text-link">
                    <i class="fas fa-images"></i>
                  </span>
                  <span>Product Images</span>
                  <span v-if="productImages.length > 0" class="tag is-light ml-2">{{ productImages.length }}</span>
                </span>
              </p>
            </header>
            <div class="card-content">
              <!-- Images Gallery -->
              <div v-if="productImages.length > 0" class="columns is-multiline">
                <div v-for="image in productImages" :key="image.id" class="column is-3">
                  <div class="card">
                    <div class="card-image">
                      <figure class="image is-square">
                        <img :src="image.imageUrl"
                             :alt="image.altText || 'Product image'"
                             @error="handleImageError"
                             class="is-rounded">
                      </figure>
                    </div>
                    <div class="card-content p-2">
                      <div class="level is-mobile">
                        <div class="level-left">
                          <div class="level-item">
                            <span class="tag is-small" :class="image.isMain ? 'is-primary' : 'is-light'">
                              <span class="icon is-small">
                                <i class="fas" :class="image.isMain ? 'fa-star' : 'fa-image'"></i>
                              </span>
                              <span>{{ image.isMain ? 'Main' : `#${image.order}` }}</span>
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- No Images State -->
              <div v-else class="has-text-centered py-6">
                <div class="notification is-light">
                  <span class="icon is-large has-text-grey-light">
                    <i class="fas fa-images fa-3x"></i>
                  </span>
                  <p class="has-text-grey mt-3">
                    <strong>No images available</strong><br>
                    <small>This product doesn't have any images yet.</small>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- SEO Meta Card -->
        <div v-if="product.metaTitle || product.metaDescription || product.metaImage" class="column is-6">
          <div class="card">
            <header class="card-header">
              <p class="card-header-title">
                <span class="icon-text">
                  <span class="icon has-text-warning">
                    <i class="fas fa-search"></i>
                  </span>
                  <span>SEO Meta</span>
                </span>
              </p>
            </header>
            <div class="card-content">
              <div v-if="product.metaTitle" class="field">
                <label class="label has-text-grey">Meta Title</label>
                <div class="content">
                  <p class="has-text-dark">{{ product.metaTitle }}</p>
                </div>
              </div>
              <div v-if="product.metaDescription" class="field">
                <label class="label has-text-grey">Meta Description</label>
                <div class="content">
                  <p class="has-text-dark">{{ product.metaDescription }}</p>
                </div>
              </div>
              <div v-if="product.metaImage" class="field">
                <label class="label has-text-grey">Meta Image</label>
                <div class="content has-text-centered">
                  <figure class="image is-128x128 is-inline-block">
                    <img :src="product.metaImage" :alt="product.name"
                         @error="handleImageError" class="is-rounded">
                  </figure>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Timestamps Card -->
        <div class="column is-6">
          <div class="card">
            <header class="card-header">
              <p class="card-header-title">
                <span class="icon-text">
                  <span class="icon has-text-grey">
                    <i class="fas fa-clock"></i>
                  </span>
                  <span>Timestamps</span>
                </span>
              </p>
            </header>
            <div class="card-content">
              <div class="field">
                <label class="label has-text-grey">Created At</label>
                <div class="content">
                  <span class="icon-text">
                    <span class="icon">
                      <i class="fas fa-calendar-plus"></i>
                    </span>
                    <span>{{ formatDate(product.createdAt) }}</span>
                  </span>
                </div>
              </div>
              <div v-if="product.updatedAt" class="field">
                <label class="label has-text-grey">Updated At</label>
                <div class="content">
                  <span class="icon-text">
                    <span class="icon">
                      <i class="fas fa-calendar-edit"></i>
                    </span>
                    <span>{{ formatDate(product.updatedAt) }}</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { productsService } from '@/admin/services/products';
import { categoriesService } from '@/admin/services/categories';
import { companiesService } from '@/admin/services/companies';
import api from '@/services/api';

// Props
const props = defineProps({
  productId: {
    type: String,
    required: false
  }
});

// Route
const route = useRoute();
const router = useRouter();

// State
const product = ref(null);
const loading = ref(true);
const error = ref(null);
const companies = ref([]);
const categories = ref([]);
const productImages = ref([]);

// Get product ID from props or route
const currentProductId = computed(() => {
  return props.productId || route.params.id;
});

// Computed properties
const companyName = computed(() => {
  if (!product.value?.companyId || !companies.value.length) return 'Unknown Company';
  const company = companies.value.find(c => c.id === product.value.companyId);
  return company?.name || 'Unknown Company';
});

const categoryName = computed(() => {
  // First try to get category name from product.categoryName
  if (product.value?.categoryName) {
    // Extract only the last part of the hierarchy (Grandchild)
    // If categoryName is "Parent > Child > Grandchild", we want only "Grandchild"
    const parts = product.value.categoryName.split(' > ');
    return parts[parts.length - 1].trim();
  }

  // If categoryName is null, try to find category by ID in loaded categories
  if (product.value?.categoryId && categories.value.length > 0) {
    const category = categories.value.find(cat => cat.id === product.value.categoryId);
    return category?.name || 'Unknown Category';
  }

  return 'Unknown Category';
});

const currentCompany = computed(() => {
  if (!product.value?.companyId || !companies.value.length) return null;
  return companies.value.find(c => c.id === product.value.companyId);
});

const parsedAttributes = computed(() => {
  if (!product.value?.attributes) return {};

  try {
    if (typeof product.value.attributes === 'string') {
      return JSON.parse(product.value.attributes);
    }
    return product.value.attributes;
  } catch (e) {
    console.error('Error parsing attributes:', e);
    return {};
  }
});

const mainImage = computed(() => {
  return productImages.value.find(img => img.isMain) || productImages.value[0] || null;
});

// Methods
const getStatusClass = (status) => {
  if (!status) return 'is-light';
  const statusStr = String(status).toLowerCase();
  switch (statusStr) {
    case 'approved': case '1': return 'is-success';
    case 'pending': case '0': return 'is-warning';
    case 'rejected': case '2': return 'is-danger';
    default: return 'is-light';
  }
};

const getStatusLabel = (status) => {
  if (!status) return 'Unknown';
  const statusStr = String(status).toLowerCase();
  switch (statusStr) {
    case 'approved': case '1': return 'Approved';
    case 'pending': case '0': return 'Pending';
    case 'rejected': case '2': return 'Rejected';
    default: return 'Unknown';
  }
};

const getApprovalClass = (isApproved) => {
  return isApproved ? 'is-success' : 'is-warning';
};

const formatDate = (dateString) => {
  // Додаткове логування для діагностики
  console.log('🕐 Formatting date:', { dateString, type: typeof dateString });

  if (!dateString) {
    console.warn('⚠️ Empty date string provided');
    return 'N/A';
  }

  try {
    // Handle different date formats from backend
    let date;

    if (typeof dateString === 'string') {
      // Handle ISO string format: "2025-06-02T20:35:40.231835+03:00"
      // Handle backend format: "2025-06-02 20:35:40.231835+03"
      // Handle simple format: "2025-06-02"

      // Normalize the date string
      let normalizedDate = dateString.trim();

      // If it contains space instead of T, replace it
      if (normalizedDate.includes(' ') && !normalizedDate.includes('T')) {
        normalizedDate = normalizedDate.replace(' ', 'T');
      }

      // If timezone is in format +03 instead of +03:00, fix it
      if (/[+-]\d{2}$/.test(normalizedDate)) {
        normalizedDate += ':00';
      }

      console.log('🔄 Normalized date string:', normalizedDate);
      date = new Date(normalizedDate);
    } else if (dateString instanceof Date) {
      date = dateString;
    } else {
      // Try to convert to date directly
      date = new Date(dateString);
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
      console.warn('❌ Invalid date after parsing:', { original: dateString, parsed: date });
      return 'Invalid Date';
    }

    const formatted = new Intl.DateTimeFormat('uk-UA', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZone: 'Europe/Kiev'
    }).format(date);

    console.log('✅ Date formatted successfully:', { original: dateString, formatted });
    return formatted;
  } catch (e) {
    console.error('❌ Error formatting date:', e, dateString);
    return String(dateString) || 'N/A';
  }
};

const handleImageError = (event) => {
  event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjgiIGhlaWdodD0iMTI4IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk2IDgwLjU2ODUgOTYgNjRDOTYgNDcuNDMxNSA4MC41Njg1IDMyIDY0IDMyQzQ3LjQzMTUgMzIgMzIgNDcuNDMxNSAzMiA2NEMzMiA4MC41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEMUQxRDEiIHN0cm9rZS13aWR0aD0iNCIgZmlsbD0ibm9uZSIvPgo8L3N2Zz4K';
};

const viewCompany = () => {
  if (currentCompany.value) {
    // Navigate to company view page
    router.push(`/admin/companies/${currentCompany.value.id}`);
  }
};

const goBack = () => {
  router.push('/admin/products');
};

// Load data
const loadProduct = async () => {
  try {
    loading.value = true;
    error.value = null;

    console.log('🔄 Loading product with ID:', currentProductId.value);
    const response = await productsService.getProductById(currentProductId.value);
    product.value = response.data || response;

    console.log('📦 Product loaded:', product.value);
    console.log('📅 Product dates:', {
      createdAt: product.value?.createdAt,
      updatedAt: product.value?.updatedAt,
      approvedAt: product.value?.approvedAt
    });

    // Load product images
    await loadProductImages();
  } catch (err) {
    console.error('❌ Error loading product:', err);
    error.value = err.message || 'Failed to load product';
  } finally {
    loading.value = false;
  }
};

const loadProductImages = async () => {
  try {
    const response = await api.get(`/api/admin/products/${currentProductId.value}/with-images`);

    if (response.data && response.data.success && response.data.data) {
      productImages.value = response.data.data.images || [];
      console.log('Product images loaded:', productImages.value.length);
    }
  } catch (err) {
    console.error('Error loading product images:', err);
    productImages.value = [];
  }
};

const loadCompanies = async () => {
  try {
    console.log('🏢 Loading companies for view...');
    const response = await companiesService.getCompanies({ pageSize: 200 });
    console.log('📦 Raw companies response:', response);

    // Defensive programming: ensure we always get an array
    let companiesData = [];
    if (response && response.data && Array.isArray(response.data)) {
      companiesData = response.data;
    } else if (response && response.companies && Array.isArray(response.companies)) {
      companiesData = response.companies;
    } else if (Array.isArray(response)) {
      companiesData = response;
    } else {
      console.warn('⚠️ Unexpected companies response structure:', response);
      companiesData = [];
    }

    companies.value = companiesData;
    console.log('✅ Companies loaded for view:', companies.value.length);
  } catch (err) {
    console.error('❌ Error loading companies for view:', err);
    companies.value = [];
  }
};

const loadCategories = async () => {
  try {
    console.log('🏷️ Loading categories for view...');
    const categoriesData = await productsService.getCategories({ pageSize: 1000 });
    categories.value = Array.isArray(categoriesData) ? categoriesData : [];
    console.log('✅ Categories loaded for view:', categories.value.length);
  } catch (err) {
    console.error('❌ Error loading categories for view:', err);
    categories.value = [];
  }
};

// Initialize
onMounted(async () => {
  await Promise.all([
    loadProduct(),
    loadCompanies(),
    loadCategories()
  ]);
});
</script>

<style scoped>
.product-view {
  padding: 1rem;
}

.loader {
  width: 3rem;
  height: 3rem;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.box {
  margin-bottom: 1.5rem;
}

.tags .tag {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.image img {
  object-fit: cover;
}
</style>
