/**
 * Модуль для роботи з ролями користувачів
 * Забезпечує єдиний інтерфейс для роботи з ролями в усьому додатку
 */

// Числові значення ролей, які використовуються на бекенді
export const ROLE_VALUES = {
  BUYER: 0,
  SELLER: 1,
  SELLER_OWNER: 2,
  MODERATOR: 3,
  ADMIN: 4
};

// Рядкові ідентифікатори ролей, які використовуються на фронтенді
export const ROLE_KEYS = {
  BUYER: 'buyer',
  SELLER: 'seller',
  SELLER_OWNER: 'sellerowner',
  MODERATOR: 'moderator',
  ADMIN: 'admin'
};

// Відображувані назви ролей
export const ROLE_DISPLAY_NAMES = {
  [ROLE_KEYS.BUYER]: 'Buyer',
  [ROLE_KEYS.SELLER]: 'Seller',
  [ROLE_KEYS.SELLER_OWNER]: 'Seller Owner',
  [ROLE_KEYS.MODERATOR]: 'Moderator',
  [ROLE_KEYS.ADMIN]: 'Admin',
  'unknown': 'Unknown'
};

// Мапа для перетворення числових значень ролей у рядкові ідентифікатори
export const ROLE_NUMBER_TO_KEY = {
  [ROLE_VALUES.BUYER]: ROLE_KEYS.BUYER,
  [ROLE_VALUES.SELLER]: ROLE_KEYS.SELLER,
  [ROLE_VALUES.SELLER_OWNER]: ROLE_KEYS.SELLER_OWNER,
  [ROLE_VALUES.MODERATOR]: ROLE_KEYS.MODERATOR,
  [ROLE_VALUES.ADMIN]: ROLE_KEYS.ADMIN
};

// Мапа для перетворення рядкових ідентифікаторів ролей у числові значення
export const ROLE_KEY_TO_NUMBER = {
  [ROLE_KEYS.BUYER]: ROLE_VALUES.BUYER,
  [ROLE_KEYS.SELLER]: ROLE_VALUES.SELLER,
  [ROLE_KEYS.SELLER_OWNER]: ROLE_VALUES.SELLER_OWNER,
  [ROLE_KEYS.MODERATOR]: ROLE_VALUES.MODERATOR,
  [ROLE_KEYS.ADMIN]: ROLE_VALUES.ADMIN
};

// Порядок сортування ролей (менше значення = вищий пріоритет)
// Admin-Moderator-Seller Owner-Seller-Buyer
export const ROLE_ORDER = {
  [ROLE_KEYS.ADMIN]: 1,
  [ROLE_KEYS.MODERATOR]: 2,
  [ROLE_KEYS.SELLER_OWNER]: 3,
  [ROLE_KEYS.SELLER]: 4,
  [ROLE_KEYS.BUYER]: 5,
  'unknown': 6
};

// CSS-класи для відображення ролей
export const ROLE_CLASSES = {
  [ROLE_KEYS.ADMIN]: 'is-danger',
  [ROLE_KEYS.MODERATOR]: 'is-warning',
  [ROLE_KEYS.SELLER]: 'is-info',
  [ROLE_KEYS.SELLER_OWNER]: 'is-info',
  [ROLE_KEYS.BUYER]: 'is-success',
  'unknown': 'is-light'
};

/**
 * Перетворює роль у рядковий ідентифікатор
 * @param {number|string|object} role - Роль користувача
 * @returns {string} - Рядковий ідентифікатор ролі
 */
export function getRoleKey(role) {
  if (role === undefined || role === null) {
    return 'unknown';
  }

  // Якщо роль - це число, перетворюємо його на рядковий ідентифікатор
  if (typeof role === 'number') {
    return ROLE_NUMBER_TO_KEY[role] || 'unknown';
  }

  // Якщо роль - це рядок, перевіряємо, чи це валідний ідентифікатор
  if (typeof role === 'string') {
    const roleLower = role.toLowerCase();
    if (Object.values(ROLE_KEYS).includes(roleLower)) {
      return roleLower;
    }
    
    // Перевіряємо, чи це відображуване ім'я ролі
    const roleKey = Object.keys(ROLE_DISPLAY_NAMES).find(
      key => ROLE_DISPLAY_NAMES[key].toLowerCase() === roleLower
    );
    
    if (roleKey) {
      return roleKey;
    }
    
    return 'unknown';
  }

  // Якщо роль - це об'єкт, намагаємося витягнути роль з нього
  if (typeof role === 'object') {
    if (role.hasOwnProperty('name')) {
      return getRoleKey(role.name);
    }
    
    if (role.hasOwnProperty('value')) {
      return getRoleKey(role.value);
    }
  }

  return 'unknown';
}

/**
 * Перетворює роль у числове значення
 * @param {number|string|object} role - Роль користувача
 * @returns {number} - Числове значення ролі
 */
export function getRoleValue(role) {
  const roleKey = getRoleKey(role);
  return ROLE_KEY_TO_NUMBER[roleKey] !== undefined 
    ? ROLE_KEY_TO_NUMBER[roleKey] 
    : -1;
}

/**
 * Повертає відображуване ім'я ролі
 * @param {number|string|object} role - Роль користувача
 * @returns {string} - Відображуване ім'я ролі
 */
export function getRoleDisplayName(role) {
  const roleKey = getRoleKey(role);
  return ROLE_DISPLAY_NAMES[roleKey] || 'Unknown';
}

/**
 * Повертає CSS-клас для відображення ролі
 * @param {number|string|object} role - Роль користувача
 * @returns {string} - CSS-клас
 */
export function getRoleClass(role) {
  const roleKey = getRoleKey(role);
  return ROLE_CLASSES[roleKey] || 'is-light';
}

/**
 * Порівнює дві ролі для сортування
 * @param {number|string|object} roleA - Перша роль
 * @param {number|string|object} roleB - Друга роль
 * @returns {number} - Результат порівняння (-1, 0, 1)
 */
export function compareRoles(roleA, roleB) {
  const keyA = getRoleKey(roleA);
  const keyB = getRoleKey(roleB);
  
  return (ROLE_ORDER[keyA] || 5) - (ROLE_ORDER[keyB] || 5);
}

/**
 * Повертає список всіх можливих ролей
 * @returns {Array} - Масив об'єктів з ключем, значенням та відображуваним ім'ям ролі
 */
export function getAllRoles() {
  return Object.values(ROLE_KEYS).map(key => ({
    key,
    value: ROLE_KEY_TO_NUMBER[key],
    displayName: ROLE_DISPLAY_NAMES[key]
  }));
}

/**
 * Перевіряє, чи є роль адміністратором
 * @param {number|string|object} role - Роль користувача
 * @returns {boolean} - true, якщо роль - адміністратор
 */
export function isAdmin(role) {
  return getRoleKey(role) === ROLE_KEYS.ADMIN;
}

/**
 * Перевіряє, чи є роль модератором
 * @param {number|string|object} role - Роль користувача
 * @returns {boolean} - true, якщо роль - модератор
 */
export function isModerator(role) {
  return getRoleKey(role) === ROLE_KEYS.MODERATOR;
}

/**
 * Перевіряє, чи є роль продавцем
 * @param {number|string|object} role - Роль користувача
 * @returns {boolean} - true, якщо роль - продавець
 */
export function isSeller(role) {
  const key = getRoleKey(role);
  return key === ROLE_KEYS.SELLER || key === ROLE_KEYS.SELLER_OWNER;
}

/**
 * Перевіряє, чи є роль покупцем
 * @param {number|string|object} role - Роль користувача
 * @returns {boolean} - true, якщо роль - покупець
 */
export function isBuyer(role) {
  return getRoleKey(role) === ROLE_KEYS.BUYER;
}
