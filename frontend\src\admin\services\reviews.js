import api from '@/services/api';

export const reviewsService = {
  async getReviews(params = {}) {
    try {
      const response = await api.get('/api/admin/reviews', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching reviews:', error);
      throw new Error(error.response?.data?.message || 'Failed to load reviews');
    }
  },

  async getReviewById(id) {
    try {
      const response = await api.get(`/api/admin/reviews/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching review:', error);
      throw new Error(error.response?.data?.message || 'Failed to load review details');
    }
  },

  async updateReview(id, data) {
    try {
      const response = await api.put(`/api/admin/reviews/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating review:', error);
      throw new Error(error.response?.data?.message || 'Failed to update review');
    }
  },

  async deleteReview(id) {
    try {
      const response = await api.delete(`/api/admin/reviews/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting review:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete review');
    }
  },

  async bulkDeleteReviews(ids) {
    try {
      const response = await api.post('/api/admin/reviews/bulk-delete', { ids });
      return response.data;
    } catch (error) {
      console.error('Error bulk deleting reviews:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete reviews');
    }
  },

  async getReviewStats() {
    try {
      const response = await api.get('/api/admin/reviews/stats');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching review stats:', error);
      throw new Error(error.response?.data?.message || 'Failed to load review statistics');
    }
  }
};
