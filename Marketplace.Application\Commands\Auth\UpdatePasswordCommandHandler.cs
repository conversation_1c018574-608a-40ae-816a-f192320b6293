﻿using AutoMapper;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using Marketplace.Infrastructure.Services.Auth;
using MediatR;

namespace Marketplace.Application.Commands.Auth;

public class UpdatePasswordCommandHandler : IRequestHandler<UpdatePasswordCommand>
{
    private readonly IUserRepository _userRepository;
    private readonly IPasswordHasher _passwordHasher;
    private readonly IMapper _mapper;

    public UpdatePasswordCommandHandler(IUserRepository userRepository, IMapper mapper, IPasswordHasher passwordHasher)
    {
        _userRepository = userRepository;
        _mapper = mapper;
        _passwordHasher = passwordHasher;
    }

    public async Task Handle(UpdatePasswordCommand request, CancellationToken cancellationToken)
    {
        var user = await _userRepository.GetByIdAsync(request.Id, cancellationToken)
        ?? throw new KeyNotFoundException("Користувача не знайдено!");

        if (!string.IsNullOrEmpty(user.Password.Value))
        {
            bool passwordVerified = _passwordHasher.VerifyPassword(user.Password.Value, request.CurrentPassword);
            if (!passwordVerified)
                throw new UnauthorizedAccessException("Невірний пароль!");
        }

        // Hash the new password
        string hashedPassword = _passwordHasher.HashPassword(request.NewPassword);
        user.Password = new Password(hashedPassword);
        await _userRepository.UpdateAsync(user, cancellationToken);
    }
}