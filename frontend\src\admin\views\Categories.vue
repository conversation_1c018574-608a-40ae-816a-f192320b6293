<template>
  <div class="admin-page">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <h1 class="admin-page-title">
          <i class="fas fa-tags admin-page-icon"></i>
          Categories Management
        </h1>
        <p class="admin-page-subtitle">Manage category hierarchy and product organization</p>
      </div>
      <div class="admin-page-actions">
        <router-link
          to="/admin/categories/create"
          class="admin-btn admin-btn-primary"
          :class="{ 'admin-btn-disabled': loading }">
          <i class="fas fa-plus"></i>
          Add Category
        </router-link>
        <button
          class="admin-btn admin-btn-info"
          @click="toggleView"
          :disabled="loading">
          <i :class="showHierarchy ? 'fas fa-table' : 'fas fa-sitemap'"></i>
          {{ showHierarchy ? 'Table View' : 'Hierarchy View' }}
        </button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="admin-stats-grid">
      <div class="admin-stat-card">
        <div class="admin-stat-content">
          <div class="admin-stat-info">
            <div class="admin-stat-label">Total Categories</div>
            <div class="admin-stat-value">{{ stats.total }}</div>
          </div>
          <div class="admin-stat-icon admin-stat-icon-primary">
            <i class="fas fa-folder"></i>
          </div>
        </div>
      </div>

      <div class="admin-stat-card">
        <div class="admin-stat-content">
          <div class="admin-stat-info">
            <div class="admin-stat-label">Root Categories</div>
            <div class="admin-stat-value">{{ stats.rootCategories }}</div>
          </div>
          <div class="admin-stat-icon admin-stat-icon-info">
            <i class="fas fa-sitemap"></i>
          </div>
        </div>
      </div>

      <div class="admin-stat-card">
        <div class="admin-stat-content">
          <div class="admin-stat-info">
            <div class="admin-stat-label">With Products</div>
            <div class="admin-stat-value">{{ stats.withProducts }}</div>
          </div>
          <div class="admin-stat-icon admin-stat-icon-success">
            <i class="fas fa-box"></i>
          </div>
        </div>
      </div>

      <div class="admin-stat-card">
        <div class="admin-stat-content">
          <div class="admin-stat-info">
            <div class="admin-stat-label">Total Products</div>
            <div class="admin-stat-value">{{ stats.totalProducts }}</div>
          </div>
          <div class="admin-stat-icon admin-stat-icon-warning">
            <i class="fas fa-shopping-bag"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="admin-card">
      <div class="admin-card-header">
        <h3 class="admin-card-title">
          <i class="fas fa-search"></i>
          Search & Filters
        </h3>
      </div>
      <div class="admin-card-content">
        <div class="admin-form-grid admin-form-grid-3">
          <div class="admin-form-field">
            <label class="admin-form-label">Search Categories</label>
            <div class="admin-form-control admin-form-control-group">
              <select v-model="searchField" class="admin-form-select admin-form-select-compact">
                <option value="all">Name & Slug</option>
                <option value="name">Name Only</option>
                <option value="slug">Slug Only</option>
                <option value="description">Description Only</option>
              </select>
              <div class="admin-form-input-group">
                <input
                  class="admin-form-input"
                  type="text"
                  :placeholder="getSearchPlaceholder()"
                  v-model="searchQuery"
                  @input="debouncedSearch">
                <div class="admin-form-input-icon">
                  <i class="fas fa-search"></i>
                </div>
              </div>
              <button
                v-if="searchQuery"
                class="admin-btn admin-btn-sm admin-btn-light"
                @click="clearSearch"
                title="Clear search">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>

          <div class="admin-form-field">
            <label class="admin-form-label">Parent Category</label>
            <div class="admin-form-control">
              <select v-model="filters.parentId" class="admin-form-select">
                <option value="">All Categories</option>
                <option value="root">Root Categories Only</option>
                <option
                  v-for="category in filterCategories"
                  :key="category.id"
                  :value="category.id">
                  {{ category.name }}
                </option>
              </select>
            </div>
          </div>

          <div class="admin-form-field">
            <label class="admin-form-label">Product Count</label>
            <div class="admin-form-control">
              <select v-model="filters.productCount" class="admin-form-select">
                <option value="">Any</option>
                <option value="empty">Empty (0 products)</option>
                <option value="hasProducts">Has Products (>0)</option>
                <option value="many">Many Products (>10)</option>
              </select>
            </div>
          </div>
        </div>

        <div class="admin-form-grid admin-form-grid-2">
          <div class="admin-form-field">
            <label class="admin-form-label">Sort By</label>
            <div class="admin-form-control">
              <select v-model="sortBy" class="admin-form-select">
                <option value="name">Name</option>
                <option value="productCount">Product Count</option>
              </select>
            </div>
          </div>

          <div class="admin-form-field">
            <label class="admin-form-label">Order</label>
            <div class="admin-form-control">
              <select v-model="sortOrder" class="admin-form-select">
                <option value="asc">Ascending</option>
                <option value="desc">Descending</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
          <div class="column is-3">
            <div class="field">
              <label class="label">Items per page</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="pagination.perPage" @change="changePageSize">
                    <option value="10">10 per page</option>
                    <option value="20">20 per page</option>
                    <option value="50">50 per page</option>
                    <option value="100">100 per page</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="column is-3">
            <div class="field">
              <label class="label">&nbsp;</label>
              <div class="control">
                <div class="buttons">
                  <button
                    class="button is-light"
                    @click="resetFilters"
                    title="Reset Filters">
                    <span class="icon">
                      <i class="fas fa-undo"></i>
                    </span>
                    <span>Reset</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    <!-- Loading State -->
    <div v-if="loading" class="has-text-centered py-6">
      <div class="loader-wrapper">
        <div class="loader is-loading"></div>
        <p class="mt-3">Loading categories...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="notification is-danger">
      <button class="delete" @click="error = ''"></button>
      {{ error }}
    </div>

    <!-- Content -->
    <div v-else>
      <!-- Hierarchy View -->
      <div v-if="showHierarchy">
        <CategoryHierarchy
          :categories="allCategories"
          @edit="editCategory"
          @delete="confirmDelete"
          @add-child="createChildCategory"
          @view-products="viewCategoryProducts"
        />
      </div>

      <!-- Table View -->
      <div v-else>
        <CategoryTable
          :categories="categories"
          :all-categories="allCategories"
          :loading="loading"
          @edit="editCategory"
          @delete="confirmDelete"
          @view-products="viewCategoryProducts"
        />
        
        <!-- Pagination -->
        <nav class="pagination is-centered mt-5" v-if="pagination.totalPages > 1">
          <button 
            class="pagination-previous" 
            @click="changePage(pagination.currentPage - 1)"
            :disabled="pagination.currentPage <= 1">
            Previous
          </button>
          <button 
            class="pagination-next" 
            @click="changePage(pagination.currentPage + 1)"
            :disabled="pagination.currentPage >= pagination.totalPages">
            Next
          </button>
          <ul class="pagination-list">
            <li v-for="page in visiblePages" :key="page">
              <button 
                v-if="page !== '...'"
                class="pagination-link"
                :class="{ 'is-current': page === pagination.currentPage }"
                @click="changePage(page)">
                {{ page }}
              </button>
              <span v-else class="pagination-ellipsis">&hellip;</span>
            </li>
          </ul>
        </nav>
      </div>
    </div>

    <!-- Modals -->
    <ConfirmDialog
      :is-open="showDeleteDialog"
      :title="'Delete Category'"
      :message="`Are you sure you want to delete '${categoryToDelete?.name}'? This action cannot be undone.`"
      :confirm-text="'Delete'"
      :confirm-button-class="'is-danger'"
      @confirm="handleDelete"
      @cancel="closeDeleteDialog"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import CategoryTable from '@/admin/components/categories/CategoryTable.vue';
import CategoryHierarchy from '@/admin/components/categories/CategoryHierarchy.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
import { categoriesService } from '@/admin/services/categories';

// Debounce utility function
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Router
const router = useRouter();

// Reactive data
const loading = ref(false);
const error = ref('');
const categories = ref([]);
const selectedCategory = ref(null);
const categoryToDelete = ref(null);

// UI state
const showDeleteDialog = ref(false);
const showHierarchy = ref(false);

// Search and filters
const searchQuery = ref('');
const searchField = ref('all');
const filters = ref({
  parentId: '',
  productCount: ''
});
const sortBy = ref('name');
const sortOrder = ref('asc');

// Pagination
const pagination = ref({
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  perPage: 20
});

// Stats
const stats = ref({
  total: 0,
  rootCategories: 0,
  withProducts: 0,
  totalProducts: 0
});

// Computed properties
const parentCategories = computed(() => {
  return allCategories.value.filter(cat => !cat.parentId);
});

// Get all categories that belong to a specific root category
const getCategoriesByRoot = (rootCategoryId) => {
  const result = [];
  const visited = new Set();

  const findChildren = (parentId) => {
    allCategories.value.forEach(cat => {
      if (cat.parentId === parentId && !visited.has(cat.id)) {
        visited.add(cat.id);
        result.push(cat);
        findChildren(cat.id); // Recursively find children
      }
    });
  };

  findChildren(rootCategoryId);
  return result;
};

// Only root categories for dropdown filter
const filterCategories = computed(() => {
  return parentCategories.value;
});

const visiblePages = computed(() => {
  const current = pagination.value.currentPage;
  const total = pagination.value.totalPages;
  const pages = [];

  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    if (current <= 4) {
      pages.push(1, 2, 3, 4, 5, '...', total);
    } else if (current >= total - 3) {
      pages.push(1, '...', total - 4, total - 3, total - 2, total - 1, total);
    } else {
      pages.push(1, '...', current - 1, current, current + 1, '...', total);
    }
  }

  return pages;
});

// Debounced search
const debouncedSearch = debounce(() => {
  pagination.value.currentPage = 1;
  fetchCategories();
}, 300);

// Store all categories for dropdown and hierarchy
const allCategories = ref([]);

// Fetch all categories for dropdown and other operations
const fetchAllCategories = async () => {
  try {
    const response = await categoriesService.getAll({ pageSize: 10000 }); // Increase limit
    allCategories.value = response.data || [];
    console.log('📊 Fetched all categories:', allCategories.value.length);
    console.log('📊 Category hierarchy structure:', allCategories.value.map(cat => ({
      id: cat.id,
      name: cat.name,
      parentId: cat.parentId
    })));
  } catch (err) {
    console.error('Error fetching all categories:', err);
  }
};

// Methods
const fetchCategories = async () => {
  loading.value = true;
  error.value = '';

  try {
    // Always fetch all categories for dropdown
    await fetchAllCategories();
    const params = {
      page: pagination.value.currentPage,
      pageSize: pagination.value.perPage,
      // Don't send productCount sorting to backend as it's calculated after DB query
      orderBy: sortBy.value === 'productCount' ? 'name' : sortBy.value,
      descending: sortOrder.value === 'desc'
    };

    // Add search parameters
    if (searchQuery.value && searchField.value === 'all') {
      // Use backend search for name and slug
      params.filter = searchQuery.value;
    }

    // Add parent filter
    if (filters.value.parentId === 'root') {
      params.parentId = null;
    } else if (filters.value.parentId) {
      // Don't send parentId to backend, we'll filter on frontend
      // to include all descendants of the selected root category
    }

    // Note: Product count filter is applied on frontend after data is received

    const response = await categoriesService.getAll(params);

    let categoriesData = response.data || [];

    // If we have search query but not using backend search, get more data
    if (searchQuery.value && searchField.value !== 'all') {
      // Get all categories for frontend search
      try {
        const allResponse = await categoriesService.getAll({
          pageSize: 1000,
          orderBy: sortBy.value === 'productCount' ? 'name' : sortBy.value,
          descending: sortOrder.value === 'desc'
        });
        categoriesData = allResponse.data || [];
      } catch (err) {
        console.warn('Failed to get all categories for search, using current page:', err);
        // Use current data if failed to get all
        categoriesData = response.data || [];
      }
    }

    // Apply parent category filter (show all descendants of selected root)
    if (filters.value.parentId && filters.value.parentId !== 'root') {
      const selectedRootId = filters.value.parentId;
      const descendantIds = new Set();

      console.log('🔍 Filtering by root category:', selectedRootId);
      console.log('📊 All categories available:', allCategories.value.length);
      console.log('📊 Categories before filter:', categoriesData.length);

      // Get all descendants of the selected root category using allCategories
      const addDescendants = (parentId) => {
        const children = allCategories.value.filter(cat => cat.parentId === parentId);
        console.log(`🔍 Found ${children.length} children for parent ${parentId}`);

        children.forEach(cat => {
          if (!descendantIds.has(cat.id)) {
            descendantIds.add(cat.id);
            console.log(`➕ Added descendant: ${cat.name} (${cat.id})`);
            addDescendants(cat.id); // Recursively add children
          }
        });
      };

      // Add the root category itself and all its descendants
      descendantIds.add(selectedRootId);
      console.log(`➕ Added root category: ${selectedRootId}`);
      addDescendants(selectedRootId);

      console.log('📊 Total descendant IDs:', descendantIds.size);
      console.log('📊 Descendant IDs:', Array.from(descendantIds));

      // Filter to show only categories that belong to this root
      categoriesData = categoriesData.filter(cat => descendantIds.has(cat.id));

      console.log('📊 Categories after filter:', categoriesData.length);
      console.log('📊 Filtered categories:', categoriesData.map(cat => `${cat.name} (${cat.id})`));
    }

    // Apply frontend filters
    if (filters.value.productCount) {
      categoriesData = categoriesData.filter(cat => {
        switch (filters.value.productCount) {
          case 'empty':
            return (cat.productCount || 0) === 0;
          case 'hasProducts':
            return (cat.productCount || 0) > 0;
          case 'many':
            return (cat.productCount || 0) > 10;
          default:
            return true;
        }
      });
    }

    // Apply search field filter if specific field is selected
    if (searchQuery.value && searchField.value !== 'all') {
      const query = searchQuery.value.toLowerCase();
      categoriesData = categoriesData.filter(cat => {
        switch (searchField.value) {
          case 'name':
            return cat.name?.toLowerCase().includes(query);
          case 'slug':
            return cat.slug?.toLowerCase().includes(query);
          case 'description':
            return cat.description?.toLowerCase().includes(query);
          default:
            return true;
        }
      });
    }

    // Apply search for 'all' fields (backend search might have failed)
    if (searchQuery.value && searchField.value === 'all' && categoriesData.length === 0) {
      // Fallback to frontend search if backend search failed
      try {
        const allResponse = await categoriesService.getAll({
          pageSize: 1000,
          orderBy: sortBy.value === 'productCount' ? 'name' : sortBy.value,
          descending: sortOrder.value === 'desc'
        });
        const allData = allResponse.data || [];
        const query = searchQuery.value.toLowerCase();
        categoriesData = allData.filter(cat =>
          cat.name?.toLowerCase().includes(query) ||
          cat.slug?.toLowerCase().includes(query) ||
          cat.description?.toLowerCase().includes(query)
        );
      } catch (err) {
        console.warn('Fallback search also failed:', err);
      }
    }

    // Apply frontend sorting if we have frontend filtering or if sorting by productCount
    const hasFiltering = filters.value.productCount ||
                        (searchQuery.value && searchField.value !== 'all') ||
                        (filters.value.parentId && filters.value.parentId !== 'root') ||
                        searchQuery.value; // Always sort if we have any search

    const needsFrontendSorting = hasFiltering || sortBy.value === 'productCount';

    if (needsFrontendSorting && categoriesData.length > 0) {
      categoriesData.sort((a, b) => {
        let aValue, bValue;

        switch (sortBy.value) {
          case 'name':
            aValue = a.name?.toLowerCase() || '';
            bValue = b.name?.toLowerCase() || '';
            break;
          case 'productCount':
            aValue = a.productCount || 0;
            bValue = b.productCount || 0;
            break;
          case 'createdAt':
            aValue = new Date(a.createdAt || 0);
            bValue = new Date(b.createdAt || 0);
            break;
          default:
            aValue = a.name?.toLowerCase() || '';
            bValue = b.name?.toLowerCase() || '';
        }

        if (aValue < bValue) return sortOrder.value === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortOrder.value === 'asc' ? 1 : -1;
        return 0;
      });
    }

    categories.value = categoriesData;

    // Update pagination (adjust for frontend filtering)
    const originalTotal = response.total || 0;
    const filteredTotal = categoriesData.length;

    pagination.value = {
      currentPage: response.currentPage || 1,
      totalPages: hasFiltering ? 1 : (response.totalPages || Math.ceil(originalTotal / (response.pageSize || 20))),
      totalItems: hasFiltering ? filteredTotal : originalTotal,
      perPage: response.pageSize || 20
    };

    // Hierarchy is built by CategoryHierarchy component from allCategories

    // Update stats
    await updateStats();

  } catch (err) {
    console.error('Error fetching categories:', err);
    error.value = 'Failed to load categories. Please try again.';
  } finally {
    loading.value = false;
  }
};

// Hierarchy is now handled by CategoryHierarchy component

const updateStats = async () => {
  try {
    // Try to get stats from backend
    const backendStats = await categoriesService.getStats();
    stats.value = {
      total: backendStats.totalCategories || 0,
      rootCategories: backendStats.rootCategories || 0,
      withProducts: backendStats.categoriesWithProducts || 0,
      totalProducts: backendStats.totalProducts || 0
    };
  } catch (err) {
    console.warn('Failed to fetch stats from backend, using local calculation:', err);
    // Fallback to local calculation
    stats.value.total = pagination.value.totalItems;
    stats.value.rootCategories = categories.value.filter(cat => !cat.parentId).length;
    stats.value.withProducts = categories.value.filter(cat => cat.productCount > 0).length;
    stats.value.totalProducts = categories.value.reduce((sum, cat) => sum + (cat.productCount || 0), 0);
  }
};

const getSearchPlaceholder = () => {
  const placeholders = {
    all: 'Search by name and slug (backend + frontend)...',
    name: 'Search by category name (frontend only)...',
    slug: 'Search by category slug (frontend only)...',
    description: 'Search by description (frontend only)...'
  };
  return placeholders[searchField.value] || placeholders.all;
};

// Category display utilities removed - no longer needed

const clearSearch = () => {
  searchQuery.value = '';
  searchField.value = 'all';
  pagination.value.currentPage = 1;
  fetchCategories();
};

const resetFilters = () => {
  searchQuery.value = '';
  searchField.value = 'all';
  filters.value.parentId = '';
  filters.value.productCount = '';
  sortBy.value = 'name';
  sortOrder.value = 'asc';
  pagination.value.currentPage = 1;
  fetchCategories();
};

const changePageSize = () => {
  pagination.value.currentPage = 1;
  fetchCategories();
};



const toggleView = async () => {
  showHierarchy.value = !showHierarchy.value;
  // CategoryHierarchy component will build hierarchy from allCategories
  // No additional loading needed
};

const changePage = (page) => {
  if (page >= 1 && page <= pagination.value.totalPages) {
    pagination.value.currentPage = page;
    fetchCategories();
  }
};

// Navigation handlers
const editCategory = (category) => {
  router.push(`/admin/categories/${category.id}/edit`);
};

const createChildCategory = (parentCategory) => {
  router.push({
    path: '/admin/categories/create',
    query: { parentId: parentCategory.id }
  });
};

const confirmDelete = (category) => {
  categoryToDelete.value = category;
  showDeleteDialog.value = true;
};

const closeDeleteDialog = () => {
  showDeleteDialog.value = false;
  categoryToDelete.value = null;
};

const handleDelete = async () => {
  if (!categoryToDelete.value) return;

  try {
    console.log('Deleting category:', categoryToDelete.value);
    await categoriesService.delete(categoryToDelete.value.id);
    console.log('Category deleted successfully');
    closeDeleteDialog();

    // Refresh both views
    await fetchCategories();
    await fetchAllCategories(); // For hierarchy view

    console.log('Categories refreshed after delete');
  } catch (err) {
    console.error('Error deleting category:', err);
    closeDeleteDialog();

    // Обробляємо специфічні помилки від сервера
    if (err.response && err.response.data && err.response.data.message) {
      error.value = err.response.data.message;
    } else {
      error.value = `Failed to delete category: ${err.message || 'Please try again.'}`;
    }
  }
};

const viewCategoryProducts = (category) => {
  // Navigate to category view page
  router.push(`/admin/categories/${category.id}`);
};

// Watchers
watch([sortBy, sortOrder], () => {
  pagination.value.currentPage = 1;
  fetchCategories();
});

watch(() => filters.value.parentId, () => {
  pagination.value.currentPage = 1;
  fetchCategories();
});

watch(() => filters.value.productCount, () => {
  pagination.value.currentPage = 1;
  fetchCategories();
});

// Hierarchy view doesn't need special watchers anymore

// Lifecycle
onMounted(() => {
  fetchCategories();
});
</script>
