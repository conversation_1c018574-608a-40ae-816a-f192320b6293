import{_ as se,g as f,h as ce,x as ue,i as ae,c as r,o as d,a as e,k as D,d as W,t as _,n as F,z as L,C as j,F as G,p as Z,m as te,J as me,q,f as pe,b as de,A as fe,e as he}from"./index-BKy0rL_2.js";import{p as ee}from"./products-A8mTMjnr.js";import{c as ye}from"./companies-DFFZKRwv.js";const _e={class:"category-select"},be={class:"label"},Ie={key:0,class:"has-text-danger"},Ce={class:"control"},$e={class:"dropdown-trigger"},we={class:"field has-addons"},ke={class:"control is-expanded"},xe=["placeholder","readonly"],Ae={class:"control"},De=["disabled"],Me={class:"icon"},Pe={class:"dropdown-menu",role:"menu"},Se={class:"dropdown-content"},Ee={key:0,class:"dropdown-item"},Fe={key:1,class:"dropdown-item"},Ue=["onMousedown"],Te={class:"category-item"},ze={class:"category-name"},Le={class:"category-slug has-text-grey is-size-7"},Ve={key:0,class:"help"},je={__name:"CategorySelect",props:{modelValue:{type:[String,null],default:null},label:{type:String,default:"Category"},placeholder:{type:String,default:"Search and select category..."},required:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(b,{emit:O}){const w=b,U=O,g=f([]),E=f(!1),v=f(""),p=f(!1),m=f(null),C=ce(()=>{var I;const t=Array.isArray(g.value)?g.value:[];if(console.log("🏷️ Filtering categories:",{totalCategories:t.length,searchQuery:v.value,isDropdownOpen:p.value}),!v.value.trim()||v.value===(((I=m.value)==null?void 0:I.name)||""))return console.log("📋 Showing all categories:",t.length),t;const u=v.value.toLowerCase().trim(),c=t.filter($=>!$||typeof $!="object"?!1:$.name&&$.name.toLowerCase().includes(u)||$.slug&&$.slug.toLowerCase().includes(u)).slice(0,50);return console.log("🎯 Filtered categories:",c.length),c}),A=async()=>{try{E.value=!0,console.log("🏷️ Fetching categories...");const t=await ee.getCategories({pageSize:1e3});if(console.log("📦 Categories response:",t),g.value=Array.isArray(t)?t:[],console.log("✅ Categories loaded:",g.value.length),w.modelValue&&g.value.length>0){const u=g.value.find(c=>c&&c.id===w.modelValue);u?(m.value=u,v.value=u.name||"",console.log("🎯 Selected category found:",u.name)):console.log("❌ Category not found for ID:",w.modelValue)}}catch(t){console.error("❌ Error fetching categories:",t),g.value=[]}finally{E.value=!1}},V=t=>{if(!t||typeof t!="object"||!t.id){console.error("❌ Invalid category object:",t);return}m.value=t,v.value=t.name||"",p.value=!1,console.log("✅ Category selected:",t.name),U("update:modelValue",t.id),U("change",t)},y=()=>{w.readonly||(p.value=!0,console.log("📂 Category dropdown opened"))},T=()=>{p.value=!1,console.log("📁 Category dropdown closed")},a=()=>{w.readonly||(p.value?T():(m.value&&v.value===m.value.name&&(v.value=""),y()))},x=()=>{p.value||(p.value=!0,console.log("📂 Category dropdown opened via search input"))},S=()=>{setTimeout(()=>{T(),m.value?v.value=m.value.name:v.value=""},200)};return ue(()=>w.modelValue,t=>{const u=Array.isArray(g.value)?g.value:[];if(t&&u.length>0){const c=u.find(I=>I&&I.id===t);c?(m.value=c,v.value=c.name||"",console.log("🔄 Category updated via watch:",c.name)):console.log("🔍 Category not found in watch for ID:",t)}else t||(m.value=null,v.value="",console.log("🧹 Cleared category selection"))}),ae(()=>{A()}),(t,u)=>(d(),r("div",_e,[e("label",be,[W(_(b.label)+" ",1),b.required?(d(),r("span",Ie,"*")):D("",!0)]),e("div",Ce,[e("div",{class:F(["dropdown",{"is-active":p.value}])},[e("div",$e,[e("div",we,[e("div",ke,[L(e("input",{class:"input",type:"text",placeholder:b.placeholder,"onUpdate:modelValue":u[0]||(u[0]=c=>v.value=c),onInput:x,onFocus:y,onBlur:S,readonly:b.readonly},null,40,xe),[[j,v.value]])]),e("div",Ae,[e("button",{class:"button",type:"button",onClick:a,disabled:b.readonly},[e("span",Me,[e("i",{class:F(["fas fa-chevron-down",{"fa-rotate-180":p.value}])},null,2)])],8,De)])])]),e("div",Pe,[e("div",Se,[E.value?(d(),r("div",Ee,u[1]||(u[1]=[e("div",{class:"has-text-centered"},[e("span",{class:"icon"},[e("i",{class:"fas fa-spinner fa-spin"})]),W(" Loading categories... ")],-1)]))):C.value.length===0?(d(),r("div",Fe,u[2]||(u[2]=[e("div",{class:"has-text-grey has-text-centered"}," No categories found ",-1)]))):(d(!0),r(G,{key:2},Z(C.value,c=>{var I;return d(),r("a",{key:c.id,class:F(["dropdown-item",{"is-active":((I=m.value)==null?void 0:I.id)===c.id}]),onMousedown:te($=>V(c),["prevent"])},[e("div",Te,[e("div",ze,_(c.name),1),e("div",Le,_(c.slug),1)])],42,Ue)}),128))])])],2)]),m.value?(d(),r("p",Ve," Selected: "+_(m.value.name),1)):D("",!0)]))}},Ne=se(je,[["__scopeId","data-v-80c00f51"]]),Oe={class:"company-select"},Be={class:"label"},Re={key:0,class:"has-text-danger"},qe={class:"control"},We={class:"dropdown-trigger"},Ge={class:"field has-addons"},Ze={class:"control is-expanded"},Qe=["placeholder","readonly"],He={class:"control"},Je=["disabled"],Ye={class:"icon"},Xe={class:"dropdown-menu",role:"menu"},Ke={class:"dropdown-content"},es={key:0,class:"dropdown-item"},ss={key:1,class:"dropdown-item"},as=["onMousedown"],ts={class:"company-item"},ls={class:"company-name"},os={class:"company-details has-text-grey is-size-7"},ns={key:0,class:"help"},is={__name:"CompanySelect",props:{modelValue:{type:[String,null],default:null},label:{type:String,default:"Company"},placeholder:{type:String,default:"Search and select company..."},required:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(b,{emit:O}){const w=b,U=O,g=f([]),E=f(!1),v=f(""),p=f(!1),m=f(null),C=ce(()=>{var I;const t=Array.isArray(g.value)?g.value:[];if(console.log("🔍 Filtering companies:",{totalCompanies:t.length,searchQuery:v.value,isDropdownOpen:p.value}),!v.value.trim()||v.value===(((I=m.value)==null?void 0:I.name)||""))return console.log("📋 Showing all companies:",t.length),t.slice(0,50);const u=v.value.toLowerCase().trim(),c=t.filter($=>!$||typeof $!="object"?!1:$.name&&$.name.toLowerCase().includes(u)||$.contactEmail&&$.contactEmail.toLowerCase().includes(u)||$.addressCity&&$.addressCity.toLowerCase().includes(u)||$.slug&&$.slug.toLowerCase().includes(u)).slice(0,50);return console.log("🎯 Filtered companies:",c.length),c}),A=async()=>{try{E.value=!0,console.log("🏢 Fetching companies...");const t=await ye.getCompanies({pageSize:1e3});console.log("📦 Companies API response:",t);let u=[];if(t&&t.data&&Array.isArray(t.data)?u=t.data:t&&t.companies&&Array.isArray(t.companies)?u=t.companies:Array.isArray(t)?u=t:(console.warn("⚠️ Unexpected API response structure:",t),u=[]),g.value=u,console.log("✅ Companies loaded:",g.value.length),w.modelValue&&Array.isArray(g.value)){const c=g.value.find(I=>I&&I.id===w.modelValue);c?(m.value=c,v.value=c.name||"",console.log("🎯 Selected company found:",c.name)):console.log("❌ Company not found for ID:",w.modelValue)}}catch(t){console.error("❌ Error fetching companies:",t),g.value=[]}finally{E.value=!1}},V=t=>{if(!t||typeof t!="object"||!t.id){console.error("❌ Invalid company object:",t);return}m.value=t,v.value=t.name||"",p.value=!1,console.log("✅ Company selected:",t.name),U("update:modelValue",t.id),U("change",t)},y=()=>{w.readonly||(p.value=!0,console.log("📂 Dropdown opened"))},T=()=>{p.value=!1,console.log("📁 Dropdown closed")},a=()=>{w.readonly||(p.value?T():(m.value&&v.value===m.value.name&&(v.value=""),y()))},x=()=>{p.value||(p.value=!0,console.log("📂 Dropdown opened via search input"))},S=()=>{setTimeout(()=>{T(),m.value?v.value=m.value.name||"":v.value=""},200)};return ue(()=>w.modelValue,t=>{const u=Array.isArray(g.value)?g.value:[];if(t&&u.length>0){const c=u.find(I=>I&&I.id===t);c?(m.value=c,v.value=c.name||"",console.log("🔄 Company updated via watch:",c.name)):console.log("🔍 Company not found in watch for ID:",t)}else t||(m.value=null,v.value="",console.log("🧹 Cleared company selection"))}),ae(()=>{A()}),(t,u)=>(d(),r("div",Oe,[e("label",Be,[W(_(b.label)+" ",1),b.required?(d(),r("span",Re,"*")):D("",!0)]),e("div",qe,[e("div",{class:F(["dropdown",{"is-active":p.value}])},[e("div",We,[e("div",Ge,[e("div",Ze,[L(e("input",{class:"input",type:"text",placeholder:b.placeholder,"onUpdate:modelValue":u[0]||(u[0]=c=>v.value=c),onInput:x,onFocus:y,onBlur:S,readonly:b.readonly},null,40,Qe),[[j,v.value]])]),e("div",He,[e("button",{class:"button",type:"button",onClick:a,disabled:b.readonly},[e("span",Ye,[e("i",{class:F(["fas fa-chevron-down",{"fa-rotate-180":p.value}])},null,2)])],8,Je)])])]),e("div",Xe,[e("div",Ke,[E.value?(d(),r("div",es,u[1]||(u[1]=[e("div",{class:"has-text-centered"},[e("span",{class:"icon"},[e("i",{class:"fas fa-spinner fa-spin"})]),W(" Loading companies... ")],-1)]))):C.value.length===0?(d(),r("div",ss,u[2]||(u[2]=[e("div",{class:"has-text-grey has-text-centered"}," No companies found ",-1)]))):(d(!0),r(G,{key:2},Z(C.value,c=>{var I;return d(),r("a",{key:c.id,class:F(["dropdown-item",{"is-active":((I=m.value)==null?void 0:I.id)===c.id}]),onMousedown:te($=>V(c),["prevent"])},[e("div",ts,[e("div",ls,_(c.name),1),e("div",os,_(c.contactEmail)+" • "+_(c.addressCity),1)])],42,as)}),128))])])],2)]),m.value?(d(),r("p",ns," Selected: "+_(m.value.name),1)):D("",!0)]))}},rs=se(is,[["__scopeId","data-v-becaac52"]]),ds={key:0,class:"product-image-manager-loading"},cs={key:1,class:"product-image-manager"},us={class:"upload-section mb-5"},ms={class:"field"},vs={class:"control"},gs=["disabled"],ps={class:"drop-zone-content"},fs={class:"icon is-large has-text-grey-light"},hs={key:0,class:"fas fa-cloud-upload-alt fa-3x"},ys={key:1,class:"fas fa-spinner fa-spin fa-3x"},_s={class:"has-text-grey mt-3"},bs={key:0},Is={key:1},Cs={key:0,class:"help"},$s={key:0,class:"notification is-info"},ws={class:"level"},ks={class:"level-left"},xs={class:"level-item"},As={class:"icon-text"},Ds={class:"level-right"},Ms={class:"level-item"},Ps=["value","max"],Ss={key:0,class:"current-images mb-5"},Es={class:"label"},Fs={class:"columns is-multiline"},Us={class:"card image-card"},Ts={class:"card-image"},zs={class:"image is-4by3"},Ls=["src","alt","onClick"],Vs={class:"image-overlay"},js={class:"image-actions"},Ns=["onClick"],Os=["onClick"],Bs=["onClick"],Rs={class:"card-content p-3"},qs={class:"level is-mobile"},Ws={class:"level-left"},Gs={class:"level-item"},Zs={class:"level-right"},Qs={class:"level-item"},Hs={key:0,class:"tag is-small is-info"},Js={key:1,class:"pending-images mb-5"},Ys={class:"label"},Xs={class:"columns is-multiline"},Ks={class:"card image-card"},ea={class:"card-image"},sa={class:"image is-4by3"},aa=["src","alt"],ta={class:"card-content p-3"},la={class:"level is-mobile"},oa={class:"level-left"},na={class:"level-item"},ia={class:"level-right"},ra={class:"level-item"},da={class:"buttons are-small"},ca=["onClick"],ua=["onClick"],ma={class:"content is-small"},va={key:2,class:"no-images has-background-light has-text-centered p-6 is-rounded"},ga={class:"modal-content"},pa={class:"image"},fa=["src"],ha=5*1024*1024,ya={__name:"ProductImageManager",props:{productId:{type:String,default:null},images:{type:Array,default:()=>[]},isCreate:{type:Boolean,default:!1}},emits:["images-updated","main-image-changed","image-uploaded","image-deleted","pending-images-changed"],setup(b,{expose:O,emit:w}){const U=b,g=w,E=f(!1),v=f(!1),p=f(!1),m=f(null),C=f(!1),A=f(!1),V=f([]),y=f([]),T=f(!1),a=f(!1),x=f(null),S=f(null),t=f({current:0,total:0}),u=["image/jpeg","image/jpg","image/png","image/gif","image/webp","image/jfif"],c=()=>{if(!A.value&&m.value&&v.value&&p.value)try{m.value&&typeof m.value.click=="function"?m.value.click():console.warn("File input element not properly initialized")}catch(n){console.warn("File input not ready:",n)}},I=n=>{if(!n.target||!n.target.files)return;const l=Array.from(n.target.files);Y(l),n.target.value=""},$=n=>{if(n.preventDefault(),C.value=!1,A.value)return;const l=Array.from(n.dataTransfer.files);Y(l)},J=n=>{n.preventDefault(),A.value||(C.value=!0)},le=()=>{C.value=!1},Y=n=>{if(n.length===0)return;const l=[],M=[];n.forEach(P=>{if(!u.includes(P.type.toLowerCase())){M.push(`${P.name}: Unsupported file type`);return}if(P.size>ha){M.push(`${P.name}: File too large (max 5MB)`);return}l.push(P)}),M.length>0&&alert(`Some files were rejected:
`+M.join(`
`)),l.length!==0&&(V.value=l,l.forEach((P,h)=>{const k=new FileReader;k.onload=async z=>{const N={id:`temp-${Date.now()}-${Math.random()}-${h}`,file:P,name:P.name,preview:z.target.result,size:P.size,isTemp:!0,isMain:y.value.length===0&&U.images.length===0};y.value.push(N),await me(),g("pending-images-changed",y.value)},k.readAsDataURL(P)}))},X=async n=>{if(U.productId)try{const l=await q.patch(`/api/admin/products/${U.productId}/images/${n}/main`);l.data&&l.data.success&&(g("main-image-changed",n),g("images-updated"))}catch(l){console.error("Error setting main image:",l),alert("Failed to set main image. Please try again.")}},oe=n=>{y.value.forEach(l=>{l.isMain=l.id===n}),g("pending-images-changed",y.value)},Q=n=>{const l=y.value.findIndex(M=>M.id===n);l!==-1&&(y.value.splice(l,1),g("pending-images-changed",y.value))},H=n=>{x.value=n,T.value=!0},K=()=>{T.value=!1,x.value=null},ne=n=>{S.value=n,a.value=!0},B=()=>{a.value=!1,S.value=null},ie=async()=>{if(S.value)try{await q.delete(`/api/admin/products/${U.productId}/images/${S.value.id}`),g("image-deleted",S.value.id),g("images-updated"),B()}catch(n){console.error("Error deleting image:",n),alert("Failed to delete image. Please try again.")}},re=n=>{n.target.dataset.errorHandled||(n.target.dataset.errorHandled="true",n.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjhGOUZBIiByeD0iOCIvPgo8cGF0aCBkPSJNMTAwIDExMEMxMTQuMTQyIDExMCAxMjYgOTguMTQyMSAxMjYgODRDMTI2IDY5Ljg1NzkgMTE0LjE0MiA1OCAxMDAgNThDODUuODU3OSA1OCA3NCA2OS44NTc5IDc0IDg0Qzc0IDk4LjE0MjEgODUuODU3OSAxMTAgMTAwIDExMFoiIHN0cm9rZT0iI0QxRDFEMSIgc3Ryb2tlLXdpZHRoPSIzIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik04NiA3NEg5MFY3OEg4NlY3NFoiIGZpbGw9IiNEMUQxRDEiLz4KPHA+CjwvcGF0aD4KPC9zdmc+Cg==",n.target.classList.add("image-error"))},o=n=>{if(!n)return"";try{const l=new Date(n);return new Intl.DateTimeFormat("uk-UA",{month:"short",day:"numeric"}).format(l)}catch{return""}},s=async n=>{var l,M,P,h;if(y.value.length!==0)try{A.value=!0,t.value={current:0,total:y.value.length};for(const k of y.value)try{const z=new FormData,N=i(k.file.name),ve=new File([k.file],N,{type:k.file.type,lastModified:k.file.lastModified});z.append("file",ve),console.log(`Uploading image: ${N}, Type: ${k.file.type}, Size: ${k.file.size}`);const R=await q.post(`/api/admin/products/${n}/images/single`,z,{headers:{"Content-Type":"multipart/form-data"},timeout:3e4});if(console.log("Upload response:",R.data),k.isMain&&R.data&&R.data.success&&R.data.data){const ge=R.data.data;await X(ge)}t.value.current++,g("image-uploaded",R.data)}catch(z){console.error(`Failed to upload image: ${k.name}`,z);let N="Unknown error occurred";throw(M=(l=z.response)==null?void 0:l.data)!=null&&M.message?N=z.response.data.message:(h=(P=z.response)==null?void 0:P.data)!=null&&h.error?N=z.response.data.error:z.message&&(N=z.message),new Error(`Failed to upload "${k.name}": ${N}`)}y.value=[],g("pending-images-changed",[]),g("images-updated")}catch(k){throw console.error("Error uploading pending images:",k),k}finally{A.value=!1,t.value={current:0,total:0}}},i=n=>n.replace(/[а-яё]/gi,M=>({а:"a",б:"b",в:"v",г:"g",д:"d",е:"e",ё:"yo",ж:"zh",з:"z",и:"i",й:"y",к:"k",л:"l",м:"m",н:"n",о:"o",п:"p",р:"r",с:"s",т:"t",у:"u",ф:"f",х:"h",ц:"ts",ч:"ch",ш:"sh",щ:"sch",ъ:"",ы:"y",ь:"",э:"e",ю:"yu",я:"ya"})[M.toLowerCase()]||M).replace(/[^a-zA-Z0-9.-]/g,"_").replace(/_+/g,"_").replace(/^_|_$/g,"")||"image";return O({uploadPendingImages:s,getPendingImages:()=>y.value,clearPendingImages:()=>{y.value=[],g("pending-images-changed",[])}}),ae(async()=>{try{E.value=!0,await me(),await new Promise(n=>{const l=()=>{document.readyState==="complete"||document.readyState==="interactive"?(v.value=!0,n()):setTimeout(l,10)};l()}),setTimeout(()=>{p.value=!0},50)}catch(n){console.warn("ProductImageManager mount error:",n),setTimeout(()=>{E.value=!0,v.value=!0,p.value=!0},200)}}),(n,l)=>{var M,P;return!E.value||!v.value||!p.value?(d(),r("div",ds,l[0]||(l[0]=[e("div",{class:"has-text-centered py-4"},[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-spin fa-2x"})]),e("p",{class:"mt-2"},"Loading image manager...")],-1)]))):(d(),r("div",cs,[l[13]||(l[13]=e("h2",{class:"title is-5 mb-4"},"🖼️ Product Images",-1)),e("div",us,[e("div",ms,[l[2]||(l[2]=e("label",{class:"label"},"Upload Images",-1)),e("div",vs,[e("div",{class:F(["drop-zone",{"is-dragover":C.value,"is-disabled":A.value}]),onDrop:$,onDragover:te(J,["prevent"]),onDragleave:le,onClick:c},[e("input",{ref_key:"fileInput",ref:m,type:"file",accept:"image/*,.jfif",multiple:"",onChange:I,disabled:A.value,style:{display:"none"}},null,40,gs),e("div",ps,[e("span",fs,[A.value?(d(),r("i",ys)):(d(),r("i",hs))]),e("p",_s,[A.value?(d(),r("strong",Is,"Uploading images...")):(d(),r("strong",bs,"Drop images here or click to browse"))]),l[1]||(l[1]=e("p",{class:"has-text-grey-light is-size-7"}," Supports: JPG, PNG, GIF, WebP, JFIF (max 5MB each) ",-1))])],34)]),V.value.length>0?(d(),r("p",Cs,_(V.value.length)+" file(s) selected ",1)):D("",!0)]),A.value?(d(),r("div",$s,[e("div",ws,[e("div",ks,[e("div",xs,[e("span",As,[l[3]||(l[3]=e("span",{class:"icon"},[e("i",{class:"fas fa-spinner fa-spin"})],-1)),e("span",null,"Uploading "+_(t.value.current)+" of "+_(t.value.total)+" images...",1)])])]),e("div",Ds,[e("div",Ms,[e("progress",{class:"progress is-info",value:t.value.current,max:t.value.total},_(Math.round(t.value.current/t.value.total*100))+"% ",9,Ps)])])])])):D("",!0)]),b.images.length>0?(d(),r("div",Ss,[e("label",Es,"Current Images ("+_(b.images.length)+")",1),e("div",Fs,[(d(!0),r(G,null,Z(b.images,h=>(d(),r("div",{key:h.id,class:"column is-6-tablet is-4-desktop"},[e("div",Us,[e("div",Ts,[e("figure",zs,[e("img",{src:h.imageUrl||h.url,alt:h.altText||"Product image",onError:re,onClick:k=>H(h)},null,40,Ls)]),e("div",Vs,[e("div",js,[h.isMain?D("",!0):(d(),r("button",{key:0,class:"button is-small is-info",onClick:k=>X(h.id),title:"Set as main image"},l[4]||(l[4]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-star"})],-1)]),8,Ns)),e("button",{class:"button is-small is-primary",onClick:k=>H(h),title:"View full size"},l[5]||(l[5]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-eye"})],-1)]),8,Os),e("button",{class:"button is-small is-danger",onClick:k=>ne(h),title:"Delete image"},l[6]||(l[6]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-trash"})],-1)]),8,Bs)])])]),e("div",Rs,[e("div",qs,[e("div",Ws,[e("div",Gs,[e("span",{class:F(["tag is-small",h.isMain?"is-primary":"is-light"])},_(h.isMain?"Main":`#${h.order||0}`),3)])]),e("div",Zs,[e("div",Qs,[h.createdAt?(d(),r("span",Hs,_(o(h.createdAt)),1)):D("",!0)])])])])])]))),128))])])):D("",!0),y.value.length>0?(d(),r("div",Js,[e("label",Ys,"Images to Upload ("+_(y.value.length)+")",1),l[9]||(l[9]=e("div",{class:"notification is-warning is-light"},[e("span",{class:"icon-text"},[e("span",{class:"icon"},[e("i",{class:"fas fa-info-circle"})]),e("span",null,"These images will be uploaded when you save the product.")])],-1)),e("div",Xs,[(d(!0),r(G,null,Z(y.value,h=>(d(),r("div",{key:h.id,class:"column is-6-tablet is-4-desktop"},[e("div",Ks,[e("div",ea,[e("figure",sa,[e("img",{src:h.preview,alt:h.name},null,8,aa)])]),e("div",ta,[e("div",la,[e("div",oa,[e("div",na,[e("span",{class:F(["tag is-small",h.isMain?"is-warning":"is-light"])},_(h.isMain?"Will be Main":"Pending"),3)])]),e("div",ia,[e("div",ra,[e("div",da,[h.isMain?D("",!0):(d(),r("button",{key:0,class:"button is-warning is-small",onClick:k=>oe(h.id),title:"Set as main"},l[7]||(l[7]=[e("span",{class:"icon"},[e("i",{class:"fas fa-star"})],-1)]),8,ca)),e("button",{class:"button is-danger is-small",onClick:k=>Q(h.id),title:"Remove"},l[8]||(l[8]=[e("span",{class:"icon"},[e("i",{class:"fas fa-times"})],-1)]),8,ua)])])])]),e("div",ma,[e("p",null,[e("strong",null,_(h.name),1)]),e("p",null,_((h.size/1024/1024).toFixed(2))+" MB",1)])])])]))),128))])])):D("",!0),b.images.length===0&&y.value.length===0?(d(),r("div",va,l[10]||(l[10]=[e("span",{class:"icon is-large has-text-grey"},[e("i",{class:"fas fa-images fa-3x"})],-1),e("p",{class:"has-text-grey mt-2"},"No images uploaded yet",-1),e("p",{class:"has-text-grey-light is-size-7"},"Use the upload area above to add product images",-1)]))):D("",!0),e("div",{class:F(["modal",{"is-active":T.value}])},[e("div",{class:"modal-background",onClick:K}),e("div",ga,[e("p",pa,[x.value?(d(),r("img",{key:0,src:((M=x.value)==null?void 0:M.imageUrl)||((P=x.value)==null?void 0:P.url)},null,8,fa)):D("",!0)])]),e("button",{class:"modal-close is-large",onClick:K})],2),e("div",{class:F(["modal",{"is-active":a.value}])},[e("div",{class:"modal-background",onClick:B}),e("div",{class:"modal-card"},[e("header",{class:"modal-card-head"},[l[11]||(l[11]=e("p",{class:"modal-card-title"},"Confirm Delete",-1)),e("button",{class:"delete",onClick:B})]),l[12]||(l[12]=e("section",{class:"modal-card-body"},[e("p",null,"Are you sure you want to delete this image?"),e("p",{class:"has-text-danger is-size-7 mt-2"},"This action cannot be undone.")],-1)),e("footer",{class:"modal-card-foot"},[e("button",{class:"button is-danger",onClick:ie},"Delete"),e("button",{class:"button",onClick:B},"Cancel")])])],2)]))}}},_a=se(ya,[["__scopeId","data-v-d91299b9"]]),ba={class:"product-edit"},Ia={key:0,class:"has-text-centered py-6"},Ca={key:1,class:"notification is-danger"},$a={class:"level mb-5"},wa={class:"level-left"},ka={class:"level-item"},xa={class:"title is-3"},Aa={key:0,class:"subtitle is-6 has-text-grey"},Da={class:"level-right"},Ma={class:"level-item"},Pa={class:"buttons"},Sa={class:"form-sections"},Ea={class:"box mb-5"},Fa={class:"columns is-multiline"},Ua={class:"column is-6"},Ta={class:"column is-6"},za={class:"column is-12"},La={class:"field"},Va={class:"control"},ja={class:"column is-12"},Na={class:"field"},Oa={class:"control"},Ba={class:"column is-12"},Ra={class:"field"},qa={class:"control"},Wa={class:"box mb-5"},Ga={class:"box mb-5"},Za={class:"columns"},Qa={class:"column is-4"},Ha={class:"field"},Ja={class:"control has-icons-left"},Ya={class:"column is-4"},Xa={class:"field"},Ka={class:"control has-icons-left"},et={class:"column is-4"},st={class:"field"},at={class:"control"},tt={class:"select is-fullwidth"},lt={class:"box mb-5"},ot={class:"field is-grouped mb-4"},nt={class:"control is-expanded"},it={class:"control is-expanded"},rt={key:0,class:"table-container"},dt={class:"table is-fullwidth is-striped is-hoverable"},ct={class:"tags"},ut=["onClick"],mt=["onClick"],vt={key:1,class:"notification is-light has-text-centered"},gt={class:"box mb-5"},pt={class:"columns is-multiline"},ft={class:"column is-12"},ht={class:"field"},yt={class:"control"},_t={class:"column is-12"},bt={class:"field"},It={class:"control"},Ct={class:"column is-12"},$t={class:"field"},wt={class:"field has-addons"},kt={class:"control is-expanded"},xt={class:"file has-name is-fullwidth"},At={class:"file-label"},Dt=["disabled"],Mt={key:0,class:"file-icon"},Pt={class:"file-label"},St={class:"file-name"},Et={key:0,class:"control"},Ft=["disabled"],Ut={key:0,class:"has-text-centered mt-4"},Tt={class:"box has-background-light p-4"},zt={class:"image is-128x128 is-inline-block"},Lt=["src","alt"],Vt={key:0,class:"box mb-5"},jt={class:"columns"},Nt={class:"column is-6"},Ot={class:"field"},Bt={class:"control"},Rt=["value"],qt={key:0,class:"column is-6"},Wt={class:"field"},Gt={class:"control"},Zt=["value"],Qt={__name:"ProductEdit",props:{productId:{type:String,required:!1},isCreate:{type:Boolean,default:!1}},emits:["save","cancel"],setup(b,{emit:O}){const w=b,U=O,g=pe(),E=he(),v=f(!1),p=f(!1),m=f(!1),C=f(null),A=f(""),V=f([]),y=f(null),T=f(""),a=f({id:"",companyId:"",name:"",slug:"",description:"",priceAmount:0,priceCurrency:0,stock:0,status:"0",categoryId:"",attributes:{},metaTitle:"",metaDescription:"",metaImage:"",createdAt:null,updatedAt:null}),x=f({key:"",value:""}),S=ce(()=>w.productId||g.params.id),t=()=>{if(!x.value.key.trim()||!x.value.value.trim())return;const o=x.value.key.trim(),s=x.value.value.trim();a.value.attributes[o]?Array.isArray(a.value.attributes[o])?a.value.attributes[o].push(s):a.value.attributes[o]=[a.value.attributes[o],s]:a.value.attributes[o]=s,x.value.key="",x.value.value=""},u=o=>{delete a.value.attributes[o]},c=(o,s)=>{const i=a.value.attributes[o];Array.isArray(i)?i.length===1?delete a.value.attributes[o]:i.splice(s,1):delete a.value.attributes[o]},I=async o=>{const s=o.target.files[0];if(s){A.value=s.name,m.value=!0;try{const i=new FileReader;i.onload=n=>{a.value.metaImage=n.target.result},i.readAsDataURL(s)}catch(i){console.error("Error processing meta image:",i),C.value="Failed to process meta image. Please try again."}finally{m.value=!1}}},$=()=>{a.value.metaImage="",A.value=""},J=async o=>{if(!a.value.metaImage||!a.value.metaImage.startsWith("data:"))return;const i=await(await fetch(a.value.metaImage)).blob(),n=new File([i],A.value||"meta-image.jpg",{type:i.type}),l=new FormData;l.append("image",n),await q.post(`/api/admin/products/${o}/meta-image`,l,{headers:{"Content-Type":"multipart/form-data"}})},le=o=>{console.log("Main image changed:",o)},Y=o=>{console.log("Image uploaded:",o),Q()},X=o=>{console.log("Image deleted:",o)},oe=o=>{console.log("Pending images changed:",o.length)},Q=async()=>{if(!(w.isCreate||!S.value))try{const o=await q.get(`/api/admin/products/${S.value}/with-images`);o.data&&o.data.success&&o.data.data&&(V.value=o.data.data.images||[])}catch(o){console.error("Error loading product images:",o),V.value=[]}},H=o=>{if(!o)return"N/A";try{const s=new Date(o);return new Intl.DateTimeFormat("uk-UA",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).format(s)}catch(s){return console.error("Error formatting date:",s,o),o}},K=o=>{console.log("Category changed:",o)},ne=o=>{console.log("Company changed:",o)},B=()=>{E.push("/admin/products")},ie=async()=>{try{if(p.value=!0,C.value=null,!a.value.companyId){C.value="Company is required",p.value=!1;return}if(!a.value.name||!a.value.name.trim()){C.value="Product name is required",p.value=!1;return}if(!a.value.description||!a.value.description.trim()){C.value="Product description is required",p.value=!1;return}if(!a.value.categoryId){C.value="Category is required",p.value=!1;return}if(!a.value.priceAmount||a.value.priceAmount<=0){C.value="Price must be greater than 0",p.value=!1;return}const o=a.value.slug||a.value.name.toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim("-"),s={companyId:a.value.companyId,name:a.value.name.trim(),slug:o,description:a.value.description.trim(),priceCurrency:0,priceAmount:parseFloat(a.value.priceAmount)||0,stock:parseInt(a.value.stock)||0,categoryId:a.value.categoryId,status:parseInt(a.value.status)||0,attributes:a.value.attributes||null,metaTitle:a.value.metaTitle||a.value.name||"",metaDescription:a.value.metaDescription||a.value.description||"",metaImage:a.value.metaImage&&!a.value.metaImage.startsWith("data:")?a.value.metaImage.trim():"https://via.placeholder.com/300x200.png?text=Product+Image"};console.log("📤 Sending product data:",s);let i;if(w.isCreate){const n=await ee.createProduct(s);if(console.log("✅ Product created:",n),i=n.data||n,a.value.metaImage&&a.value.metaImage.startsWith("data:")&&i)try{await J(i),console.log("✅ Meta image uploaded for new product")}catch(l){console.error("❌ Error uploading meta image:",l)}}else{const n=await ee.updateProduct(S.value,s);if(console.log("✅ Product updated:",n),i=S.value,a.value.metaImage&&a.value.metaImage.startsWith("data:"))try{await J(i),console.log("✅ Meta image updated")}catch(l){console.error("❌ Error updating meta image:",l)}else if(!a.value.metaImage&&T.value)try{await q.delete(`/api/admin/products/${i}/meta-image`),console.log("✅ Meta image deleted")}catch(l){console.error("❌ Error deleting meta image:",l)}}if(y.value&&i){const n=y.value.getPendingImages();n.length>0&&(console.log(`🔄 Uploading ${n.length} pending images...`),await y.value.uploadPendingImages(i))}U("save",s)}catch(o){console.error("❌ Error saving product:",o);let s="Failed to save product";o.response&&o.response.data?o.response.data.message?s=o.response.data.message:o.response.data.errors?s=Object.values(o.response.data.errors).flat().join(", "):typeof o.response.data=="string"&&(s=o.response.data):o.message&&(s=o.message),C.value=s}finally{p.value=!1}};ue(()=>a.value.name,o=>{o&&!a.value.slug&&(a.value.slug=o.toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim("-"))});const re=async()=>{if(!w.isCreate)try{v.value=!0,C.value=null;const o=await ee.getProductById(S.value),s=o.data||o;if(Object.keys(a.value).forEach(i=>{s[i]!==void 0&&(a.value[i]=s[i])}),T.value=s.metaImage||"",s.attributes)try{a.value.attributes=typeof s.attributes=="string"?JSON.parse(s.attributes):s.attributes}catch(i){console.error("Error parsing attributes:",i),a.value.attributes={}}console.log("Product loaded for editing:",a.value),await Q()}catch(o){console.error("Error loading product:",o),C.value=o.message||"Failed to load product"}finally{v.value=!1}};return ae(async()=>{await re()}),(o,s)=>(d(),r("div",ba,[v.value?(d(),r("div",Ia,s[13]||(s[13]=[e("div",{class:"loader is-loading"},null,-1),e("p",{class:"mt-4"},"Loading product data...",-1)]))):C.value?(d(),r("div",Ca,[s[14]||(s[14]=e("strong",null,"Error:",-1)),W(" "+_(C.value),1)])):(d(),r("form",{key:2,onSubmit:te(ie,["prevent"]),class:"product-form"},[e("div",$a,[e("div",wa,[e("div",{class:"level-item"},[e("button",{class:"button",onClick:B},s[15]||(s[15]=[e("span",{class:"icon"},[e("i",{class:"fas fa-arrow-left"})],-1),e("span",null,"Back to Products",-1)]))]),e("div",ka,[e("div",null,[e("h1",xa,_(b.isCreate?"Create Product":"Edit Product"),1),b.isCreate?D("",!0):(d(),r("p",Aa,_(a.value.id),1))])])]),e("div",Da,[e("div",Ma,[e("div",Pa,[e("button",{type:"button",class:"button",onClick:s[0]||(s[0]=i=>o.$emit("cancel"))}," Cancel "),e("button",{type:"submit",class:F(["button is-primary",{"is-loading":p.value}])},_(b.isCreate?"Create Product":"Save Changes"),3)])])])]),e("div",Sa,[e("div",Ea,[s[20]||(s[20]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-primary"},[e("i",{class:"fas fa-info-circle"})]),e("span",null,"Basic Information")])],-1)),e("div",Fa,[e("div",Ua,[de(rs,{modelValue:a.value.companyId,"onUpdate:modelValue":s[1]||(s[1]=i=>a.value.companyId=i),label:"Company",placeholder:"Search and select company...",required:!0,onChange:ne},null,8,["modelValue"])]),e("div",Ta,[de(Ne,{modelValue:a.value.categoryId,"onUpdate:modelValue":s[2]||(s[2]=i=>a.value.categoryId=i),label:"Category",placeholder:"Search and select category...",required:!0,onChange:K},null,8,["modelValue"])]),e("div",za,[e("div",La,[s[16]||(s[16]=e("label",{class:"label"},"Product Name *",-1)),e("div",Va,[L(e("input",{"onUpdate:modelValue":s[3]||(s[3]=i=>a.value.name=i),class:"input",type:"text",placeholder:"Enter product name",required:""},null,512),[[j,a.value.name]])])])]),e("div",ja,[e("div",Na,[s[17]||(s[17]=e("label",{class:"label"},"Slug",-1)),e("div",Oa,[L(e("input",{"onUpdate:modelValue":s[4]||(s[4]=i=>a.value.slug=i),class:"input",type:"text",placeholder:"Auto-generated from name"},null,512),[[j,a.value.slug]])]),s[18]||(s[18]=e("p",{class:"help"},"Leave empty to auto-generate from product name",-1))])]),e("div",Ba,[e("div",Ra,[s[19]||(s[19]=e("label",{class:"label"},"Description",-1)),e("div",qa,[L(e("textarea",{"onUpdate:modelValue":s[5]||(s[5]=i=>a.value.description=i),class:"textarea",placeholder:"Enter product description",rows:"4"},null,512),[[j,a.value.description]])])])])])]),e("div",Wa,[s[21]||(s[21]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-link"},[e("i",{class:"fas fa-images"})]),e("span",null,"Product Images")])],-1)),de(_a,{ref_key:"imageManager",ref:y,"product-id":S.value,images:V.value,"is-create":w.isCreate,onImagesUpdated:Q,onMainImageChanged:le,onImageUploaded:Y,onImageDeleted:X,onPendingImagesChanged:oe},null,8,["product-id","images","is-create"])]),e("div",Ga,[s[28]||(s[28]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-success"},[e("i",{class:"fas fa-dollar-sign"})]),e("span",null,"Pricing & Inventory")])],-1)),e("div",Za,[e("div",Qa,[e("div",Ha,[s[23]||(s[23]=e("label",{class:"label"},"Price *",-1)),e("div",Ja,[L(e("input",{"onUpdate:modelValue":s[6]||(s[6]=i=>a.value.priceAmount=i),class:"input",type:"number",step:"0.01",placeholder:"0.00",required:""},null,512),[[j,a.value.priceAmount,void 0,{number:!0}]]),s[22]||(s[22]=e("span",{class:"icon is-small is-left"},[e("i",{class:"fas fa-hryvnia-sign"})],-1))])])]),e("div",Ya,[e("div",Xa,[s[25]||(s[25]=e("label",{class:"label"},"Stock *",-1)),e("div",Ka,[L(e("input",{"onUpdate:modelValue":s[7]||(s[7]=i=>a.value.stock=i),class:"input",type:"number",min:"0",placeholder:"0",required:""},null,512),[[j,a.value.stock,void 0,{number:!0}]]),s[24]||(s[24]=e("span",{class:"icon is-small is-left"},[e("i",{class:"fas fa-boxes"})],-1))])])]),e("div",et,[e("div",st,[s[27]||(s[27]=e("label",{class:"label"},"Status",-1)),e("div",at,[e("div",tt,[L(e("select",{"onUpdate:modelValue":s[8]||(s[8]=i=>a.value.status=i)},s[26]||(s[26]=[e("option",{value:"0"},"Pending",-1),e("option",{value:"1"},"Approved",-1),e("option",{value:"2"},"Rejected",-1)]),512),[[fe,a.value.status]])])])])])])]),e("div",lt,[s[33]||(s[33]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-info"},[e("i",{class:"fas fa-tags"})]),e("span",null,"Product Attributes")])],-1)),e("div",ot,[e("div",nt,[L(e("input",{"onUpdate:modelValue":s[9]||(s[9]=i=>x.value.key=i),class:"input",type:"text",placeholder:"Attribute name (e.g., Color, Size)"},null,512),[[j,x.value.key]])]),e("div",it,[L(e("input",{"onUpdate:modelValue":s[10]||(s[10]=i=>x.value.value=i),class:"input",type:"text",placeholder:"Attribute value (e.g., Red, Large)"},null,512),[[j,x.value.value]])]),e("div",{class:"control"},[e("button",{type:"button",class:"button is-primary",onClick:t},s[29]||(s[29]=[e("span",{class:"icon"},[e("i",{class:"fas fa-plus"})],-1),e("span",null,"Add",-1)]))])]),Object.keys(a.value.attributes).length>0?(d(),r("div",rt,[e("table",dt,[s[31]||(s[31]=e("thead",null,[e("tr",null,[e("th",null,"Attribute"),e("th",null,"Values"),e("th",{width:"100"},"Actions")])],-1)),e("tbody",null,[(d(!0),r(G,null,Z(a.value.attributes,(i,n)=>(d(),r("tr",{key:n},[e("td",null,[e("strong",null,_(n),1)]),e("td",null,[e("span",ct,[(d(!0),r(G,null,Z(Array.isArray(i)?i:[i],(l,M)=>(d(),r("span",{key:M,class:"tag is-light"},[W(_(l)+" ",1),e("button",{type:"button",class:"delete is-small",onClick:P=>c(n,M)},null,8,ut)]))),128))])]),e("td",null,[e("button",{type:"button",class:"button is-small is-danger",onClick:l=>u(n)},s[30]||(s[30]=[e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1)]),8,mt)])]))),128))])])])):(d(),r("div",vt,s[32]||(s[32]=[e("span",{class:"icon-text"},[e("span",{class:"icon"},[e("i",{class:"fas fa-info-circle"})]),e("span",null,"No attributes added yet. Use the form above to add product attributes.")],-1)])))]),e("div",gt,[s[40]||(s[40]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-warning"},[e("i",{class:"fas fa-search"})]),e("span",null,"SEO Meta Information")])],-1)),e("div",pt,[e("div",ft,[e("div",ht,[s[34]||(s[34]=e("label",{class:"label"},"Meta Title",-1)),e("div",yt,[L(e("input",{"onUpdate:modelValue":s[11]||(s[11]=i=>a.value.metaTitle=i),class:"input",type:"text",placeholder:"SEO title for search engines"},null,512),[[j,a.value.metaTitle]])])])]),e("div",_t,[e("div",bt,[s[35]||(s[35]=e("label",{class:"label"},"Meta Description",-1)),e("div",It,[L(e("textarea",{"onUpdate:modelValue":s[12]||(s[12]=i=>a.value.metaDescription=i),class:"textarea",placeholder:"SEO description for search engines",rows:"3"},null,512),[[j,a.value.metaDescription]])])])]),e("div",Ct,[e("div",$t,[s[38]||(s[38]=e("label",{class:"label"},"Meta Image",-1)),e("div",wt,[e("div",kt,[e("div",xt,[e("label",At,[e("input",{class:"file-input",type:"file",accept:"image/*",onChange:I,disabled:m.value},null,40,Dt),e("span",{class:F(["file-cta",{"is-loading":m.value}])},[m.value?D("",!0):(d(),r("span",Mt,s[36]||(s[36]=[e("i",{class:"fas fa-upload"},null,-1)]))),e("span",Pt,_(m.value?"Uploading...":"Choose image..."),1)],2),e("span",St,_(A.value||"No file selected"),1)])])]),a.value.metaImage?(d(),r("div",Et,[e("button",{type:"button",class:"button is-danger",onClick:$,disabled:m.value},s[37]||(s[37]=[e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1),e("span",null,"Clear",-1)]),8,Ft)])):D("",!0)])])])]),a.value.metaImage?(d(),r("div",Ut,[e("div",Tt,[e("figure",zt,[e("img",{src:a.value.metaImage,alt:a.value.name,class:"is-rounded"},null,8,Lt)]),s[39]||(s[39]=e("p",{class:"has-text-grey mt-2"},[e("small",null,"Meta Image Preview")],-1))])])):D("",!0)]),b.isCreate?D("",!0):(d(),r("div",Vt,[s[43]||(s[43]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-grey"},[e("i",{class:"fas fa-clock"})]),e("span",null,"Timestamps")])],-1)),e("div",jt,[e("div",Nt,[e("div",Ot,[s[41]||(s[41]=e("label",{class:"label"},"Created At",-1)),e("div",Bt,[e("input",{class:"input",type:"text",value:H(a.value.createdAt),readonly:""},null,8,Rt)])])]),a.value.updatedAt?(d(),r("div",qt,[e("div",Wt,[s[42]||(s[42]=e("label",{class:"label"},"Updated At",-1)),e("div",Gt,[e("input",{class:"input",type:"text",value:H(a.value.updatedAt),readonly:""},null,8,Zt)])])])):D("",!0)])]))])],32))]))}},Xt=se(Qt,[["__scopeId","data-v-1e287472"]]);export{Xt as default};
