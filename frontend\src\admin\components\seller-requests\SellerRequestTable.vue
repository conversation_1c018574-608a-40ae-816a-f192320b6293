<template>
  <div class="seller-request-table">
    <div class="table-container">
      <table class="table">
        <thead>
          <tr>
            <th>ID</th>
            <th>User</th>
            <th>Business Name</th>
            <th>Submitted</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody v-if="!loading && requests.length > 0">
          <tr v-for="request in requests" :key="request.id">
            <td>
              <span class="admin-seller-request-id">{{ request.id }}</span>
            </td>
            <td>
              <div class="admin-user-info">
                <div class="admin-user-name">{{ request.userName }}</div>
              </div>
            </td>
            <td>
              <div class="admin-business-info">
                <div class="admin-business-name">{{ request.businessName }}</div>
              </div>
            </td>
            <td>{{ formatDate(request.submittedAt) }}</td>
            <td>
              <status-badge :status="request.status" />
            </td>
            <td>
              <div class="admin-seller-request-actions">
                <button
                  class="button is-info"
                  @click="$emit('view', request.id)"
                  title="View Details">
                  <i class="fas fa-eye"></i>
                </button>
                <button
                  v-if="request.status === 'Pending'"
                  class="button is-success" 
                  @click="$emit('approve', request.id)">
                  <span class="icon"><i class="fas fa-check"></i></span>
                </button>
                <button 
                  v-if="request.status === 'Pending'"
                  class="button is-danger" 
                  @click="$emit('reject', request.id)">
                  <span class="icon"><i class="fas fa-times"></i></span>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else-if="loading">
          <tr>
            <td colspan="6" class="has-text-centered">
              <div class="loader-wrapper">
                <div class="loader is-loading"></div>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else>
          <tr>
            <td colspan="6" class="has-text-centered">
              No seller applications found.
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import StatusBadge from '@/components/admin/common/StatusBadge.vue';

defineProps({
  requests: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

defineEmits(['view', 'approve', 'reject']);

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};
</script>

<style scoped>
.loader-wrapper {
  padding: 2rem;
  display: flex;
  justify-content: center;
}

.loader {
  height: 80px;
  width: 80px;
}
</style>
