<template>
  <div class="admin-seller-request-table">
    <div class="admin-table-container">
      <table class="admin-table">
        <thead class="admin-table-header">
          <tr>
            <th class="admin-table-th">ID</th>
            <th class="admin-table-th">User</th>
            <th class="admin-table-th">Business Name</th>
            <th class="admin-table-th">Submitted</th>
            <th class="admin-table-th">Status</th>
            <th class="admin-table-th">Actions</th>
          </tr>
        </thead>
        <tbody v-if="!loading && requests.length > 0" class="admin-table-body">
          <tr v-for="request in requests" :key="request.id" class="admin-table-row">
            <td class="admin-table-td">
              <span class="admin-seller-request-id">{{ request.id }}</span>
            </td>
            <td class="admin-table-td">
              <div class="admin-user-info">
                <div class="admin-user-name">{{ request.userName }}</div>
              </div>
            </td>
            <td class="admin-table-td">
              <div class="admin-business-info">
                <div class="admin-business-name">{{ request.businessName }}</div>
              </div>
            </td>
            <td class="admin-table-td">{{ formatDate(request.submittedAt) }}</td>
            <td class="admin-table-td">
              <status-badge :status="request.status" />
            </td>
            <td class="admin-table-td">
              <div class="admin-table-actions">
                <button
                  class="admin-btn admin-btn-secondary admin-btn-sm"
                  @click="$emit('view', request.id)"
                  title="View Details">
                  <i class="fas fa-eye"></i>
                </button>
                <button
                  v-if="request.status === 'Pending'"
                  class="admin-btn admin-btn-success admin-btn-sm"
                  @click="$emit('approve', request.id)">
                  <i class="fas fa-check"></i>
                </button>
                <button
                  v-if="request.status === 'Pending'"
                  class="admin-btn admin-btn-danger admin-btn-sm"
                  @click="$emit('reject', request.id)">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else-if="loading" class="admin-table-body">
          <tr>
            <td colspan="6" class="admin-table-td admin-table-loading">
              <div class="admin-loading-state">
                <div class="admin-spinner">
                  <i class="fas fa-spinner fa-pulse"></i>
                </div>
                <p class="admin-loading-text">Loading...</p>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else class="admin-table-body">
          <tr>
            <td colspan="6" class="admin-table-td admin-table-empty">
              <div class="admin-empty-state">
                <div class="admin-empty-icon">
                  <i class="fas fa-store"></i>
                </div>
                <p class="admin-empty-text">No seller applications found.</p>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import StatusBadge from '@/components/admin/common/StatusBadge.vue';

defineProps({
  requests: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

defineEmits(['view', 'approve', 'reject']);

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};
</script>

<style scoped>
.admin-seller-request-table {
  background: var(--admin-white);
  border-radius: var(--admin-radius-lg);
  border: 1px solid var(--admin-border-light);
  overflow: hidden;
}

.admin-table-actions {
  display: flex;
  gap: var(--admin-space-xs);
  justify-content: flex-start;
}

.admin-table-loading,
.admin-table-empty {
  text-align: center;
  padding: var(--admin-space-2xl);
}

.admin-seller-request-id {
  font-family: var(--admin-font-mono);
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
}

.admin-user-name,
.admin-business-name {
  font-weight: var(--admin-font-medium);
  color: var(--admin-gray-900);
}
</style>
