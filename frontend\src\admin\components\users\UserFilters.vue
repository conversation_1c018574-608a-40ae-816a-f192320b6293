<template>
  <div class="admin-user-filters">
    <div class="columns is-multiline is-centered">
      <div class="column is-4">
        <div class="field">
          <label class="label">Search</label>
          <div class="control has-icons-left">
            <input
              class="input"
              type="text"
              placeholder="Search by username, email..."
              v-model="filters.search"
              @input="debounceSearch">
            <span class="icon is-small is-left">
              <i class="fas fa-search"></i>
            </span>
          </div>
        </div>
      </div>

      <div class="column is-4">
        <div class="field">
          <label class="label">Role</label>
          <div class="control">
            <div class="select is-fullwidth">
              <select v-model="filters.role">
                <option value="">All Roles</option>
                <option value="Admin">Admin</option>
                <option value="Moderator">Moderator</option>
                <option value="Seller">Seller</option>
                <option value="Buyer">Buyer</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="columns is-centered">
      <div class="column is-8">
        <div class="buttons is-centered">
          <button class="admin-btn admin-btn-secondary" @click="resetFilters">
            <i class="fas fa-undo"></i>
            <span>Reset</span>
          </button>
          <button class="admin-btn admin-btn-primary" @click="applyFilters">
            <i class="fas fa-filter"></i>
            <span>Apply Filters</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue';

const emit = defineEmits(['filter-changed']);

// Filter state
const filters = reactive({
  search: '',
  role: ''
});

// Apply filters
const applyFilters = () => {
  // Create a clean filter object (remove null/empty values)
  const cleanFilters = {};

  Object.entries(filters).forEach(([key, value]) => {
    if (value !== null && value !== '') {
      cleanFilters[key] = value;
    }
  });

  emit('filter-changed', cleanFilters);
};

// Reset filters
const resetFilters = () => {
  filters.search = '';
  filters.role = '';

  emit('filter-changed', {});
};

// Debounce search to avoid too many requests
let searchTimeout;
const debounceSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    applyFilters();
  }, 500);
};
</script>

<style scoped>
.mb-4 {
  margin-bottom: 1.5rem;
}

.box {
  padding: 1.5rem;
}

.field {
  margin-bottom: 1rem;
}

.buttons {
  margin-top: 1rem;
}

/* Ensure consistent spacing on different screen sizes */
@media screen and (max-width: 768px) {
  .columns.is-centered {
    display: block;
  }

  .column {
    padding: 0.5rem;
  }

  .buttons {
    display: flex;
    justify-content: center;
  }
}
</style>
