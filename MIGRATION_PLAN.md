# План міграції компонентів на уніфіковану архітектуру

## Поточний статус

### ✅ Завершено
- [x] Створено уніфіковану архітектуру сервісів (7 нових файлів)
- [x] Видалено дублюючі сервіси (2 файли, 573 рядки)
- [x] Рефакторинг backend контролерів (BaseReportController + оптимізація)
- [x] Міграція SalesReport.vue на новий сервіс
- [x] Оновлення ExportButtons.vue для нового API

### 🔄 В процесі
- [ ] Міграція решти Vue компонентів

### ⏳ Заплановано
- [ ] Тестування та валідація
- [ ] Документація для розробників

## Детальний план міграції компонентів

### Фаза 1: Міграція основних компонентів звітів

#### 1.1 FinancialReport.vue
**Розташування**: `Marketplace.Presentation/wwwroot/js/admin/reports/components/reports/FinancialReport.vue`

**Зміни що потрібні**:
```javascript
// Замінити імпорти
- import { useReportsStore } from '../../stores/reportsStore.js'
+ import { reportsService, formatters } from '../../../../../src/services/reports'

// Замінити store логіку на прямі виклики сервісу
- const reportsStore = useReportsStore()
- const isLoading = computed(() => reportsStore.isLoading)
+ const isLoading = ref(false)
+ const reportData = ref(null)

// Замінити методи отримання даних
- await reportsStore.fetchReport('financial', filters)
+ reportData.value = await reportsService.getFinancialReport(filters)
```

#### 1.2 ProductsReport.vue
**Розташування**: `Marketplace.Presentation/wwwroot/js/admin/reports/components/reports/ProductsReport.vue`

**Зміни що потрібні**:
- Аналогічні зміни як в FinancialReport.vue
- Оновлення методів фільтрації продуктів
- Використання нових formatters для відображення даних

#### 1.3 UsersReport.vue
**Розташування**: `Marketplace.Presentation/wwwroot/js/admin/reports/components/reports/UsersReport.vue`

**Зміни що потрібні**:
- Міграція на новий сервіс
- Оновлення логіки фільтрації користувачів
- Використання уніфікованих форматерів

#### 1.4 OrdersReport.vue
**Розташування**: `Marketplace.Presentation/wwwroot/js/admin/reports/components/reports/OrdersReport.vue`

**Зміни що потрібні**:
- Міграція на новий API
- Оновлення статусів замовлень
- Інтеграція з новою системою експорту

### Фаза 2: Міграція допоміжних компонентів

#### 2.1 ReportFilters.vue
**Розташування**: `Marketplace.Presentation/wwwroot/js/admin/reports/components/shared/ReportFilters.vue`

**Потенційні покращення**:
- Використання нових валідаторів з formatters
- Інтеграція з кешуванням фільтрів
- Покращена UX для вибору дат

#### 2.2 MetricCard.vue
**Розташування**: `Marketplace.Presentation/wwwroot/js/admin/reports/components/shared/MetricCard.vue`

**Потенційні покращення**:
- Використання нових formatters для відображення метрик
- Стандартизовані іконки та кольори
- Покращена анімація та інтерактивність

#### 2.3 ReportTable.vue
**Розташування**: `Marketplace.Presentation/wwwroot/js/admin/reports/components/shared/ReportTable.vue`

**Потенційні покращення**:
- Інтеграція з новою системою сортування
- Використання уніфікованих форматерів для колонок
- Покращена пагінація

### Фаза 3: Оптимізація та очищення

#### 3.1 Видалення застарілих файлів
- [ ] Перевірити та видалити неіспользуемые stores
- [ ] Очистити застарілі утиліти та хелпери
- [ ] Видалити дублюючі компоненти в різних директоріях

#### 3.2 Стандартизація структури
- [ ] Уніфікувати структуру компонентів
- [ ] Стандартизувати naming conventions
- [ ] Оптимізувати імпорти та залежності

## Технічні деталі міграції

### Шаблон міграції компонента

```javascript
// 1. Оновити імпорти
import { reportsService, formatters } from '@/services/reports'

// 2. Замінити store на локальний стан
const isLoading = ref(false)
const error = ref(null)
const reportData = ref(null)
const filters = ref({ /* default filters */ })

// 3. Створити метод завантаження даних
const fetchReportData = async (newFilters = null) => {
  try {
    isLoading.value = true
    error.value = null
    
    const currentFilters = newFilters || filters.value
    reportData.value = await reportsService.getReport(reportType, currentFilters)
    
    if (newFilters) {
      filters.value = { ...filters.value, ...newFilters }
    }
  } catch (err) {
    error.value = err.message
  } finally {
    isLoading.value = false
  }
}

// 4. Оновити computed properties
const hasData = computed(() => reportData.value && reportData.value.type === reportType)
const metrics = computed(() => reportData.value?.metrics?.items || [])

// 5. Використовувати нові formatters
const formatCurrency = (value) => formatters.formatCurrency(value)
const formatDate = (value) => formatters.formatDate(value)
```

### Контрольний список для кожного компонента

- [ ] Оновлено імпорти на новий сервіс
- [ ] Замінено store логіку на локальний стан
- [ ] Створено методи для роботи з новим API
- [ ] Оновлено computed properties
- [ ] Використано нові formatters
- [ ] Перевірено функціональність експорту
- [ ] Протестовано обробку помилок
- [ ] Перевірено кешування даних

## Тестування

### План тестування після міграції

#### Функціональне тестування
1. **Завантаження даних**
   - [ ] Перевірити завантаження всіх типів звітів
   - [ ] Протестувати фільтрацію та сортування
   - [ ] Перевірити обробку помилок

2. **Експорт функціональність**
   - [ ] Протестувати експорт в Excel
   - [ ] Протестувати експорт в PDF
   - [ ] Протестувати експорт в CSV
   - [ ] Перевірити генерацію імен файлів

3. **Кешування**
   - [ ] Перевірити кешування запитів
   - [ ] Протестувати очищення кешу
   - [ ] Перевірити TTL кешу

#### Продуктивність
- [ ] Порівняти швидкість завантаження до/після
- [ ] Перевірити використання пам'яті
- [ ] Протестувати під навантаженням

#### Сумісність
- [ ] Перевірити роботу в різних браузерах
- [ ] Протестувати на мобільних пристроях
- [ ] Перевірити accessibility

## Ризики та мітигація

### Потенційні ризики

1. **Втрата функціональності**
   - **Мітигація**: Детальне тестування кожного компонента
   - **План Б**: Можливість швидкого rollback

2. **Проблеми з продуктивністю**
   - **Мітигація**: Моніторинг метрик до/після міграції
   - **План Б**: Оптимізація кешування

3. **Проблеми з UX**
   - **Мітигація**: Тестування з користувачами
   - **План Б**: Поетапне впровадження

### План відкату

У випадку критичних проблем:
1. Відновити видалені сервіси з git history
2. Повернути компоненти до попередньої версії
3. Відключити нові endpoints в backend
4. Провести додаткове тестування

## Часові рамки

### Орієнтовний план
- **Фаза 1** (Міграція основних компонентів): 2-3 дні
- **Фаза 2** (Допоміжні компоненти): 1-2 дні  
- **Фаза 3** (Оптимізація та очищення): 1 день
- **Тестування**: 1-2 дні

**Загальний час**: 5-8 робочих днів

## Критерії успіху

### Технічні критерії
- [ ] Всі компоненти мігровано без втрати функціональності
- [ ] Продуктивність покращена або залишилась на тому ж рівні
- [ ] Кодова база зменшена мінімум на 50%
- [ ] Всі тести проходять успішно

### Бізнес критерії
- [ ] Користувачі не помічають змін в UX
- [ ] Час завантаження звітів покращений
- [ ] Експорт працює стабільно
- [ ] Немає критичних багів в продакшені

## Наступні кроки

1. **Почати з FinancialReport.vue** - найпростіший для міграції
2. **Протестувати на dev середовищі**
3. **Поступово мігрувати інші компоненти**
4. **Провести повне тестування**
5. **Задеплоїти на staging**
6. **Отримати feedback від користувачів**
7. **Задеплоїти на production**

Цей план забезпечує поступову та безпечну міграцію всіх компонентів на нову уніфіковану архітектуру.
