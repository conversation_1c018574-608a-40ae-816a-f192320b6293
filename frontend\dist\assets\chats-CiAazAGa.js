import{q as o}from"./index-BKy0rL_2.js";const c={async getChats(t={}){var r,e;try{return(await o.get("/api/admin/chats",{params:t})).data.data}catch(a){throw console.error("Error fetching chats:",a),new Error(((e=(r=a.response)==null?void 0:r.data)==null?void 0:e.message)||"Failed to load chats")}},async getChatById(t){var r,e;try{return(await o.get(`/api/admin/chats/${t}`)).data.data}catch(a){throw console.error("Error fetching chat:",a),new Error(((e=(r=a.response)==null?void 0:r.data)==null?void 0:e.message)||"Failed to load chat details")}},async getChatMessages(t,r={}){var e,a;try{return(await o.get(`/api/admin/chats/${t}/messages`,{params:r})).data.data}catch(s){throw console.error("Error fetching chat messages:",s),new Error(((a=(e=s.response)==null?void 0:e.data)==null?void 0:a.message)||"Failed to load chat messages")}},async deleteMessage(t){var r,e;try{return(await o.delete(`/api/admin/messages/${t}`)).data}catch(a){throw console.error("Error deleting message:",a),new Error(((e=(r=a.response)==null?void 0:r.data)==null?void 0:e.message)||"Failed to delete message")}},async getChatStats(){var t,r;try{return(await o.get("/api/admin/chats/stats")).data.data}catch(e){throw console.error("Error fetching chat stats:",e),new Error(((r=(t=e.response)==null?void 0:t.data)==null?void 0:r.message)||"Failed to load chat statistics")}},async moderateChat(t,r){var e,a;try{return(await o.post(`/api/admin/chats/${t}/moderate`,{action:r})).data}catch(s){throw console.error("Error moderating chat:",s),new Error(((a=(e=s.response)==null?void 0:e.data)==null?void 0:a.message)||"Failed to moderate chat")}}};export{c};
