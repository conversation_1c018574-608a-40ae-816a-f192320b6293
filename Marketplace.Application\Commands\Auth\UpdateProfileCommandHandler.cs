﻿using AutoMapper;
using Marketplace.Application.Commands.Auth;
using Marketplace.Application.Responses;
using Marketplace.Application.Services.Auth;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Configuration;

public class UpdateProfileCommandHandler : IRequestHandler<UpdateProfileCommand, UserResponse>
{
    private readonly IUserRepository _userRepository;
    private readonly ITokenService _tokenService;
    private readonly IMapper _mapper;
    private readonly string _baseUrl;
    private readonly IEmailService? _emailService;

    public UpdateProfileCommandHandler(IUserRepository userRepository, IConfiguration configuration, ITokenService tokenService, IMapper mapper, IEmailService? emailService = null)
    {
        _userRepository = userRepository;
        _tokenService = tokenService;
        _mapper = mapper;
        _emailService = emailService;
        _baseUrl = configuration["Frontend:BaseUrl"]
            ?? throw new ArgumentNullException("Frontend:BaseUrl is not configured.");
    }

    public async Task<UserResponse> Handle(UpdateProfileCommand request, CancellationToken cancellationToken)
    {
        var user = await _userRepository.GetByIdAsync(request.Id, cancellationToken)
        ?? throw new KeyNotFoundException("Користувача не знайдено!");

        // Оновлення імені користувача
        if (!string.IsNullOrEmpty(request.Username) && request.Username != user.Username)
        {
            user.Username = request.Username; // Пряме присвоєння для тестування
        }

        // Оновлення електронної пошти
        if (!string.IsNullOrEmpty(request.Email) && request.Email != user.Email)
        {
            var emailExists = await _userRepository.GetByEmailAsync(request.Email, cancellationToken);
            if (emailExists != null && emailExists.Id != user.Id)
                throw new InvalidOperationException("Користувач з даною електронною адресою вже існує!");

            user.Email = new Email(request.Email);
            user.EmailConfirmationToken = _tokenService.GenerateToken();
            user.EmailConfirmedAt = null;

            var confirmationLink = $"{_baseUrl}/confirm-email?id={user.Id}&token={Uri.EscapeDataString(user.EmailConfirmationToken)}";
            if (_emailService != null)
            {
                await _emailService.SendEmailAsync(
                user.Email,
                "Підтвердження нової електронної пошти",
                $"Будь ласка, підтвердьте вашу нову пошту, перейшовши за посиланням: <a href='{confirmationLink}'>Підтвердити</a>",
                cancellationToken);
            }
        }

        // Оновлення дати народження
        if (request.Birthday.HasValue)
        {
            user.Birthday = request.Birthday.Value;
        }

        await _userRepository.UpdateAsync(user, cancellationToken);

        // Створюємо відповідь напряму, щоб уникнути проблем з маппінгом
        var response = new UserResponse
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email.Value,
            Password = user.Password.Value,
            Birthday = user.Birthday ?? DateTime.MinValue,
            Role = user.Role,
            LastSeenAt = user.LastSeenAt ?? DateTime.MinValue,
            EmailConfirmationToken = user.EmailConfirmationToken ?? string.Empty,
            PasswordResetToken = user.PasswordResetToken ?? string.Empty,
            EmailConfirmedAt = user.EmailConfirmedAt ?? DateTime.MinValue,
            IsApproved = user.IsApproved,
            ApprovedAt = user.ApprovedAt ?? DateTime.MinValue,
            ApprovedByUserId = user.ApprovedByUserId ?? Guid.Empty
        };

        return response;
    }
}