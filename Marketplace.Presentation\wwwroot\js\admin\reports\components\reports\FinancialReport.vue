<template>
  <div class="financial-report">
    <!-- Header -->
    <div class="report-header">
      <div class="header-content">
        <h1 class="report-title">
          <i class="fas fa-dollar-sign"></i>
          Financial Report
        </h1>
        <p class="report-description">
          Comprehensive financial analysis including revenue, expenses, and profitability metrics
        </p>
      </div>
      
      <!-- Export Actions -->
      <div class="header-actions">
        <ExportButtons 
          :report-type="'financial'"
          :filters="filters"
          :disabled="isLoading"
        />
      </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
      <ReportFilters 
        :show-report-type="false"
        :report-type="'financial'"
        @filters-changed="handleFiltersChanged"
      />
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading financial data...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <div class="error-content">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Failed to Load Financial Data</h3>
        <p>{{ error }}</p>
        <button @click="refreshData" class="retry-btn">
          <i class="fas fa-redo"></i>
          Try Again
        </button>
      </div>
    </div>

    <!-- Report Content -->
    <div v-else-if="hasData" class="report-content">
      <!-- Key Metrics -->
      <div class="metrics-section">
        <h3 class="section-title">
          <i class="fas fa-chart-line"></i>
          Key Financial Metrics
        </h3>
        
        <div class="metrics-grid">
          <MetricCard
            v-for="metric in financialMetrics"
            :key="metric.key"
            :metric="metric"
            :loading="isLoading"
          />
        </div>
      </div>

      <!-- Revenue Trend Chart -->
      <div class="chart-section">
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">Revenue Trend</h3>
            <div class="chart-controls">
              <select v-model="chartPeriod" @change="updateChart" class="chart-select">
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>
          </div>
          
          <div class="chart-content">
            <canvas ref="revenueChartCanvas" id="revenue-chart"></canvas>
          </div>
        </div>
      </div>

      <!-- Revenue Distribution -->
      <div class="distribution-section">
        <div class="distribution-container">
          <div class="distribution-header">
            <h3 class="distribution-title">Revenue by Payment Method</h3>
          </div>
          
          <div class="distribution-content">
            <div class="chart-wrapper">
              <canvas ref="paymentChartCanvas" id="payment-chart"></canvas>
            </div>
            
            <div class="distribution-legend">
              <div
                v-for="(item, index) in paymentMethodData"
                :key="item.method"
                class="legend-item"
              >
                <div
                  class="legend-color"
                  :style="{ backgroundColor: getPaymentMethodColor(index) }"
                ></div>
                <div class="legend-info">
                  <div class="legend-label">{{ item.method }}</div>
                  <div class="legend-value">
                    {{ formatCurrency(item.revenue) }} ({{ item.percentage.toFixed(1) }}%)
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Financial Insights -->
      <div class="insights-section">
        <h3 class="section-title">
          <i class="fas fa-lightbulb"></i>
          Financial Insights
        </h3>
        
        <div class="insights-grid">
          <div
            v-for="insight in financialInsights"
            :key="insight.id"
            class="insight-card"
            :class="insight.type"
          >
            <div class="insight-icon">
              <i :class="insight.icon"></i>
            </div>
            <div class="insight-content">
              <h4 class="insight-title">{{ insight.title }}</h4>
              <p class="insight-description">{{ insight.description }}</p>
              <div v-if="insight.action" class="insight-action">
                <button @click="handleInsightAction(insight)" class="action-btn">
                  {{ insight.action.label }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Financial Table -->
      <div class="table-section">
        <ReportTable
          :data="tableData"
          :columns="tableColumns"
          :loading="isLoading"
          title="Financial Details"
          :exportable="true"
        />
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="empty-state">
      <div class="empty-content">
        <i class="fas fa-chart-bar"></i>
        <h3>No Financial Data Available</h3>
        <p>Try adjusting your filters or date range to see financial data.</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useReportsStore } from '../../stores/reportsStore.js'
import { Chart } from 'chart.js'
import ReportFilters from '../shared/ReportFilters.vue'
import MetricCard from '../shared/MetricCard.vue'
import ExportButtons from '../shared/ExportButtons.vue'
import ReportTable from '../shared/ReportTable.vue'

export default {
  name: 'FinancialReport',
  components: {
    ReportFilters,
    MetricCard,
    ExportButtons,
    ReportTable
  },
  setup() {
    const reportsStore = useReportsStore()

    // Reactive references
    const revenueChartCanvas = ref(null)
    const paymentChartCanvas = ref(null)
    const chartPeriod = ref('monthly')
    const revenueChart = ref(null)
    const paymentChart = ref(null)

    // Computed properties
    const isLoading = computed(() => reportsStore.isLoading)
    const error = computed(() => reportsStore.error)
    const hasData = computed(() => reportsStore.hasData && reportsStore.currentReport?.type === 'financial')
    const filters = computed(() => reportsStore.filters)

    const financialMetrics = computed(() => {
      const data = reportsStore.currentReport
      if (!data?.metrics?.items) return []

      return data.metrics.items.map(item => ({
        key: item.key,
        label: item.label,
        value: item.value,
        type: item.type || 'currency',
        icon: item.icon || 'fas fa-dollar-sign',
        trend: item.trend || 'neutral',
        changePercentage: item.changePercentage || 0,
        previousValue: item.previousValue
      }))
    })

    const paymentMethodData = computed(() => {
      const data = reportsStore.currentReport
      if (!data?.charts?.paymentMethods?.data) return []

      return data.charts.paymentMethods.data.map(item => ({
        method: item.label,
        revenue: item.value,
        percentage: item.percentage || 0
      }))
    })

    const financialInsights = computed(() => {
      const data = reportsStore.currentReport
      return data?.insights || []
    })

    const tableData = computed(() => {
      const data = reportsStore.currentReport
      return data?.table?.data || []
    })

    const tableColumns = computed(() => {
      const data = reportsStore.currentReport
      return data?.table?.columns || [
        { key: 'date', label: 'Date', type: 'date' },
        { key: 'revenue', label: 'Revenue', type: 'currency' },
        { key: 'expenses', label: 'Expenses', type: 'currency' },
        { key: 'profit', label: 'Profit', type: 'currency' },
        { key: 'margin', label: 'Margin %', type: 'percentage' }
      ]
    })

    // Methods
    const handleFiltersChanged = async (newFilters) => {
      await reportsStore.fetchReport('financial', newFilters)
      await nextTick()
      updateCharts()
    }

    const refreshData = async () => {
      await reportsStore.fetchReport('financial')
      await nextTick()
      updateCharts()
    }

    const updateChart = async () => {
      await nextTick()
      updateCharts()
    }

    const updateCharts = () => {
      updateRevenueChart()
      updatePaymentChart()
    }

    const updateRevenueChart = () => {
      if (!revenueChartCanvas.value) return

      const data = reportsStore.currentReport
      if (!data?.charts?.revenue) return

      const ctx = revenueChartCanvas.value.getContext('2d')

      if (revenueChart.value) {
        revenueChart.value.destroy()
      }

      revenueChart.value = new Chart(ctx, {
        type: 'line',
        data: {
          labels: data.charts.revenue.labels || [],
          datasets: [{
            label: 'Revenue',
            data: data.charts.revenue.data || [],
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return formatCurrency(value)
                }
              }
            }
          }
        }
      })
    }

    const updatePaymentChart = () => {
      if (!paymentChartCanvas.value || !paymentMethodData.value.length) return

      const ctx = paymentChartCanvas.value.getContext('2d')

      if (paymentChart.value) {
        paymentChart.value.destroy()
      }

      paymentChart.value = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: paymentMethodData.value.map(item => item.method),
          datasets: [{
            data: paymentMethodData.value.map(item => item.revenue),
            backgroundColor: paymentMethodData.value.map((_, index) => getPaymentMethodColor(index))
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          }
        }
      })
    }

    const getPaymentMethodColor = (index) => {
      const colors = [
        '#3b82f6', '#ef4444', '#10b981', '#f59e0b',
        '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
      ]
      return colors[index % colors.length]
    }

    const formatCurrency = (value) => {
      if (typeof value !== 'number') return value
      return new Intl.NumberFormat('uk-UA', {
        style: 'currency',
        currency: 'UAH'
      }).format(value)
    }

    const handleInsightAction = (insight) => {
      if (insight.action?.callback) {
        insight.action.callback()
      }
    }

    // Lifecycle
    onMounted(async () => {
      await reportsStore.fetchReport('financial')
      await nextTick()
      updateCharts()
    })

    // Watch for data changes
    watch(() => reportsStore.currentReport, async () => {
      await nextTick()
      updateCharts()
    }, { deep: true })

    return {
      revenueChartCanvas,
      paymentChartCanvas,
      chartPeriod,
      isLoading,
      error,
      hasData,
      filters,
      financialMetrics,
      paymentMethodData,
      financialInsights,
      tableData,
      tableColumns,
      handleFiltersChanged,
      refreshData,
      updateChart,
      getPaymentMethodColor,
      formatCurrency,
      handleInsightAction
    }
  }
}
</script>

<style scoped>
.financial-report {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 0;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.report-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.report-title i {
  color: #10b981;
}

.report-description {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  flex-shrink: 0;
}

.filters-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-container,
.error-container,
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-spinner,
.error-content,
.empty-content {
  text-align: center;
  color: #6b7280;
}

.loading-spinner i {
  font-size: 2rem;
  color: #3b82f6;
  margin-bottom: 1rem;
}

.error-content i,
.empty-content i {
  font-size: 3rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.empty-content i {
  color: #9ca3af;
}

.retry-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 1rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #2563eb;
}

.report-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.metrics-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.chart-section,
.distribution-section,
.insights-section,
.table-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-container,
.distribution-container {
  width: 100%;
}

.chart-header,
.distribution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.chart-title,
.distribution-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 1rem;
}

.chart-select {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 0.875rem;
  cursor: pointer;
}

.chart-content {
  position: relative;
  height: 400px;
}

.distribution-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  align-items: center;
}

.chart-wrapper {
  position: relative;
  height: 300px;
}

.distribution-legend {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.legend-info {
  flex: 1;
}

.legend-label {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.legend-value {
  font-size: 0.875rem;
  color: #6b7280;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.insight-card {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  background: #f8fafc;
}

.insight-card.warning {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.insight-card.success {
  border-left-color: #10b981;
  background: #f0fdf4;
}

.insight-card.danger {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.insight-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.insight-card.warning .insight-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.insight-card.success .insight-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.insight-card.danger .insight-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.insight-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.insight-action {
  margin-top: 1rem;
}

.action-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background: #2563eb;
}

@media (max-width: 768px) {
  .report-header {
    flex-direction: column;
    gap: 1rem;
  }

  .distribution-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .chart-wrapper {
    height: 250px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }
}
</style>
