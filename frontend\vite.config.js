import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:5296',
        changeOrigin: true,
      },
      '/uploads': {
        target: 'http://localhost:5296',
        changeOrigin: true,
      },
    },
  },
});
