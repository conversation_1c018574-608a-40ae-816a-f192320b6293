﻿using AutoMapper;
using Marketplace.Application.Responses;
using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.Address;

public class UpdateAddressCommandHandler : IRequestHandler<UpdateAddressCommand, AddressResponse>
{
    private readonly IAddressRepository _repository;
    private readonly IUserRepository _userRepository;
    private readonly IMapper _mapper;

    public UpdateAddressCommandHandler(IAddressRepository repository, IUserRepository userRepository, IMapper mapper)
    {
        _userRepository = userRepository;
        _repository = repository;
        _mapper = mapper;
    }

    public async Task<AddressResponse> Handle(UpdateAddressCommand command, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи ID не є Guid.Empty
        if (command.Id == Guid.Empty)
            throw new ArgumentException("Address ID cannot be empty.", nameof(command.Id));

        var item = await _repository.GetByIdAsync(command.Id, cancellationToken);
        if (item == null)
            throw new InvalidOperationException($"Address with ID {command.Id} not found.");

        var user = await _userRepository.GetByIdAsync(command.UserId.Value, cancellationToken);
        if (user == null)
            throw new InvalidOperationException($"User with ID {command.UserId} not found.");

        // Перевіряємо, чи адреса належить користувачу, якщо UserId вказано
        if (user.Role.ToString() != "Admin" && command.UserId.HasValue && item.UserId.HasValue && item.UserId.Value != command.UserId.Value)
        {
            throw new UnauthorizedAccessException($"User with ID {command.UserId} is not authorized to update this address.");
        }

        item.Update(
                region: command.Region,
                city: command.City,
                street: command.Street,
                postalCode: command.PostalCode
            );
        // Викликаємо метод Update із доменної моделі
        //item.Update(
        //    name: command.Name,
        //    slug: command.Slug != null ? new Slug(command.Slug) : null,
        //    description: command.Description,
        //    image: command.Image != null ? new Url(command.Image) : null,
        //    parentId: command.ParentId,
        //    metaTitle: command.MetaTitle,
        //    metaDescription: command.MetaDescription,
        //    metaImage: command.MetaImage != null ? new Url(command.MetaImage) : null
        //);

        await _repository.UpdateAsync(item, cancellationToken);

        // Повертаємо оновлену адресу
        return _mapper.Map<AddressResponse>(item);
    }
}

