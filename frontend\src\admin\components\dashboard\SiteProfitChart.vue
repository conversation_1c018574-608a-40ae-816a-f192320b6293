<template>
  <div class="card">
    <div class="card-header">
      <p class="card-header-title">
        <span class="icon">
          <i class="fas fa-chart-line"></i>
        </span>
        <span>Site Profit (15% Commission)</span>
      </p>
      <div class="card-header-icon">
        <div class="field">
          <div class="control">
            <div class="select is-small">
              <select v-model="selectedPeriod" @change="onPeriodChange">
                <option value="week">Last 7 days</option>
                <option value="month">Last 30 days</option>
                <option value="quarter">Last 3 months</option>
                <option value="year">Last 12 months</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="card-content">
      <div class="level">
        <div class="level-left">
          <div class="level-item">
            <div>
              <p class="heading">Total Profit ({{ selectedPeriod === 'week' ? 'Last 7 days' : selectedPeriod === 'month' ? 'Last 30 days' : selectedPeriod === 'quarter' ? 'Last 3 months' : 'Last 12 months' }})</p>
              <p class="title is-4">{{ formatCurrency(currentPeriodProfit) }}</p>
            </div>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <div>
              <p class="heading">Average Daily</p>
              <p class="title is-5">{{ formatCurrency(averageDailyProfit) }}</p>
            </div>
          </div>
        </div>
      </div>

      <div v-if="loading" class="has-text-centered py-6">
        <span class="icon is-large">
          <i class="fas fa-spinner fa-pulse fa-2x"></i>
        </span>
        <p class="mt-2">Loading profit data...</p>
      </div>
      <div v-else-if="!data || data.length === 0" class="has-text-centered py-6">
        <span class="icon is-large">
          <i class="fas fa-chart-line fa-2x"></i>
        </span>
        <p class="mt-2">No profit data available for this period</p>
      </div>
      <div v-else class="chart-container">
        <canvas ref="chartCanvas" height="300"></canvas>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import Chart from 'chart.js/auto';

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  totalProfit: {
    type: Number,
    default: 0
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['period-changed']);

// Reactive data
const chartCanvas = ref(null);
const selectedPeriod = ref('month');
const loading = ref(false);
let chartInstance = null;

// Computed properties
const averageDailyProfit = computed(() => {
  if (!props.data || props.data.length === 0) return 0;
  const totalDays = props.data.length;
  const totalValue = props.data.reduce((sum, item) => sum + (item.value || 0), 0);
  return totalValue / totalDays;
});

// Computed total profit for the current period
const currentPeriodProfit = computed(() => {
  if (!props.data || props.data.length === 0) return 0;
  return props.data.reduce((sum, item) => sum + (item.value || 0), 0);
});

// Format currency
const formatCurrency = (value) => {
  const numValue = typeof value === 'string' ? Number(value) : value;
  if (isNaN(numValue)) return 'UAH 0.00';
  
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(numValue);
};

// Handle period change
const onPeriodChange = () => {
  emit('period-changed', selectedPeriod.value);
};

// Initialize chart
const initChart = () => {
  if (!chartCanvas.value || !props.data || props.data.length === 0) return;

  // Destroy existing chart
  if (chartInstance) {
    chartInstance.destroy();
  }

  const ctx = chartCanvas.value.getContext('2d');
  
  chartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels: props.data.map(item => item.label || item.date),
      datasets: [{
        label: 'Site Profit (UAH)',
        data: props.data.map(item => item.value || 0),
        borderColor: '#3273dc',
        backgroundColor: 'rgba(50, 115, 220, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#3273dc',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 5,
        pointHoverRadius: 8
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          borderColor: '#3273dc',
          borderWidth: 1,
          callbacks: {
            label: function(context) {
              return `Profit: ${formatCurrency(context.parsed.y)}`;
            }
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            color: '#4a4a4a',
            font: {
              weight: 'bold',
              size: 12
            },
            callback: function(value) {
              return formatCurrency(value);
            }
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          }
        },
        x: {
          ticks: {
            color: '#4a4a4a',
            font: {
              weight: 'bold',
              size: 12
            }
          },
          grid: {
            display: false
          }
        }
      }
    }
  });
};

// Update chart when data changes
watch(() => props.data, () => {
  loading.value = false;
  
  // Wait for next tick to ensure DOM is updated
  setTimeout(() => {
    if (props.data && props.data.length > 0) {
      initChart();
    }
  }, 0);
}, { deep: true });

onMounted(() => {
  if (props.data && props.data.length > 0) {
    initChart();
  }
});
</script>

<style scoped>
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

.card-header-icon .field {
  margin-bottom: 0;
}

.level {
  margin-bottom: 1.5rem;
}

.heading {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  color: #7a7a7a;
  margin-bottom: 0.25rem;
}

.title {
  margin-bottom: 0;
}
</style>
