﻿using AutoMapper;
using Marketplace.Application.Commands.Auth;
using Marketplace.Application.Extensions;
using Marketplace.Application.Responses;
using Marketplace.Application.Services.Auth;
using Marketplace.Domain.Entities;
using Marketplace.Domain.Exceptions;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using Marketplace.Infrastructure.Services.Auth;
using MediatR;
using Microsoft.Extensions.Configuration;

public class RegisterCommandHandler : IRequestHandler<RegisterCommand, UserResponse>
{
    private readonly IUserRepository _userRepository;
    private readonly IMapper _mapper;
    private readonly string _baseUrl;
    private readonly IEmailService _emailService;
    private readonly IPasswordHasher _passwordHasher;
    private readonly ITokenService _tokenService;

    public RegisterCommandHandler(
        IUserRepository userRepository,
        IMapper mapper,
        IConfiguration configuration,
        IEmailService emailService,
        IPasswordHasher passwordHasher,
        ITokenService tokenService)
    {
        _userRepository = userRepository;
        _mapper = mapper;
        _baseUrl = configuration["Frontend:BaseUrl"]
            ?? throw new ArgumentNullException("Frontend:BaseUrl is not configured.");
        _emailService = emailService;
        _passwordHasher = passwordHasher;
        _tokenService = tokenService;
    }

    public async Task<UserResponse> Handle(RegisterCommand request, CancellationToken cancellationToken)
    {
        var existingUser = await _userRepository.GetByEmailAsync(request.Email, cancellationToken);
        if (existingUser != null)
            throw new DomainException("Користувач з даною електронною адресою вже існує!");

        // Hash the password
        string hashedPassword = _passwordHasher.HashPassword(request.Password);

        // Generate email confirmation token
        string emailToken = _tokenService.GenerateToken();

        // Create new user with default Buyer role
        var user = new User(
            request.Username ?? request.Email.Split('@')[0],
            new Email(request.Email),
            new Password(hashedPassword),
            Role.Buyer // Default role for new registrations
        );

        // Set email confirmation token
        user.EmailConfirmationToken = emailToken;

        await _userRepository.AddAsync(user, cancellationToken);

        try
        {
            var confirmationLink = $"{_baseUrl}/confirm-email?id={user.Id}&token={Uri.EscapeDataString(user.EmailConfirmationToken)}";
            await _emailService.SendEmailAsync(
                user.Email,
                "Підтвердження електронної пошти",
                $"Будь ласка, підтвердьте вашу пошту, перейшовши за посиланням: <a href='{confirmationLink}'>Підтвердити</a>",
                cancellationToken);
        }
        catch (Exception)
        {
            // Ігноруємо помилку, але не перериваємо процес реєстрації
            // Можна додати код для повторної спроби або запису в чергу для подальшої обробки
        }

        return _mapper.Map<UserResponse>(user);
    }


}