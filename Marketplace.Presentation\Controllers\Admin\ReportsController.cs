using MediatR;
using Microsoft.AspNetCore.Mvc;
using Marketplace.Application.Queries.Reports;
using Marketplace.Application.Services;
using Microsoft.Extensions.Logging;

namespace Marketplace.Presentation.Controllers.Admin
{
    /// <summary>
    /// Reports controller - now inherits from BaseReportController to avoid code duplication
    /// </summary>
    public class ReportsController : BaseReportController
    {
        public ReportsController(
            IMediator mediator,
            IExportService exportService,
            ILogger<ReportsController> logger)
            : base(mediator, exportService, logger)
        {
        }

        [HttpGet("financial")]
        public async Task<IActionResult> GetFinancialReport(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            // Validate parameters using base controller method
            var validation = ValidateCommonParameters(startDate, endDate);
            if (validation != null) return validation;

            var query = new GetFinancialReportQuery
            {
                StartDate = startDate ?? DateTime.Now.AddMonths(-1),
                EndDate = endDate ?? DateTime.Now
            };

            return await HandleReportRequest<GetFinancialReportQuery, object>(query, "Financial");
        }

        [HttpGet("sales")]
        public async Task<IActionResult> GetSalesReport(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string category = null,
            [FromQuery] string sortBy = "sales",
            [FromQuery] string sortOrder = "desc")
        {
            var validation = ValidateCommonParameters(startDate, endDate);
            if (validation != null) return validation;

            var query = new GetSalesReportQuery
            {
                StartDate = startDate ?? DateTime.Now.AddMonths(-1),
                EndDate = endDate ?? DateTime.Now,
                Category = category,
                SortBy = sortBy,
                SortOrder = sortOrder
            };

            return await HandleReportRequest<GetSalesReportQuery, object>(query, "Sales");
        }

        [HttpGet("products")]
        public async Task<IActionResult> GetProductsReport(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string category = null,
            [FromQuery] int limit = 100)
        {
            var validation = ValidateCommonParameters(startDate, endDate, limit);
            if (validation != null) return validation;

            var query = new GetProductsReportQuery
            {
                StartDate = startDate ?? DateTime.Now.AddMonths(-1),
                EndDate = endDate ?? DateTime.Now,
                Category = category,
                Limit = limit
            };

            return await HandleReportRequest<GetProductsReportQuery, object>(query, "Products");
        }

        [HttpGet("users")]
        public async Task<IActionResult> GetUsersReport(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string userType = null)
        {
            var validation = ValidateCommonParameters(startDate, endDate);
            if (validation != null) return validation;

            var query = new GetUsersReportQuery
            {
                StartDate = startDate ?? DateTime.Now.AddMonths(-1),
                EndDate = endDate ?? DateTime.Now,
                UserType = userType
            };

            return await HandleReportRequest<GetUsersReportQuery, object>(query, "Users");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users report");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpGet("orders")]
        public async Task<IActionResult> GetOrdersReport(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string status = null)
        {
            var validation = ValidateCommonParameters(startDate, endDate);
            if (validation != null) return validation;

            var query = new GetOrdersReportQuery
            {
                StartDate = startDate ?? DateTime.Now.AddMonths(-1),
                EndDate = endDate ?? DateTime.Now,
                Status = status
            };

            return await HandleReportRequest<GetOrdersReportQuery, object>(query, "Orders");
        }

        // Unified Export endpoints - replaces all individual export methods
        [HttpGet("{reportType}/export/{format}")]
        public async Task<IActionResult> ExportReport(
            string reportType,
            string format,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string category = null,
            [FromQuery] string status = null,
            [FromQuery] string userType = null,
            [FromQuery] string sortBy = null,
            [FromQuery] string sortOrder = "desc",
            [FromQuery] int limit = 100)
        {
            var validation = ValidateCommonParameters(startDate, endDate, limit);
            if (validation != null) return validation;

            // Create appropriate query based on report type
            object query = reportType.ToLowerInvariant() switch
            {
                "financial" => new GetFinancialReportQuery
                {
                    StartDate = startDate ?? DateTime.Now.AddMonths(-1),
                    EndDate = endDate ?? DateTime.Now
                },
                "sales" => new GetSalesReportQuery
                {
                    StartDate = startDate ?? DateTime.Now.AddMonths(-1),
                    EndDate = endDate ?? DateTime.Now,
                    Category = category,
                    SortBy = sortBy ?? "sales",
                    SortOrder = sortOrder
                },
                "products" => new GetProductsReportQuery
                {
                    StartDate = startDate ?? DateTime.Now.AddMonths(-1),
                    EndDate = endDate ?? DateTime.Now,
                    Category = category,
                    Limit = limit
                },
                "users" => new GetUsersReportQuery
                {
                    StartDate = startDate ?? DateTime.Now.AddMonths(-1),
                    EndDate = endDate ?? DateTime.Now,
                    UserType = userType
                },
                "orders" => new GetOrdersReportQuery
                {
                    StartDate = startDate ?? DateTime.Now.AddMonths(-1),
                    EndDate = endDate ?? DateTime.Now,
                    Status = status
                },
                _ => null
            };

            if (query == null)
            {
                return BadRequest(new { message = $"Unsupported report type: {reportType}" });
            }

            // Use base controller method for export
            return reportType.ToLowerInvariant() switch
            {
                "financial" => await HandleExportRequest<GetFinancialReportQuery, object>((GetFinancialReportQuery)query, format, "Financial"),
                "sales" => await HandleExportRequest<GetSalesReportQuery, object>((GetSalesReportQuery)query, format, "Sales"),
                "products" => await HandleExportRequest<GetProductsReportQuery, object>((GetProductsReportQuery)query, format, "Products"),
                "users" => await HandleExportRequest<GetUsersReportQuery, object>((GetUsersReportQuery)query, format, "Users"),
                "orders" => await HandleExportRequest<GetOrdersReportQuery, object>((GetOrdersReportQuery)query, format, "Orders"),
                _ => BadRequest(new { message = $"Unsupported report type: {reportType}" })
            };
        }


        /// <summary>
        /// Get dashboard summary with key metrics from all reports
        /// </summary>
        /// <returns>Dashboard summary</returns>
        [HttpGet("dashboard")]
        public async Task<IActionResult> GetDashboardSummary()
        {
            try
            {
                // This could be implemented as a separate query or aggregate multiple reports
                var summary = new
                {
                    timestamp = DateTime.UtcNow,
                    message = "Dashboard summary endpoint - implement based on business requirements"
                };

                return Ok(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard summary");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }
    }
}
                var reportData = await _mediator.Send(query);
                var csvData = await _exportService.ExportToCsvAsync(reportData, "sales");

                var fileName = $"sales_report_{startDate:yyyy-MM-dd}_to_{endDate:yyyy-MM-dd}.csv";
                
                return File(csvData, "text/csv", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting sales report to CSV");
                return StatusCode(500, new { message = "Export failed" });
            }
        }
    }
}
