using Microsoft.AspNetCore.Mvc;
using Marketplace.Application.Queries.Reports;
using Marketplace.Application.Services;
using Microsoft.Extensions.Logging;
using MediatR;

namespace Marketplace.Presentation.Controllers.Admin
{
    /// <summary>
    /// Reports controller - optimized with unified ReportService
    /// </summary>
    [ApiController]
    [Route("api/admin/reports")]
    public class ReportsController : BaseReportController
    {
        private readonly IReportService _reportService;

        public ReportsController(
            IReportService reportService,
            IExportService exportService,
            IMediator mediator,
            ILogger<ReportsController> logger) : base(mediator, exportService, logger)
        {
            _reportService = reportService;
        }

        [HttpGet("financial")]
        public async Task<IActionResult> GetFinancialReport(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                if (!_reportService.ValidateReportParameters(startDate, endDate, out var errorMessage))
                {
                    return BadRequest(new { message = errorMessage });
                }

                var (defaultStart, defaultEnd) = _reportService.GetDefaultDateRange();
                var query = new GetFinancialReportQuery
                {
                    StartDate = startDate ?? defaultStart,
                    EndDate = endDate ?? defaultEnd
                };

                var result = await _reportService.GetFinancialReportAsync(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating financial report");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpGet("sales")]
        public async Task<IActionResult> GetSalesReport(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string category = null,
            [FromQuery] string sortBy = "sales",
            [FromQuery] string sortOrder = "desc")
        {
            try
            {
                if (!_reportService.ValidateReportParameters(startDate, endDate, out var errorMessage))
                {
                    return BadRequest(new { message = errorMessage });
                }

                var (defaultStart, defaultEnd) = _reportService.GetDefaultDateRange();
                var query = new GetSalesReportQuery
                {
                    StartDate = startDate ?? defaultStart,
                    EndDate = endDate ?? defaultEnd,
                    SortBy = sortBy,
                    SortDirection = sortOrder
                };

                var result = await _reportService.GetSalesReportAsync(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating sales report");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpGet("products")]
        public async Task<IActionResult> GetProductsReport(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] Guid? categoryId = null,
            [FromQuery] int pageSize = 100)
        {
            try
            {
                if (!_reportService.ValidateReportParameters(startDate, endDate, out var errorMessage))
                {
                    return BadRequest(new { message = errorMessage });
                }

                var (defaultStart, defaultEnd) = _reportService.GetDefaultDateRange();
                var query = new GetProductsReportQuery
                {
                    StartDate = startDate ?? defaultStart,
                    EndDate = endDate ?? defaultEnd,
                    CategoryId = categoryId,
                    PageSize = pageSize
                };

                var result = await _reportService.GetProductsReportAsync(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting products report");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpGet("users")]
        public async Task<IActionResult> GetUsersReport(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string userRole = null)
        {
            try
            {
                if (!_reportService.ValidateReportParameters(startDate, endDate, out var errorMessage))
                {
                    return BadRequest(new { message = errorMessage });
                }

                var (defaultStart, defaultEnd) = _reportService.GetDefaultDateRange();
                var query = new GetUsersReportQuery
                {
                    StartDate = startDate ?? defaultStart,
                    EndDate = endDate ?? defaultEnd,
                    UserRole = userRole
                };

                var result = await _reportService.GetUsersReportAsync(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users report");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpGet("orders")]
        public async Task<IActionResult> GetOrdersReport(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string orderStatus = null)
        {
            try
            {
                if (!_reportService.ValidateReportParameters(startDate, endDate, out var errorMessage))
                {
                    return BadRequest(new { message = errorMessage });
                }

                var (defaultStart, defaultEnd) = _reportService.GetDefaultDateRange();
                var query = new GetOrdersReportQuery
                {
                    StartDate = startDate ?? defaultStart,
                    EndDate = endDate ?? defaultEnd,
                    OrderStatus = orderStatus
                };

                var result = await _reportService.GetOrdersReportAsync(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting orders report");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // Unified Export endpoints - replaces all individual export methods
        [HttpGet("{reportType}/export/{format}")]
        public async Task<IActionResult> ExportReport(
            string reportType,
            string format,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] Guid? categoryId = null,
            [FromQuery] string status = null,
            [FromQuery] string userType = null,
            [FromQuery] string sortBy = null,
            [FromQuery] string sortOrder = "desc",
            [FromQuery] int limit = 100)
        {
            try
            {
                if (!_reportService.ValidateReportParameters(startDate, endDate, out var errorMessage))
                {
                    return BadRequest(new { message = errorMessage });
                }

                var normalizedReportType = _reportService.NormalizeReportType(reportType);
                var (defaultStart, defaultEnd) = _reportService.GetDefaultDateRange();

            // Create appropriate query based on report type
            object query = reportType.ToLowerInvariant() switch
            {
                "financial" => new GetFinancialReportQuery
                {
                    StartDate = startDate ?? DateTime.Now.AddMonths(-1),
                    EndDate = endDate ?? DateTime.Now
                },
                "sales" => new GetSalesReportQuery
                {
                    StartDate = startDate ?? DateTime.Now.AddMonths(-1),
                    EndDate = endDate ?? DateTime.Now,
                    SortBy = sortBy ?? "sales",
                    SortDirection = sortOrder
                },
                "products" => new GetProductsReportQuery
                {
                    StartDate = startDate ?? DateTime.Now.AddMonths(-1),
                    EndDate = endDate ?? DateTime.Now,
                    CategoryId = categoryId,
                    PageSize = limit
                },
                "users" => new GetUsersReportQuery
                {
                    StartDate = startDate ?? DateTime.Now.AddMonths(-1),
                    EndDate = endDate ?? DateTime.Now,
                    UserRole = userType
                },
                "orders" => new GetOrdersReportQuery
                {
                    StartDate = startDate ?? DateTime.Now.AddMonths(-1),
                    EndDate = endDate ?? DateTime.Now,
                    OrderStatus = status
                },
                _ => null
            };

            if (query == null)
            {
                return BadRequest(new { message = $"Unsupported report type: {reportType}" });
            }

            // Use base controller method for export
            return reportType.ToLowerInvariant() switch
            {
                "financial" => await HandleExportRequest<GetFinancialReportQuery, ReportResult>((GetFinancialReportQuery)query, format, "Financial"),
                "sales" => await HandleExportRequest<GetSalesReportQuery, ReportResult>((GetSalesReportQuery)query, format, "Sales"),
                "products" => await HandleExportRequest<GetProductsReportQuery, ReportResult>((GetProductsReportQuery)query, format, "Products"),
                "users" => await HandleExportRequest<GetUsersReportQuery, ReportResult>((GetUsersReportQuery)query, format, "Users"),
                "orders" => await HandleExportRequest<GetOrdersReportQuery, ReportResult>((GetOrdersReportQuery)query, format, "Orders"),
                _ => BadRequest(new { message = $"Unsupported report type: {reportType}" })
            };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting {ReportType} report", reportType);
                return StatusCode(500, new { message = "Export failed" });
            }
        }


        /// <summary>
        /// Get dashboard summary with key metrics from all reports
        /// </summary>
        /// <returns>Dashboard summary</returns>
        [HttpGet("dashboard")]
        public async Task<IActionResult> GetDashboardSummary()
        {
            try
            {
                // This could be implemented as a separate query or aggregate multiple reports
                var summary = new
                {
                    timestamp = DateTime.UtcNow,
                    message = "Dashboard summary endpoint - implement based on business requirements"
                };

                return Ok(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard summary");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }
    }
}
