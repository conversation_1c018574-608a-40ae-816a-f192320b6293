<template>
  <div class="card">
    <div class="card-header">
      <p class="card-header-title requests-title">Pending Seller Requests</p>
      <div class="card-header-icon">
        <router-link to="/admin/seller-requests" class="button is-small is-primary">
          <span>View All</span>
          <span class="icon is-small">
            <i class="fas fa-arrow-right"></i>
          </span>
        </router-link>
      </div>
    </div>
    <div class="card-content">
      <div v-if="loading" class="has-text-centered py-4">
        <span class="icon is-large">
          <i class="fas fa-spinner fa-pulse fa-2x"></i>
        </span>
        <p class="mt-2">Loading requests...</p>
      </div>
      <div v-else-if="!requests || requests.length === 0" class="has-text-centered py-4">
        <span class="icon is-large">
          <i class="fas fa-store fa-2x"></i>
        </span>
        <p class="mt-2">No pending seller requests</p>
      </div>
      <div v-else>
        <div class="seller-request" v-for="request in requests" :key="request.id">
          <div class="seller-request-header">
            <div class="seller-info">
              <h4 class="seller-name">{{ request.userName }}</h4>
              <p class="store-name">{{ request.companyName }}</p>
            </div>
            <div class="request-date">
              {{ formatDate(request.createdAt) }}
            </div>
          </div>
          <div class="seller-request-actions">
            <router-link :to="`/admin/seller-requests/${request.id}`" class="button is-small">
              <span class="icon is-small">
                <i class="fas fa-eye"></i>
              </span>
              <span>View</span>
            </router-link>
            <button
              class="button is-small is-success"
              @click="approveRequest(request.id)">
              <span class="icon is-small">
                <i class="fas fa-check"></i>
              </span>
              <span>Approve</span>
            </button>
            <button
              class="button is-small is-danger"
              @click="rejectRequest(request.id)">
              <span class="icon is-small">
                <i class="fas fa-times"></i>
              </span>
              <span>Reject</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  requests: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['approve', 'reject']);

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';

  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.round(diffMs / 1000);
  const diffMin = Math.round(diffSec / 60);
  const diffHour = Math.round(diffMin / 60);
  const diffDay = Math.round(diffHour / 24);

  // If less than a minute ago
  if (diffSec < 60) {
    return 'Just now';
  }

  // If less than an hour ago
  if (diffMin < 60) {
    return `${diffMin} minute${diffMin !== 1 ? 's' : ''} ago`;
  }

  // If less than a day ago
  if (diffHour < 24) {
    return `${diffHour} hour${diffHour !== 1 ? 's' : ''} ago`;
  }

  // If less than a week ago
  if (diffDay < 7) {
    return `${diffDay} day${diffDay !== 1 ? 's' : ''} ago`;
  }

  // Otherwise, return formatted date
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

// Approve request
const approveRequest = (id) => {
  emit('approve', id);
};

// Reject request
const rejectRequest = (id) => {
  emit('reject', id);
};
</script>

<style scoped>
.card {
  height: 100%;
  transition: box-shadow 0.3s;
}

.card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-header-title {
  font-weight: 600;
}

.card-header-icon {
  padding: 0.75rem;
}

.seller-request {
  padding: 1rem;
  border-bottom: 1px solid #f1f1f1;
}

.seller-request:last-child {
  border-bottom: none;
}

.seller-request-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.seller-name {
  font-size: 1rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: #ffffff;
}

.store-name {
  font-size: 0.9rem;
  color: #e0e0e0;
  font-weight: 500;
}

.request-date {
  font-size: 0.85rem;
  color: #e0e0e0;
  font-weight: 500;
}

.seller-request-actions {
  display: flex;
  gap: 0.5rem;
}

.button.is-primary {
  background-color: transparent;
  border-color: #ff7700;
  color: #ff7700;
}

.button.is-primary:hover {
  background-color: #ff7700;
  color: white;
}

.py-4 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

@media screen and (max-width: 768px) {
  .seller-request-actions {
    flex-wrap: wrap;
  }

  .seller-request-actions .button {
    flex: 1;
    min-width: 80px;
  }
}
</style>
