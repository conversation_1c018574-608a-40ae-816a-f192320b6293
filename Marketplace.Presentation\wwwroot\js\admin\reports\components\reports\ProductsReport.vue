<template>
  <div class="products-report">
    <!-- Header -->
    <div class="report-header">
      <div class="header-content">
        <h1 class="report-title">
          <i class="fas fa-box"></i>
          Products Report
        </h1>
        <p class="report-description">
          Comprehensive analysis of product performance, inventory, and sales metrics
        </p>
      </div>
      
      <!-- Export Actions -->
      <div class="header-actions">
        <ExportButtons 
          :report-type="'products'"
          :filters="filters"
          :disabled="isLoading"
        />
      </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
      <ReportFilters 
        :show-report-type="false"
        :report-type="'products'"
        @filters-changed="handleFiltersChanged"
      />
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading products data...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <div class="error-content">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Failed to Load Products Data</h3>
        <p>{{ error }}</p>
        <button @click="refreshData" class="retry-btn">
          <i class="fas fa-redo"></i>
          Try Again
        </button>
      </div>
    </div>

    <!-- Report Content -->
    <div v-else-if="hasData" class="report-content">
      <!-- Key Metrics -->
      <div class="metrics-section">
        <h3 class="section-title">
          <i class="fas fa-chart-line"></i>
          Product Performance Metrics
        </h3>
        
        <div class="metrics-grid">
          <MetricCard
            v-for="metric in productMetrics"
            :key="metric.key"
            :metric="metric"
            :loading="isLoading"
          />
        </div>
      </div>

      <!-- Top Products Chart -->
      <div class="chart-section">
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">Top Selling Products</h3>
            <div class="chart-controls">
              <select v-model="chartPeriod" @change="updateChart" class="chart-select">
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
              <select v-model="chartMetric" @change="updateChart" class="chart-select">
                <option value="sales">By Sales</option>
                <option value="revenue">By Revenue</option>
                <option value="views">By Views</option>
              </select>
            </div>
          </div>
          
          <div class="chart-content">
            <canvas ref="topProductsChartCanvas" id="top-products-chart"></canvas>
          </div>
        </div>
      </div>

      <!-- Category Distribution -->
      <div class="distribution-section">
        <div class="distribution-container">
          <div class="distribution-header">
            <h3 class="distribution-title">Sales by Category</h3>
          </div>
          
          <div class="distribution-content">
            <div class="chart-wrapper">
              <canvas ref="categoryChartCanvas" id="category-chart"></canvas>
            </div>
            
            <div class="distribution-legend">
              <div
                v-for="(item, index) in categoryData"
                :key="item.category"
                class="legend-item"
              >
                <div
                  class="legend-color"
                  :style="{ backgroundColor: getCategoryColor(index) }"
                ></div>
                <div class="legend-info">
                  <div class="legend-label">{{ item.category }}</div>
                  <div class="legend-value">
                    {{ formatNumber(item.sales) }} sales ({{ item.percentage.toFixed(1) }}%)
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Product Insights -->
      <div class="insights-section">
        <h3 class="section-title">
          <i class="fas fa-lightbulb"></i>
          Product Insights
        </h3>
        
        <div class="insights-grid">
          <div
            v-for="insight in productInsights"
            :key="insight.id"
            class="insight-card"
            :class="insight.type"
          >
            <div class="insight-icon">
              <i :class="insight.icon"></i>
            </div>
            <div class="insight-content">
              <h4 class="insight-title">{{ insight.title }}</h4>
              <p class="insight-description">{{ insight.description }}</p>
              <div v-if="insight.action" class="insight-action">
                <button @click="handleInsightAction(insight)" class="action-btn">
                  {{ insight.action.label }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Top Products Table -->
      <div class="table-section">
        <ReportTable
          :data="tableData"
          :columns="tableColumns"
          :loading="isLoading"
          title="Top Products Details"
          :exportable="true"
          @row-click="viewProductDetails"
        />
      </div>

      <!-- Low Stock Alert -->
      <div v-if="lowStockProducts.length" class="alert-section">
        <div class="alert-container warning">
          <div class="alert-header">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>Low Stock Alert</h3>
          </div>
          <div class="alert-content">
            <p>{{ lowStockProducts.length }} products are running low on stock:</p>
            <div class="low-stock-list">
              <div
                v-for="product in lowStockProducts.slice(0, 5)"
                :key="product.id"
                class="low-stock-item"
              >
                <span class="product-name">{{ product.name }}</span>
                <span class="stock-level">{{ product.stock }} left</span>
              </div>
              <div v-if="lowStockProducts.length > 5" class="more-items">
                +{{ lowStockProducts.length - 5 }} more products
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="empty-state">
      <div class="empty-content">
        <i class="fas fa-box-open"></i>
        <h3>No Products Data Available</h3>
        <p>Try adjusting your filters or date range to see products data.</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useReportsStore } from '../../stores/reportsStore.js'
import { Chart } from 'chart.js'
import ReportFilters from '../shared/ReportFilters.vue'
import MetricCard from '../shared/MetricCard.vue'
import ExportButtons from '../shared/ExportButtons.vue'
import ReportTable from '../shared/ReportTable.vue'

export default {
  name: 'ProductsReport',
  components: {
    ReportFilters,
    MetricCard,
    ExportButtons,
    ReportTable
  },
  setup() {
    const reportsStore = useReportsStore()
    
    // Reactive references
    const topProductsChartCanvas = ref(null)
    const categoryChartCanvas = ref(null)
    const chartPeriod = ref('monthly')
    const chartMetric = ref('sales')
    const topProductsChart = ref(null)
    const categoryChart = ref(null)
    
    // Computed properties
    const isLoading = computed(() => reportsStore.isLoading)
    const error = computed(() => reportsStore.error)
    const hasData = computed(() => reportsStore.hasData && reportsStore.currentReport?.type === 'products')
    const filters = computed(() => reportsStore.filters)
    
    const productMetrics = computed(() => {
      const data = reportsStore.currentReport
      if (!data?.metrics?.items) return []
      
      return data.metrics.items.map(item => ({
        key: item.key,
        label: item.label,
        value: item.value,
        type: item.type || 'number',
        icon: item.icon || 'fas fa-box',
        trend: item.trend || 'neutral',
        changePercentage: item.changePercentage || 0,
        previousValue: item.previousValue
      }))
    })
    
    const categoryData = computed(() => {
      const data = reportsStore.currentReport
      if (!data?.charts?.categories?.data) return []
      
      return data.charts.categories.data.map(item => ({
        category: item.label,
        sales: item.value,
        percentage: item.percentage || 0
      }))
    })
    
    const productInsights = computed(() => {
      const data = reportsStore.currentReport
      return data?.insights || []
    })
    
    const tableData = computed(() => {
      const data = reportsStore.currentReport
      return data?.table?.data || []
    })
    
    const tableColumns = computed(() => {
      const data = reportsStore.currentReport
      return data?.table?.columns || [
        { key: 'name', label: 'Product Name', sortable: true },
        { key: 'category', label: 'Category', sortable: true },
        { key: 'sales', label: 'Sales', type: 'number', sortable: true },
        { key: 'revenue', label: 'Revenue', type: 'currency', sortable: true },
        { key: 'stock', label: 'Stock', type: 'number', sortable: true },
        { key: 'status', label: 'Status', type: 'status', sortable: true }
      ]
    })
    
    const lowStockProducts = computed(() => {
      const data = reportsStore.currentReport
      return data?.lowStock || []
    })
    
    // Methods
    const handleFiltersChanged = async (newFilters) => {
      await reportsStore.fetchReport('products', newFilters)
      await nextTick()
      updateCharts()
    }
    
    const refreshData = async () => {
      await reportsStore.fetchReport('products')
      await nextTick()
      updateCharts()
    }
    
    const updateChart = async () => {
      await nextTick()
      updateCharts()
    }
    
    const updateCharts = () => {
      updateTopProductsChart()
      updateCategoryChart()
    }

    const updateTopProductsChart = () => {
      if (!topProductsChartCanvas.value) return

      const data = reportsStore.currentReport
      if (!data?.charts?.topProducts) return

      const ctx = topProductsChartCanvas.value.getContext('2d')

      if (topProductsChart.value) {
        topProductsChart.value.destroy()
      }

      topProductsChart.value = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: data.charts.topProducts.labels || [],
          datasets: [{
            label: chartMetric.value === 'sales' ? 'Sales' : chartMetric.value === 'revenue' ? 'Revenue' : 'Views',
            data: data.charts.topProducts.data || [],
            backgroundColor: 'rgba(59, 130, 246, 0.8)',
            borderColor: '#3b82f6',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return chartMetric.value === 'revenue' ? formatCurrency(value) : formatNumber(value)
                }
              }
            },
            x: {
              ticks: {
                maxRotation: 45
              }
            }
          }
        }
      })
    }

    const updateCategoryChart = () => {
      if (!categoryChartCanvas.value || !categoryData.value.length) return

      const ctx = categoryChartCanvas.value.getContext('2d')

      if (categoryChart.value) {
        categoryChart.value.destroy()
      }

      categoryChart.value = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: categoryData.value.map(item => item.category),
          datasets: [{
            data: categoryData.value.map(item => item.sales),
            backgroundColor: categoryData.value.map((_, index) => getCategoryColor(index))
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          }
        }
      })
    }

    const getCategoryColor = (index) => {
      const colors = [
        '#3b82f6', '#ef4444', '#10b981', '#f59e0b',
        '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
      ]
      return colors[index % colors.length]
    }

    const formatCurrency = (value) => {
      if (typeof value !== 'number') return value
      return new Intl.NumberFormat('uk-UA', {
        style: 'currency',
        currency: 'UAH'
      }).format(value)
    }

    const formatNumber = (value) => {
      if (typeof value !== 'number') return value
      return new Intl.NumberFormat('uk-UA').format(value)
    }

    const handleInsightAction = (insight) => {
      if (insight.action?.callback) {
        insight.action.callback()
      }
    }

    const viewProductDetails = (product) => {
      // Navigate to product details or show modal
      console.log('View product details:', product)
    }

    // Lifecycle
    onMounted(async () => {
      await reportsStore.fetchReport('products')
      await nextTick()
      updateCharts()
    })

    // Watch for data changes
    watch(() => reportsStore.currentReport, async () => {
      await nextTick()
      updateCharts()
    }, { deep: true })

    return {
      topProductsChartCanvas,
      categoryChartCanvas,
      chartPeriod,
      chartMetric,
      isLoading,
      error,
      hasData,
      filters,
      productMetrics,
      categoryData,
      productInsights,
      tableData,
      tableColumns,
      lowStockProducts,
      handleFiltersChanged,
      refreshData,
      updateChart,
      getCategoryColor,
      formatCurrency,
      formatNumber,
      handleInsightAction,
      viewProductDetails
    }
  }
}
</script>

<style scoped>
.products-report {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 0;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.report-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.report-title i {
  color: #3b82f6;
}

.report-description {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  flex-shrink: 0;
}

.filters-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-container,
.error-container,
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-spinner,
.error-content,
.empty-content {
  text-align: center;
  color: #6b7280;
}

.loading-spinner i {
  font-size: 2rem;
  color: #3b82f6;
  margin-bottom: 1rem;
}

.error-content i,
.empty-content i {
  font-size: 3rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.empty-content i {
  color: #9ca3af;
}

.retry-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 1rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #2563eb;
}

.report-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.metrics-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.chart-section,
.distribution-section,
.insights-section,
.table-section,
.alert-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-container,
.distribution-container {
  width: 100%;
}

.chart-header,
.distribution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.chart-title,
.distribution-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 1rem;
}

.chart-select {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 0.875rem;
  cursor: pointer;
}

.chart-content {
  position: relative;
  height: 400px;
}

.distribution-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  align-items: center;
}

.chart-wrapper {
  position: relative;
  height: 300px;
}

.distribution-legend {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.legend-info {
  flex: 1;
}

.legend-label {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.legend-value {
  font-size: 0.875rem;
  color: #6b7280;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.insight-card {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  background: #f8fafc;
}

.insight-card.warning {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.insight-card.success {
  border-left-color: #10b981;
  background: #f0fdf4;
}

.insight-card.danger {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.insight-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.insight-card.warning .insight-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.insight-card.success .insight-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.insight-card.danger .insight-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.insight-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.insight-action {
  margin-top: 1rem;
}

.action-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background: #2563eb;
}

.alert-container {
  border-radius: 8px;
  padding: 1.5rem;
}

.alert-container.warning {
  background: #fffbeb;
  border: 1px solid #fed7aa;
}

.alert-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.alert-header i {
  color: #f59e0b;
  font-size: 1.25rem;
}

.alert-header h3 {
  color: #92400e;
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.alert-content {
  color: #92400e;
}

.alert-content p {
  margin: 0 0 1rem 0;
}

.low-stock-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.low-stock-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 4px;
}

.product-name {
  font-weight: 500;
}

.stock-level {
  font-size: 0.875rem;
  color: #d97706;
  font-weight: 600;
}

.more-items {
  font-size: 0.875rem;
  color: #92400e;
  font-style: italic;
  text-align: center;
  padding: 0.5rem;
}

@media (max-width: 768px) {
  .report-header {
    flex-direction: column;
    gap: 1rem;
  }

  .distribution-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .chart-wrapper {
    height: 250px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }

  .chart-controls {
    flex-direction: column;
    gap: 0.5rem;
  }

  .low-stock-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}
</style>
