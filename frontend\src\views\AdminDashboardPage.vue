<template>
  <div class="admin-dashboard">
    <h1 class="mb-4">Admin Dashboard</h1>
    
    <div class="row">
      <div class="col-md-3">
        <div class="dashboard-card">
          <h2 class="dashboard-card-title">Users</h2>
          <div class="d-flex align-items-center">
            <div class="display-4 me-3">{{ stats.users }}</div>
            <div>Total Users</div>
          </div>
          <a href="#" class="btn btn-primary mt-3">Manage Users</a>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="dashboard-card">
          <h2 class="dashboard-card-title">Products</h2>
          <div class="d-flex align-items-center">
            <div class="display-4 me-3">{{ stats.products }}</div>
            <div>Total Products</div>
          </div>
          <a href="#" class="btn btn-primary mt-3">Manage Products</a>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="dashboard-card">
          <h2 class="dashboard-card-title">Orders</h2>
          <div class="d-flex align-items-center">
            <div class="display-4 me-3">{{ stats.orders }}</div>
            <div>Total Orders</div>
          </div>
          <a href="#" class="btn btn-primary mt-3">Manage Orders</a>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="dashboard-card">
          <h2 class="dashboard-card-title">Categories</h2>
          <div class="d-flex align-items-center">
            <div class="display-4 me-3">{{ stats.categories }}</div>
            <div>Total Categories</div>
          </div>
          <a href="#" class="btn btn-primary mt-3">Manage Categories</a>
        </div>
      </div>
    </div>
    
    <div class="row mt-4">
      <div class="col-md-6">
        <div class="dashboard-card">
          <h2 class="dashboard-card-title">Recent Users</h2>
          <table class="table">
            <thead>
              <tr>
                <th>Username</th>
                <th>Email</th>
                <th>Role</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(user, index) in recentUsers" :key="index">
                <td>{{ user.username }}</td>
                <td>{{ user.email }}</td>
                <td>{{ user.role }}</td>
                <td>
                  <button class="btn btn-sm btn-outline-primary me-1">Edit</button>
                  <button class="btn btn-sm btn-outline-danger">Delete</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="dashboard-card">
          <h2 class="dashboard-card-title">Recent Orders</h2>
          <table class="table">
            <thead>
              <tr>
                <th>Order ID</th>
                <th>Customer</th>
                <th>Amount</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(order, index) in recentOrders" :key="index">
                <td>{{ order.id }}</td>
                <td>{{ order.customer }}</td>
                <td>${{ order.amount.toFixed(2) }}</td>
                <td>
                  <span class="badge" :class="getStatusClass(order.status)">
                    {{ order.status }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdminDashboard',
  data() {
    return {
      stats: {
        users: 125,
        products: 432,
        orders: 89,
        categories: 15
      },
      recentUsers: [
        { username: 'john_doe', email: '<EMAIL>', role: 'User' },
        { username: 'jane_smith', email: '<EMAIL>', role: 'Seller' },
        { username: 'robert_johnson', email: '<EMAIL>', role: 'User' },
        { username: 'sarah_williams', email: '<EMAIL>', role: 'Seller' }
      ],
      recentOrders: [
        { id: 'ORD-1234', customer: 'John Doe', amount: 125.99, status: 'Completed' },
        { id: 'ORD-1235', customer: 'Jane Smith', amount: 89.50, status: 'Processing' },
        { id: 'ORD-1236', customer: 'Robert Johnson', amount: 245.75, status: 'Pending' },
        { id: 'ORD-1237', customer: 'Sarah Williams', amount: 55.25, status: 'Cancelled' }
      ]
    };
  },
  methods: {
    getStatusClass(status) {
      switch (status) {
        case 'Completed':
          return 'bg-success';
        case 'Processing':
          return 'bg-primary';
        case 'Pending':
          return 'bg-warning';
        case 'Cancelled':
          return 'bg-danger';
        default:
          return 'bg-secondary';
      }
    }
  }
};
</script>

<style scoped>
.badge {
  padding: 0.5em 0.75em;
}
</style>
