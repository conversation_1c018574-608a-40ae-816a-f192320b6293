import axios from 'axios';

// Create axios instance with base URL
const apiClient = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json'
  },
  // Add a timeout to prevent infinite waiting
  timeout: 30000 // 30 seconds
});

// Add a request interceptor to add the auth token to requests
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle token expiration and log errors
apiClient.interceptors.response.use(
  (response) => {
    // Log successful responses for debugging
    console.log(`API Response [${response.config.method.toUpperCase()}] ${response.config.url}:`, response.status);
    return response;
  },
  async (error) => {
    // Log detailed error information
    console.error('API Error:', error);

    if (error.response) {
      console.error(`API Error Response [${error.config.method.toUpperCase()}] ${error.config.url}:`, {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });

      const originalRequest = error.config;

      // If the error is 401 and not already retrying
      if (error.response.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        // Here you could implement token refresh logic if needed

        // For now, just redirect to login if token is invalid
        localStorage.removeItem('user');
        localStorage.removeItem('token');
        window.location.href = '/login';
      }
    } else if (error.request) {
      // The request was made but no response was received
      console.error('API Error: No response received', error.request);
    } else {
      // Something happened in setting up the request
      console.error('API Error: Request setup error', error.message);
    }

    return Promise.reject(error);
  }
);

export default apiClient;

