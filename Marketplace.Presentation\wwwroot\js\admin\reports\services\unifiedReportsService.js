// Unified Reports Service for Admin Panel
// Combines functionality from all existing reports services
class UnifiedReportsService {
  constructor() {
    this.baseURL = '/api/admin/reports'
    this.cache = new Map()
    this.cacheTimeout = 5 * 60 * 1000 // 5 minutes
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  }

  // Generic method to make API requests with error handling and caching
  async makeRequest(url, options = {}) {
    try {
      // Check cache first for GET requests
      if (!options.method || options.method === 'GET') {
        const cached = this.getFromCache(url)
        if (cached) {
          return cached
        }
      }

      const config = {
        method: 'GET',
        headers: {
          ...this.defaultHeaders,
          'Authorization': `Bearer ${this.getAuthToken()}`,
          ...options.headers
        },
        ...options
      }

      const response = await fetch(url, config)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
      }

      // Handle different response types
      const contentType = response.headers.get('content-type')
      let data
      if (contentType && contentType.includes('application/json')) {
        data = await response.json()
      } else {
        data = await response.blob()
      }

      // Cache successful GET responses
      if (!options.method || options.method === 'GET') {
        this.setCache(url, data)
      }

      return data
    } catch (error) {
      console.error('API request failed:', error)
      throw new Error(error.message || 'Failed to fetch data')
    }
  }

  // Cache management
  getFromCache(key) {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data
    }
    this.cache.delete(key)
    return null
  }

  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  clearCache() {
    this.cache.clear()
  }

  // Get authentication token
  getAuthToken() {
    return localStorage.getItem('auth_token') || 
           sessionStorage.getItem('auth_token') || 
           document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
           ''
  }

  // Build query string from filters object with array support
  buildQueryString(filters) {
    const params = new URLSearchParams()
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(item => params.append(key, item))
        } else {
          params.append(key, value)
        }
      }
    })
    
    return params.toString()
  }

  // Get report data with enhanced error handling
  async getReport(reportType, filters = {}) {
    try {
      const queryString = this.buildQueryString(filters)
      const url = `${this.baseURL}/${reportType}?${queryString}`
      
      const data = await this.makeRequest(url)
      
      // Validate response structure
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid response format')
      }

      return data
    } catch (error) {
      console.error(`Error fetching ${reportType} report:`, error)
      
      // Return mock data for development/fallback
      if (process.env.NODE_ENV === 'development') {
        return this.getMockData(reportType)
      }
      
      throw error
    }
  }

  // Export report with progress tracking
  async exportReport(reportType, format, filters = {}) {
    const queryString = this.buildQueryString(filters)
    const url = `${this.baseURL}/${reportType}/export/${format}?${queryString}`
    
    const headers = {
      'Accept': this.getAcceptHeader(format)
    }
    
    return await this.makeRequest(url, { headers })
  }

  // Get appropriate Accept header for export format
  getAcceptHeader(format) {
    switch (format.toLowerCase()) {
      case 'excel':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      case 'pdf':
        return 'application/pdf'
      case 'csv':
        return 'text/csv'
      default:
        return 'application/octet-stream'
    }
  }

  // Specific report methods
  async getFinancialReport(filters = {}) {
    return await this.getReport('financial', filters)
  }

  async getSalesReport(filters = {}) {
    return await this.getReport('sales', filters)
  }

  async getProductsReport(filters = {}) {
    return await this.getReport('products', filters)
  }

  async getUsersReport(filters = {}) {
    return await this.getReport('users', filters)
  }

  async getOrdersReport(filters = {}) {
    return await this.getReport('orders', filters)
  }

  // Export methods for each report type
  async exportFinancialReport(format, filters = {}) {
    return await this.exportReport('financial', format, filters)
  }

  async exportSalesReport(format, filters = {}) {
    return await this.exportReport('sales', format, filters)
  }

  async exportProductsReport(format, filters = {}) {
    return await this.exportReport('products', format, filters)
  }

  async exportUsersReport(format, filters = {}) {
    return await this.exportReport('users', format, filters)
  }

  async exportOrdersReport(format, filters = {}) {
    return await this.exportReport('orders', format, filters)
  }

  // Get dashboard summary data
  async getDashboardSummary(filters = {}) {
    try {
      const promises = [
        this.getFinancialReport(filters),
        this.getSalesReport(filters),
        this.getProductsReport(filters),
        this.getUsersReport(filters),
        this.getOrdersReport(filters)
      ]

      const [financial, sales, products, users, orders] = await Promise.allSettled(promises)

      return {
        financial: financial.status === 'fulfilled' ? financial.value : null,
        sales: sales.status === 'fulfilled' ? sales.value : null,
        products: products.status === 'fulfilled' ? products.value : null,
        users: users.status === 'fulfilled' ? users.value : null,
        orders: orders.status === 'fulfilled' ? orders.value : null
      }
    } catch (error) {
      console.error('Error fetching dashboard summary:', error)
      throw error
    }
  }

  // Email report functionality
  async emailReport(reportType, format, email, filters = {}) {
    const url = `${this.baseURL}/${reportType}/email`
    const data = {
      format,
      email,
      filters
    }

    return await this.makeRequest(url, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  // Get mock data for development
  getMockData(reportType) {
    const mockData = {
      financial: {
        type: 'financial',
        metrics: {
          items: [
            { key: 'revenue', label: 'Total Revenue', value: 125000, type: 'currency', icon: 'fas fa-dollar-sign', trend: 'up', changePercentage: 12.5 },
            { key: 'expenses', label: 'Total Expenses', value: 85000, type: 'currency', icon: 'fas fa-credit-card', trend: 'down', changePercentage: -5.2 },
            { key: 'profit', label: 'Net Profit', value: 40000, type: 'currency', icon: 'fas fa-chart-line', trend: 'up', changePercentage: 18.7 },
            { key: 'margin', label: 'Profit Margin', value: 32, type: 'percentage', icon: 'fas fa-percentage', trend: 'up', changePercentage: 3.2 }
          ]
        },
        charts: {
          revenue: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            data: [10000, 15000, 20000, 18000, 22000, 25000]
          },
          expenses: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            data: [8000, 12000, 15000, 14000, 16000, 18000]
          },
          paymentMethods: {
            data: [
              { label: 'Credit Card', value: 45000, percentage: 45 },
              { label: 'PayPal', value: 30000, percentage: 30 },
              { label: 'Bank Transfer', value: 25000, percentage: 25 }
            ]
          }
        },
        insights: [
          { id: 1, type: 'success', icon: 'fas fa-arrow-up', title: 'Revenue Growth', description: 'Revenue increased by 12.5% compared to last period' },
          { id: 2, type: 'warning', icon: 'fas fa-exclamation-triangle', title: 'High Expenses', description: 'Expenses are higher than expected this month' }
        ],
        table: {
          data: [
            { category: 'Sales', revenue: 75000, expenses: 45000, profit: 30000, margin: 40 },
            { category: 'Marketing', revenue: 25000, expenses: 20000, profit: 5000, margin: 20 },
            { category: 'Operations', revenue: 25000, expenses: 20000, profit: 5000, margin: 20 }
          ],
          columns: [
            { key: 'category', label: 'Category', sortable: true },
            { key: 'revenue', label: 'Revenue', type: 'currency', sortable: true },
            { key: 'expenses', label: 'Expenses', type: 'currency', sortable: true },
            { key: 'profit', label: 'Profit', type: 'currency', sortable: true },
            { key: 'margin', label: 'Margin', type: 'percentage', sortable: true }
          ]
        }
      },
      sales: {
        type: 'sales',
        metrics: {
          items: [
            { key: 'totalSales', label: 'Total Sales', value: 28456.78, type: 'currency', icon: 'fas fa-chart-line', trend: 'up', changePercentage: 15.3 },
            { key: 'totalOrders', label: 'Total Orders', value: 356, type: 'number', icon: 'fas fa-shopping-cart', trend: 'up', changePercentage: 8.7 },
            { key: 'averageOrder', label: 'Average Order Value', value: 79.93, type: 'currency', icon: 'fas fa-receipt', trend: 'up', changePercentage: 6.2 },
            { key: 'conversionRate', label: 'Conversion Rate', value: 3.2, type: 'percentage', icon: 'fas fa-percentage', trend: 'neutral', changePercentage: 0.1 }
          ]
        },
        charts: {
          sales: {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            data: [8000, 12000, 8456.78, 10000]
          },
          topProducts: {
            data: [
              { label: 'Product A', value: 5000, percentage: 25 },
              { label: 'Product B', value: 4000, percentage: 20 },
              { label: 'Product C', value: 3000, percentage: 15 },
              { label: 'Product D', value: 2500, percentage: 12.5 },
              { label: 'Others', value: 5500, percentage: 27.5 }
            ]
          },
          categories: {
            data: [
              { label: 'Electronics', value: 12000, orders: 120, growth: 15.5 },
              { label: 'Clothing', value: 8000, orders: 200, growth: -2.3 },
              { label: 'Books', value: 5000, orders: 150, growth: 8.7 },
              { label: 'Home & Garden', value: 3456.78, orders: 86, growth: 22.1 }
            ]
          }
        },
        insights: [
          { id: 1, type: 'success', icon: 'fas fa-arrow-up', title: 'Sales Growth', description: 'Sales increased by 15.3% this period', action: { label: 'View Details', callback: () => {} } },
          { id: 2, type: 'info', icon: 'fas fa-info-circle', title: 'Top Category', description: 'Electronics category is performing best with 42% of total sales' }
        ],
        table: {
          data: [
            { product: 'Product A', category: 'Electronics', sales: 5000, quantity: 50, revenue: 5000, profit: 1500, margin: 30 },
            { product: 'Product B', category: 'Clothing', sales: 4000, quantity: 80, revenue: 4000, profit: 800, margin: 20 },
            { product: 'Product C', category: 'Books', sales: 3000, quantity: 100, revenue: 3000, profit: 900, margin: 30 }
          ],
          columns: [
            { key: 'product', label: 'Product', sortable: true },
            { key: 'category', label: 'Category', sortable: true },
            { key: 'sales', label: 'Sales', type: 'currency', sortable: true },
            { key: 'quantity', label: 'Quantity', type: 'number', sortable: true },
            { key: 'revenue', label: 'Revenue', type: 'currency', sortable: true },
            { key: 'profit', label: 'Profit', type: 'currency', sortable: true },
            { key: 'margin', label: 'Margin', type: 'percentage', sortable: true }
          ]
        },
        alerts: [
          { id: 1, type: 'Low Stock', message: 'Product A is running low on inventory' },
          { id: 2, type: 'Price Alert', message: 'Product B price needs review due to low margin' }
        ]
      },
      products: {
        type: 'products',
        metrics: {
          items: [
            { key: 'totalProducts', label: 'Total Products', value: 1250, type: 'number', icon: 'fas fa-box', trend: 'up', changePercentage: 5.2 },
            { key: 'activeProducts', label: 'Active Products', value: 1180, type: 'number', icon: 'fas fa-check-circle', trend: 'up', changePercentage: 3.1 },
            { key: 'outOfStock', label: 'Out of Stock', value: 70, type: 'number', icon: 'fas fa-exclamation-triangle', trend: 'down', changePercentage: -12.5 },
            { key: 'lowStock', label: 'Low Stock', value: 45, type: 'number', icon: 'fas fa-exclamation-circle', trend: 'neutral', changePercentage: 2.3 }
          ]
        },
        charts: {
          performance: {
            labels: ['Electronics', 'Clothing', 'Books', 'Home & Garden', 'Sports'],
            data: [350, 280, 220, 180, 150]
          },
          categories: {
            data: [
              { label: 'Electronics', value: 350, percentage: 28 },
              { label: 'Clothing', value: 280, percentage: 22.4 },
              { label: 'Books', value: 220, percentage: 17.6 },
              { label: 'Home & Garden', value: 180, percentage: 14.4 },
              { label: 'Sports', value: 150, percentage: 12 },
              { label: 'Others', value: 70, percentage: 5.6 }
            ]
          }
        },
        insights: [
          { id: 1, type: 'warning', icon: 'fas fa-exclamation-triangle', title: 'Stock Alert', description: '70 products are out of stock and need restocking' },
          { id: 2, type: 'success', icon: 'fas fa-arrow-up', title: 'New Products', description: '25 new products added this week' }
        ],
        table: {
          data: [
            { name: 'Product A', category: 'Electronics', stock: 150, sales: 89, revenue: 4450, status: 'active' },
            { name: 'Product B', category: 'Clothing', stock: 0, sales: 45, revenue: 2250, status: 'out_of_stock' },
            { name: 'Product C', category: 'Books', stock: 25, sales: 67, revenue: 1340, status: 'low_stock' }
          ],
          columns: [
            { key: 'name', label: 'Product Name', sortable: true },
            { key: 'category', label: 'Category', sortable: true },
            { key: 'stock', label: 'Stock', type: 'number', sortable: true },
            { key: 'sales', label: 'Sales', type: 'number', sortable: true },
            { key: 'revenue', label: 'Revenue', type: 'currency', sortable: true },
            { key: 'status', label: 'Status', type: 'status', sortable: true }
          ]
        },
        lowStockItems: [
          { id: 1, name: 'Product A', stock: 5, reorderLevel: 20 },
          { id: 2, name: 'Product C', stock: 8, reorderLevel: 25 },
          { id: 3, name: 'Product E', stock: 12, reorderLevel: 30 }
        ]
      },
      users: {
        type: 'users',
        metrics: {
          items: [
            { key: 'totalUsers', label: 'Total Users', value: 5420, type: 'number', icon: 'fas fa-users', trend: 'up', changePercentage: 8.3 },
            { key: 'activeUsers', label: 'Active Users', value: 4890, type: 'number', icon: 'fas fa-user-check', trend: 'up', changePercentage: 5.7 },
            { key: 'newUsers', label: 'New Users', value: 230, type: 'number', icon: 'fas fa-user-plus', trend: 'up', changePercentage: 15.2 },
            { key: 'engagementRate', label: 'Engagement Rate', value: 78.5, type: 'percentage', icon: 'fas fa-chart-bar', trend: 'up', changePercentage: 3.1 }
          ]
        },
        charts: {
          registrations: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            data: [100, 150, 230, 180, 200, 250]
          },
          activity: {
            data: Array.from({ length: 24 }, (_, hour) =>
              Array.from({ length: 7 }, (_, day) => ({
                hour,
                day,
                value: Math.floor(Math.random() * 100)
              }))
            ).flat()
          },
          userTypes: {
            data: [
              { label: 'Regular', value: 3200, percentage: 59 },
              { label: 'Premium', value: 1500, percentage: 28 },
              { label: 'VIP', value: 720, percentage: 13 }
            ]
          }
        },
        insights: [
          { id: 1, type: 'success', icon: 'fas fa-arrow-up', title: 'User Growth', description: 'New user registrations increased by 15.2% this month' },
          { id: 2, type: 'info', icon: 'fas fa-clock', title: 'Peak Hours', description: 'Most users are active between 2-4 PM and 8-10 PM' }
        ],
        table: {
          data: [
            { name: 'John Doe', email: '<EMAIL>', type: 'Premium', registeredAt: '2024-01-15', lastActive: '2024-01-20', orders: 15 },
            { name: 'Jane Smith', email: '<EMAIL>', type: 'Regular', registeredAt: '2024-01-10', lastActive: '2024-01-19', orders: 8 },
            { name: 'Bob Johnson', email: '<EMAIL>', type: 'VIP', registeredAt: '2024-01-05', lastActive: '2024-01-21', orders: 25 }
          ],
          columns: [
            { key: 'name', label: 'Name', sortable: true },
            { key: 'email', label: 'Email', sortable: true },
            { key: 'type', label: 'Type', type: 'status', sortable: true },
            { key: 'registeredAt', label: 'Registered', type: 'date', sortable: true },
            { key: 'lastActive', label: 'Last Active', type: 'date', sortable: true },
            { key: 'orders', label: 'Orders', type: 'number', sortable: true }
          ]
        },
        recentUsers: [
          { id: 1, name: 'Alice Brown', email: '<EMAIL>', registeredAt: '2024-01-21' },
          { id: 2, name: 'Charlie Wilson', email: '<EMAIL>', registeredAt: '2024-01-20' },
          { id: 3, name: 'Diana Davis', email: '<EMAIL>', registeredAt: '2024-01-19' }
        ]
      },
      orders: {
        type: 'orders',
        metrics: {
          items: [
            { key: 'totalOrders', label: 'Total Orders', value: 1250, type: 'number', icon: 'fas fa-shopping-cart', trend: 'up', changePercentage: 12.3 },
            { key: 'pendingOrders', label: 'Pending Orders', value: 45, type: 'number', icon: 'fas fa-clock', trend: 'down', changePercentage: -8.5 },
            { key: 'completedOrders', label: 'Completed Orders', value: 1180, type: 'number', icon: 'fas fa-check-circle', trend: 'up', changePercentage: 15.2 },
            { key: 'averageOrderValue', label: 'Average Order Value', value: 89.50, type: 'currency', icon: 'fas fa-receipt', trend: 'up', changePercentage: 5.7 }
          ]
        },
        charts: {
          orders: {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            data: [280, 320, 350, 300]
          },
          status: {
            data: [
              { label: 'Pending', value: 45, percentage: 3.6 },
              { label: 'Processing', value: 25, percentage: 2 },
              { label: 'Shipped', value: 80, percentage: 6.4 },
              { label: 'Delivered', value: 1050, percentage: 84 },
              { label: 'Cancelled', value: 35, percentage: 2.8 },
              { label: 'Returned', value: 15, percentage: 1.2 }
            ]
          },
          fulfillment: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            processing: [2.5, 2.2, 2.8, 2.1, 2.6, 3.2, 2.9],
            shipping: [3.2, 3.5, 3.1, 3.8, 3.4, 4.1, 3.6]
          }
        },
        fulfillment: {
          averageProcessingTime: '2.5 days',
          averageShippingTime: '3.2 days',
          onTimeDeliveryRate: 94.5,
          returnRate: 2.1
        },
        insights: [
          { id: 1, type: 'success', icon: 'fas fa-arrow-up', title: 'Order Growth', description: 'Orders increased by 12.3% compared to last period' },
          { id: 2, type: 'warning', icon: 'fas fa-exclamation-triangle', title: 'Processing Delay', description: '5 orders are taking longer than usual to process' }
        ],
        table: {
          data: [
            { id: 'ORD-001', customer: 'John Doe', status: 'delivered', total: 125.50, items: 3, createdAt: '2024-01-15', updatedAt: '2024-01-18' },
            { id: 'ORD-002', customer: 'Jane Smith', status: 'shipped', total: 89.99, items: 2, createdAt: '2024-01-16', updatedAt: '2024-01-19' },
            { id: 'ORD-003', customer: 'Bob Johnson', status: 'processing', total: 199.99, items: 5, createdAt: '2024-01-17', updatedAt: '2024-01-20' }
          ],
          columns: [
            { key: 'id', label: 'Order ID', sortable: true },
            { key: 'customer', label: 'Customer', sortable: true },
            { key: 'status', label: 'Status', type: 'status', sortable: true },
            { key: 'total', label: 'Total', type: 'currency', sortable: true },
            { key: 'items', label: 'Items', type: 'number', sortable: true },
            { key: 'createdAt', label: 'Created', type: 'date', sortable: true },
            { key: 'updatedAt', label: 'Updated', type: 'date', sortable: true }
          ]
        },
        problemOrders: [
          { id: 'ORD-004', type: 'Payment Failed', value: 156.78 },
          { id: 'ORD-005', type: 'Shipping Delay', value: 89.99 },
          { id: 'ORD-006', type: 'Customer Complaint', value: 234.50 }
        ]
      }
    }

    return mockData[reportType] || {}
  }

  // Health check method
  async healthCheck() {
    try {
      const response = await this.makeRequest(`${this.baseURL}/health`)
      return response
    } catch (error) {
      console.error('Health check failed:', error)
      return { status: 'error', message: error.message }
    }
  }

  // Get available report types
  getAvailableReportTypes() {
    return ['financial', 'sales', 'products', 'users', 'orders']
  }

  // Validate filters
  validateFilters(filters) {
    const validFilters = {}
    const allowedKeys = ['startDate', 'endDate', 'category', 'status', 'limit', 'offset', 'sortBy', 'sortOrder']

    Object.entries(filters).forEach(([key, value]) => {
      if (allowedKeys.includes(key) && value !== null && value !== undefined && value !== '') {
        validFilters[key] = value
      }
    })

    return validFilters
  }
}

// Export singleton instance
export default new UnifiedReportsService()
