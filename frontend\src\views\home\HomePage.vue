<template>
  <div class="home-page">
    <!-- Hero Banner -->
    <div class="hero-banner">
      <div class="banner-container">
        <img src="@/assets/images/spring-banner.jpg" alt="Spring Banner" class="banner-image" />
        <div class="banner-text">
          <h2>ЗУСТРІЧАЙ ВЕШНЮ ПРАВИЛЬНО</h2>
        </div>
        <button class="banner-nav-btn banner-next">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 18l6-6-6-6"></path>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Categories Section -->
    <section class="categories-section">
      <div class="container">
        <h2 class="section-title">Розділи на сервісі</h2>
          <HomeCategories 
          :categories="categories"
          />
      </div>
    </section>
    
    <!-- Featured Products -->
    <section class="featured-section">
      <div class="container">
        <TopProducts 
        :products="filteredTopProducts"
        />
      </div>
    </section>
    
    <!-- Household Appliances -->
    <section class="appliances-section">
      <div class="container">
        <HouseholdAppliances 
        :products="filteredHouseholdProducts"
        />
      </div>
    </section>
    
    <!-- Recommended Products -->
    <section class="recommended-section">
      <div class="container">
        <RecommendedProducts 
        :products="filteredRecommendedProducts"
        />
      </div>
    </section>
  </div>
</template>

<script setup>
import TopProducts from '@/components/home/<USER>';
import RecommendedProducts from '@/components/home/<USER>';
import HouseholdAppliances from '@/components/home/<USER>';
import HomeCategories from '@/components/home/<USER>';
</script>

<script>
import ProductService from '@/services/product.service';
import CategoryService from '@/services/category.service';

export default 
{
  data() 
  {
    return {
      categories: [],
      topProducts: [],
      householdProducts: [],
      recommendedProducts: [],
      error: null,
    };
  },
  async mounted()
  {
    await this.fetchCategories();
    await this.fetchTopProducts();
    await this.fetchHouseholdProducts();
    await this.fetchRecommendedProducts();
  },
  computed: {
    filteredTopProducts() {
      return this.topProducts.filter(product => product.status == 1); // Skip out-of-stock products
    },
    filteredHouseholdProducts() {
      return this.householdProducts.filter(product => product.status == 1);
    },
    filteredRecommendedProducts() {
      return this.recommendedProducts.filter(product => product.status == 1);
    }
  },
  methods: {
    async fetchCategories(params = {pageSize: 18}) {
      try {
        const response = await CategoryService.getAllRootCategories(params);
        console.log(response);
        this.categories = response.data.data;
        this.error = null;
      } catch (error) {
        this.error = 'Failed to load categories. Please try again.';
        console.error(error);
      }
    },
    async fetchHouseholdProducts(params = {}) {
      try {
        const response = await CategoryService.getProducts('category-5',params);
        this.householdProducts = response.data.data;
        this.error = null;
      } catch (error) {
        this.error = 'Failed to load household products. Please try again.';
        console.error(error);
      }
    },
    async fetchTopProducts(params = {}) {
      try {
        const response = await ProductService.getAll(params);
        this.topProducts = response.data.data;
        this.error = null;
      } catch (error) {
        this.error = 'Failed to load top products. Please try again.';
        console.error(error);
      }
    },
    async fetchRecommendedProducts(params = {}) {
      try {
        const response = await CategoryService.getProducts('category-0',params);
        this.recommendedProducts = response.data.data;
        this.error = null;
      } catch (error) {
        this.error = 'Failed to load recommended products. Please try again.';
        console.error(error);
      }
    }
  }
};

</script>

<style scoped>
.home-page {
  padding-bottom: 32px;
}

.hero-banner {
  margin-bottom: 32px;
}

.banner-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  max-width: 1200px;
  margin: 0 auto;
}

.banner-image {
  width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: cover;
  display: block;
}

.banner-text {
  position: absolute;
  top: 50%;
  right: 10%;
  transform: translateY(-50%);
  color: white;
  text-align: right;
}

.banner-text h2 {
  font-size: 32px;
  font-weight: 700;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  max-width: 300px;
  line-height: 1.2;
}

.banner-nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.8);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.banner-next {
  right: 16px;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #333;
  text-align: center;
}

.featured-section,
.appliances-section,
.recommended-section {
  margin-bottom: 48px;
}

.blue-bg {
  background-color: #d4e6f1;
}

.yellow-bg {
  background-color: #fcf3cf;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 24px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

@media (max-width: 768px) {
  
  .section-title {
    font-size: 20px;
  }
  
  .banner-text h2 {
    font-size: 24px;
  }
}

.categories-section {
  margin-bottom: 48px;
}

.appliances-section,
.recommended-section {
  margin-bottom: 48px;
}
</style>
