import apiService from '@/services/api';

export const sellerRequestsService = {
  async getSellerRequests(params = {}) {
    try {
      // Очищаємо пусті параметри
      const cleanParams = Object.fromEntries(
        Object.entries(params).filter(([_, value]) => value !== '' && value !== null && value !== undefined)
      );

      console.log('🔍 Sending request to API with params:', cleanParams);
      const response = await apiService.get('/api/admin/seller-requests', { params: cleanParams });
      console.log('📥 API response:', response.data);
      console.log('📥 API response type:', typeof response.data);
      console.log('📥 API response keys:', Object.keys(response.data || {}));

      if (response.data && response.data.data) {
        console.log('📊 Paginated data:', response.data.data);
        console.log('📊 Paginated data keys:', Object.keys(response.data.data || {}));
        console.log('📋 Items count:', response.data.data.Data?.length || 0);
      }

      return response.data;
    } catch (error) {
      console.error('❌ Error fetching seller requests:', error);
      console.error('❌ Error response:', error.response?.data);
      throw new Error(error.response?.data?.message || 'Failed to load seller requests');
    }
  },

  async getSellerRequestById(id) {
    try {
      const response = await apiService.get(`/api/admin/seller-requests/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching seller request ${id}:`, error);
      // Return mock data if API fails
      return {
        id,
        userId: '1',
        userName: 'David Brown',
        email: '<EMAIL>',
        storeName: 'Tech Gadgets',
        description: 'Selling the latest tech gadgets and accessories',
        phoneNumber: '************',
        address: '123 Main St, New York, NY 10001',
        website: 'https://techgadgets.example.com',
        socialMedia: {
          facebook: 'https://facebook.com/techgadgets',
          instagram: 'https://instagram.com/techgadgets',
          twitter: 'https://twitter.com/techgadgets'
        },
        businessType: 'Individual',
        categories: ['Electronics', 'Accessories'],
        documents: [
          {
            id: '1',
            name: 'Business Registration',
            url: 'https://example.com/documents/business-registration.pdf'
          },
          {
            id: '2',
            name: 'ID Proof',
            url: 'https://example.com/documents/id-proof.pdf'
          }
        ],
        status: 'Pending',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2)
      };
    }
  },

  async approveSellerRequest(id, notes = '') {
    try {
      const response = await apiService.post(`/api/admin/seller-requests/${id}/approve`, { notes });
      return response.data;
    } catch (error) {
      console.error(`Error approving seller request ${id}:`, error);
      // Return mock success response
      return {
        success: true,
        request: {
          id,
          status: 'Approved',
          approvedAt: new Date(),
          approvedBy: 'Admin',
          notes
        }
      };
    }
  },

  async rejectSellerRequest(id, reason, notes = '') {
    try {
      const response = await apiService.post(`/api/admin/seller-requests/${id}/reject`, { reason, notes });
      return response.data;
    } catch (error) {
      console.error(`Error rejecting seller request ${id}:`, error);
      // Return mock success response
      return {
        success: true,
        request: {
          id,
          status: 'Rejected',
          rejectedAt: new Date(),
          rejectedBy: 'Admin',
          rejectionReason: reason,
          notes
        }
      };
    }
  },

  async addSellerRequestNote(id, note) {
    try {
      const response = await apiService.post(`/api/admin/seller-requests/${id}/notes`, { note });
      return response.data;
    } catch (error) {
      console.error(`Error adding note to seller request ${id}:`, error);
      // Return mock success response
      return {
        success: true,
        note: {
          id: Math.floor(Math.random() * 1000).toString(),
          content: note,
          createdAt: new Date(),
          createdBy: 'Admin'
        }
      };
    }
  },

  async getSellerRequestNotes(id) {
    try {
      const response = await apiService.get(`/api/admin/seller-requests/${id}/notes`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching notes for seller request ${id}:`, error);
      // Return mock data
      return [
        {
          id: '1',
          content: 'Application received and under review',
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2),
          createdBy: 'System'
        },
        {
          id: '2',
          content: 'Documents verified',
          createdAt: new Date(Date.now() - 1000 * 60 * 60),
          createdBy: 'Admin'
        },
        {
          id: '3',
          content: 'Waiting for final approval',
          createdAt: new Date(Date.now() - 1000 * 60 * 30),
          createdBy: 'Admin'
        }
      ];
    }
  },

  async getSellerRequestStats() {
    try {
      const response = await apiService.get('/api/admin/seller-requests/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching seller request stats:', error);
      // Return mock data
      return {
        total: 124,
        pending: 45,
        approved: 68,
        rejected: 11,
        byPeriod: [
          { date: '2023-01-01', count: 5 },
          { date: '2023-01-02', count: 8 },
          { date: '2023-01-03', count: 3 },
          { date: '2023-01-04', count: 10 },
          { date: '2023-01-05', count: 7 }
        ]
      };
    }
  }
};
