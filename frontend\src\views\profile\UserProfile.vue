<template>
  <div class="user-profile">
    <div class="container">
      <div class="columns">
        <!-- Sidebar -->
        <div class="column is-3">
          <div class="card">
            <div class="card-content">
              <div class="has-text-centered mb-4">
                <figure class="image is-128x128 mx-auto mb-3">
                  <img
                    :src="userAvatar"
                    alt="User avatar"
                    class="is-rounded"
                    @error="handleAvatarError">
                </figure>
                <h3 class="title is-4">{{ user.username }}</h3>
                <p class="subtitle is-6">{{ formatRole(user.role) }}</p>
              </div>
              
              <div class="menu">
                <ul class="menu-list">
                  <li>
                    <a 
                      :class="{ 'is-active': activeTab === 'profile' }"
                      @click="activeTab = 'profile'">
                      <span class="icon"><i class="fas fa-user"></i></span>
                      <span>Профіль</span>
                    </a>
                  </li>
                  <li>
                    <a 
                      :class="{ 'is-active': activeTab === 'orders' }"
                      @click="activeTab = 'orders'">
                      <span class="icon"><i class="fas fa-shopping-bag"></i></span>
                      <span>Мої замовлення</span>
                    </a>
                  </li>
                  <li>
                    <a 
                      :class="{ 'is-active': activeTab === 'wishlist' }"
                      @click="activeTab = 'wishlist'">
                      <span class="icon"><i class="fas fa-heart"></i></span>
                      <span>Список бажань</span>
                    </a>
                  </li>
                  <li>
                    <a 
                      :class="{ 'is-active': activeTab === 'settings' }"
                      @click="activeTab = 'settings'">
                      <span class="icon"><i class="fas fa-cog"></i></span>
                      <span>Налаштування</span>
                    </a>
                  </li>
                  <li v-if="isBuyer">
                    <a 
                      :class="{ 'is-active': activeTab === 'seller-request' }"
                      @click="activeTab = 'seller-request'">
                      <span class="icon"><i class="fas fa-store"></i></span>
                      <span>Стати продавцем</span>
                    </a>
                  </li>
                  <li v-if="isSeller">
                    <a 
                      :class="{ 'is-active': activeTab === 'seller-dashboard' }"
                      @click="activeTab = 'seller-dashboard'">
                      <span class="icon"><i class="fas fa-chart-line"></i></span>
                      <span>Панель продавця</span>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Main Content -->
        <div class="column is-9">
          <!-- Profile Tab -->
          <div v-if="activeTab === 'profile'" class="card">
            <div class="card-header">
              <p class="card-header-title">
                <span class="icon mr-2"><i class="fas fa-user"></i></span>
                Інформація профілю
              </p>
            </div>
            <div class="card-content">
              <div class="content">
                <div class="field">
                  <label class="label">Ім'я користувача</label>
                  <p>{{ user.username }}</p>
                </div>
                
                <div class="field">
                  <label class="label">Email</label>
                  <p>{{ user.email }}</p>
                </div>
                
                <div class="field">
                  <label class="label">Роль</label>
                  <p>{{ formatRole(user.role) }}</p>
                </div>
                
                <div class="field">
                  <label class="label">Дата реєстрації</label>
                  <p>{{ formatDate(user.createdAt) }}</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Orders Tab -->
          <div v-else-if="activeTab === 'orders'" class="card">
            <div class="card-header">
              <p class="card-header-title">
                <span class="icon mr-2"><i class="fas fa-shopping-bag"></i></span>
                Мої замовлення
              </p>
            </div>
            <div class="card-content">
              <div v-if="loading" class="has-text-centered py-6">
                <span class="icon is-large">
                  <i class="fas fa-spinner fa-pulse fa-2x"></i>
                </span>
                <p class="mt-2">Завантаження замовлень...</p>
              </div>
              <div v-else-if="!orders.length" class="has-text-centered py-6">
                <span class="icon is-large">
                  <i class="fas fa-shopping-bag fa-2x"></i>
                </span>
                <p class="mt-2">У вас ще немає замовлень</p>
                <router-link to="/products" class="button is-primary mt-4">
                  Перейти до покупок
                </router-link>
              </div>
              <div v-else>
                <!-- Orders list will be implemented later -->
                <p>Список замовлень буде реалізовано пізніше</p>
              </div>
            </div>
          </div>
          
          <!-- Wishlist Tab -->
          <div v-else-if="activeTab === 'wishlist'" class="card">
            <div class="card-header">
              <p class="card-header-title">
                <span class="icon mr-2"><i class="fas fa-heart"></i></span>
                Список бажань
              </p>
            </div>
            <div class="card-content">
              <div v-if="loading" class="has-text-centered py-6">
                <span class="icon is-large">
                  <i class="fas fa-spinner fa-pulse fa-2x"></i>
                </span>
                <p class="mt-2">Завантаження списку бажань...</p>
              </div>
              <div v-else-if="!wishlist.length" class="has-text-centered py-6">
                <span class="icon is-large">
                  <i class="fas fa-heart fa-2x"></i>
                </span>
                <p class="mt-2">Ваш список бажань порожній</p>
                <router-link to="/products" class="button is-primary mt-4">
                  Перейти до покупок
                </router-link>
              </div>
              <div v-else>
                <!-- Wishlist will be implemented later -->
                <p>Список бажань буде реалізовано пізніше</p>
              </div>
            </div>
          </div>
          
          <!-- Settings Tab -->
          <div v-else-if="activeTab === 'settings'" class="card">
            <div class="card-header">
              <p class="card-header-title">
                <span class="icon mr-2"><i class="fas fa-cog"></i></span>
                Налаштування
              </p>
            </div>
            <div class="card-content">
              <!-- Settings form will be implemented later -->
              <p>Налаштування профілю буде реалізовано пізніше</p>
            </div>
          </div>
          
          <!-- Seller Request Tab -->
          <div v-else-if="activeTab === 'seller-request'" class="card">
            <div class="card-header">
              <p class="card-header-title">
                <span class="icon mr-2"><i class="fas fa-store"></i></span>
                Стати продавцем
              </p>
            </div>
            <div class="card-content">
              <div v-if="loading" class="has-text-centered py-6">
                <span class="icon is-large">
                  <i class="fas fa-spinner fa-pulse fa-2x"></i>
                </span>
                <p class="mt-2">Завантаження...</p>
              </div>
              <div v-else-if="sellerRequests.length > 0">
                <div class="notification is-info">
                  <p>
                    <span class="icon"><i class="fas fa-info-circle"></i></span>
                    У вас вже є заявка на отримання статусу продавця.
                  </p>
                </div>
                
                <div 
                  v-for="request in sellerRequests" 
                  :key="request.id" 
                  class="box">
                  <div class="level">
                    <div class="level-left">
                      <div class="level-item">
                        <div>
                          <p class="heading">Статус</p>
                          <p class="title is-5">
                            <span 
                              class="tag" 
                              :class="{
                                'is-warning': request.status === 'pending',
                                'is-success': request.status === 'approved',
                                'is-danger': request.status === 'rejected'
                              }">
                              {{ formatStatus(request.status) }}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                    <div class="level-right">
                      <div class="level-item">
                        <div>
                          <p class="heading">Дата подання</p>
                          <p class="title is-6">{{ formatDate(request.createdAt) }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="content">
                    <p><strong>Назва магазину:</strong> {{ request.storeName }}</p>
                    <p><strong>Опис:</strong> {{ request.storeDescription }}</p>
                    <p><strong>Тип бізнесу:</strong> {{ request.businessType }}</p>
                    
                    <div v-if="request.status === 'rejected'" class="notification is-danger is-light mt-4">
                      <p><strong>Причина відхилення:</strong> {{ request.rejectionReason || 'Не вказано' }}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else>
                <div class="notification is-info">
                  <p>
                    <span class="icon"><i class="fas fa-info-circle"></i></span>
                    Заповніть форму нижче, щоб подати заявку на отримання статусу продавця.
                  </p>
                </div>
                
                <form @submit.prevent="submitSellerRequest">
                  <!-- Company Information -->
                  <div class="box">
                    <h4 class="title is-5">Інформація про компанію</h4>

                    <div class="columns">
                      <div class="column is-6">
                        <div class="field">
                          <label class="label">Назва компанії*</label>
                          <div class="control">
                            <input
                              class="input"
                              type="text"
                              v-model="sellerRequestForm.companyName"
                              required
                              placeholder="Введіть назву вашої компанії">
                          </div>
                        </div>
                      </div>
                      <div class="column is-6">
                        <div class="field">
                          <label class="label">Слаг компанії*</label>
                          <div class="control">
                            <input
                              class="input"
                              type="text"
                              v-model="sellerRequestForm.companySlug"
                              required
                              placeholder="company-slug">
                          </div>
                          <p class="help">Унікальний ідентифікатор для URL (тільки латинські літери, цифри та дефіси)</p>
                        </div>
                      </div>
                    </div>

                    <div class="field">
                      <label class="label">Опис компанії*</label>
                      <div class="control">
                        <textarea
                          class="textarea"
                          v-model="sellerRequestForm.companyDescription"
                          required
                          placeholder="Опишіть вашу компанію та товари, які ви плануєте продавати"></textarea>
                      </div>
                    </div>

                    <div class="columns">
                      <div class="column is-6">
                        <div class="field">
                          <label class="label">Email для зв'язку*</label>
                          <div class="control">
                            <input
                              class="input"
                              type="email"
                              v-model="sellerRequestForm.contactEmail"
                              required
                              placeholder="<EMAIL>">
                          </div>
                        </div>
                      </div>
                      <div class="column is-6">
                        <div class="field">
                          <label class="label">Телефон для зв'язку*</label>
                          <div class="control">
                            <input
                              class="input"
                              type="tel"
                              v-model="sellerRequestForm.contactPhone"
                              required
                              placeholder="+380501234567">
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="field">
                      <label class="label">URL зображення компанії</label>
                      <div class="control">
                        <input
                          class="input"
                          type="url"
                          v-model="sellerRequestForm.companyImageUrl"
                          placeholder="https://example.com/logo.png">
                      </div>
                    </div>
                  </div>

                  <!-- Address Information -->
                  <div class="box">
                    <h4 class="title is-5">Адреса компанії</h4>

                    <div class="columns">
                      <div class="column is-6">
                        <div class="field">
                          <label class="label">Область*</label>
                          <div class="control">
                            <input
                              class="input"
                              type="text"
                              v-model="sellerRequestForm.addressRegion"
                              required
                              placeholder="Київська область">
                          </div>
                        </div>
                      </div>
                      <div class="column is-6">
                        <div class="field">
                          <label class="label">Місто*</label>
                          <div class="control">
                            <input
                              class="input"
                              type="text"
                              v-model="sellerRequestForm.addressCity"
                              required
                              placeholder="Київ">
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="columns">
                      <div class="column is-8">
                        <div class="field">
                          <label class="label">Вулиця та номер будинку*</label>
                          <div class="control">
                            <input
                              class="input"
                              type="text"
                              v-model="sellerRequestForm.addressStreet"
                              required
                              placeholder="вул. Хрещатик, 1">
                          </div>
                        </div>
                      </div>
                      <div class="column is-4">
                        <div class="field">
                          <label class="label">Поштовий індекс*</label>
                          <div class="control">
                            <input
                              class="input"
                              type="text"
                              v-model="sellerRequestForm.addressPostalCode"
                              required
                              placeholder="01001">
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Financial Information -->
                  <div class="box">
                    <h4 class="title is-5">Фінансова інформація</h4>

                    <div class="columns">
                      <div class="column is-6">
                        <div class="field">
                          <label class="label">Банківський рахунок*</label>
                          <div class="control">
                            <input
                              class="input"
                              type="text"
                              v-model="sellerRequestForm.bankAccount"
                              required
                              placeholder="*****************************">
                          </div>
                        </div>
                      </div>
                      <div class="column is-6">
                        <div class="field">
                          <label class="label">Назва банку*</label>
                          <div class="control">
                            <input
                              class="input"
                              type="text"
                              v-model="sellerRequestForm.bankName"
                              required
                              placeholder="ПриватБанк">
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="columns">
                      <div class="column is-6">
                        <div class="field">
                          <label class="label">МФО банку*</label>
                          <div class="control">
                            <input
                              class="input"
                              type="text"
                              v-model="sellerRequestForm.bankCode"
                              required
                              placeholder="305299">
                          </div>
                        </div>
                      </div>
                      <div class="column is-6">
                        <div class="field">
                          <label class="label">Податковий номер*</label>
                          <div class="control">
                            <input
                              class="input"
                              type="text"
                              v-model="sellerRequestForm.taxId"
                              required
                              placeholder="**********">
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="field">
                      <label class="label">Деталі платежів</label>
                      <div class="control">
                        <textarea
                          class="textarea"
                          v-model="sellerRequestForm.paymentDetails"
                          placeholder="Додаткова інформація про платежі"></textarea>
                      </div>
                    </div>
                  </div>

                  <!-- Schedule Information -->
                  <div class="box">
                    <h4 class="title is-5">Розклад роботи</h4>

                    <div v-for="(day, index) in sellerRequestForm.daySchedules" :key="index" class="field">
                      <div class="columns is-vcentered">
                        <div class="column is-2">
                          <label class="label">{{ getDayName(day.day) }}</label>
                        </div>
                        <div class="column is-2">
                          <div class="field">
                            <div class="control">
                              <label class="checkbox">
                                <input type="checkbox" v-model="day.isClosed" @change="toggleDaySchedule(index)">
                                Вихідний
                              </label>
                            </div>
                          </div>
                        </div>
                        <div class="column is-3" v-if="!day.isClosed">
                          <div class="field">
                            <label class="label is-small">Відкриття</label>
                            <div class="control">
                              <input
                                class="input"
                                type="time"
                                v-model="day.openTime">
                            </div>
                          </div>
                        </div>
                        <div class="column is-3" v-if="!day.isClosed">
                          <div class="field">
                            <label class="label is-small">Закриття</label>
                            <div class="control">
                              <input
                                class="input"
                                type="time"
                                v-model="day.closeTime">
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Meta Information -->
                  <div class="box">
                    <h4 class="title is-5">SEO інформація</h4>

                    <div class="field">
                      <label class="label">Meta заголовок</label>
                      <div class="control">
                        <input
                          class="input"
                          type="text"
                          v-model="sellerRequestForm.metaTitle"
                          placeholder="SEO заголовок для пошукових систем">
                      </div>
                    </div>

                    <div class="field">
                      <label class="label">Meta опис</label>
                      <div class="control">
                        <textarea
                          class="textarea"
                          v-model="sellerRequestForm.metaDescription"
                          placeholder="SEO опис для пошукових систем"></textarea>
                      </div>
                    </div>

                    <div class="field">
                      <label class="label">Meta зображення URL</label>
                      <div class="control">
                        <input
                          class="input"
                          type="url"
                          v-model="sellerRequestForm.metaImageUrl"
                          placeholder="https://example.com/meta-image.png">
                      </div>
                    </div>
                  </div>

                  <div class="field mt-5">
                    <div class="control">
                      <button 
                        type="submit" 
                        class="button is-primary is-fullwidth"
                        :class="{ 'is-loading': submitting }"
                        :disabled="submitting">
                        Подати заявку
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
          
          <!-- Seller Dashboard Tab -->
          <div v-else-if="activeTab === 'seller-dashboard'" class="card">
            <div class="card-header">
              <p class="card-header-title">
                <span class="icon mr-2"><i class="fas fa-chart-line"></i></span>
                Панель продавця
              </p>
            </div>
            <div class="card-content">
              <div class="notification is-info">
                <p>
                  <span class="icon"><i class="fas fa-info-circle"></i></span>
                  Ви можете керувати своїми товарами та замовленнями в панелі продавця.
                </p>
              </div>
              
              <div class="buttons">
                <router-link to="/seller/products" class="button is-primary">
                  <span class="icon"><i class="fas fa-box"></i></span>
                  <span>Мої товари</span>
                </router-link>
                <router-link to="/seller/orders" class="button is-info">
                  <span class="icon"><i class="fas fa-shopping-bag"></i></span>
                  <span>Замовлення</span>
                </router-link>
                <router-link to="/seller/dashboard" class="button is-success">
                  <span class="icon"><i class="fas fa-chart-line"></i></span>
                  <span>Статистика</span>
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useStore } from 'vuex';
import { isBuyer, isSeller } from '@/services/auth';
import { sellerRequestService } from '@/services/seller-request.service';

// Store
const store = useStore();

// State
const user = computed(() => store.getters['auth/user'] || {});
const activeTab = ref('profile');
const loading = ref(false);
const submitting = ref(false);
const orders = ref([]);
const wishlist = ref([]);
const sellerRequests = ref([]);
const userAvatar = ref('https://via.placeholder.com/128?text=User');

// Form state
const sellerRequestForm = reactive({
  // Company Information
  companyName: '',
  companySlug: '',
  companyDescription: '',
  contactEmail: '',
  contactPhone: '',
  companyImageUrl: '',

  // Address Information
  addressRegion: '',
  addressCity: '',
  addressStreet: '',
  addressPostalCode: '',

  // Financial Information
  bankAccount: '',
  bankName: '',
  bankCode: '',
  taxId: '',
  paymentDetails: '',

  // Schedule Information
  daySchedules: [
    { day: 1, openTime: '09:00', closeTime: '18:00', isClosed: false }, // Monday
    { day: 2, openTime: '09:00', closeTime: '18:00', isClosed: false }, // Tuesday
    { day: 3, openTime: '09:00', closeTime: '18:00', isClosed: false }, // Wednesday
    { day: 4, openTime: '09:00', closeTime: '18:00', isClosed: false }, // Thursday
    { day: 5, openTime: '09:00', closeTime: '18:00', isClosed: false }, // Friday
    { day: 6, openTime: '10:00', closeTime: '16:00', isClosed: false }, // Saturday
    { day: 0, openTime: '10:00', closeTime: '16:00', isClosed: true }   // Sunday
  ],

  // Meta Information
  metaTitle: '',
  metaDescription: '',
  metaImageUrl: '',

  // Additional Information
  additionalInfo: ''
});

// Computed properties
const isBuyerUser = computed(() => isBuyer());
const isSellerUser = computed(() => isSeller());

// Methods
const formatDate = (dateString) => {
  if (!dateString) return 'Не вказано';
  
  return new Intl.DateTimeFormat('uk-UA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date(dateString));
};

const formatRole = (role) => {
  if (typeof role === 'string') {
    const roleMap = {
      'admin': 'Адміністратор',
      'moderator': 'Модератор',
      'seller': 'Продавець',
      'sellerowner': 'Власник магазину',
      'buyer': 'Покупець'
    };
    return roleMap[role.toLowerCase()] || 'Покупець';
  } else if (typeof role === 'number') {
    const roleMap = {
      0: 'Покупець',
      1: 'Продавець',
      2: 'Власник магазину',
      3: 'Модератор',
      4: 'Адміністратор'
    };
    return roleMap[role] || 'Покупець';
  }
  return 'Покупець';
};

const formatStatus = (status) => {
  const statusMap = {
    'pending': 'На розгляді',
    'approved': 'Схвалено',
    'rejected': 'Відхилено'
  };
  return statusMap[status] || 'Невідомо';
};

const handleAvatarError = (event) => {
  event.target.src = 'https://via.placeholder.com/128?text=User';
};

const getDayName = (day) => {
  const dayNames = {
    0: 'Неділя',
    1: 'Понеділок',
    2: 'Вівторок',
    3: 'Середа',
    4: 'Четвер',
    5: 'П\'ятниця',
    6: 'Субота'
  };
  return dayNames[day] || 'Невідомо';
};

const toggleDaySchedule = (index) => {
  if (sellerRequestForm.daySchedules[index].isClosed) {
    sellerRequestForm.daySchedules[index].openTime = '';
    sellerRequestForm.daySchedules[index].closeTime = '';
  } else {
    sellerRequestForm.daySchedules[index].openTime = '09:00';
    sellerRequestForm.daySchedules[index].closeTime = '18:00';
  }
};

const fetchSellerRequests = async () => {
  loading.value = true;
  try {
    const response = await sellerRequestService.getUserSellerRequests();
    sellerRequests.value = response.requests || [];
  } catch (error) {
    console.error('Error fetching seller requests:', error);
  } finally {
    loading.value = false;
  }
};

const submitSellerRequest = async () => {
  submitting.value = true;
  try {
    // Створюємо об'єкт для відправки на сервер
    const requestData = {
      // Company Information
      companyName: sellerRequestForm.companyName,
      companySlug: sellerRequestForm.companySlug,
      companyDescription: sellerRequestForm.companyDescription,
      contactEmail: sellerRequestForm.contactEmail,
      contactPhone: sellerRequestForm.contactPhone,
      companyImageUrl: sellerRequestForm.companyImageUrl || null,

      // Address Information
      addressRegion: sellerRequestForm.addressRegion,
      addressCity: sellerRequestForm.addressCity,
      addressStreet: sellerRequestForm.addressStreet,
      addressPostalCode: sellerRequestForm.addressPostalCode,

      // Financial Information
      bankAccount: sellerRequestForm.bankAccount,
      bankName: sellerRequestForm.bankName,
      bankCode: sellerRequestForm.bankCode,
      taxId: sellerRequestForm.taxId,
      paymentDetails: sellerRequestForm.paymentDetails || null,

      // Schedule Information
      daySchedules: sellerRequestForm.daySchedules.filter(day => !day.isClosed).map(day => ({
        day: day.day,
        openTime: day.openTime,
        closeTime: day.closeTime
      })),

      // Meta Information
      metaTitle: sellerRequestForm.metaTitle || null,
      metaDescription: sellerRequestForm.metaDescription || null,
      metaImageUrl: sellerRequestForm.metaImageUrl || null,

      // Additional Information
      additionalInfo: sellerRequestForm.additionalInfo || null
    };

    // Відправляємо заявку
    await sellerRequestService.createSellerRequest(requestData);

    // Очищаємо форму
    Object.keys(sellerRequestForm).forEach(key => {
      if (key === 'daySchedules') {
        sellerRequestForm[key] = [
          { day: 1, openTime: '09:00', closeTime: '18:00', isClosed: false },
          { day: 2, openTime: '09:00', closeTime: '18:00', isClosed: false },
          { day: 3, openTime: '09:00', closeTime: '18:00', isClosed: false },
          { day: 4, openTime: '09:00', closeTime: '18:00', isClosed: false },
          { day: 5, openTime: '09:00', closeTime: '18:00', isClosed: false },
          { day: 6, openTime: '10:00', closeTime: '16:00', isClosed: false },
          { day: 0, openTime: '10:00', closeTime: '16:00', isClosed: true }
        ];
      } else {
        sellerRequestForm[key] = '';
      }
    });

    // Оновлюємо список заявок
    await fetchSellerRequests();

    // Показуємо повідомлення про успіх
    alert('Заявка успішно подана! Ми розглянемо її найближчим часом.');
  } catch (error) {
    console.error('Error submitting seller request:', error);
    alert('Помилка при поданні заявки. Будь ласка, спробуйте ще раз.');
  } finally {
    submitting.value = false;
  }
};

// Lifecycle hooks
onMounted(async () => {
  // Завантажуємо заявки на отримання статусу продавця
  if (isBuyerUser.value) {
    await fetchSellerRequests();
  }
});
</script>

<style scoped>
.user-profile {
  padding: 2rem 0;
}

.menu-list a {
  display: flex;
  align-items: center;
}

.menu-list a .icon {
  margin-right: 0.5rem;
}

.menu-list a.is-active {
  background-color: #ff7700;
}

.card {
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

.card-header {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.card-header-title {
  display: flex;
  align-items: center;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mt-5 {
  margin-top: 2rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

.image.is-128x128 {
  width: 128px;
  height: 128px;
}

.image.is-128x128 img {
  object-fit: cover;
}
</style>
