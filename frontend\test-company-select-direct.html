<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test CompanySelect Direct</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div id="app" class="container mt-4">
        <h1 class="title">Test CompanySelect Component Direct</h1>
        
        <div class="box">
            <h2 class="subtitle">Company Select Test</h2>
            
            <div class="field">
                <label class="label">Company</label>
                <div class="control">
                    <company-select 
                        v-model="selectedCompanyId" 
                        label="Company"
                        placeholder="Search and select company..."
                        @change="onCompanyChange"
                    ></company-select>
                </div>
            </div>
            
            <div class="field">
                <label class="label">Selected Company ID:</label>
                <div class="control">
                    <input class="input" type="text" :value="selectedCompanyId" readonly>
                </div>
            </div>
            
            <div class="field">
                <label class="label">Selected Company Object:</label>
                <div class="control">
                    <textarea class="textarea" :value="JSON.stringify(selectedCompany, null, 2)" readonly rows="5"></textarea>
                </div>
            </div>
        </div>
        
        <div class="box">
            <h2 class="subtitle">Debug Info</h2>
            <div v-for="log in debugLogs" :key="log.id" class="notification" :class="log.type">
                <strong>{{ log.timestamp }}:</strong> {{ log.message }}
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;

        // Simulate the companies service
        const companiesService = {
            async getCompanies(params = {}) {
                try {
                    console.log('🏢 Fetching companies with params:', params);
                    
                    const apiParams = {};
                    if (params.page) apiParams.page = params.page;
                    if (params.pageSize) apiParams.pageSize = params.pageSize;
                    if (params.orderBy) apiParams.orderBy = params.orderBy;
                    if (params.descending !== undefined) apiParams.descending = params.descending;
                    if (params.search && params.search.trim() !== '') {
                        apiParams.filter = params.search.trim();
                    } else if (params.filter && params.filter.trim() !== '') {
                        apiParams.filter = params.filter.trim();
                    }
                    
                    console.log('Final API params for companies:', apiParams);
                    
                    const response = await axios.get('http://localhost:5296/api/companies', { params: apiParams });
                    console.log('Companies API response:', response.data);
                    return response.data;
                } catch (error) {
                    console.error('Error fetching companies:', error);
                    throw new Error(error.response?.data?.message || 'Failed to load companies');
                }
            }
        };

        // CompanySelect component
        const CompanySelect = {
            props: {
                modelValue: String,
                label: String,
                placeholder: String,
                required: Boolean,
                readonly: Boolean
            },
            emits: ['update:modelValue', 'change'],
            setup(props, { emit }) {
                const companies = ref([]);
                const selectedCompany = ref(null);
                const searchQuery = ref('');
                const isDropdownOpen = ref(false);
                const loading = ref(false);

                const filteredCompanies = Vue.computed(() => {
                    const companiesArray = Array.isArray(companies.value) ? companies.value : [];
                    
                    if (!searchQuery.value.trim()) {
                        return companiesArray.slice(0, 50);
                    }

                    const query = searchQuery.value.toLowerCase().trim();
                    return companiesArray.filter(company => {
                        if (!company || typeof company !== 'object') return false;
                        
                        return (
                            (company.name && company.name.toLowerCase().includes(query)) ||
                            (company.contactEmail && company.contactEmail.toLowerCase().includes(query)) ||
                            (company.addressCity && company.addressCity.toLowerCase().includes(query)) ||
                            (company.slug && company.slug.toLowerCase().includes(query))
                        );
                    }).slice(0, 50);
                });

                const fetchCompanies = async () => {
                    try {
                        loading.value = true;
                        console.log('🏢 Fetching companies...');

                        const response = await companiesService.getCompanies({ pageSize: 200 });
                        console.log('📦 Companies API response:', response);

                        let companiesData = [];
                        if (response && response.data && Array.isArray(response.data)) {
                            companiesData = response.data;
                        } else if (response && response.companies && Array.isArray(response.companies)) {
                            companiesData = response.companies;
                        } else if (Array.isArray(response)) {
                            companiesData = response;
                        } else {
                            console.warn('⚠️ Unexpected API response structure:', response);
                            companiesData = [];
                        }

                        companies.value = companiesData;
                        console.log('✅ Companies loaded:', companies.value.length);

                        if (props.modelValue && Array.isArray(companies.value)) {
                            const found = companies.value.find(comp => comp && comp.id === props.modelValue);
                            if (found) {
                                selectedCompany.value = found;
                                searchQuery.value = found.name || '';
                                console.log('🎯 Selected company found:', found.name);
                            } else {
                                console.log('❌ Company not found for ID:', props.modelValue);
                            }
                        }
                    } catch (error) {
                        console.error('❌ Error fetching companies:', error);
                        companies.value = [];
                    } finally {
                        loading.value = false;
                    }
                };

                const selectCompany = (company) => {
                    if (!company || typeof company !== 'object' || !company.id) {
                        console.error('❌ Invalid company object:', company);
                        return;
                    }

                    selectedCompany.value = company;
                    searchQuery.value = company.name || '';
                    isDropdownOpen.value = false;

                    console.log('✅ Company selected:', company.name);
                    emit('update:modelValue', company.id);
                    emit('change', company);
                };

                Vue.onMounted(() => {
                    fetchCompanies();
                });

                return {
                    companies,
                    selectedCompany,
                    searchQuery,
                    isDropdownOpen,
                    loading,
                    filteredCompanies,
                    selectCompany,
                    toggleDropdown: () => isDropdownOpen.value = !isDropdownOpen.value
                };
            },
            template: `
                <div class="company-select">
                    <div class="dropdown" :class="{ 'is-active': isDropdownOpen }">
                        <div class="dropdown-trigger">
                            <div class="field has-addons">
                                <div class="control is-expanded">
                                    <input 
                                        class="input" 
                                        type="text" 
                                        :placeholder="placeholder"
                                        v-model="searchQuery"
                                        @focus="isDropdownOpen = true"
                                        @input="isDropdownOpen = true"
                                        :readonly="readonly"
                                    >
                                </div>
                                <div class="control">
                                    <button 
                                        class="button" 
                                        type="button"
                                        @click="toggleDropdown"
                                        :disabled="readonly"
                                    >
                                        <span class="icon">
                                            <i class="fas fa-chevron-down" :class="{ 'fa-rotate-180': isDropdownOpen }"></i>
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="dropdown-menu" v-show="isDropdownOpen">
                            <div class="dropdown-content">
                                <div v-if="loading" class="dropdown-item">
                                    <span class="icon">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </span>
                                    Loading companies...
                                </div>
                                <div v-else-if="filteredCompanies.length === 0" class="dropdown-item">
                                    No companies found
                                </div>
                                <a 
                                    v-else
                                    v-for="company in filteredCompanies" 
                                    :key="company.id"
                                    class="dropdown-item"
                                    @click="selectCompany(company)"
                                >
                                    <div class="company-item">
                                        <div class="company-name">{{ company.name }}</div>
                                        <div class="company-details is-size-7 has-text-grey">
                                            {{ company.contactEmail }} • {{ company.addressCity }}
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            `
        };

        createApp({
            components: {
                CompanySelect
            },
            setup() {
                const selectedCompanyId = ref('da9dcbee-6752-4121-82b6-e6c8e7106eaf');
                const selectedCompany = ref(null);
                const debugLogs = ref([]);
                let logId = 0;

                const addLog = (message, type = 'is-info') => {
                    debugLogs.value.push({
                        id: ++logId,
                        message,
                        type,
                        timestamp: new Date().toLocaleTimeString()
                    });
                };

                const onCompanyChange = (company) => {
                    selectedCompany.value = company;
                    addLog(`Company changed: ${company.name}`, 'is-success');
                };

                addLog('Component initialized', 'is-info');

                return {
                    selectedCompanyId,
                    selectedCompany,
                    debugLogs,
                    onCompanyChange
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
