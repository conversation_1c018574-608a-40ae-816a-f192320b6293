<template>
  <div class="reports-page">
    <!-- Header Section -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="fas fa-chart-bar"></i>
          Reports & Analytics
        </h1>
        <p class="page-description">
          Comprehensive business intelligence and analytics dashboard for monitoring performance, 
          tracking sales, and generating detailed reports across all business metrics.
        </p>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
      <ReportFilters 
        v-model:reportType="selectedReportType"
        v-model:dateRange="dateRange"
        v-model:additionalFilters="additionalFilters"
        @filtersChanged="handleFiltersChange"
      />
    </div>

    <!-- Report Content -->
    <div class="report-content">
      <div class="content-wrapper">
        <!-- Loading State -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Generating report...</p>
          </div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="error-container">
          <div class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>Error Loading Report</h3>
            <p>{{ error }}</p>
            <button @click="loadReportData" class="retry-btn">
              <i class="fas fa-redo"></i>
              Retry
            </button>
          </div>
        </div>

        <!-- Report Preview -->
        <div v-else-if="reportData" class="report-preview">
          <!-- Financial Report -->
          <FinancialReport
            v-if="selectedReportType === 'financial'"
            :data="reportData"
            :dateRange="dateRange"
            :filters="additionalFilters"
          />

          <!-- Sales Report -->
          <SalesReport
            v-else-if="selectedReportType === 'sales'"
            :data="reportData"
            :dateRange="dateRange"
            :filters="additionalFilters"
          />

          <!-- Products Report -->
          <ProductsReport
            v-else-if="selectedReportType === 'products'"
            :data="reportData"
            :dateRange="dateRange"
            :filters="additionalFilters"
          />

          <!-- Users Report -->
          <UsersReport
            v-else-if="selectedReportType === 'users'"
            :data="reportData"
            :dateRange="dateRange"
            :filters="additionalFilters"
          />

          <!-- Orders Report -->
          <OrdersReport
            v-else-if="selectedReportType === 'orders'"
            :data="reportData"
            :dateRange="dateRange"
            :filters="additionalFilters"
          />

          <!-- Generic Report Preview (fallback for other report types) -->
          <ReportPreview
            v-else
            :reportType="selectedReportType"
            :data="reportData"
            :dateRange="dateRange"
            :filters="additionalFilters"
          />
        </div>

        <!-- Empty State -->
        <div v-else class="empty-state">
          <div class="empty-message">
            <i class="fas fa-chart-line"></i>
            <h3>Select Report Parameters</h3>
            <p>Choose a report type and date range to generate your analytics report.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Export Section -->
    <div v-if="reportData" class="export-section">
      <ReportExport 
        :reportType="selectedReportType"
        :data="reportData"
        :dateRange="dateRange"
        :filters="additionalFilters"
        @exportStarted="handleExportStarted"
        @exportCompleted="handleExportCompleted"
        @exportError="handleExportError"
      />
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useToast } from '@/composables/useToast'
import ReportFilters from '@/components/admin/reports/ReportFilters.vue'
import ReportPreview from '@/components/admin/reports/ReportPreview.vue'
import ReportExport from '@/components/admin/reports/ReportExport.vue'
import FinancialReport from '@/components/admin/reports/FinancialReport.vue'
import SalesReport from '@/components/admin/reports/SalesReport.vue'
import ProductsReport from '@/components/admin/reports/ProductsReport.vue'
import UsersReport from '@/components/admin/reports/UsersReport.vue'
import OrdersReport from '@/components/admin/reports/OrdersReport.vue'
import { reportsService } from '@/services/reports.service'

export default {
  name: 'Reports',
  components: {
    ReportFilters,
    ReportPreview,
    ReportExport,
    FinancialReport,
    SalesReport,
    ProductsReport,
    UsersReport,
    OrdersReport
  },
  setup() {
    const { showToast } = useToast()

    // Reactive state
    const loading = ref(false)
    const error = ref(null)
    const reportData = ref(null)
    const selectedReportType = ref('financial')
    const dateRange = reactive({
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      endDate: new Date()
    })
    const additionalFilters = reactive({})

    // Computed properties
    const canGenerateReport = computed(() => {
      return selectedReportType.value && dateRange.startDate && dateRange.endDate
    })

    // Methods
    const loadReportData = async () => {
      if (!canGenerateReport.value) {
        return
      }

      loading.value = true
      error.value = null

      try {
        const params = {
          startDate: dateRange.startDate,
          endDate: dateRange.endDate,
          ...additionalFilters
        }

        // Call the appropriate service method based on report type
        let data
        switch (selectedReportType.value) {
          case 'financial':
            data = await reportsService.getFinancialReport(params)
            break
          case 'sales':
            data = await reportsService.getSalesReport(params)
            break
          case 'products':
            data = await reportsService.getProductsReport(params)
            break
          case 'users':
            data = await reportsService.getUsersReport(params)
            break
          case 'orders':
            data = await reportsService.getOrdersReport(params)
            break
          default:
            throw new Error(`Unknown report type: ${selectedReportType.value}`)
        }

        reportData.value = data
        showToast('Report generated successfully', 'success')
      } catch (err) {
        error.value = err.message || 'Failed to load report data'
        showToast('Failed to generate report', 'error')
        console.error('Report loading error:', err)
      } finally {
        loading.value = false
      }
    }

    const handleFiltersChange = () => {
      // Reset report data when filters change
      reportData.value = null
      error.value = null
      
      // Auto-generate report if all required filters are set
      if (canGenerateReport.value) {
        loadReportData()
      }
    }

    const handleExportStarted = (format) => {
      showToast(`Starting ${format.toUpperCase()} export...`, 'info')
    }

    const handleExportCompleted = (format, filename) => {
      showToast(`${format.toUpperCase()} export completed: ${filename}`, 'success')
    }

    const handleExportError = (format, error) => {
      showToast(`${format.toUpperCase()} export failed: ${error}`, 'error')
    }

    // Watchers
    watch([selectedReportType, () => dateRange.startDate, () => dateRange.endDate], () => {
      handleFiltersChange()
    }, { deep: true })

    // Lifecycle
    onMounted(() => {
      // Load initial report if parameters are valid
      if (canGenerateReport.value) {
        loadReportData()
      }
    })

    return {
      loading,
      error,
      reportData,
      selectedReportType,
      dateRange,
      additionalFilters,
      canGenerateReport,
      loadReportData,
      handleFiltersChange,
      handleExportStarted,
      handleExportCompleted,
      handleExportError
    }
  }
}
</script>

<style scoped>
.reports-page {
  padding: 24px;
  background-color: #f8fafc;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 800px;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-title i {
  font-size: 2rem;
}

.page-description {
  font-size: 1.1rem;
  opacity: 0.9;
  line-height: 1.6;
  margin: 0;
}

.filters-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.report-content {
  background: white;
  border-radius: 12px;
  min-height: 400px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.content-wrapper {
  padding: 24px;
}

.loading-container,
.error-container,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
}

.loading-spinner i {
  font-size: 3rem;
  color: #667eea;
  margin-bottom: 16px;
}

.loading-spinner p {
  font-size: 1.1rem;
  color: #6b7280;
}

.error-message i {
  font-size: 3rem;
  color: #ef4444;
  margin-bottom: 16px;
}

.error-message h3 {
  font-size: 1.5rem;
  color: #374151;
  margin-bottom: 8px;
}

.error-message p {
  color: #6b7280;
  margin-bottom: 24px;
}

.retry-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #dc2626;
}

.empty-message i {
  font-size: 3rem;
  color: #9ca3af;
  margin-bottom: 16px;
}

.empty-message h3 {
  font-size: 1.5rem;
  color: #374151;
  margin-bottom: 8px;
}

.empty-message p {
  color: #6b7280;
}

.export-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-top: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}
</style>
