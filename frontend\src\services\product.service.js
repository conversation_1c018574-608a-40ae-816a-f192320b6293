import apiClient from './api.service';

class ProductService {
  // Get all products with optional filtering and pagination
  async getAll(params = {}) {
    return await apiClient.get('/products', { params });
  }

  // Get product by ID
  async getById(id) {
    return await apiClient.get(`/products/${id}`);
  }

  // Create new product
  async create(productData) {
    return await apiClient.post('/products', productData);
  }

  // Update existing product
  async update(id, productData) {
    return await apiClient.put(`/products/${id}`, productData);
  }

  // Delete product
  async delete(id) {
    return await apiClient.delete(`/products/${id}`);
  }

  // Upload product image
  async uploadImage(productId, formData) {
    return await apiClient.post(`/productimages/${productId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  // Delete product image
  async deleteImage(imageId) {
    return await apiClient.delete(`/productimages/${imageId}`);
  }

  // Get product statistics
  async getStats() {
    return await apiClient.get('/products/stats');
  }

  async getFilters(categorySlug) {
    return await apiClient.get(`/products/filters/${categorySlug}`)
  }
}

export default new ProductService();
