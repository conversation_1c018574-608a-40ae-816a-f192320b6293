/* ===== ADMIN ORDERS PAGE STYLES ===== */
/* Based on Reports page design patterns */

/* ===== ORDERS PAGE LAYOUT ===== */
.admin-orders {
  padding: var(--admin-space-2xl);
  background-color: var(--admin-bg-primary);
  min-height: 100vh;
}

.admin-orders .admin-page-header {
  background: var(--admin-gradient-primary);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-3xl);
  margin-bottom: var(--admin-space-2xl);
  color: white;
  box-shadow: var(--admin-shadow-lg);
}

.admin-orders .admin-page-title {
  font-size: var(--admin-text-4xl);
  font-weight: var(--admin-font-bold);
  color: white;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-lg);
}

/* ===== ORDER FILTERS ===== */
.admin-order-filters {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  margin-bottom: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
}

.admin-order-filters .field {
  margin-bottom: var(--admin-space-lg);
}

.admin-order-filters .label {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-700);
  margin-bottom: var(--admin-space-sm);
  font-size: var(--admin-text-sm);
}

.admin-order-filters .input,
.admin-order-filters .select select {
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-md);
  font-size: var(--admin-text-sm);
  transition: all var(--admin-transition-fast);
}

.admin-order-filters .input:focus,
.admin-order-filters .select select:focus {
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px var(--admin-primary-bg);
  outline: none;
}

.admin-order-filters .control.has-icons-left .icon {
  color: var(--admin-gray-400);
}

.admin-order-filters .buttons {
  margin-top: var(--admin-space-xl);
  display: flex;
  justify-content: center;
  gap: var(--admin-space-md);
}

/* ===== ORDER TABLE ===== */
.admin-order-table {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  margin-bottom: var(--admin-space-2xl);
}

.order-table {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  margin-bottom: var(--admin-space-2xl);
}

.admin-order-table .table,
.order-table .table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.admin-order-table .table th,
.order-table .table th {
  background: var(--admin-gray-50);
  color: var(--admin-gray-700);
  font-weight: var(--admin-font-semibold);
  padding: var(--admin-space-lg);
  text-align: left;
  border-bottom: 2px solid var(--admin-border-color);
  font-size: var(--admin-text-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-order-table .table td,
.order-table .table td {
  padding: var(--admin-space-lg);
  border-bottom: 1px solid var(--admin-border-light);
  vertical-align: middle;
  font-size: var(--admin-text-sm);
}

.admin-order-table .table tr:hover,
.order-table .table tr:hover {
  background: var(--admin-gray-25);
}

.admin-order-table .table tr:last-child td,
.order-table .table tr:last-child td {
  border-bottom: none;
}

/* ===== ORDER ID ===== */
.admin-order-id {
  font-family: var(--admin-font-mono);
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-700);
  background: var(--admin-gray-100);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  display: inline-block;
  font-weight: var(--admin-font-medium);
}

/* ===== ORDER CUSTOMER INFO ===== */
.admin-order-customer {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-order-customer-name {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
}

.admin-order-customer-email {
  font-size: var(--admin-text-xs);
  color: var(--admin-gray-500);
  font-family: var(--admin-font-mono);
}

/* ===== ORDER DATE ===== */
.admin-order-date {
  color: var(--admin-gray-700);
  font-size: var(--admin-text-sm);
}

/* ===== ORDER TOTAL ===== */
.admin-order-total {
  font-weight: var(--admin-font-bold);
  color: var(--admin-success-dark);
  background: var(--admin-success-bg);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-md);
  border: 1px solid var(--admin-success-light);
  display: inline-block;
  font-size: var(--admin-text-sm);
}

/* ===== ORDER STATUS ===== */
.admin-order-status {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-full);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-order-status.processing {
  background: var(--admin-info-bg);
  color: var(--admin-info-dark);
  border: 1px solid var(--admin-info-light);
}

.admin-order-status.pending {
  background: var(--admin-warning-bg);
  color: var(--admin-warning-dark);
  border: 1px solid var(--admin-warning-light);
}

.admin-order-status.shipped {
  background: var(--admin-primary-bg);
  color: var(--admin-primary-dark);
  border: 1px solid var(--admin-primary-light);
}

.admin-order-status.delivered {
  background: var(--admin-success-bg);
  color: var(--admin-success-dark);
  border: 1px solid var(--admin-success-light);
}

.admin-order-status.cancelled {
  background: var(--admin-danger-bg);
  color: var(--admin-danger-dark);
  border: 1px solid var(--admin-danger-light);
}

/* ===== PAYMENT STATUS ===== */
.admin-payment-status {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-full);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-payment-status.completed {
  background: var(--admin-success-bg);
  color: var(--admin-success-dark);
  border: 1px solid var(--admin-success-light);
}

.admin-payment-status.pending {
  background: var(--admin-warning-bg);
  color: var(--admin-warning-dark);
  border: 1px solid var(--admin-warning-light);
}

.admin-payment-status.failed {
  background: var(--admin-danger-bg);
  color: var(--admin-danger-dark);
  border: 1px solid var(--admin-danger-light);
}

.admin-payment-status.refunded {
  background: var(--admin-gray-100);
  color: var(--admin-gray-600);
  border: 1px solid var(--admin-gray-200);
}

/* ===== ORDER ACTION BUTTONS ===== */
.admin-order-actions {
  display: flex;
  gap: var(--admin-space-xs);
  align-items: center;
}

.admin-order-actions .button {
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-md);
  border: 1px solid transparent;
  font-size: var(--admin-text-xs);
  transition: all var(--admin-transition-fast);
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-order-actions .button.is-info {
  background: var(--admin-info);
  color: white;
  border-color: var(--admin-info);
}

.admin-order-actions .button.is-info:hover {
  background: var(--admin-info-dark);
  border-color: var(--admin-info-dark);
  transform: translateY(-1px);
}

.admin-order-actions .button.is-primary {
  background: var(--admin-primary);
  color: white;
  border-color: var(--admin-primary);
}

.admin-order-actions .button.is-primary:hover {
  background: var(--admin-primary-dark);
  border-color: var(--admin-primary-dark);
  transform: translateY(-1px);
}

.admin-order-actions .button.is-success {
  background: var(--admin-success);
  color: white;
  border-color: var(--admin-success);
}

.admin-order-actions .button.is-success:hover {
  background: var(--admin-success-dark);
  border-color: var(--admin-success-dark);
  transform: translateY(-1px);
}

/* ===== LOADING STATES ===== */
.admin-order-table .admin-loading,
.order-table .admin-loading {
  text-align: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.admin-order-table .admin-loading i,
.order-table .admin-loading i {
  font-size: 2rem;
  color: var(--admin-primary);
  margin-bottom: var(--admin-space-lg);
  animation: spin 1s linear infinite;
}

.admin-order-table .admin-empty,
.order-table .admin-empty {
  text-align: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.admin-order-table .admin-empty i,
.order-table .admin-empty i {
  font-size: 3rem;
  color: var(--admin-gray-300);
  margin-bottom: var(--admin-space-lg);
}

.admin-order-table .admin-empty-text,
.order-table .admin-empty-text {
  font-size: var(--admin-text-lg);
  margin-bottom: var(--admin-space-sm);
}

.admin-order-table .admin-empty-subtext,
.order-table .admin-empty-subtext {
  color: var(--admin-gray-400);
  font-size: var(--admin-text-sm);
}

/* ===== PAGINATION WRAPPER ===== */
.admin-orders .pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: var(--admin-space-2xl);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .admin-order-table .table,
  .order-table .table {
    font-size: var(--admin-text-xs);
  }
  
  .admin-order-table .table th,
  .admin-order-table .table td,
  .order-table .table th,
  .order-table .table td {
    padding: var(--admin-space-md);
  }
}

@media (max-width: 768px) {
  .admin-orders {
    padding: var(--admin-space-lg);
  }
  
  .admin-orders .admin-page-header {
    padding: var(--admin-space-2xl);
  }
  
  .admin-orders .admin-page-title {
    font-size: var(--admin-text-2xl);
  }
  
  .admin-order-filters {
    padding: var(--admin-space-lg);
  }
  
  .admin-order-table,
  .order-table {
    padding: var(--admin-space-lg);
    overflow-x: auto;
  }
  
  .admin-order-table .table,
  .order-table .table {
    min-width: 700px;
  }
  
  .admin-order-actions {
    flex-direction: column;
    gap: var(--admin-space-xs);
  }
  
  .admin-order-actions .button {
    width: 100%;
    min-width: auto;
  }
}
