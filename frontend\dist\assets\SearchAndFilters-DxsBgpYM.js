import{_ as V,g as F,h as U,c as s,o as l,a as e,k as p,n as x,t as d,z as u,C as m,F as _,p as b,A as k,d as I,N}from"./index-BKy0rL_2.js";const A={class:"search-and-filters"},j={class:"box"},w={class:"columns"},B={class:"field"},L={class:"label"},O={class:"control has-icons-left"},z=["placeholder"],D={class:"field"},E={class:"label"},M={class:"control"},P={key:0,class:"select is-fullwidth"},R=["onUpdate:modelValue"],T={value:""},Q=["value"],$=["onUpdate:modelValue"],q=["placeholder","onUpdate:modelValue"],G=["placeholder","onUpdate:modelValue"],H={class:"column is-2"},J={class:"field"},K={class:"control"},W=["disabled"],X={key:0,class:"level"},Y={class:"level-left"},Z={class:"level-item"},ee={class:"has-text-grey"},te={key:0},se={key:1},le={key:0},ae={class:"level-right"},oe={class:"level-item"},ne={__name:"SearchAndFilters",props:{filters:{type:Object,default:()=>({})},filterFields:{type:Object,default:()=>({})},searchLabel:{type:String,default:"Search"},searchPlaceholder:{type:String,default:"Search..."},searchColumnClass:{type:String,default:"is-4"},totalItems:{type:Number,default:null},itemName:{type:String,default:"items"},loading:{type:Boolean,default:!1}},emits:["search-changed","filter-changed","reset-filters"],setup(t,{emit:g}){const h=t,v=g,r=F(""),y=U(()=>Object.values(h.filters).some(c=>c!==""&&c!==null&&c!==void 0)),S=()=>{v("search-changed",r.value)},f=()=>{v("filter-changed",h.filters)},C=()=>{r.value="",Object.keys(h.filters).forEach(c=>{h.filters[c]=""}),v("reset-filters")};return(c,i)=>(l(),s("div",A,[e("div",j,[e("div",w,[e("div",{class:x(["column",t.searchColumnClass])},[e("div",B,[e("label",L,d(t.searchLabel),1),e("div",O,[u(e("input",{type:"text",class:"input",placeholder:t.searchPlaceholder,"onUpdate:modelValue":i[0]||(i[0]=o=>r.value=o),onInput:S},null,40,z),[[m,r.value]]),i[1]||(i[1]=e("span",{class:"icon is-small is-left"},[e("i",{class:"fas fa-search"})],-1))])])],2),(l(!0),s(_,null,b(t.filterFields,(o,n)=>(l(),s("div",{key:n,class:"column is-3"},[e("div",D,[e("label",E,d(o.label),1),e("div",M,[o.type==="select"?(l(),s("div",P,[u(e("select",{"onUpdate:modelValue":a=>t.filters[n]=a,onChange:f},[e("option",T,"All "+d(o.label),1),(l(!0),s(_,null,b(o.options,a=>(l(),s("option",{key:a.value,value:a.value},d(a.label),9,Q))),128))],40,R),[[k,t.filters[n]]])])):o.type==="date"?u((l(),s("input",{key:1,type:"date",class:"input","onUpdate:modelValue":a=>t.filters[n]=a,onChange:f},null,40,$)),[[m,t.filters[n]]]):o.type==="number"?u((l(),s("input",{key:2,type:"number",class:"input",placeholder:o.placeholder,"onUpdate:modelValue":a=>t.filters[n]=a,onInput:f},null,40,q)),[[m,t.filters[n]]]):u((l(),s("input",{key:3,type:"text",class:"input",placeholder:o.placeholder,"onUpdate:modelValue":a=>t.filters[n]=a,onInput:f},null,40,G)),[[m,t.filters[n]]])])])]))),128)),e("div",H,[e("div",J,[i[3]||(i[3]=e("label",{class:"label"}," ",-1)),e("div",K,[e("button",{class:"button is-light is-fullwidth",onClick:C,disabled:t.loading},i[2]||(i[2]=[e("span",{class:"icon"},[e("i",{class:"fas fa-undo"})],-1),e("span",null,"Reset",-1)]),8,W)])])])]),t.totalItems!==null?(l(),s("div",X,[e("div",Y,[e("div",Z,[e("p",ee,[t.loading?(l(),s("span",te,"Loading...")):(l(),s("span",se,[I(" Showing "+d(t.totalItems)+" "+d(t.itemName)+" ",1),r.value||y.value?(l(),s("span",le," (filtered) ")):p("",!0)]))])])]),e("div",ae,[e("div",oe,[N(c.$slots,"actions",{},void 0)])])])):p("",!0)])]))}},ce=V(ne,[["__scopeId","data-v-293c4f0e"]]);export{ce as S};
