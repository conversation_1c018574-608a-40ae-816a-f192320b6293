import apiService from '@/services/api';

// Cache for API responses
const cache = {
  categories: null,
  categoryTree: null,
  categoryDetails: {},
  lastFetched: {
    categories: null,
    categoryTree: null
  },
  cacheTimeout: 5 * 60 * 1000 // 5 minutes in milliseconds
};

// Check if cache is valid
const isCacheValid = (key) => {
  if (!cache.lastFetched[key]) return false;
  const now = new Date().getTime();
  return (now - cache.lastFetched[key]) < cache.cacheTimeout;
};

export const categoriesService = {
  async getAll(params = {}) {
    return categoriesService.getCategories(params);
  },

  async getCategories(params = {}) {
    // If no specific filters and cache is valid, use cached data
    const hasFilters = Object.keys(params).length > 0;
    if (!hasFilters && isCacheValid('categories') && cache.categories) {
      return cache.categories;
    }

    try {
      // First try the admin endpoint (which doesn't exist, so skip to public)
      try {
        console.log('Fetching categories with params:', params);
        // Skip admin endpoint for now and use public endpoint directly
        const response = await apiService.get('/api/categories/all', { params });
        console.log('Categories API response:', response.data);

        // Handle paginated response from backend
        let transformedData = {};

        if (response.data) {
          // Check if it's a paginated response (PaginatedResponse<CategoryResponse>)
          if (response.data.data && Array.isArray(response.data.data)) {
            transformedData = {
              data: response.data.data,
              total: response.data.total || 0,
              currentPage: response.data.currentPage || 1,
              totalPages: response.data.lastPage || 1,
              totalItems: response.data.total || 0,
              pageSize: response.data.perPage || 15,
              from: response.data.from || 0,
              to: response.data.to || 0,
              // For backward compatibility
              categories: response.data.data,
              totalCount: response.data.total || 0
            };
          }
          // Check if it's a direct array (fallback)
          else if (Array.isArray(response.data)) {
            transformedData = {
              data: response.data,
              total: response.data.length,
              currentPage: 1,
              totalPages: 1,
              totalItems: response.data.length,
              pageSize: response.data.length,
              from: response.data.length > 0 ? 1 : 0,
              to: response.data.length,
              categories: response.data,
              totalCount: response.data.length
            };
          }
          else {
            // Unknown format, return empty
            transformedData = {
              data: [],
              total: 0,
              currentPage: 1,
              totalPages: 1,
              totalItems: 0,
              pageSize: 15,
              from: 0,
              to: 0,
              categories: [],
              totalCount: 0
            };
          }
        }

        // Cache the response if no filters were applied
        if (!hasFilters) {
          cache.categories = transformedData;
          cache.lastFetched.categories = new Date().getTime();
        }

        return transformedData;
      } catch (adminError) {
        // If admin endpoint fails, try the public endpoint
        console.warn('Admin categories endpoint failed, falling back to public endpoint:', adminError.message);

        try {
          const fallbackResponse = await apiService.get('/api/categories/all', { params });
          console.log('Fallback categories API response:', fallbackResponse.data);

          let transformedData = {};

          if (fallbackResponse.data) {
            if (fallbackResponse.data.data && Array.isArray(fallbackResponse.data.data)) {
              transformedData = {
                data: fallbackResponse.data.data,
                total: fallbackResponse.data.total || 0,
                currentPage: fallbackResponse.data.currentPage || 1,
                totalPages: fallbackResponse.data.lastPage || 1,
                totalItems: fallbackResponse.data.total || 0,
                pageSize: fallbackResponse.data.perPage || 15,
                categories: fallbackResponse.data.data,
                totalCount: fallbackResponse.data.total || 0
              };
            } else if (Array.isArray(fallbackResponse.data)) {
              transformedData = {
                data: fallbackResponse.data,
                total: fallbackResponse.data.length,
                currentPage: 1,
                totalPages: 1,
                totalItems: fallbackResponse.data.length,
                pageSize: fallbackResponse.data.length,
                categories: fallbackResponse.data,
                totalCount: fallbackResponse.data.length
              };
            }
          }

          // Cache the transformed response
          if (!hasFilters) {
            cache.categories = transformedData;
            cache.lastFetched.categories = new Date().getTime();
          }

          return transformedData;
        } catch (publicError) {
          console.error('Both admin and public categories endpoints failed:', publicError.message);

          // Return empty data structure instead of throwing error
          const emptyData = {
            data: [],
            total: 0,
            categories: [],
            totalCount: 0
          };

          return emptyData;
        }
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  },

  async getCategoryById(id) {
    // Check if we have this category in cache
    if (cache.categoryDetails[id]) {
      return cache.categoryDetails[id];
    }

    try {
      const response = await apiService.get(`/api/admin/categories/${id}`);

      // Cache the response
      cache.categoryDetails[id] = response.data;

      return response.data;
    } catch (error) {
      console.error(`Error fetching category ${id}:`, error);
      throw error;
    }
  },

  async getById(id) {
    try {
      const response = await apiService.get(`/api/admin/categories/${id}`);

      // Update cache if this category exists
      if (cache.categoryDetails[id]) {
        cache.categoryDetails[id] = response.data;
      }

      return response.data;
    } catch (error) {
      console.error(`Error fetching category ${id}:`, error);
      throw error;
    }
  },

  async getBySlug(slug) {
    try {
      const response = await apiService.get(`/api/categories/slug/${slug}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching category by slug ${slug}:`, error);
      throw error;
    }
  },

  async createCategory(categoryData) {
    try {
      const response = await apiService.post('/api/admin/categories', categoryData);

      // Invalidate cache
      cache.categories = null;
      cache.categoryTree = null;
      cache.lastFetched.categories = null;
      cache.lastFetched.categoryTree = null;

      return response.data;
    } catch (error) {
      console.error('Error creating category:', error);
      throw error;
    }
  },

  async updateCategory(id, categoryData) {
    try {
      const response = await apiService.put(`/api/admin/categories/${id}`, categoryData);

      // Invalidate cache
      cache.categories = null;
      cache.categoryTree = null;
      cache.lastFetched.categories = null;
      cache.lastFetched.categoryTree = null;

      // Update category details cache if it exists
      if (cache.categoryDetails[id]) {
        cache.categoryDetails[id] = response.data.category || response.data;
      }

      return response.data;
    } catch (error) {
      console.error(`Error updating category ${id}:`, error);
      throw error;
    }
  },

  async deleteCategory(id) {
    try {
      const response = await apiService.delete(`/api/admin/categories/${id}`);

      // Invalidate cache
      cache.categories = null;
      cache.categoryTree = null;
      cache.lastFetched.categories = null;
      cache.lastFetched.categoryTree = null;

      // Remove from category details cache
      if (cache.categoryDetails[id]) {
        delete cache.categoryDetails[id];
      }

      return response.data;
    } catch (error) {
      console.error(`Error deleting category ${id}:`, error);
      throw error;
    }
  },

  // Alias for deleteCategory
  async delete(id) {
    return this.deleteCategory(id);
  },

  async uploadCategoryImage(id, imageFile) {
    try {
      const formData = new FormData();
      formData.append('image', imageFile);

      const response = await apiService.post(`/api/admin/categories/${id}/image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      // Invalidate specific category in cache
      if (cache.categoryDetails[id]) {
        delete cache.categoryDetails[id];
      }

      return response.data;
    } catch (error) {
      console.error(`Error uploading image for category ${id}:`, error);
      throw error;
    }
  },



  async getCategoryTree() {
    // If cache is valid, use cached data
    if (isCacheValid('categoryTree') && cache.categoryTree) {
      return cache.categoryTree;
    }

    try {
      // First try the admin endpoint
      try {
        const params = filter ? { filter } : {};
        const response = await apiService.get('/api/admin/categories/tree', { params });

        // Cache the response
        cache.categoryTree = response.data;
        cache.lastFetched.categoryTree = new Date().getTime();

        return response.data;
      } catch (adminError) {
        // If admin endpoint fails, build tree from regular categories endpoint
        console.warn('Admin categories tree endpoint failed, building tree from regular categories:', adminError.message);

        // Get all categories and build tree manually
        const categoriesResponse = await this.getCategories();
        const allCategories = categoriesResponse.categories || [];

        // Build tree structure
        const tree = this.buildCategoryTree(allCategories);

        // Cache the tree
        cache.categoryTree = tree;
        cache.lastFetched.categoryTree = new Date().getTime();

        return tree;
      }
    } catch (error) {
      console.error('Error fetching category tree:', error);

      // Provide a more user-friendly error message
      const errorMessage = error.response?.status === 404
        ? 'Category tree endpoint not found. Please check API configuration.'
        : error.response?.status === 403
          ? 'You do not have permission to access the category tree.'
          : error.response?.status === 401
            ? 'Authentication required. Please log in again.'
            : error.message || 'Failed to fetch category tree';

      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = error;
      throw enhancedError;
    }
  },

  // Helper method to build a category tree from a flat list
  buildCategoryTree(categories) {
    // First, create a map of all categories by ID for quick lookup
    const categoryMap = {};
    categories.forEach(category => {
      categoryMap[category.id] = {
        ...category,
        children: []
      };
    });

    // Build the tree structure
    const rootCategories = [];
    categories.forEach(category => {
      const categoryWithChildren = categoryMap[category.id];

      if (!category.parentId) {
        // This is a root category
        rootCategories.push(categoryWithChildren);
      } else if (categoryMap[category.parentId]) {
        // This is a child category, add it to its parent
        categoryMap[category.parentId].children.push(categoryWithChildren);
      } else {
        // Parent doesn't exist, treat as root
        rootCategories.push(categoryWithChildren);
      }
    });

    return rootCategories;
  },

  async moveCategory(id, newParentId) {
    try {
      const response = await apiService.patch(`/api/admin/categories/${id}/move`, { parentId: newParentId });

      // Invalidate cache
      cache.categories = null;
      cache.categoryTree = null;
      cache.lastFetched.categories = null;
      cache.lastFetched.categoryTree = null;

      // Update category details cache if it exists
      if (cache.categoryDetails[id]) {
        delete cache.categoryDetails[id];
      }

      return response.data;
    } catch (error) {
      console.error(`Error moving category ${id}:`, error);
      throw error;
    }
  },

  // Get category statistics
  async getStats() {
    try {
      const response = await apiService.get('/api/admin/categories/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching category stats:', error);
      throw error;
    }
  },

  // Get category products
  async getCategoryProducts(categoryId, params = {}) {
    try {
      // Use products API with category filter
      const response = await apiService.get('/api/admin/products', {
        params: {
          ...params,
          categoryId: categoryId
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching products for category ${categoryId}:`, error);
      throw error;
    }
  },

  // Get subcategories
  async getSubcategories(categoryId, params = {}) {
    try {
      // Use categories API with parent filter
      const response = await apiService.get('/api/admin/categories', {
        params: {
          ...params,
          parentId: categoryId,
          pageSize: 100 // Get all subcategories
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching subcategories for category ${categoryId}:`, error);
      throw error;
    }
  },

  // Bulk update products category
  async bulkUpdateProductsCategory(fromCategoryId, toCategoryId) {
    try {
      const response = await apiService.post(`/api/admin/categories/${fromCategoryId}/bulk-update-products-category`, {
        toCategoryId: toCategoryId
      });

      // Invalidate cache since products have moved
      cache.categories = null;
      cache.categoryTree = null;
      cache.lastFetched.categories = null;
      cache.lastFetched.categoryTree = null;

      // Clear category details cache for both categories
      if (cache.categoryDetails[fromCategoryId]) {
        delete cache.categoryDetails[fromCategoryId];
      }
      if (cache.categoryDetails[toCategoryId]) {
        delete cache.categoryDetails[toCategoryId];
      }

      return response.data;
    } catch (error) {
      console.error(`Error bulk updating products category from ${fromCategoryId} to ${toCategoryId}:`, error);
      throw error;
    }
  },

  // Helper method to clear cache
  clearCache() {
    cache.categories = null;
    cache.categoryTree = null;
    cache.categoryDetails = {};
    cache.lastFetched.categories = null;
    cache.lastFetched.categoryTree = null;
  }
};
