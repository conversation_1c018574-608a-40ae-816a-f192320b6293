import axios from 'axios';
import store from '@/store';
import requestManager from './request-manager';

// Create axios instance
const api = axios.create({
  // Use relative URLs in development to leverage Vite proxy, direct URL in production
  baseURL: import.meta.env.DEV ? '' : (import.meta.env.VITE_API_URL || 'http://localhost:5296'),
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  // Add a global timeout to prevent infinite hanging requests
  timeout: 30000 // 30 seconds
});

// Add a request interceptor to add the auth token to requests
api.interceptors.request.use(
  (config) => {
    // Track request in the loading store
    store.dispatch('loading/startRequest');

    // Add auth token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    store.dispatch('loading/finishRequest');
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle common errors
api.interceptors.response.use(
  (response) => {
    // Complete request in the loading store
    store.dispatch('loading/finishRequest');
    return response;
  },
  (error) => {
    // Complete request in the loading store
    store.dispatch('loading/finishRequest');

    // Skip handling for cancelled requests
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return Promise.reject(error);
    }

    // Handle 401 Unauthorized errors (token expired or invalid)
    if (error.response && error.response.status === 401) {
      // Clear local storage
      localStorage.removeItem('token');
      localStorage.removeItem('user');

      // Redirect to login page if not already there
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }

    // Handle 403 Forbidden errors (insufficient permissions)
    if (error.response && error.response.status === 403) {
      console.error('Permission denied:', error.response.data.message || 'You do not have permission to perform this action');
    }

    // Handle 404 Not Found errors
    if (error.response && error.response.status === 404) {
      console.error('Resource not found:', error.response.data.message || 'The requested resource was not found');
    }

    // Handle 422 Validation errors
    if (error.response && error.response.status === 422) {
      console.error('Validation error:', error.response.data.errors || error.response.data.message || 'Validation failed');
    }

    // Handle 500 Server errors
    if (error.response && error.response.status >= 500) {
      console.error('Server error:', error.response.data.message || 'An unexpected server error occurred');
    }

    // Handle network errors
    if (error.message === 'Network Error') {
      console.error('Network error: Please check your internet connection');
    }

    // Handle timeout errors
    if (error.code === 'ECONNABORTED') {
      console.error('Request timeout: The server took too long to respond');
      error.isTimeout = true;
    }

    // Log all errors for debugging
    console.error('API Error:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      message: error.message,
      code: error.code
    });

    return Promise.reject(error);
  }
);

// Helper methods for common API operations with cancellation support
const apiService = {
  // GET request
  async get(url, config = {}) {
    try {
      // Create a cancellable request
      const controller = new AbortController();
      const configWithSignal = { ...config, signal: controller.signal };

      // Add to pending requests
      const requestKey = `GET:${url}`;
      if (requestManager.pendingRequests.has(requestKey)) {
        requestManager.pendingRequests.get(requestKey).abort();
      }
      requestManager.pendingRequests.set(requestKey, controller);

      const response = await api.get(url, configWithSignal);

      // Remove from pending requests
      requestManager.pendingRequests.delete(requestKey);

      return response;
    } catch (error) {
      if (!axios.isCancel(error)) {
        console.error(`GET ${url} error:`, error);
      }
      throw error;
    }
  },

  // POST request
  async post(url, data = {}, config = {}) {
    try {
      // Create a cancellable request
      const controller = new AbortController();
      const configWithSignal = { ...config, signal: controller.signal };

      // Add to pending requests
      const requestKey = `POST:${url}`;
      if (requestManager.pendingRequests.has(requestKey)) {
        requestManager.pendingRequests.get(requestKey).abort();
      }
      requestManager.pendingRequests.set(requestKey, controller);

      const response = await api.post(url, data, configWithSignal);

      // Remove from pending requests
      requestManager.pendingRequests.delete(requestKey);

      return response;
    } catch (error) {
      if (!axios.isCancel(error)) {
        console.error(`POST ${url} error:`, error);
      }
      throw error;
    }
  },

  // PUT request
  async put(url, data = {}, config = {}) {
    try {
      // Create a cancellable request
      const controller = new AbortController();
      const configWithSignal = { ...config, signal: controller.signal };

      // Add to pending requests
      const requestKey = `PUT:${url}`;
      if (requestManager.pendingRequests.has(requestKey)) {
        requestManager.pendingRequests.get(requestKey).abort();
      }
      requestManager.pendingRequests.set(requestKey, controller);

      const response = await api.put(url, data, configWithSignal);

      // Remove from pending requests
      requestManager.pendingRequests.delete(requestKey);

      return response;
    } catch (error) {
      if (!axios.isCancel(error)) {
        console.error(`PUT ${url} error:`, error);
      }
      throw error;
    }
  },

  // PATCH request
  async patch(url, data = {}, config = {}) {
    try {
      // Create a cancellable request
      const controller = new AbortController();
      const configWithSignal = { ...config, signal: controller.signal };

      // Add to pending requests
      const requestKey = `PATCH:${url}`;
      if (requestManager.pendingRequests.has(requestKey)) {
        requestManager.pendingRequests.get(requestKey).abort();
      }
      requestManager.pendingRequests.set(requestKey, controller);

      const response = await api.patch(url, data, configWithSignal);

      // Remove from pending requests
      requestManager.pendingRequests.delete(requestKey);

      return response;
    } catch (error) {
      if (!axios.isCancel(error)) {
        console.error(`PATCH ${url} error:`, error);
      }
      throw error;
    }
  },

  // DELETE request
  async delete(url, config = {}) {
    try {
      // Create a cancellable request
      const controller = new AbortController();
      const configWithSignal = { ...config, signal: controller.signal };

      // Add to pending requests
      const requestKey = `DELETE:${url}`;
      if (requestManager.pendingRequests.has(requestKey)) {
        requestManager.pendingRequests.get(requestKey).abort();
      }
      requestManager.pendingRequests.set(requestKey, controller);

      const response = await api.delete(url, configWithSignal);

      // Remove from pending requests
      requestManager.pendingRequests.delete(requestKey);

      return response;
    } catch (error) {
      if (!axios.isCancel(error)) {
        console.error(`DELETE ${url} error:`, error);
      }
      throw error;
    }
  },

  // Upload file
  async upload(url, formData, onUploadProgress = null, config = {}) {
    try {
      // Create a cancellable request
      const controller = new AbortController();
      const uploadConfig = {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        ...config,
        signal: controller.signal
      };

      if (onUploadProgress) {
        uploadConfig.onUploadProgress = onUploadProgress;
      }

      // Add to pending requests
      const requestKey = `UPLOAD:${url}`;
      if (requestManager.pendingRequests.has(requestKey)) {
        requestManager.pendingRequests.get(requestKey).abort();
      }
      requestManager.pendingRequests.set(requestKey, controller);

      const response = await api.post(url, formData, uploadConfig);

      // Remove from pending requests
      requestManager.pendingRequests.delete(requestKey);

      return response;
    } catch (error) {
      if (!axios.isCancel(error)) {
        console.error(`UPLOAD ${url} error:`, error);
      }
      throw error;
    }
  },

  // Cancel all pending requests
  cancelAllRequests() {
    requestManager.cancelAllRequests();
  },

  // Cancel requests for a specific route
  cancelRequestsForRoute(routePath) {
    requestManager.cancelRequestsForRoute(routePath);
  }
};

export { api };
export default apiService;
